<template>
	<!-- 家庭住址信息 -->
	<view class="container">
		<u-navbar bgColor="transparent" placeholder :autoBack="true" />
		<scroll-view class="scroll-view" :scroll-y="true">
			<view class="scroll-container">
				<view class="title">家庭住址信息</view>
				<view class="sub-title">招聘呗利用您的地址信息为您提供更为个性化的求职岗位，您可在此页面管理地址信息</view>

				<view class="card-container">
					<view class="title">如何添加您的家庭住址信息</view>
					<view class="add-box">
						<view class="item">
							<image
								class="image"
								src="https://api-test.zhaopinbei.com/storage/uploads/images/hrkKJjNil3h2oTvJhiRc8obobenNOjzS7gmQegO2.png"></image>
							<text class="text">您可以在“看公司地址”页面手动添加家庭住址信息</text>
						</view>
					</view>
				</view>

				<view class="card-container">
					<view class="title">如何使用您的家庭住址信息</view>
					<view class="use-box">
						<view class="item">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/Wgih53aKpmLNIVZzdhmXmyqP4eOSL1jsjJIfTcKd.png"></image>
							<view class="text">
								向您推荐周边岗位、计算通勤距离、将您推荐给距离较近的伯乐。为保护您的求职安全，我们并不会像伯乐展示您的住址信息。
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	},
};
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f7;
	background-image: url(https://api-test.zhaopinbei.com/storage/uploads/images/yh61OTbqqeNkLP7YWlq8jD4gHo4FDul3MSxdHpXI.png);
	background-size: contain;
	background-repeat: no-repeat;
	display: flex;
	flex-direction: column;

	.scroll-view {
		flex: 1;
		overflow-y: auto;

		.scroll-container {
			padding: 32rpx;
			display: flex;
			flex-direction: column;
			gap: 32rpx;
			padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

			.title {
				color: #333333;
				font-size: 32rpx;
			}

			.sub-title {
				color: #777777;
				font-size: 24rpx;
			}

			.card-container {
				padding: 32rpx;
				border-radius: 24rpx;
				background-color: #ffffff;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.use-box {
					display: flex;
					flex-direction: column;
					gap: 30rpx;

					.item {
						display: flex;
						align-items: center;
						gap: 24rpx;

						.image {
							width: 104rpx;
							height: 104rpx;
						}

						.text {
							flex: 1;
							color: #777777;
							font-size: 24rpx;
						}
					}
				}

				.title {
					color: #333333;
					font-size: 28rpx;
				}

				.add-box {
					display: grid;
					grid-template-columns: repeat(1, 1fr);
					gap: 32rpx;

					.item {
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.text {
							color: #666666;
							font-size: 24rpx;
						}

						.image {
							width: 100%;
							height: 190rpx;
							border-radius: 24rpx;
						}
					}
				}
			}
		}
	}
}
</style>
