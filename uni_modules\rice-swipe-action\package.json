{"id": "rice-swipe-action", "displayName": "iOS风格的滑动单元格，支持左滑右滑操作，同时兼容uniapp、uniappx", "version": "1.0.4", "description": "滑动单元格，可左右滑动，iOS风格，左滑删除，滑动操作，同时兼容uniapp/uniappx，支持app，h5和微信小程序，跟手联动，没有延迟", "keywords": ["滑动单元格", "左滑删除", "滑动操作", "左右滑动", "swipe-action"], "repository": "", "engines": {"HBuilderX": "^4.41"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "n", "app-uvue": "y", "app-harmony": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}