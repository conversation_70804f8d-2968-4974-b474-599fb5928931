<template>
	<view>
		<view class="submit">
			<view class="submit-chat">
				<!-- 文本框 -->
                <!-- @input="inputs" -->
				<textarea :auto-height="true" class="chat-send btn" :class="{displaynone:isrecord}"
					@focus="focus" @blur="blur" v-model="msg" :show-confirm-bar="false" adjust-position :cursorSpacing="20"></textarea>
                    <view></view>
				<view class="bt-img">
					<view v-if="sendText" @tap.stop="inputs" class="sendTextBtn">发送</view>
					<image src="../../static/images/chat/sendIcon.png" mode="" @tap="more"  v-else></image>
				</view>
			</view>
			<!-- 更多 -->
			<view class="more" :class="{displaynone:!ismore}">
				<!-- 图片发送方法 -->
				<view class="more-list" @tap="sendImg('album')">
					<image src="../../static/images/chat/imgIcon.png" mode=""></image>
					<view class="more-list-title">图片</view>
				</view>
				<!-- 图片发送方法 -->
				<view class="more-list" @tap="sendImg('camera')">
					<image src="../../static/images/chat/videoIcon.png" mode=""></image>
					<view class="more-list-title">视频</view>
				</view>
				<!-- 获取位置的方法 -->
				<!-- <view class="more-list" @tap="showPopup">
					<image src="../../static/images/chat/changeIcon.png" mode=""></image>
					<view class="more-list-title">切换岗位</view>
				</view> -->
				<!-- <view class="more-list">
					<image src="../../static/images/chat/sendJobIcon.png" mode=""></image>
					<view class="more-list-title">发送职位需求</view>
				</view> -->
				<!-- <view class="more-list">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/eGIVbjEBi1jEe2iEc6DjebYtw8SDLLTXB4Avd5xf.png" mode=""></image>
					<view class="more-list-title">文件</view>
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	// 录音
	// const recorderManager = uni.getRecorderManager();
    import {uploadImg} from '../../config/api.js'
	export default {
		data() {
			return {
				isrecord: false,
				ismore: false,
				pageY: 0,
				msg: "",
				// 直接引用地址可能出不来，需要用require
				// toc: require('../../static/icon/allorder.png'),
				timer: '', //计时器
                sendText:false
			};
		},
		methods: {
			//获取高度方法
			getElementHeight() {
				const query = uni.createSelectorQuery().in(this);
				query.select('.submit').boundingClientRect(data => {
					this.$emit('heights', data.height);
				}).exec();
			},
			//文字发送
			inputs(e) {

                this.sendText = false;
                this.send(this.msg, 'text')
                // 在这里能拿到输入的文字
    //             console.log('res',e)
				// var chatm = e.detail.value;
				// var pos = chatm.indexOf('\n');
				// 检索字符串没有数据，返回-1
				// if (pos != -1 && chatm.length > 1) {
				// this.$emit('inputs', this.msg);
				// setTimeout(() => {
				// 	this.msg = '';
				// }, 0)
				// }

				// if (pos != -1 && chatm.length > 1) {
				// 	// 0为表情和文字
				// 	this.send(this.msg, 0)
				// }

			},
			// 输入框聚焦
			focus() {
                this.sendText = true;
				//关闭其他项
				this.ismore = false;
				setTimeout(() => {
					this.getElementHeight()
				}, 10)
			},
            blur() {
                this.sendText = false;
            },
			// 表格退格
			emojiBack() {
				if (this.msg.length > 0) {
					this.msg = this.msg.substring(0, this.msg.length - 1);
				}
			},
			//更多功能
			more() {
				this.ismore = !this.ismore;
				//切换的时候关闭其他界面
				this.isrecord = false;
				// this.toc = require("../../static/icon/allorder.png");
				setTimeout(() => {
					this.getElementHeight();
				}, 10)
			},
			//图片发送
			sendImg(e) {
				let count = 9;
                let type = 'image';
				if (e == 'album') {
					count = 9;
                   type = 'image';
                   uni.chooseImage({
                   	count: count, //默认9
                   	sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
                   	// sourceType: [e], //从相册选择
                   	success: (res) => {
                   		console.log(JSON.stringify(res.tempFilePaths));
                           // uploadImg
                   		const filePaths = res.tempFilePaths;

                   		for (let i = 0; i < filePaths.length; i++) {
                   			// this.send(filePaths[i], type)
                               this.convertToBase64(filePaths[i], type,filePaths[i].split('.')[1]);
                   		}
                   	}
                   });
				} else {
					count = 1;
                    type = 'video';
                    uni.chooseVideo({
                    	count: count, //默认9
                    	sizeType: ['album', 'camera'], //可以指定是原图还是压缩图，默认二者都有
                    	// sourceType: [e], //从相册选择
                    	success: (res) => {
                    		const filePaths = res.tempFilePath;
                           this.convertToBase64(filePaths, type,filePaths.split('.')[1]);
                    	}
                    });
				}


			},
            convertToBase64(filePath, type,ext) {
                uni.getFileSystemManager().readFile({
                    filePath: filePath,
                    encoding: 'base64',
                    success: (result) => {
                        const content = result.data;
                        this.uploadImg(type,content,ext);
                    },
                    fail: (err) => {
                        console.error('读取文件失败', err);
                    }
                });
            },
            async uploadImg(type,content,ext) {

                let params = {
                    content:content,
                    ext:ext,
                    org_name:'chat'
                }
                const res =  await uploadImg(params)
                this.send(res.data.url,type)
                // 这里是接口调用的逻辑
                // 假设使用 uni.request 来调用上传接口
            },
			//获取位置
			choseLocation() {
				uni.chooseLocation({
					// success: function(res) {
					success: res => {
						let data = {
							name: res.name,
							address: res.address,
							latitude: res.latitude,
							longitude: res.longitude
						}
						this.send(data, 3);
						// console.log('位置名称：' + res.name);
						// console.log('详细地址：' + res.address);
						// console.log('纬度：' + res.latitude);
						// console.log('经度：' + res.longitude);

					}
				});
			},
            showPopup() {
                this.ismore = false;
                this.$emit('showPopup', true);
            },
			//发送
			send(msg, type) {
                if(!msg) {
                    return
                }
                this.ismore = false;
				let date = {
					message: msg,
					type: type
				}
                console.log('子组件发送',date)
				this.$emit('inputs', date);
				setTimeout(() => {
					this.msg = '';
				}, 0)
			}
		}
	};
</script>

<style lang="scss" scoped>
	.submit {
		background: #fff;
		border-top: 1px solid rgba(39, 40, 50, 0.1);
		width: 100%;
		position: fixed;
		bottom: 30rpx;
		z-index: 100;
		// padding-bottom: var(--status-bar-height);
		// padding-bottom: env(safe-area-inset-bottom);
	}

	.displaynone {
		display: none;
	}

	.submit-chat {
		width: 100%;
		display: flex;
		// align-items: flex-end;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
		padding: 24rpx 14rpx;

		image {
			width: 56rpx;
			height: 56rpx;
			margin: 0 10rpx;
			flex: auto;
		}

		.btn {
			flex: auto;
			background-color: #F5F5F7;
			border-radius: 10rpx;
			padding: 20rpx;
			max-height: 160rpx;
			margin: 0 10rpx;
			border-radius: 40rpx;
		}

		.chat-send {
			line-height: 44rpx;
		}
	}

    .sendTextBtn {
        white-space: nowrap;
    }



	.more {
		width: 100%;
		height: 256rpx;
		background: #fff;
		// box-shadow: 0px 11rpx 0px 0px rgba(0, 0, 0, 0.1);
		bottom: env(safe-area-inset-bottom);
		padding: 8rpx 20rpx;
		box-sizing: border-box;

		.more-list {
			width: 25%;
			text-align: center;
			float: left;
			padding-top: 32rpx;

			image {
				width: 72rpx;
				height: 72rpx;
				padding: 24rpx;
				background: #F5F5F7;
				border-radius: 24rpx;
			}

			.more-list-title {
				font-size: 24rpx;
				color: rgba(39, 40, 50, 0.5);
				line-height: 34rpx;
			}
		}
	}
</style>
