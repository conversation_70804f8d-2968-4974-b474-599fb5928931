<template>
    <view class="main" :style="{paddingBottom:tabIndex==0?'120rpx':'0'}">
        <u-sticky bgColor="#F5F5F5">
            <view class="tabs">
                <u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
					color: '#4F8CF0',
					fontWeight: 'bold',
					transform: 'scale(1.05)'
				}" :inactiveStyle="{
					color: '#999999',
					transform: 'scale(1)'
				}" :list="tabs" @click="changeTab"></u-tabs>
            </view>
        </u-sticky>
        <view class="list">
            <block v-if="tabIndex==0">
                <staff-item v-for="(item,index) in page.data" :key="index" :item="item" :itemIndex="index"></staff-item>
            </block>
            <block v-else>
                <pc-role-item v-for="(item,index) in page.data" :key="index" :item="item"></pc-role-item>
            </block>
        </view>
        <Pages :status="page.status"></Pages>
        <view class="footer" v-if="tabIndex==0">
            <view class="next sure" @click="add">
                新增员工
            </view>
        </view>
    </view>
</template>

<script>
    import StaffItem from '../components/staffItem.vue'
    import PcRoleItem from '../components/pcRoleItem.vue'
    import {staffList} from "../../config/company_api";
    import Pages from "../../components/pages.vue";
    import {getCompanyRoleList} from "../../config/api";

    export default {
        components: {
            Pages,
            StaffItem,
            PcRoleItem
        },
        data() {
            return {
                tabIndex: 0,
                tabs: [
                    {
                        name: '员工管理',
                    },
                    {
                        name: 'PC角色管理',
                    },
                    {
                        name: '小程序角色管理',
                    }
                ],
                page: {
                    form: {
                        limit: 10,
                        page: 1,
                    },
                    status: 'loadmore',
                    more: false,
                    data: [],
                },
            }
        },
        onShow() {
            var storeUser = uni.getStorageSync('storeUser');
            var editUser = uni.getStorageSync('editUser');
            var userItemIndex = uni.getStorageSync('userItemIndex');
            uni.removeStorageSync('storeUser');
            uni.removeStorageSync('editUser');
            uni.removeStorageSync('userItemIndex');

            if (typeof(storeUser) == 'object') {
                this.page.data.unshift(storeUser);
            }

            if (typeof(editUser) == 'object') {
                this.page.data.splice(userItemIndex, 1, editUser);
            }
        },
        mounted() {
            this.getList()
        },
        //触底加载更多
        onReachBottom() {
            if (this.page.more) {
                this.page.status = 'loading';
                this.page.form.page++;
                this.getList();
            }
        },
        methods: {
            add() {
                uni.navigateTo({
                    url: "/pagesA/add/addStaff?flag=add"
                })
            },

            changeTab(op) {
                this.initPage();
                this.tabIndex = op.index;
                this.getList();
            },
            initPage() {
                this.page.data = [];
                this.page.form.page = 1;
                this.page.status = 'loadmore';
            },
            getList() {
                switch (this.tabIndex) {
                    case 0:
                        this.staffList();
                        break;
                    case 1:
                        this.roleList('pc');
                        break;
                    case 2:
                        this.roleList('mini_program');
                        break;
                }
            },
            roleList(terminalType) {
                this.page.form['terminal_type'] = terminalType;
                getCompanyRoleList(this.page.form).then(response => {
                    if (response.status_code == '200') {
                        this.page.more = response.data.more
                        this.page.data = this.page.data.concat(response.data.data);
                        this.page.status = this.page.more ? 'loadmore' : 'nomore';
                    }
                });
            },
            staffList() {
                staffList(this.page.form).then(response => {
                    if (response.status_code == '200') {
                        this.page.more = response.data.more
                        this.page.data = this.page.data.concat(response.data.data);
                        this.page.status = this.page.more ? 'loadmore' : 'nomore';
                    }
                });

            }
        }
    }
</script>
<style lang="scss" src="../../static/css/pagesA/list/staff_management_list.scss"></style>
