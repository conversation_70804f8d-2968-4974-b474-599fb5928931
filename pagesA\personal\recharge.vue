<template>
	<view class="home-index">
		<view class="item">
			<view class="item-up">
				<view class="name">
					充值账户
				</view>
				<view class="desc">
					{{userInfo && userInfo.company?userInfo.company.name:''}}
				</view>
			</view>
		</view>

		<view class="item">
			<view class="item-up">
				<view class="name">
					充值份数
				</view>
				<view class="desc">
					{{quantity?quantity:0}}/币
				</view>
			</view>

			<view class="item-down">
				<!-- <input type="text" /> -->
				<u--input placeholder="请输入充值金额" inputAlign="right" color="#F07C10" border="none"
					v-model="quantity"></u--input>
			</view>
		</view>

		<view class="type">
			<view class="logo">
				<image src="../static/images/wx.png" mode=""></image>
				微信支付
			</view>
			<view class="select">
				<image src="/pagesA/static/images/selected.png" mode=""></image>
			</view>
		</view>

		<view class="btn" @click="rechargePoint">
			确认支付
		</view>
	</view>
</template>

<script>
	import {
		getWechatUserAuthInfo,
		getWechatUserInfo,
		rechargePoint
	} from "../../config/api.js"
	export default {
		data() {
			return {
				quantity: '',
				code: '',
				mini_open_id: ''
			}
		},
		
		computed: {
			userInfo() {
				return this.$store.state.userInfo || uni.getStorageSync('userInfo')
			}
		},
		
		onLoad() {
			let self = this;
			wx.login({
				success: (res) => {
					console.log(res, "微信")
					if (res.errMsg == 'login:ok') {
						self.code = res.code
						self.getWechatUserInfo()
					}
				}
			})
		},

		methods: {
			async rechargePoint() {
				if (!this.quantity) return uni.$u.toast('请输入充值数量')
				let params = {
					quantity: this.quantity, //数量
					code: '123456', //充值验证码
					open_id: this.mini_open_id, //小程序授权open_id
					pay_channel: 'wx_lite', //支付渠道：wx_lite-微信小程序支付，alipay_qr-支付宝扫码支付
					size: '', //扫码支付，二维码尺寸 （否）
				}
				const res = await rechargePoint(params)
				if (res.status_code == 200) {
					const {
						payment
					} = res.data
					uni.requestPayment({
						provider: 'wxpay', // 微信支付
						appId: payment.appId,
						timeStamp: payment.timeStamp,
						nonceStr: payment.nonceStr,
						package: payment.package,
						signType: payment.signType,
						paySign: payment.paySign,
						success: (res) => {
							console.log('支付成功', res);
							uni.showToast({
								title: '支付成功',
								icon: 'success',
								duration: 1000
							})
							setTimeout(() => {
								uni.navigateBack({
									delta: 1
								});
							}, 1000)
						},
						fail: (err) => {
							console.error('支付失败', err);
						}
					});
				}
			},

			async getWechatUserInfo() {
				let params = {
					code: this.code
				}
				const {
					status_code,
					data
				} = await getWechatUserInfo(params)
				if (status_code == 200) {
					this.mini_open_id = data.mini_open_id
					console.log("open_id数据：", this.mini_open_id)
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding: 32rpx 32rpx 0 32rpx;
	}

	.type {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 112rpx;
		padding: 0 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;

		.logo {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 32rpx;
			color: rgba(0, 0, 0, 0.9);

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 10rpx;
			}
		}

		.select {
			image {
				width: 48rpx;
				height: 48rpx;
			}
		}
	}

	.item {
		display: flex;
		flex-direction: column;
		padding: 0 32rpx;
		background-color: #FFFFFF;
		margin-bottom: 32rpx;
		border-radius: 24rpx;

		.item-up {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 96rpx;

			.name {
				font-weight: 400;
				font-size: 28rpx;
				color: #CCCCCC;
			}

			.desc {
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
			}
		}

		.item-down {
			height: 96rpx;
		}
	}

	.btn {
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 90%;
		height: 100rpx;
		bottom: 32rpx;
		left: 5%;
		background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
		border-radius: 16rpx;
		font-weight: 600;
		font-size: 34rpx;
		color: #FFFFFF;
	}
</style>