<template>
	<view class="home-index">
		<u--textarea v-model="form.introduce" placeholder="请输入自我介绍" maxlength="150" count ></u--textarea>
		
		<view class="footer">
			<view class="next pub" @click="save">
				保存
			</view>
		</view>
	</view>
</template>

<script>
	import { addResume ,updateLoginInfo} from "../../config/api.js"
	export default{
		data(){
			return{
				form:{
					introduce:''
				}
			}
		},
		onLoad(options) {
			this.id = options.id
		},
		methods:{
			async save(){
				let params = {
					id:this.id,
					...this.form
				}
				const result = await addResume(params)
				if(result.status_code==200){
					let loginInfo = await updateLoginInfo()
					if (loginInfo.status_code == 200) {
						this.$store.commit('setUserInfo', loginInfo.data)
						return uni.$u.toast('保存信息成功')
					}
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.home-index{
		padding: 32rpx;
	}
	.footer {
		display: flex;
		justify-content: space-around;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 16rpx 16rpx 0 0;
	
		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}
		.save{
			border: 1px solid #4F8CF0;
			color: #4F8CF0;
		}
		.pub {
			background: #4F8CF0;
			border: 1px solid #4F8CF0;
			color: #FFFFFF;
		}
	}
</style>