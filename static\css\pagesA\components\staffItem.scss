.item {
    display: flex;
    align-items: center;
    border-radius: 24rpx;
    background: #FFFFFF;
    padding: 32rpx;
    margin-bottom: 24rpx;
    // &:last-child{
    // 	margin-bottom: 0;
    // }
    & > image {
        width: 104rpx;
        height: 104rpx;
		border-radius: 50%;
    }
    .info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        padding-left: 24rpx;
        .user {
            display: flex;
            align-items: center;
            .name {
                font-weight: 600;
                font-size: 32rpx;
                color: #333333;
            }
            .status {
                display: flex;
                align-items: center;
                padding: 0 12rpx;
                height: 40rpx;
                font-weight: 600;
                font-size: 20rpx;
                margin-left: 16rpx;
                border-radius: 8rpx;
            }
            .yrz {
                background: rgba(87, 213, 28, 0.1);
                color: #57D51C;
            }
            .wrz {
                background: rgba(249, 173, 20, 0.1);
                color: #F9AD14;
                
            }
        }
        
        .phone {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            margin-top: 24rpx;
        }
    }
    
    .reason {
        font-weight: 500;
        font-size: 24rpx;
        color: #FE4D4F;
    }
}