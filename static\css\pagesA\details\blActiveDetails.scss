.home-index {
    padding: 0 0 170rpx 0;
}

.swiper {
    image {
        width: 100%;
        height: 376rpx;
    }
}

.wrap {
    position: relative;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;
    padding: 24rpx 32rpx;
    border-radius: 24rpx;
    margin-top: -60rpx;
    z-index: 100;
    
    .active-info {
        display: flex;
        flex-direction: column;
        
        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .name {
                font-weight: 600;
                font-size: 40rpx;
                color: #333333;
            }
            
           
        }
        
        .time {
            font-weight: 400;
            font-size: 22rpx;
            color: #999999;
            margin: 16rpx 0;
        }
    }
    .status {
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 0 16rpx;
        width: 110rpx;
        height: 50rpx;
        font-weight: 500;
        font-size: 28rpx;
        border-radius: 8rpx;
    }
    
    .ing {
        background: rgba(87, 213, 28, 0.1) !important;
        color: #57D51C !important;
    }
    
    .over {
        background: #CCCCCC !important;
        color: #FFFFFF !important;
    }
    
    .bm {
        background: rgba(79, 140, 240, 0.1) !important;
        color: #4F8CF0 !important;
    }
    .wks {
        color: #27CDF2 !important;
        background: rgba(39, 205, 242, 0.1) !important;
    }
    
    .sub-wrap {
        display: flex;
        flex-direction: column;
        margin-top: 32rpx;
        
        .title {
            font-weight: 600;
            font-size: 32rpx;
            color: #333333;
			display: flex;
			justify-content: space-between;
        }
        
        .desc {
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            margin-top: 16rpx;
        }
        
        .times {
            display: flex;
            align-items: center;
            margin-top: 16rpx;
            
            .time {
                display: flex;
                flex: 1;
                align-items: center;
                justify-content: center;
                height: 50rpx;
                background: #F1F6FE;
                font-weight: 500;
                font-size: 24rpx;
                color: #4F8CF0;
                border-radius: 8rpx;
            }
            
            .line {
                color: #4F8CF0;
            }
        }
        
        .pos {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            margin: 16rpx 0;
            
            image {
                width: 40rpx;
                height: 40rpx;
                margin-right: 16rpx;
            }
        }
    }
}

.footer {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 140rpx;
    width: 100%;
    left: 0;
    bottom: 0;
    background: #FFFFFF;
    font-weight: 600;
    font-size: 28rpx;
    z-index: 1000;
    
    .btns {
        display: flex;
        width: 90%;
        
        .btn {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
            height: 80rpx;
            background: #F5F5F7;
            color: #333333;
            border-radius: 16rpx;
            
            &:first-child {
                margin-right: 20rpx;
            }
            
        }
        
        .agree {
            background: #4F8CF0;
            color: #FFFFFF;
        }
        
        .zx {
            color: #4F8CF0;
            background: rgba(79, 140, 240, 0.1);
        }
        
        .over {
            background: #CCCCCC;
            color: #FFFFFF;
        }
    }
}

.companyBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 16rpx;
}

.companyChild {
    width: 332rpx;
    height: 140rpx;
    background: #F5F5F7;
	display: flex;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    box-sizing: border-box;
    /* 盒模型 */
    margin-bottom: 24rpx;
    /* 上下间距为24rpx */
    // flex-direction: column;
    align-items: center;
    justify-content: space-between;
    padding: 18rpx;
    font-weight: bold;
    font-size: 28rpx;
    color: #333333;
}

.companyImg {
    width: 90rpx;
    height: 90rpx;
    border-radius: 16rpx;
}

.companyBtn {
    width: 88rpx;
    height: 56rpx;
    background: #4F8CF0;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 56rpx;
	margin-left: 120rpx;
}

.title-right {
    display: flex;
    align-items: center;

    text {
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
    }
}



.companyName {
	max-width: 8em;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}