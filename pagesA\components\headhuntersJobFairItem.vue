<template>
	<view class="item" >
		<view class="up">
			<image :src="item.image.path_url" mode=""></image>
			<view class="info" @click.stop="go">
				<view class="name">
					{{item.title}}
				</view>
				<view class="time">
					{{item.start}}-{{item.end}}
				</view>
				<view class="desc">
					已有{{item.company_count}}家企业入职
				</view>
			</view>
		</view>
		<view class="sure-time">
			<view class="time">
				提交时间：{{item.created_at}}
			</view>
			<view :class="['status',item.status_name == '驳回' ? 'back': item.status_name == '通过' ? 'ing': 'wait']">
				{{item.status_name}}
			</view>
		</view>
		<view class="down">
			<view class="reason">
<!--				驳回原因：就要驳回-->
			</view>
			<view class="btns">
				<view class="btn edit" v-if="item.but.edit_but==1" @click.stop="edit">
					编辑
				</view>
				<view class="btn del" v-if="item.but.delete_but==1" @click.stop="del">
					删除
				</view>
				<!-- <view class="btn sign" v-if="item.but.report_company_but==1">
					职位列表
				</view> -->
				<!-- <view class="btn sign">
					发布职位
				</view> -->
				<view class="btn sign" v-if="item.but.report_company_but==1" @click="goCompanyList(item.id)">
					报名企业
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "headhuntersJobFairItem",
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},
		computed: {
			roleType() {
				return this.$store.state.roleType || uni.getStorageSync("roleType")
			}
		},
		methods: {
            goCompanyList(id) {
                uni.navigateTo({
                    url:'/pagesA/headhunterList/registration_company?id='+id
                })
            },
			go() {
				if (this.roleType == 'member') {
					uni.navigateTo({
						url: "/pagesA/details/qlmActiveDetails?id=" + this.item.id
					})
				} else if (this.roleType == 'company' || this.roleType == 'headhunters') {
					uni.navigateTo({
						url: "/pagesA/details/blActiveDetails?id=" + this.item.id
					})
				}
			},
			edit() {
				uni.navigateTo({
					url: "/pagesA/add/pubJobFair?id=" + this.item.id
				})
			},
			del() {
				this.$emit('del', this.item)
			}
		}
	}
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		margin-bottom: 32rpx;
		border-radius: 16rpx;
		background-color: #FFFFFF;
		padding: 32rpx;

		.up {
			display: flex;

			&>image {
				width: 184rpx;
				height: 140rpx;
				border-radius: 8rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				padding-left: 24rpx;

				.name {
					font-weight: 500;
					font-size: 32rpx;
					color: #333333;
				}

				.time {
					font-weight: 400;
					font-size: 28rpx;
					color: #999999;
				}

				.desc {
					font-weight: 400;
					font-size: 24rpx;
					color: #CCCCCC;
				}
			}
		}

		.sure-time {
			display: flex;
			justify-content: space-between;
			padding: 32rpx 0;

			.time {
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
			}

			.status {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 50rpx;
				padding: 0 16rpx;
				border-radius: 8rpx;
				font-weight: 500;
				font-size: 24rpx;
			}

			.wait {
				background: rgba(249, 173, 20, 0.1);
				color: #F9AD14;
			}

			.back {
				background: rgba(254, 77, 79, 0.1);
				color: #FE4D4F;
			}

			.not {
				background: #F5F5F7;
				color: #999999;
			}

			.over {
				background: #CCCCCC;
				color: #FFFFFF;
			}

			.ing {
				background: rgba(87, 213, 28, 0.1);
				color: #57D51C;
			}
		}

		.down {
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-top: 1px solid #F5F5F7;
			padding-top: 32rpx;

			.reason {
				font-weight: 500;
				font-size: 24rpx;
				color: #FE4D4F;
			}

			.btns {
				display: flex;

				.btn {
					align-items: center;
					display: flex;
					height: 64rpx;
					padding: 0 24rpx;
					border-radius: 12rpx;
					font-weight: 600;
					font-size: 28rpx;
					color: #FFFFFF;
					margin-right: 16rpx;

					&:last-child {
						margin-right: 0;
					}
				}

				.edit {
					background: #F9AD14;
				}

				.del {
					background: #FE4D4F;
				}

				.sign {
					background: #4F8CF0;
				}
			}
		}

		.reverse {
			display: flex;
			flex-direction: row-reverse;
		}
	}
</style>