<template>
	<view class="warp">
		<view class="top">
			<view class="arrow">
				<u-icon name="arrow-left" color="#fff" size="20" @click="back"></u-icon>
			</view>
			<view class="top-title">
				信息记录
			</view>
			<view class="top-title1">
				我们深知个人信息对您的重要性，我们将按法律法规要求，采取严格的安全保护措施，全力保护您的隐私安全。
			</view>
		</view>
		<view class="inner">
			<view class="box" v-for="(item,index) in list" :key="index">
				<view class="title">
					{{item.title}}
				</view>
				<view class="tip">
					{{item.tips}}
				</view>
				<view class="box-top">

				</view>
				<view class="cont" v-for="(it,ix) in item.contents">
					<view class="req">

					</view>
					<view class="cont-inn">
						{{it}}
						<text class="gop" v-if="index == 0">
							点击查看
						</text>
						<text class="gop" v-if="((index == 1) && (ix == 1))">
							点击前往
						</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					title: '完善的个人记录信息',
					tips: '通过完善的个人信息记录来保护你的隐私安全',
					contents: ['我看过、沟通过列表等，', '搜索记录、收藏记录等，', '登录设备记录，']
				}, {
					title: '任何管理记录信息',
					tips: '可以在以下地方查看及管理您的记录信息',
					contents: ['可以在我的-沟通过/已投简历进入到求职记录', '活动记录可以在我的-设置-账号与安全中心-活动记录管理进行查看及管理，']
				}, {
					title: '伯乐能看到我的记录吗',
					tips: '平台始终保护您的个人信息记录',
					contents: ['平台始终保护您的个人记录信息', '平台会为您自动屏蔽这些记录']
				}]
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.warp {
		width: 100vh;
		background: #F5F5F7;
		position: relative;
		top: 0;

		.top {
			height: 464rpx;
			width: 686rpx;
			z-index: -1;
			background: linear-gradient(360deg, #F5F5F7 0%, #9DD3FF 18%, #4F75F0 100%);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			// filter: blur(3.5999999046325684px);
			position: absolute;
			top: 0;
			padding: 0 32rpx;

			.arrow {
				margin-top: 116rpx;
			}

			.top-title {
				font-size: 32rpx;
				margin-top: 52rpx;
				color: #FFFFFF;
			}

			.top-title1 {
				margin-top: 28rpx;
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}

		.inner {
			width: 686rpx;
			position: absolute;
			top: 380rpx;
			left: 32rpx;

			.box {
				width: 638rpx;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				padding: 32rpx 24rpx;
				margin-bottom: 40rpx;
				background: #FFFFFF;

				.title {
					font-size: 28rpx;
					color: #333333;
					margin-bottom: 16rpx;
				}

				.tip {
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 24rpx;
				}

				.box-top {
					width: 638rpx;
					height: 152rpx;
					background: #D9D9D9;
					border-radius: 24rpx 24rpx 0 0;
					margin-bottom: 24rpx;
				}

				.cont {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;

					.req {
						width: 10rpx;
						height: 10rpx;
						background: #99C0FF;
						border-radius: 5rpx;
						margin-right: 16rpx;
					}

					.cont-inn {
						font-size: 24rpx;
						color: #666666;

						.gop {
							color: #4F8CF0;
						}
					}
				}
			}
		}
	}
</style>