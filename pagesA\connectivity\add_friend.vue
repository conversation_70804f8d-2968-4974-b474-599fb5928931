<template>
    <view class="autoClass">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="search-wrap">
                    <u-search placeholder="请输入手机号" bgColor="#FFFFFF" :showAction="true" v-model="keyword"
                        @custom="handleEnterPress"></u-search>
                </view>
            </view>
        </u-sticky>
        <view class="main" v-if="show" v-for="(item,index) in list">
            <view class="content" @click="selectUser(item)">
                <view class="info">
                    <image
                            :src="item.member_info.image.thumbnail_path_url"
                            mode=""></image>
                    <view class="name">
                        {{ item.member.certification_status == 1 ? item.member_certification.name : item.member.name}}
                        {{ item.member.certification_status == 2 ? '未认证' : '已认证'}}
                        </br>所属企业：{{ item.company.name }}
                    </view>
                </view>
                <view class="inp">
                    <view class="title">
                        申请信息
                    </view>
                    <u--textarea v-model="item.note" placeholder="请输入申请信息"></u--textarea>
                </view>
            </view>
            <view class="footer" @click="friendStore(item.note)">
                <view class="btns">
                    <view class="save">
                        发送邀请
                    </view>
                </view>
            </view>

        </view>

        <!-- <view v-if="list.length < 1">
      请输入正确的手机号进行搜索
    </view> -->
    </view>
</template>

<script>
    import {
        friendInfo,
        friendStore
    } from "../../config/common_api";

    export default {
        data() {
            return {
                formData: {
                    note: '',
                    user_id: ''
                },
                keyword: '',
                show: true,
                list: []
            };
        },
        methods: {
            handleEnterPress(event) {
                console.log(event)
                // 在这里处理回车事件
                this.keyword = event
                if (this.keyword.length != 11) {
                    return uni.$u.toast('手机号格式不正确')
                }
                this.list = []
                this.page = 1
                this.friendInfo()
                if (this.roleType == 'member' || this.roleType == '') {
                    // this.getRecomendList()
                } else {
                    // this.getCompanyMemberList()
                }
            },
            selectUser(item) {
                console.log('选中的用户', item)
                this.formData.user_id = item.user.id
            },
            async friendInfo() {
                let params = {
                    type: 'headhunters',
                    cellphone: this.keyword
                }
                const {
                    status_code,
                    data
                } = await friendInfo(params)
                if (status_code == "200") {
                    if (data.length > 0) {
                        this.list = data;
                        this.list.forEach((item,index)=>{
                            item.note = '';
                        })
                    }
                }
            },
            async friendStore(item) {
                this.list.forEach((tm,itx)=>{
                    if(tm.note){
                        this.formData.note = tm.note;
                    }
                });

                const {
                    status_code,
                    data
                } = await friendStore(this.formData)
                if (status_code == "200") {
                    uni.showToast({
                        title: '发送成功',
                        icon: 'success',
                        duration: 1000,
                        success: function() {
                            // 延迟1秒后跳转页面
                            setTimeout(function() {
                                uni.navigateTo({
                                    url: '/pagesA/connectivity/new_friend'
                                });
                            }, 1000);
                        }
                    })
                } else {
                    uni.showToast({
                        title: data.message,
                        icon: 'error',
                    })
                }
            }
        },
        onReady() {},
        onLoad(option) {}

    };
</script>
<style>
    page {
        background-color: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    .header {
        padding: 32rpx;
    }
    .autoClass{
        /*height: calc(100vh-240px);*/
        height: calc(93vh);
        overflow: auto;
    }
    .main {
        padding: 0 32rpx;
        margin-bottom: 32rpx;

        .content {
            background-color: #FFFFFF;
            border-radius: 24rpx;

            .info {
                display: flex;
                align-items: center;
                height: 120rpx;
                border-bottom: 1px solid #F5F5F7;
                padding: 0 32rpx;

                &>image {
                    width: 72rpx;
                    height: 72rpx;
                }

                .name {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                    margin-left: 16rpx;
                }
            }

            .inp {
                padding: 32rpx 20rpx;

                .title {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                    padding-bottom: 20rpx;
                }
            }
        }
    }

    .footer {
        display: flex;
        justify-content: center;
        align-items: center;
        position: fixed;
        left: 0;
        bottom: 0;
        height: 120rpx;
        width: 100%;
        background-color: #FFFFFF;
        z-index: 10;

        .btns {
            display: flex;
            font-weight: 600;
            font-size: 28rpx;
            color: #FFFFFF;
            width: 90%;

            .save {
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 16rpx;
                flex: 1;
                height: 80rpx;
                background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
            }
        }
    }
</style>
