const AUTH_BASE = 'common-v2/auth'
// export const login = `${AUTH_BASE}/cellphone-login`
import {
    request
} from "./request"

//企业员工列表

export const staffList = (data) => {
    return request({
        url: `/user/index`,
        data,
        method: 'post'
    })
}
//添加|修改 员工
export const staffStore = (data) => {
    return request({
        url: `/user/store`,
        data,
        method: 'post'
    })
}
//员工详情
export const staffShow = (data) => {
    return request({
        url: `/user/show`,
        data,
        method: 'post'
    })
}
//注销员工账号
export const staffDisable = (data) => {
    return request({
        url: `company-v2/user/disable`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 授权猎头详情
export const getAuthorizeShow = (data) => {
    return request({
        url: `company-v2/company/authorize/show`,
        data,
        method: 'post',
        common: 'common'
    })
}


// 申请变更管理员
export const updateUserAudit = (data) => {
    return request({
        url: `company-v2/company/headhunter-authorizes/user-audit`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 合同列表
export const getContractList = (data) => {
    return request({
        url: `company-v2/contract/index`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 面试结果
export const interviewResult = (data) => {
    return request({
        url: `company-v2/interview/result`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 分享简历列表
export const memberShareResumeAudit = (data) => {
    return request({
        url: `company-v2/member/member-share/audit`,
        data,
        method: 'post',
        common: 'common'
    })
}

export const getMemberShareResume = (data) => {
    return request({
        url: `company-v2/member/member-share/resume`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 人员列表选择使用
export const selectPeopleQY = (data) => {
    return request({
        url: `company-v2/user/index`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 抄送记录
export const jobCopyIndex = (data) => {
    return request({
        url: `company-v2/job/copy-index`,
        data,
        method: 'post',
        common: 'common'
    })
}










