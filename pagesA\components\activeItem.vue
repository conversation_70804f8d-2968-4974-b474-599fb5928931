<template>
	<view class="item">
		<view class="up">
			<view class="content">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode=""></image>
				<view class="info">
					<view class="name">
						活动名称活动名称活动名称活动名称
					</view>
					<view class="time">
						2022年12月12日-2022年12月12日
					</view>
					<view class="num">
						已有200家企业入职
					</view>
				</view>
			</view>
			<view class="desc">
				<view class="date">
					提交时间：2024/01/01
				</view>
				<view :class="['status',item.status==1?'ing':item.status==2?'over':item.status==3?'approve':item.status==4?'back':item.status==5?'wks':'']">
					{{item.status==1?'进行中':item.status==2?'已结束':item.status==3?'审核中':item.status==4?'已驳回':item.status==5?'未开始':''}}
				</view>
			</view>
		</view>
		<block v-if="item.status!=3">
			<view class="down">
				<view class="btns">
					<view class="btn edit" v-if="item.status==4" @click="edit">编辑</view>

					<view class="btn del" v-if="item.status==1||item.status==2||item.status==4||item.status==5">
						删除
					</view>
					<view class="btn look" @click="go" v-if="item.status==1||item.status==2||item.status==5">
						报名记录
					</view>
				</view>
			</view>
		</block>

	</view>
</template>

<script>
	export default {
		name:"activeItem",
		props:{
			item:{
				type:Object,
				default:()=>{}
			}
		},

		data() {
			return {

			};
		},
		methods:{
			go(){
				uni.navigateTo({
					url:"/pagesA/list/active_signup_list"
				})
			},
			edit(){
				uni.navigateTo({
					url:"/pagesA/add/pubJobFair"
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.item{
		display: flex;
		flex-direction: column;
		padding: 32rpx;
		border-radius: 24rpx;
		background-color: #FFFFFF;
		margin-bottom: 32rpx;
		.up{
			display: flex;
			flex-direction: column;
			padding-bottom: 24rpx;
			.content{
				display: flex;
				image{
					width: 184rpx;
					height: 140rpx;
				}
				.info{
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					flex: 1;
					padding-left: 16rpx;
					.name{
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
					}
					.time{
						font-weight: 400;
						font-size: 24rpx;
						color: #999999;
					}
					.num{
						font-weight: 400;
						font-size: 24rpx;
						color: #333333;
					}
				}
			}
			.desc{
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 36rpx;
				.date{
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
				}
				.status{
					display: flex;
					align-items: center;
					height: 50rpx;
					padding: 0 16rpx;
					font-weight: 500;
					font-size: 24rpx;
					border-radius: 8rpx;
				}
				.ing{
					background: rgba(79,140,240,0.1);
					color: #4F8CF0;
				}
				.approve{
					background: rgba(249,173,20,0.1);
					color: #F9AD14;

				}
				.back{
					color: #FE4D4F;
					background: rgba(254,77,79,0.1);
				}

				.over{
					background: #CCCCCC;
					color: #FFFFFF;
				}

				.wks{
					background: #F5F5F7;
					color: #999999;
				}
			}
		}

		.down{
			display: flex;
			flex-direction: row-reverse;
			padding-top: 24rpx;
			border-top: 1px solid #F5F5F7;
			.btns{
				display: flex;
				.btn{
					display: flex;
					align-items: center;
					height: 64rpx;
					font-weight: 600;
					font-size: 28rpx;
					color: #FFFFFF;
					border-radius: 12rpx;
					padding: 0 24rpx;
					margin-left: 16rpx;
				}

				.del{
					background: #FE4D4F;
				}
				.edit{
					background: #F9AD14;
				}
				.look{
					background: #4F8CF0;
				}
			}
		}
	}
</style>
