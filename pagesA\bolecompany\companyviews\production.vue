<template>
	<view class="container">
		<!-- 标题和描述 -->
		<view class="container-con">
			<view style="width: 686rpx;
		    margin: 0 auto;">
				<view class="info-card">
					<p class="font-bold">
						填写产品介绍有什么用?
					</p>
					<p class="font-blod-subtitle">
						介绍公司可提供的员工培养和晋升制度，良好的职业成长空间对人才更有吸引力
					</p>
				</view>
			</view>
			<view class="list">
				<!-- 列表项 -->
				<scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltoupper="upper"
					@scrolltolower="lower" @scroll="scroll">
					<view class="list-item" v-for="(item, index) in itemList" :key="item.id">
						<view class="item-left">
							<view class="rectangle"></view>
						</view>
						<view class="item-right">
							<text class="item-title">{{item.title}}</text>
							<text class="item-desc">{{item.desc}}</text>
						</view>
					</view>
					<view class="note">
						<text class="foottext">上传注意事项</text>
						<image :src="help" class="note-icon"></image>
					</view>
				</scroll-view>

			</view>
		</view>
		<!-- 上传注意事项和按钮 -->
		<view class="footview">
			<view class="footer-card">
				<button class="add-button" @click="addItem">添加产品或服务</button>
			</view>
		</view>
	</view>
</template>

<script>
	/* import help from 'https://api-test.zhaopinbei.com/storage/uploads/images/rYxkkwuyCNnqb4YUwWfJXzktOukmQ2dF2OuFvchb.png' */
	export default {
		data() {
			return {
				itemList: [
					// 初始化列表项数据
					{
						id: 1,
						title: '网站应用',
						desc: 'APP、网站、游戏等产品介绍'
					},
					{
						id: 2,
						title: '网站应用',
						desc: 'APP、网站、游戏等产品介绍'
					},
					{
						id: 3,
						title: '网站应用',
						desc: 'APP、网站、游戏等产品介绍'
					}
				],
				help:'https://api-test.zhaopinbei.com/storage/uploads/images/rYxkkwuyCNnqb4YUwWfJXzktOukmQ2dF2OuFvchb.png'
			};
		},
		methods: {
			addItem() {
				// 这里可以添加向列表中插入新数据项的逻辑，比如push一个新对象
				// this.itemList.push({
				// 	title: '网站应用',
				// 	desc: 'APP、网站、游戏等产品介绍'
				// });
				uni.navigateTo({
					url: '/pagesA/bolecompany/companyviews/addProduction'
				})
			}
		}
	};
</script>

<style>
	.container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.container-con {
		/* padding: 0px 32rpx; */
		display: flex;
		flex-direction: column;
		/* justify-content: space-between; */
		flex: 1;
	}

	.info-card {
		width: 686rpx;
		display: flex;
		height: 166rpx;
		flex-direction: column;
		justify-content: center;
		margin: 20px auto;
		position: relative;
		background-image: linear-gradient(to bottom, rgba(242, 248, 255, 1), rgba(255, 255, 255, 1));
		border-radius: 16rpx;
		overflow: hidden;
	}

	.info-card::before {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 1px;
		left: 1px;
		content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	}

	.info-card::after {
		position: absolute;
		width: 100%;
		height: 100%;
		bottom: 1px;
		right: 1px;
		/* margin: 0px auto; */
		content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	}

	.font-bold {
		padding: 10rpx 0;
		font-size: 24rpx;
		line-height: 28.12rpx;
		margin-left: 40rpx;
		margin-left: 40rpx;
	}

	.font-blod-subtitle {
		color: rgba(141, 154, 170, 1);
		padding: 10rpx 0;
		font-size: 20rpx;
		line-height: 23.44rpx;
		width: 606rpx;
		margin-left: 40rpx;
	}

	.scroll-Y {
		height: 100%;
	}

	.list {
		height: 970rpx;
		/* overflow-y: auto; */
	}

	.uniapp.list::-webkit-scrollbar {
		display: none;
	}

	/* 针对 Firefox 浏览器 */
	.uniapp.list {
		scrollbar-width: none;
	}

	/* 针对 IE 和 Edge 浏览器 */
	.uniapp.list {
		-ms-overflow-style: none;
	}

	.list-item {
		width: 750rpx;
		height: 162rpx;
		/* justify-content: center; */
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.item-left {
		margin-left: 32rpx;
		margin-right: 32rpx;
	}

	.rectangle {
		width: 88rpx;
		height: 88rpx;
		background-color: rgba(217, 217, 217, 1);
		border: 2rpx dashed #ccc;
		border-radius: 10rpx;
	}

	.item-right {
		display: flex;
		flex-direction: column;
		/* align-items: center; */
		justify-content: space-between;
		height: 98rpx;
	}

	.item-title {
		font-size: 32rpx;
		color: #333;
	}

	.item-desc {
		font-size: 28rpx;
		color: #666;
		margin-top: 5rpx;
	}

	.footview {
		width: 100vw;
		position: relative;
	}

	.footer-card {
		background-color: white;
		border-radius: 16rpx;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 1.1);
		padding-top: 24rpx;
		height: 128rpx;
	}

	.note {
		display: flex;
		align-items: center;
		/* margin-bottom: 10rpx; */
		font-size: 28rpx;
		color: #999;
		position: absolute;
		margin-left: 32rpx;
	}

	.note-icon {
		width: 28rpx;
		height: 28rpx;
		margin-left: 20rpx;
	}

	.add-button {
		width: 570rpx;
		height: 80rpx;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 28rpx;
		/* bottom: 80rpx; */
	}

	.foottext {
		color: rgba(153, 153, 153, 1);
		font-size: 24rpx;
		line-height: 28.12rpx;
	}
</style>
