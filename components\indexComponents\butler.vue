<template>
	<view class="container">
		<view class="tabs-container">
			<image class="image"
				src="https://api-test.zhaopinbei.com/storage/uploads/images/mNuy2sF3UdO3uWU8F9SbiJx3UP8KBZ88gBAUhNso.png">
			</image>
			<text>热门就业管家</text>
		</view>
		<view class="content">
			<view class="item" v-for="v in headunterList" :key="v.id" @click="onDetail(v.id)">
				<view class="item-start">
					<image class="image" :src="v.member_info.image.path_url"></image>
					<view class="info">
						<view class="info-start">
							<view class="name-box">
								<text class="name">{{v.name}}</text>
								<view class="identity">{{v.member.certification_status_name}}</view>
							</view>
							<view class="detal-box" v-if="v.remark">{{v.remark}}</view>
							<view class="tags">
								<view class="tag">5年经验</view>
							</view>
						</view>
						<view class="info-end">聊聊呗</view>
					</view>
				</view>
				<u-line></u-line>
				<view class="item-end">
					<view class="evaluation">
						<text class="name">专业能力</text>
						<text class="number">4.4</text>
					</view>
					<view class="evaluation">
						<text class="name">专业能力</text>
						<text class="number">4.4</text>
					</view>
					<view class="evaluation">
						<text class="name">专业能力</text>
						<text class="number">4.4</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getHeadunterUserList
	} from '@/config';
	export default {
		data() {
			return {
				headunterList: [],
				params: {
					limit: 20,
					page: 1
				},
				isLoading: false,
				more: true
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetHeadunterUserList();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetHeadunterUserList();
		},
		methods: {
			onDetail(id) {
				uni.$u.route({
				    url: '/pagesA/details/headhuntersDetail',
					params: {id}
				})
			},
			onScrollGetList() {
				if (!this.more) return;
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetHeadunterUserList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getHeadunterUserList(params);
				if (res.status_code !== '200') return;
				this.headunterList = [...this.headunterList, ...res.data.data];
				this.more = res.data.more;
				this.isLoading = false;
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.tabs-container {
			padding-inline: 32rpx;
			color: #333333;
			background-color: #f5f5f7;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			gap: 16rpx;

			.image {
				width: 32rpx;
				height: 32rpx;
			}
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding-inline: 32rpx;

			.item {
				padding: 32rpx;
				background: linear-gradient(151deg, #4F8CF0 -50%, #FFFFFF 40%);
				border-radius: 24rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-end {
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 24rpx;

					.evaluation {
						display: flex;
						align-items: center;
						gap: 8rpx;
						font-size: 24rpx;

						.name {
							color: #666666;
						}

						.number {
							color: #DD4E41;
						}
					}
				}

				.item-start {
					display: flex;
					align-items: flex-start;
					gap: 20rpx;

					.image {
						width: 96rpx;
						height: 96rpx;
						border-radius: 999rpx;
					}


					.info {
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.info-end {
							background-color: #4F8CF0;
							color: #FFFFFF;
							font-size: 24rpx;
							padding-block: 8rpx;
							padding-inline: 12rpx;
							border-radius: 8rpx;
						}

						.info-start {
							flex: 1;
							display: flex;
							flex-direction: column;
							gap: 16rpx;

							.tags {
								display: flex;
								align-items: center;
								gap: 16rpx;

								.tag {
									background: rgba(188, 213, 255, 0.2);
									border-radius: 8rpx;
									color: #7286A6;
									font-size: 24rpx;
									padding-block: 4rpx;
									padding-inline: 12rpx;
								}
							}

							.detal-box {
								flex: 1;
								color: #999999;
								font-size: 24rpx;
								overflow: hidden;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 1;
							}

							.name-box {
								display: flex;
								align-items: center;
								gap: 32rpx;

								.name {
									color: #333333;
									font-size: 28rpx;
								}

								.identity {
									color: #4787F0;
									font-size: 20rpx;
									padding-block: 6rpx;
									padding-inline: 8rpx;
									background: rgba(79, 140, 240, 0.2);
									border-radius: 8rpx;
								}
							}
						}
					}
				}
			}
		}
	}
</style>