<template>
	<view class="home-index">
		<u-sticky>
			<view class="search-wrap">
				<u-search placeholder="请输入职位名称" bgColor="#F5F5F7" :showAction="false" v-model="keyword"></u-search>
			</view>
		</u-sticky>

		<view class="cotainer">
			<scroll-view class="scroll-L" scroll-y="true">
				<view :class="['item',tabIndex==index?'active':'']" v-for="(item,index) in list" :key="index"
					@click="changeTab(index)">
					<text>{{ item.name }}</text>
				</view>
			</scroll-view>
			<scroll-view class="scroll-R" scroll-y="true">
				<view class="wrap">
					<!-- <view class="title">
            {{item.name}}
          </view> -->
					<view class="list">
						<view class="item" v-for="(sub,i) in list[tabIndex]['children']" :key="i"
							@click="handleSelected(sub)">
							{{ sub.name }}
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="footer">
			<view class="job">
				<view class="title">
					<view class="name">
						我选择的职位
					</view>
					<view class="desc">
						{{selected.length}}/3
					</view>
				</view>
				<view class="job-list">
					<view class="job-item" v-for="(item,index) in selected" :key="index">
						<text>{{ item.name }}</text>
						<image src="https://api-test.zhaopinbei.com/storage/uploads/images/oTSzUI2bt0qYqMFDRcEudXVx3LrlynCxZAC2jWKD.png" mode="" @click="del(item)"></image>
					</view>
				</view>
			</view>

			<view class="btns">
				<view class="btn" @click="saveTourist">
					确认
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		saveTourist,
		getIndustryList,
		getClassList,
		updateLoginInfo,
	} from "../../config/api.js"

	export default {
		data() {
			return {
				type: '',
				flag: '',
				tabIndex: 0,
				list: [],
				selected: []
			}
		},
		onLoad(options) {
			this.flag = options.flag
			this.getClassList()
			this.selected = uni.getStorageSync('userInfo').login_user.job_class
		},
		methods: {

			changeTab(index) {
				this.tabIndex = index
			},
			handleSelected(item) {
				if (this.selected.length == 3) return uni.$u.toast('最多只能选择三个')
				// 检查是否已存在相同 id 的项目
				const exists = this.selected.some(selectedItem => selectedItem.id === item.id);
				if (exists) {
					return uni.$u.toast('请勿选择相同职位');
				}
				console.log('item', item)
				this.selected.push(item)
			},
			del(item) {
				let index = this.selected.findIndex(selectedItem => selectedItem.id === item.id);
				this.selected.splice(index, 1)
			},
			async getIndustryList() {
				const {} = await getIndustryList()
			},
			async getClassList() {
				const {
					data
				} = await getClassList()
				this.list = data;
			},

			async saveTourist() {
				let ids = this.selected.map(item => item.id)
				let params = {}
				params = {
					flag: this.flag, //身份：student-学生，work-我是职场人
					job_class_id: ids //行业id
				}

				const {
					status_code,
					data
				} = await saveTourist(params)

				if (status_code == 200) {
					const {
						status_code,
						data
					} = await updateLoginInfo()
					if (status_code == 200) {
						this.$store.commit('setUserInfo', data)
						uni.setStorageSync('userInfo', data)
						// 确保 Vuex 状态更新完成
						this.$nextTick(() => {
							if (uni.getStorageSync('sharePage')) {
								uni.reLaunch({
									url: uni.getStorageSync('sharePage')
								});
							} else {
								uni.switchTab({
									url: '/pages/index/index'
								});
							}
						});
					}
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.home-index {
		height: 100vh;
		overflow: hidden;
	}

	.search-wrap {
		display: flex;
		align-items: center;
		height: 120rpx;
		padding: 0 32rpx;
		border-bottom: 1rpx solid #F5F5F7;
	}

	.cotainer {
		display: flex;

		height: calc(100vh - 520rpx);

		// background-color: red;
		.scroll-L {
			height: 100%;
			// background-color: blue;
			width: 248rpx;
			border-right: 1rpx solid #F5F5F7;

			.item {
				display: inline-block;
				height: 100rpx;
				line-height: 100rpx;
				width: 100%;
				// text-align: center;
				// margin: 0 auto;
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;

				text {
					margin-left: 30rpx;
				}
			}

			.active {
				position: relative;

				&:before {
					content: "";
					width: 2px;
					height: 24rpx;
					background-color: #4F8CF0;
					position: absolute;
					left: 20rpx;
					top: 50%;
					margin-top: -12rpx;
				}
			}
		}

		.scroll-R {
			height: 100%;
			flex: 1;

			.wrap {
				display: flex;
				flex-direction: column;
				padding: 32rpx;

				.title {
					display: flex;
					align-items: center;
					font-weight: 600;
					font-size: 32rpx;
					color: #000000;
					height: 100rpx;
				}

				.list {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;

					.item {
						display: flex;
						justify-content: center;
						align-items: center;
						text-align: center;
						background: #F5F5F7;
						width: 45%;
						height: 76rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #333333;
						border-radius: 16rpx;
						margin-bottom: 16rpx;
					}
				}
			}
		}
	}

	.footer {
		display: flex;
		flex-direction: column;
		height: 400rpx;
		background: #FFFFFF;
		box-shadow: 0px -2px 16px 0px rgba(0, 0, 0, 0.05);

		.job {
			display: flex;
			flex-direction: column;
			padding: 32rpx 32rpx 0 32rpx;
			flex: 1;

			.title {
				display: flex;
				justify-content: space-between;

				.name {
					font-weight: 600;
					font-size: 32rpx;
					color: #000000;
				}

				.desc {
					font-weight: 400;
					font-size: 32rpx;
					color: #999999;
				}
			}

			.job-list {
				display: flex;
				flex-wrap: wrap;
				padding-top: 32rpx;

				.job-item {
					display: flex;
					align-items: center;
					background: rgba(79, 140, 240, 0.1);
					margin-right: 32rpx;
					margin-bottom: 24rpx;
					height: 56rpx;
					padding: 0 16rpx;
					font-weight: 600;
					font-size: 28rpx;
					color: #4F8CF0;

					image {
						width: 30rpx;
						height: 30rpx;
					}
				}
			}
		}

		.btns {
			flex: 1;
			align-items: center;
			justify-content: center;
			padding: 32rpx;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 88rpx;
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				font-weight: 600;
				font-size: 34rpx;
				color: #FFFFFF;
				border-radius: 16rpx;
			}
		}

	}
</style>
