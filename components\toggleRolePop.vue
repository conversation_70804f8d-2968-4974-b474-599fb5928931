<template>
	<view>
		<u-popup :show="show" round="12" mode="center" @close="close" @open="open">
			<view class="slot-content">
				<view class="wrap">
					<view class="title">切换身份</view>

					<view class="list">
						<view :class="['item', type == 'member' ? 'active' : '']" @click="changeRole('member')">
							<view class="up">
								<image
									src="https://api-test.zhaopinbei.com/storage/uploads/images/O5K4wnQjZV7Aj6b6DpAvW7Hmt3S1edSOrpyC8mXR.png"
									mode=""></image>
								<view class="info">
									<view class="name">我找工作</view>
									<view class="desc">快速找到周边好工作</view>
								</view>
							</view>
						</view>
						<view :class="['item', type == 'company' ? 'active' : '']" @click="changeRole('company')">
							<view class="up">
								<image
									src="https://api-test.zhaopinbei.com/storage/uploads/images/GoBW3gJUIw4L2nTXGRn1L4B1n1RD7onYPO0r2UdF.png"
									mode=""></image>
								<view class="info">
									<view class="name">我招人</view>
									<view class="desc">快速找到合适人才</view>
								</view>
							</view>
						</view>
						<view class="down" v-if="many && type == 'company'">
							<view class="comp-list">
								<view class="comp" v-for="(item, index) in companylist" :key="index" @click="selectCompany(item.id)">
									<image src="../static/images/plat/selected.png" mode="" v-if="companyId == item.id"></image>
									<image src="../static/images/plat/unselected.png" mode="" v-else></image>
									<view class="">
										{{ item.company.name ? item.company.name : '未完善信息' }}
									</view>
								</view>
							</view>
						</view>

						<view :class="['item', type == 'headhunters' ? 'active' : '']" @click="changeRole('headhunters')">
							<view class="up">
								<image src="https://api-test.zhaopinbei.com/storage/uploads/images/47KshAKgg1GKiyMmxGy8iBNi198xn9NTJRZKD8PP.png" mode=""></image>
								<view class="info">
									<view class="name">我是就业管家</view>
									<view class="desc">提供便捷的职业服务</view>
								</view>
							</view>
						</view>
						<view class="down" v-if="many && type == 'headhunters'">
							<view class="comp-list">
								<view class="comp" v-for="(item, index) in companylist" :key="index" @click="selectCompany(item.id)">
									<image src="../static/images/plat/selected.png" mode="" v-if="companyId == item.id"></image>
									<image src="../static/images/plat/unselected.png" mode="" v-else></image>
									<view class="">
										{{ item.company.name ? item.company.name : '未完善信息' }}
									</view>
								</view>
							</view>
						</view>
					</view>

					<view class="btn sure" @click="sure">切换身份</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { getCompanyList } from '../config/api';
export default {
	name: 'toggleRolePop',
	data() {
		return {
			show: false,
			many: false,
			type: '',
			companylist: [],
			companyId: '',
		};
	},

	methods: {
		close() {
			this.show = false;
		},
		open() {
			if (uni.getStorageSync('roleType')) {
				this.type = uni.getStorageSync('roleType');
				if (this.type == 'headhunters' || this.type == 'company') {
					this.many = true;
					this.companyId = uni.getStorageSync('userInfo').login_user.id;
					this.getCompanyList();
					// console.log("执行1111111")
				}
			}
			this.show = true;
		},
		sure() {
			this.$emit('sure', this.type, this.companyId);
		},
		// 获取企业列表
		async getCompanyList() {
			this.companylist = [];
			let params = {
				type: this.type,
			};
			const { status_code, data } = await getCompanyList(params);
			if (status_code == '200') {
				this.companylist = data;
				this.many = true;
			}
		},
		// 切换身份
		changeRole(type) {
			this.type = type;
			const res_userInfo = uni.getStorageSync('userInfo');

			let roles = res_userInfo.roles;
			if (type != 'headhunters') {
				this.many = false;
				this.companyId = '';
			} else {
				this.companyId = '';
			}
			if (type != 'member' && roles.includes(type)) {
				//存在身份
				this.getCompanyList();
			}
			if (type != 'member' && !roles.includes(type) && type == 'headhunters') {
				//不存在就业管家 去注册
				uni.navigateTo({
					url: '/pages/register/register_headhunters',
				});
			}
		},
		selectCompany(companyId) {
			console.log('11111', companyId);
			this.companyId = companyId;
		},
	},
};
</script>

<style lang="less" scoped>
.slot-content {
	width: 90vw;

	// background-color: red;
	.wrap {
		display: flex;
		flex-direction: column;

		.title {
			display: flex;
			justify-content: center;
			padding: 32rpx 0;
			font-weight: 500;
			font-size: 28rpx;
			color: #333333;
		}

		.list {
			display: flex;
			flex-direction: column;
			padding: 0 32rpx;

			.item {
				display: flex;
				flex-direction: column;
				margin-bottom: 24rpx;
				background: #f5f5f7;
				padding: 12rpx;
				border-radius: 24rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.up {
					display: flex;
					align-items: center;

					image {
						width: 200rpx;
						height: 178rpx;
					}

					.info {
						display: flex;
						flex-direction: column;
						padding-left: 40rpx;

						.name {
							font-weight: 500;
							font-size: 32rpx;
							color: #000000;
						}

						.desc {
							font-weight: 400;
							font-size: 24rpx;
							color: rgba(0, 0, 0, 0.4);
							margin-top: 14rpx;
						}
					}
				}
			}

			.active {
				border: 1px solid #4f8cf0;
			}
		}

		.comp-list {
			display: flex;
			flex-direction: column;
			padding: 24rpx 0;

			.comp {
				display: flex;
				align-items: center;
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;
				margin-bottom: 24rpx;

				&:last-child {
					margin-bottom: 0;
				}

				image {
					width: 30rpx;
					height: 30rpx;
					margin-right: 16rpx;
				}
			}
		}

		.btn {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			border-radius: 44rpx;
			color: #ffffff;
			font-size: 28rpx;
			font-weight: 500;
			margin: 32rpx;
		}

		.sure {
			background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
		}

		.gray {
			background-color: #cccccc;
		}
	}
}
</style>
