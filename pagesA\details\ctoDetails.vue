<template>
	<view class="home-index">
		<view class="wrap">
			<view class="info">
				<view class="name">
					{{details.company_certification.name}}
				</view>
				<image :src="details.company_info.logo['path_url']" mode=""></image>
			</view>
			<view class="item">
				<view class="title">
					社会统一代码
				</view>
				<view class="cont">
					{{details.company_certification.org_code}}
				</view>
			</view>
			<view class="item">
				<view class="title">
					法人姓名
				</view>
				<view class="cont">
					{{details.company_certification.legal_rep_name}}
				</view>
			</view>
			<view class="item">
				<view class="title">
					法人手机号
				</view>
				<view class="cont">
					{{details.company_legal.mobile_no1}}

				</view>
			</view>
			<view class="item">
				<view class="title">
					法人身份证
				</view>
				<view class="cont">
					{{details.company_certification.id_no1}}

				</view>
			</view>
			<view class="item">
				<view class="title">
					企业简称
				</view>
				<view class="cont">
					{{details.company.short_name}}
				</view>
			</view>
			<view class="item">
				<view class="title">
					企业邮箱
				</view>
				<view class="cont">
					{{details.company_info.contact_email}}
				</view>
			</view>
		</view>

		<view class="wrap pd"
			v-if=" details.company_info && details.company_info.business_license&&details.company_info.business_license.length>0">
			<image :src="item.path_url" mode="widthFix" v-for="(item,index) in details.company_info.business_license"
				:key="index"></image>
		</view>

		<view class="btn" @click="go">
			企业简介详情
		</view>
	</view>
</template>

<script>
	import {
		getCompanyInfo
	} from "../../config/api.js"
	export default {
		data() {
			return {
				logo: '',
				details: {}
			}
		},

		onLoad() {
			this.getCompanyInfo()
		},

		methods: {
			go() {
				uni.navigateTo({
					url: "/pagesA/add/company_info_edit_one"
				})
			},
			async getCompanyInfo() {
				const {
					status_code,
					data,
					message
				} = await getCompanyInfo()
				if (status_code == 200) {
					this.details = data;
					console.log("详情：", this.details)
					//this.details.company_legal.mobile_no
					this.details.company_legal.mobile_no1 = this.details.company_legal.mobile_no.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
                    this.details.company_certification.id_no1 = this.details.company_certification.id_no.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
					this.logo = this.details && this.details.company_info && this.details.company_info.logo ? this
						.details.company_info.logo[
							'path_url'
						] : ''
					console.log("公司logo:", this.logo)
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding: 32rpx 32rpx 180rpx 32rpx;
	}

	.wrap {
		display: flex;
		flex-direction: column;
		padding: 0 32rpx;
		border-radius: 24rpx;
		background-color: #FFFFFF;
		margin-bottom: 32rpx;

		&:last-child {
			margin-bottom: 0;
		}

		&.pd {
			padding: 32rpx;
		}

		&>image {
			width: 100%;
		}

		.info {
			display: flex;
			// align-items: center;
			padding: 24rpx 0;
			border-bottom: 1px solid #F5F5F7;

			.name {
				display: flex;
				flex: 1;
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;
				padding-right: 24rpx;
			}

			image {
				width: 108rpx;
				height: 108rpx;
				border-radius: 50%;
			}
		}

		.item {
			display: flex;
			flex-direction: column;
			padding: 24rpx 0;
			border-bottom: 1px solid #F5F5F7;

			.title {
				font-weight: 400;
				font-size: 22rpx;
				color: #999999;
			}

			.cont {
				font-weight: 400;
				font-size: 32rpx;
				color: #333333;
				margin-top: 16rpx;
			}
		}
	}

	.btn {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		background: #4F8CF0;
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: 600;
		border-radius: 24rpx;
		left: 5%;
		bottom: 40rpx;
		height: 100rpx;
		width: 90%;
	}
</style>
