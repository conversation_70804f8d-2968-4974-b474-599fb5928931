<template>
  <view class="profile-container">
    <!-- 顶部蓝色背景区域 -->
    <view class="profile-header">
      <!-- 头像 -->
      <view class="avatar-container">
        <image
          class="avatar"
          src="/static/images/avatar-placeholder.png"
          mode="aspectFill"
        ></image>
      </view>
    </view>

    <!-- 个人信息区域 -->
    <view class="profile-info">
      <!-- 基本信息 -->
      <view class="info-section">
        <view class="info-item mt">
          <text class="info-label">昵称</text>
          <text class="info-value">哈哈</text>
        </view>
        <view class="info-item">
          <text class="info-label">姓名</text>
          <text class="info-value">李业先</text>
        </view>
        <view class="info-item">
          <text class="info-label">性别</text>
          <text class="info-value">男</text>
        </view>
        <view class="info-item">
          <text class="info-label">民族</text>
          <text class="info-value">汉族</text>
        </view>
        <view class="info-item">
          <text class="info-label">手机号码</text>
          <text class="info-value">18311111125</text>
        </view>
        <view class="info-item">
          <text class="info-label">身份号码</text>
          <text class="info-value">111123232323232323</text>
        </view>
        <view class="info-item">
          <text class="info-label">学历</text>
          <text class="info-value">本科</text>
        </view>
        <view class="info-item">
          <text class="info-label">邮箱</text>
          <text class="info-value"><EMAIL></text>
        </view>
        <!-- 权限管理 -->
        <view class="section-title">权限管理</view>
        <view class="permission-section">
          <view class="permission-item">
            <img
              class="circle-icon"
              src="https://api-test.zhaopinbei.com/storage/uploads/images/vWc06JcLyKUAcm6jcZYMwdCXL53Cb1qS09PQKsJy.png"
              alt=""
            />
            <text class="permission-text">创办招聘会</text>
          </view>
          <view class="permission-item">
            <img
              class="circle-icon"
              src="https://api-test.zhaopinbei.com/storage/uploads/images/vWc06JcLyKUAcm6jcZYMwdCXL53Cb1qS09PQKsJy.png"
              alt=""
            />
            <text class="permission-text">管理下游（发展和管理就业管家）</text>
          </view>
          <view class="permission-item">
            <img
              class="circle-icon"
              src="https://api-test.zhaopinbei.com/storage/uploads/images/vWc06JcLyKUAcm6jcZYMwdCXL53Cb1qS09PQKsJy.png"
              alt=""
            />
            <text class="permission-text">千里马管理</text>
          </view>
          <view class="permission-item">
            <img
              class="circle-icon"
              src="https://api-test.zhaopinbei.com/storage/uploads/images/vWc06JcLyKUAcm6jcZYMwdCXL53Cb1qS09PQKsJy.png"
              alt=""
            />
            <text class="permission-text">用人单位入驻和管理</text>
          </view>
          <view class="permission-item">
            <img
              class="circle-icon"
              src="https://api-test.zhaopinbei.com/storage/uploads/images/vWc06JcLyKUAcm6jcZYMwdCXL53Cb1qS09PQKsJy.png"
              alt=""
            />
            <text class="permission-text">接受千里马委托</text>
          </view>
          <view class="permission-item">
            <img
              class="circle-icon"
              src="https://api-test.zhaopinbei.com/storage/uploads/images/vWc06JcLyKUAcm6jcZYMwdCXL53Cb1qS09PQKsJy.png"
              alt=""
            />
            <text class="permission-text"
              >其他权限（可授权和管理公司的各种供应商）</text
            >
          </view>
        </view>
        <!-- 对外信息 -->
        <view class="section-title">对外信息</view>
        <view class="info-section">
          <view class="info-item">
            <text class="info-label">企业简称</text>
            <text class="info-value">简介简介</text>
          </view>
          <view class="info-item">
            <text class="info-label">岗位</text>
            <text class="info-value">人事</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 数据可以根据需要添加
    };
  },
  methods: {
    // 方法可以根据需要添加
  },
};
</script>

<style lang="scss">
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f7;
}

.profile-header {
  height: 240rpx;
  background: linear-gradient(to bottom, #1e66e6, #4585ef);
  position: relative;
}

.avatar-container {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -100rpx;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background-color: #fff;
  z-index: 1;
  overflow: hidden;
}

.avatar {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background-color: #fff;
}

.profile-info {
  padding: 30rpx;
  background: linear-gradient(180deg, #dae7ff 0%, #ffffff 84%);
  position: relative;
  z-index: 0;
  font-size: 28rpx;
  color: #041024;
}

.info-section {
  margin-bottom: 30rpx;
  border-radius: 20rpx;
}

.info-item {
  display: flex;
  padding: 24rpx 0;
}
.mt {
  margin-top: 120rpx;
}
.info-item:last-child {
  border-bottom: none;
}

.info-label {
  width: 180rpx;
  color: #666;
  font-size: 28rpx;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 28rpx;
  text-align: right;
}

.section-title {
  font-size: 32rpx;
  color: #333;
  margin: 20rpx 0 20rpx;
}

.permission-section {
  margin-bottom: 30rpx;
  border-radius: 20rpx;
}

.permission-item {
  padding: 20rpx 0;
  display: flex;
  align-items: center;
}

.permission-item:last-child {
  border-bottom: none;
}

.circle-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.permission-text {
  font-size: 28rpx;
  color: #333;
}
</style>
