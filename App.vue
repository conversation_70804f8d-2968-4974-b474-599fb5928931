<script>
	export default {
		globalData: {
			systemInfo: {},
			MenuButtonInfo: {},
			statusBarHeight: 0,
		},
		onLaunch: function() {
		},
		onShow: function() {
			uni.getSystemInfo({
				success: res => {
					this.globalData.systemInfo = res
				}
			})
			// #ifdef MP-WEIXIN
			this.globalData.MenuButtonInfo = uni.getMenuButtonBoundingClientRect()
			// #endif
			const {
				statusBarHeight
			} = this.globalData.systemInfo
			const {
				top,
				height
			} = this.globalData.MenuButtonInfo
			this.globalData.statusBarHeight = (top - statusBarHeight) * 2 + height + statusBarHeight
			
		},
		onHide: function() {
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import "uview-ui/index.scss";
</style>
