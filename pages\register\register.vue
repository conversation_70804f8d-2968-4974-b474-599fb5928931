<template>
	<view class="register-one">
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						手机号 <text class="star" v-model="form.cell">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入手机号" placeholderClass="placeholderClass" clearable  border="none" v-model="value"
							@change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						验证码<text class="star">*</text>
					</view>
					<view class="in">
						<!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
						<!-- #ifndef APP-NVUE -->
						<u-input placeholder="请输入验证码" clearable placeholderClass="placeholderClass"  border="none"
							v-model="value">
						<!-- #endif -->
							<!-- #ifdef APP-NVUE -->
							<u--input placeholder="请输入验证码" clearable placeholderClass="placeholderClass">
							<!-- #endif -->
								<template slot="suffix">
									<u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
									<u-button @tap="getCode" :text="tips" type="success" size="mini"></u-button>
								</template>
						<!-- #ifndef APP-NVUE -->
						</u-input>
						<!-- #endif -->
						<!-- #ifdef APP-NVUE -->
						</u--input>
						<!-- #endif -->
					</view>
				</view>


				<view class="inp-item">
					<view class="title">
						密码<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入密码" clearable placeholderClass="placeholderClass"  type="password" border="none"
							v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						确认密码<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入确认密码" clearable placeholderClass="placeholderClass"  type="password"
							border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="inp">
				<view class="avatar">
					<view class="inp-item">
						<view class="title">
							头像<text class="star">*</text>
						</view>
						<view class="in lab">
							请选择系统默认头像
						</view>
					</view>
					<view class="pic">
						<image src="https://api-test.zhaopinbei.com/storage/uploads/images/DjpYAiYx76keAZZls68bXyVPGHjYpURJJxA9YHcU.png" mode=""></image>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						昵称<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入内容" clearable placeholderClass="placeholderClass"  border="none" v-model="value"
							@change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						民族<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入内容" clearable placeholderClass="placeholderClass"  border="none" v-model="value"
							@change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						性别<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeSex" :value="sexIndex" :range="sexList" range-key="name">
							<view class="d-picker" :style="{color:sexIndex==0?'#c0c4cc':'#303133'}">{{sexList[sexIndex]['name']}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						年龄<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入内容" clearable placeholderClass="placeholderClass"  border="none" v-model="value"
							@change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						最高学历<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeEdu" :value="eduIndex" :range="eduList" range-key="name">
							<view class="d-picker" :style="{color:eduIndex==0?'#c0c4cc':'#303133'}">{{eduList[eduIndex]['name']}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						工作状态<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeJobStatus" :value="jobStatusIndex" :range="jobStatusList" range-key="name">
							<view class="d-picker" :style="{color:jobStatusIndex==0?'#c0c4cc':'#303133'}">{{jobStatusList[jobStatusIndex]['name']}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="next gray" @click="next">
				下一步
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tips: '',
				value: '',
				form:{
					image:[],//头像
					nick_name:'',//昵称
					password:'',//密码
					repassword:'',//重复密码
					age:'',//年龄
					sex:'',//性别
					nation:'',//民族
					education_type:'',//教育类型
					enrollment_status:'',//就读状态
					education_log:[],//教育经历
				},
				sexList: [
					{
						value: 0,
						name: '请选择'
					}, {
						value: 1,
						name: '男'
					}, {
						value: 2,
						name: '女'
					}
				],
				sexIndex:0,
				eduList: [
					{
						name: '请选择',
						value: ''
					},{
						name: '不限',
						value: 0
					}, {
						name: '初中及以下',
						value: 1
					}, {
						name: '高中',
						value: 3
					}, {
						name: '大专',
						value: 4
					}, {
						name: '本科',
						value: 5
					}, {
						name: '研究生及以上',
						value: 6
					}
				],
				eduIndex:0,
				jobStatusList: [
					{
						name: '请选择',
						value: ''
					}, {
						name: 'graduate',
						value: ''
					}, {
						name: '在读',
						value: 'studying'
					}, {
						name: '实习',
						value: 'practice'
					}
				],
				jobStatusIndex:0,
				placeholderStyle: {
					color: '#999999'
				}
			}
		},
		methods: {
			bindPickerChange(e){
				console.log(e)
				this.sexIndex = e.detail.value
			},
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			change(e) {
				console.log('change', e);
			},
			next(){
				uni.navigateTo({
					url:"/pages/register/register_next"
				})
			},

			changeEdu(e){
				console.log(e)
				this.eduIndex = e.detail.value
			},
			changeSex(e){
				this.sexIndex = e.detail.value
			},
			changeJobStatus(e){
				this.jobStatusIndex = e.detail.value
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
		/* background-image: url('/static/images/login/bg.png');
		background-position: 100% 100%;
		background-size: 100% 100%;
		background-repeat: no-repeat; */
	}
</style>
<style lang="less" scoped>
	.register-one {
		padding-bottom: 178rpx;
	}

	.wrap {
		padding: 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;
					.star{
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;
					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}
					::v-deep picker{
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;
						.d-picker{
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}



				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #c0c4cc;
				}
			}
		}
	}

	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}

		.gray {
			background-color: #cccccc;
			color: #FFFFFF;
		}
	}
</style>
