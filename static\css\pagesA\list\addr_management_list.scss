page {
    background-color: #F5F5F7;
}

.main {
    padding-bottom: 120rpx;
}

.list {
    padding: 32rpx 32rpx 0 32rpx;
    
    ::v-deep .u-swipe-action-item__right {
        bottom: 32rpx;
        border-radius: 24rpx;
    }
    
    ::v-deep .u-swipe-action-item__content {
        background: transparent !important;
    }
}

.footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    bottom: 0;
    height: 120rpx;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 10;
    
    .next {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 88rpx;
        width: 90%;
        font-size: 28rpx;
        font-weight: 600;
        border-radius: 16rpx;
    }
    
    .sure {
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        color: #FFFFFF;
    }
}