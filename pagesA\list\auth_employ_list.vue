<template>
    <view class="">
        <u-sticky>
            <view class="tabs">
                <view :class="['tab pg_1',tabIndex==0?'active':'']" @click="changeTab(0)">
                    我的就业管家
                </view>
                <view :class="['tab pg_2',tabIndex==1?'active':'']" @click="changeTab(1)">
                    已解除
                </view>
            </view>
        </u-sticky>

        <view class="list">
            <company-commission-item @click.stop v-for="(item,index) in page.data" :key="index" :item="item"
                                     @more="more"></company-commission-item>
        </view>
        <Pages :status="page.status"></Pages>
        <u-action-sheet :actions="moreList" round="24rpx" cancelText="取消" :closeOnClickOverlay="false" @close="close"
                        @select.stop="select" :show="show"></u-action-sheet>
    </view>
</template>

<script>
    import CompanyCommissionItem from "../components/companyCommissionItem.vue";
    import Pages from "../../components/pages.vue";

    import {
        getCompanyAuthEmployList
    } from "../../config/api.js"

    import {updateUserAudit} from "../../config/company_api";

    export default {
        components: {
            CompanyCommissionItem,
            Pages,
        },
        data() {
            return {
                show: false,
                tabIndex: 0,
                cancel_status_opts: {
                    0: 2,
                    1: 1,
                },
                page: {
                    form: {
                        cancel_status: 2,
                        page: 1,
                        limit: 10,
                    },
                    more: false,
                    data: [],
                    status: 'nomore',
                },
                moreList: [{
                    id: 1,
                    name: '解除',

                },
                    {
                        id: 3,
                        name: '申请变更',
                    }
                ]
            }
        },
        onLoad() {
            this.initPage();
            this.getCompanyAuthEmployList();
        },
        onReachBottom() {
            if (this.page.status == 'loading') {
                this.getCompanyAuthEmployList();
            }
        },
        methods: {
            //获取授权就业管家列表
            async getCompanyAuthEmployList() {
                var _this = this;
                _this.page.form.cancel_status = _this.cancel_status_opts[this.tabIndex] || 2;

                getCompanyAuthEmployList(_this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.more ? 'loading' : 'nomore';
                    }
                });
            },
            changeTab(index) {
                this.tabIndex = index;
                this.initPage();
                this.getCompanyAuthEmployList();
            },
            initPage() {
                var _this = this;
                _this.page.form.page = 1;
                _this.page.data = [];
                _this.page.status = 'loadmore';
            },
            more(e) {
                this.show = e.show;
                this.companyList = e.item;
            },
            close() {
                this.show = false
            },
            select(e) {
                const that = this;
                if (e.id == 3) {
                    uni.showModal({
                        title: '是否变更就业管家',
                        success: function (res) {
                            if (res.confirm) {

                                uni.navigateTo({
                                    url: '/pagesA/details/changeDetail?id=' + that.companyList.company_id
                                })
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    })

                }
                if (e.id == 1) {
                    uni.showModal({
                        title: '是否申请解除委托',
                        success: function (res) {
                            if (res.confirm) {
                                updateUserAudit({
                                    company_id: that.companyList.company_id,
                                    audit_type: 'cancel',
                                }).then(response => {
                                    uni.$u.toast(response.message);
                                    if (response.status_code == '200') {
                                        that.initPage();
                                        that.getCompanyAuthEmployList();
                                    }
                                });
                            } else if (res.cancel) {

                            }
                        }
                    })
                }
            }
        }
    }
</script>
<style>
    page {
        background: #f5f5f7;
    }
</style>
<style lang="less" scoped>
    .tabs {
        display: flex;
        justify-content: space-between;
        padding: 32rpx;

        .tab {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 88rpx;
            display: flex;
            flex: 1;
            background: #FFFFFF;
            font-weight: 400;
            font-size: 28rpx;
            color: #999999;

            &:first-child {
                border-radius: 44rpx 0 0 44rpx;
            }

            &:last-child {
                margin-left: -40rpx;
                border-radius: 0 44rpx 44rpx 0;
            }
        }

        .pg_1 {
            clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
        }

        .pg_2 {
            clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
        }

        .active {
            color: #4F8CF0;
            font-weight: 600;
        }
    }

    .list {
        padding: 0 32rpx;
    }
</style>