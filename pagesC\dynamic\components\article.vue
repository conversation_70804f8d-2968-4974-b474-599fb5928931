<template>
	<view class="container">
		<u-toast ref="uToast"></u-toast>
		<scroll-view :scroll-x="true" class="scroll-view">
			<view class="scroll-container">
				<view class="article">
					<view class="title-box">
						<image class="wenzheng" src="/static/new/火苗@2x.png" mode=""></image>
					</view>
					<view class="item">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/XZlvLicLN0X8V782rFuJdwzio9jshtFO3UZwYkEE.png">
						</image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
					<view class="item">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/IrO6l3QDPABXEeHogiCs2g1NlERO1Rx8hjXqLpeI.png">
						</image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
					<view class="item">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/FTG2fPOpIHpHzMjBHVFUGT8tYWNmYYPSaL01kcDa.png">
						</image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
				</view>
				<view class="article">
					<view class="title-box">
						<image class="wenzheng" src="/static/new/火苗@2x.png" mode=""></image>
					</view>
					<view class="item">
						<image class="image" src="/static/new/jinpai.png"></image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
					<view class="item">
						<image class="image" src="/static/new/yinpai.png"></image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
					<view class="item">
						<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/xfEwCPjT4oiDPnE9kmiTNkwAkjhER1eFPZAmp1jy.png"></image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<view class="column-container">
			<text class="title">精彩文章</text>
			<text>查看更多</text>
			<image class="image" src="/static/new/右箭头@2x1.png"></image>
		</view>

		<view class="content">
			<view class="article-item" v-for="v in articleList" :key="v.id" @click="onDetail(v)">
				<view class="item-start">
					<view class="start-left">
						<view class="user-box">
							<image class="avatar" src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"></image>
							<text>匿名</text>
							<u-line direction="col" length="40rpx"></u-line>
							<text>2025-3-14</text>
						</view>
						<view class="content-title">字节跳动Fink大规模云原生化实践</view>
						<view class="content-sub-title">
							{{v.content}}
						</view>
					</view>
					<view class="start-right">
						<image class="image" :src="v.cover"></image>
					</view>
				</view>
				<view class="item-end">
					<view class="btn">
						<image class="image" src="/static/new/浏览量****************" mode=""></image>
						<text>124</text>
					</view>
					<view class="btn" @click.stop="onCommentLikeStore(v)">
						<image class="image" :src="v.has_liked | likeType"></image>
						<text>点赞</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		commentLikeStore,
		getArticleList,
		commentLikeDestroy
	} from '@/config';

	export default {
		filters: {
			likeType(bol) {
				return bol ?
					'https://api-test.zhaopinbei.com/storage/uploads/images/DViKeiV8DmEdPMgYakhe6YZhUCYzBKxnYucT2HX3.png' :
					'https://api-test.zhaopinbei.com/storage/uploads/images/uSCed420BB9MjjBnOUpxnlj0JJAy6kig07JjPa20.png'
			}
		},
		data() {
			return {
				params: {
					form: '1',
				},
				articleList: []
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				//下面是判断哪个身份进来的，目前来看数据都一样，所以不用判断，如果确定未登录也有的话，应该要判断一下，是否是未登录状态。
				/*let objId = '';
				if(user.role_type == "headhunters"){
					objId = user.member_info.member_id;
				}else if(user.role_type == "member"){
					objId = user.member_info.member_id;
				}else if(user.role_type == "company"){
					objId = user.member_info.member_id;
				}*/
				if (!user) return {};
				return {
					//member_id: user[user.role_type].id,
					member_id: user.member_info.member_id,
				};
			}
		},
		mounted() {
			this.onGetArticleList();
		},
		methods: {
			onDetail(v) {
				uni.$u.route({
					url: '/pagesC/dynamic/article_detail',
					params: {id: v.id}
				})
			},
			async onGetArticleList() {
				const params = {
					...this.userTypeParams,
					...this.params
				}
				const res = await getArticleList(params);
				if (res.status_code !== '200') return;
				this.articleList = res.data;
			},
			async onCommentLikeStore(v) {
				const params = {
					article_id: v.id,
					type: '1',
					...this.userTypeParams
				}
				const event = v.has_liked ? commentLikeDestroy : commentLikeStore;
				const res = await event(params);
				if (res.status_code !== '200') return;
				this.$refs.uToast.show({
					message: res.message,
					complete: () => {
						this.onGetArticleList();
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding-inline: 32rpx;

			.article-item {
				background-color: #FFFFFF;
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-end {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.btn {
						display: flex;
						align-items: center;
						color: #666666;
						font-size: 24rpx;
						gap: 12rpx;

						.image {
							width: 32rpx;
							height: 32rpx;
						}
					}
				}

				.item-start {
					display: flex;
					align-items: center;
					gap: 32rpx;

					.start-right {
						.image {
							width: 176rpx;
							height: 176rpx;
							border-radius: 24rpx;
							object-fit: contain;
						}
					}

					.start-left {
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.content-title {
							color: #041024;
							font-size: 28rpx;
							width: 50%;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.content-sub-title {
							color: #666666;
							font-size: 24rpx;
							flex: 1;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.user-box {
							display: flex;
							align-items: center;
							gap: 24rpx;
							color: #777777;
							font-size: 24rpx;

							.avatar {
								width: 48rpx;
								height: 48rpx;
								border-radius: 999rpx;
							}
						}
					}
				}
			}
		}

		.column-container {
			display: flex;
			align-items: center;
			color: #999999;
			font-size: 24rpx;
			padding-inline: 32rpx;

			.title {
				font-size: 32rpx;
				color: #041024;
				margin-inline-end: auto;
			}

			.image {
				width: 32rpx;
				height: 32rpx;
			}
		}

		.scroll-view {
			.scroll-container {
				display: inline-flex;
				padding-inline: 32rpx;
				gap: 24rpx;

				.article {
					display: flex;
					padding: 32rpx;
					background: linear-gradient(180deg, #ffe3d6 0%, #ffffff 53%);
					border-radius: 24rpx;
					flex-direction: column;
					gap: 20rpx;

					.title-box {
						.wenzheng {
							width: 148rpx;
							height: 52rpx;
						}
					}

					.item {
						display: flex;
						align-items: flex-start;
						gap: 16rpx;

						.image {
							width: 40rpx;
							height: 40rpx;
						}

						.text-box {
							display: flex;
							flex-direction: column;

							.text {
								width: 70%;
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
							}

							.text-bot {
								display: flex;
								align-items: center;

								.company {
									color: #666666;
									font-size: 22rpx;
									width: 70%;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.image {
									width: 32rpx;
									margin-inline-start: auto;
									height: 32rpx;
								}

								.desc {
									color: #666666;
									font-size: 20rpx;
								}
							}
						}
					}
				}
			}
		}
	}
</style>
