<template>
	<view>
		<u-popup :show="show" mode="right" closeable @close="close" @open='open' >
			<view class="slot-content">
				<scroll-view class="scroll-H" scroll-y>
					<view class="wrap">
						<view class="sub-wrap">
							<view class="title">
								区域
							</view>
							<view class="selected" @click="selectedArea">
								<view class="name">
									请选择
								</view>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</view>
						
						<view class="sub-wrap">
							<view class="title">
								公司行业
							</view>
							
							<view class="list">
								<view class="item" v-for="item in 8">
									餐饮
								</view>
							</view>
						</view>
						
						<view class="sub-wrap">
							<view class="title">
								公司规模
							</view>
							
							<view class="list">
								<view class="item" v-for="item in 8">
									500-999人
								</view>
							</view>
						</view>
						
						<view class="sub-wrap">
							<view class="title">
								融资阶段
							</view>
							
							<view class="list">
								<view class="item" v-for="item in 8">
									未融资
								</view>
							</view>
						</view>
						
						<view class="sub-wrap">
							<view class="title">
								热门状态
							</view>
							
							<view class="list">
								<view class="item">
									开启
								</view>
								<view class="item">
									关闭
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
				
				
				<view class="footer">
					<view class="btns">
						<view class="btn reset">
							重置
						</view>
						<view class="btn save">
							保存
						</view>
					</view>
				</view>
			</view>
			<u-picker :show="showArea" :columns="columns" @cancel="closeArea" @confirm="sureArea"></u-picker>
			
			
		</u-popup>
	</view>
</template>

<script>
	export default {
		name: "filter",
		data() {
			return {
				show: false,
				showArea: false,
				columns: [
					['中国', '美国', '日本']
				],
			};
		},
		methods:{
			selectedArea(){
				this.showArea = true
			},
			closeArea(){
				this.showArea = false
			},
			sureArea(){
				this.showArea = false
			},
			open(){
				this.show=true
			},
			close(){
				this.show = false
				this.$emit('close',false)
			}
		}
	}
</script>

<style lang="less" scoped>
	.slot-content {
		display: flex;
		flex-direction: column;
		position: relative;
		height: 100vh;
		width: 100vw;
		overflow: hidden;
		background: #f5f5f7;
	}
	.scroll-H{
		height: calc(100% - 140rpx);
	}
	.footer{
		display: flex;
		justify-content: center;
		align-items: center;
		height: 120rpx;
		width: 100%;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 10;
		.btns{
			display: flex;
			justify-content: space-between;
			width: 90%;
			
			.btn{
				display: flex;
				justify-content: center;
				align-items: center;
				width: 45%;
				height: 88rpx;
				font-weight: 600;
				font-size: 28rpx;
				border-radius: 16rpx;
			}
			
			.reset{
				background: rgba(79,140,240,0.1);
				color: #4F8CF0;
			}
			.save{
				background: linear-gradient( 135deg, #4F8CF0 0%, #1E6DEE 100%);
				color: #FFFFFF;
			}
		}
	}
	.wrap {
		padding: 0 32rpx;

		.sub-wrap {
			display: flex;
			flex-direction: column;


			.title {
				display: flex;
				align-items: center;
				height: 108rpx;
				font-weight: 600;
				font-size: 32rpx;
				color: #000000;
			}

			.selected {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 32rpx;
				height: 88rpx;
				border-radius: 24rpx;
				background-color: #FFFFFF;

				.name {
					font-weight: 500;
					font-size: 28rpx;
					color: #000000;
				}
			}
			
			.list{
				display: flex;
				flex-wrap: wrap;
				padding: 32rpx 32rpx 0 32rpx;
				overflow: hidden;
				background-color: #FFFFFF;
				border-radius: 24rpx;
				.item{
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 32rpx;
					background: #F5F5F7;
					border-radius: 16rpx;
					width: calc(33.33% - 24rpx);
					margin-right: 24rpx;
					height: 66rpx;
					font-weight: 400;
					font-size: 24rpx;
					color: #000000;
					
					&:nth-child(3n){
						margin-right: 0;
					}
				}
			}
		}
	}
</style>