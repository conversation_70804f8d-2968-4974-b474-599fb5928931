<template>
	<view class="">
		<u-sticky>
			<view class="tabs">
				<view :class="['tab pg_1',tabIndex==0?'active':'']" @click="changeTab(0)">
					我的就业管家
				</view>
				<view :class="['tab pg_2',tabIndex==1?'active':'']" @click="changeTab(1)">
					已解除
				</view>
			</view>
		</u-sticky>

		<view class="list">
			<commission-item v-for="(item,index) in list" :key="index" :item="item" @more="more"></commission-item>
		</view>

		<u-action-sheet :actions="moreList" round="24rpx" cancelText="取消" :closeOnClickOverlay="false" @close="close"
			@select="select" :show="show"></u-action-sheet>
	</view>
</template>

<script>
	import CommissionItem from "../components/commissionItem.vue"
	export default {
		components: {
			CommissionItem
		},
		data() {
			return {
				show: false,
				tabIndex: 0,
				list: [{
						status: 1
					},
					{
						status: 2
					}
				],
				moreList: [{
						id: 1,
						name: '解除',

					},
					{
						id: 3,
						name: '申请变更',
					}
				]
			}
		},
		methods: {
			changeTab(index) {
				this.tabIndex = index
			},
			more(e) {
				console.log(e)
				this.show = e.show
			},
			close() {
				this.show = false
			},
			select(e) {
				if (e.id == 3) {
					uni.showModal({
						title: '是否变更就业管家',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url:'/pagesA/member/details/authorizeDetail'
								})
								console.log('用户点击确定');
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					})

				}else if(e.id==1){
					uni.showModal({
						title: '是否申请解除委托',
						success: function(res) {
							if (res.confirm) {
								console.log('用户点击确定');
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					})
				}
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.tabs {
		display: flex;
		justify-content: space-between;
		padding: 32rpx;

		.tab {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			display: flex;
			flex: 1;
			background: #FFFFFF;
			font-weight: 400;
			font-size: 28rpx;
			color: #999999;

			&:first-child {
				border-radius: 44rpx 0 0 44rpx;
			}

			&:last-child {
				margin-left: -40rpx;
				border-radius: 0 44rpx 44rpx 0;
			}
		}

		.pg_1 {
			clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
		}

		.pg_2 {
			clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
		}

		.active {
			color: #4F8CF0;
			font-weight: 600;
		}
	}

	.list {
		padding: 0 32rpx;
	}
</style>