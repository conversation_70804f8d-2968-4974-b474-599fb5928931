<template>
	<u-modal :show="show" title="选择就业方式" cancelText="取消" showCancelButton @cancel='cancel' @confirm="confirm">
		<view class="slot-content">
			<view class="workList">
				<view  v-for="(item,index) in list" :key="index" @click="handleTab(item)" :class="['workItem', { active: selectedItems.some(v => item.id == v.id) }]">
					{{item.name||item.title}}
				</view>
			</view>
		</view>
	</u-modal>
</template>


<script>
	export default {
		props: {
			list: {
				type: Array,
				default: () => []
			},
			selected: {
				type: Array,
				default: () => []
			},
		},
		data() {
			return {
				show: false,
				selectedItems: []
			}
		},

		mounted() {
			console.log("传递过来的selected:",this.list,this.selected)
		},

		methods: {
			cancel() {
				this.show = false;
			},
			confirm() {
				this.$emit('confirm', this.selectedItems)
				this.show = false
			},
			open() {
				this.show = true
			},
			handleTab(v) {
				let isExist = this.selectedItems.some(item => v.id == item.id)
				if (!isExist) {
                    console.log("111")
					this.selectedItems.push(v)
				} else {
					let index = this.selected.findIndex(item => v.id == item.id)
					this.selectedItems.splice(index, 1)
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.workList {
		display: flex;
		flex-wrap: wrap;

		.workItem {
			display: flex;
			align-items: center;
			justify-content: center;
			align-items: center;
			height: 64rpx;
			padding: 0 22rpx;
			border-radius: 8rpx;
			background-color: #F5F5F7;
			margin-bottom: 16rpx;
			margin-right: 20rpx;
			font-size: 28rpx;
			font-weight: 600;
		}

		.active {
			color: #4F8CF0;
			border: 1px solid #4F8CF0
		}
	}
</style>