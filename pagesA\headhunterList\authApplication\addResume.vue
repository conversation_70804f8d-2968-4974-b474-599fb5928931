<template>
	<view class="home-index">
		<u-steps current="1">
			<u-steps-item title="第一步" desc="基本信息">
			</u-steps-item>
			<u-steps-item title="第二步" desc="完善简历"></u-steps-item>
			<!--    <u-steps-item title="第三步" desc="工作经验"></u-steps-item>-->
		</u-steps>
		<view class="wrap">
			<view class="wrap-title">
				简历信息
			</view>
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						简历名称<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入简历名称" placeholderClass="placeholderClass" clearable border="none"
							v-model="form.resume_name"></u--input>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						是否公开<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeMain" :value="mainIndex" :range="mainList" range-key="name">
							<view class="d-picker" :style="{color:mainIndex==0?'#c0c4cc':'#303133'}">
								{{mainList[mainIndex]['name']}}
							</view>
						</picker>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						用户账号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入手机号" placeholderClass="placeholderClass" clearable border="none"
							v-model="form.cellphone"></u--input>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						联系人手机号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入手机号" placeholderClass="placeholderClass" clearable border="none"
							v-model="form.mobile"></u--input>
					</view>
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="wrap-title">
				求职期望
			</view>
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						期望薪资<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeSalary" :value="salaryIndex" :range="sysData.salary_range"
							range-key="label">
							<view class="d-picker" :style="{color:salaryIndex==0?'#c0c4cc':'#303133'}">
								{{sysData && sysData.salary_range.length>0?sysData.salary_range[salaryIndex]['label']:''}}
							</view>
						</picker>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						期望岗位<text class="star">*</text>
					</view>
					<view class="in se">
						<uni-data-picker :map="mapClass" placeholder="请选择岗位" popup-title="请选择岗位"
							:localdata="jobTypeList" v-model="form.job_class_id" @change="onchangeclass">
						</uni-data-picker>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						期望工作地点<text class="star">*</text>
					</view>
					<view class="in se">
						<uni-data-picker :map="map" placeholder="请选择工作地址" popup-title="请选择所在地区" :localdata="items"
							v-model="form.expect_address" @change="onchange">
						</uni-data-picker>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						求职类型<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeWorkStatus" :value="workStatusIndex" :range="workStatusList"
							range-key="name">
							<view class="d-picker" :style="{color:workStatusIndex==0?'#c0c4cc':'#303133'}">
								{{workStatusList[workStatusIndex]['name']}}
							</view>
						</picker>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="wrap-title">
				自我介绍<text class="star">*</text>
			</view>
			<view class="inp">
				<view class="inp-item">
					<view class="txt">
						<u--textarea v-model="form.introduce" placeholderClass="placeholderClass" border="none"
							placeholder="请输入自我介绍"></u--textarea>
					</view>
				</view>
			</view>
		</view>
		<view class="wrap">
			<view class="wrap-title">
				荣誉证书
			</view>
			<view class="pic-list">
				<view class="pic-item" v-for="(item,index) in certificates" :key="index">
					<image :src="item.url" mode=""></image>
					<view class="zz">
						<view class="del" @click="delCert(index)">
							删除
						</view>
					</view>
				</view>
				<view class="pic-item add" @click="uploadCert">
					<image src="/static/images/my/add.png" style="width: 40rpx;height: 40rpx;" mode=""></image>
				</view>
			</view>
		</view>
		<view class="wrap">
			<view class="wrap-title">
				工作经历
			</view>

			<block class="" v-for="(item,supIndex) in form.job_log" :key="supIndex">
				<view class="inp">
					<view class="inp-item">
						<view class="title">
							公司名称
						</view>
						<view class="in">
							<u--input placeholder="请输入公司名称" placeholderClass="placeholderClass" clearable border="none"
								v-model="item.company"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							职务名称
						</view>
						<view class="in">
							<u--input placeholder="请输入职务名称" placeholderClass="placeholderClass" clearable border="none"
								v-model="item.job_name"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							开始时间
						</view>
						<view class="in se">
							<picker mode="date" :value="item.start_date" @change="bindStartDateChange">
								<view class="d-picker">{{item.start_date?item.start_date:"请选择开始时间"}}</view>
							</picker>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							结束时间
						</view>
						<view class="in se">
							<picker mode="date" :value="item.end_date" @change="bindEndDateChange">
								<view class="d-picker">{{item.end_date?item.end_date:"请选择结束时间"}}</view>
							</picker>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							所属行业
						</view>
						<view class="in">
							<u--input placeholder="请输入所属行业" placeholderClass="placeholderClass" clearable border="none"
								v-model="item.industry"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							联系人
						</view>
						<view class="in">
							<u--input placeholder="请输入联系人" placeholderClass="placeholderClass" clearable border="none"
								v-model="item.contact_name"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							联系方式
						</view>
						<view class="in">
							<u--input placeholder="请输入联系方式" placeholderClass="placeholderClass" clearable border="none"
								v-model="item.contract_cellphone"></u--input>
						</view>
					</view>
				</view>

				<view class="inp">
					<view class="inp-item">
						<view class="title supTitle">
							工作经历
						</view>
						<view class="txt">
							<u--textarea v-model="item.content" placeholderClass="placeholderClass" border="none"
								placeholder="请输入工作经历"></u--textarea>
						</view>

					</view>
				</view>

				<view class="inp">
					<view class="inp-item">
						<view class="title supTitle">
							突出业绩
						</view>
						<view class="txt">
							<u--textarea v-model="item.achievement" :placeholderStyle="{fontSize:'14px'}" border="none"
								placeholder="请输入突出业绩"></u--textarea>
						</view>
					</view>
				</view>
			</block>
		</view>


		<view class="footer">
			<view class="btns">
				<view class="btn jump" @click="nextTop">
					上一步
				</view>
				<view class="btn next" @click="next">
					提交
				</view>
			</view>
		</view>


	</view>
</template>

<script>
	import {
		updateLoginInfo,
		uploadImg,
		getJobClassList,
		getCityList,
		getSysList,
		addResume,
		getRecomendList,
        getClassList
	} from "../../../config/api.js"
	import {
		resumeShow
	} from "../../../config/headhunterList_api";
	export default {
		data() {
			return {
				id: "",
				step: 1,
				certificates: [],
				salaryList: [],
				salaryIndex: 0,
				sysData: null,
				items: [],
				userFormData: {},
				map: {
					text: 'label',
					value: 'value'
				},
				mapClass: {
					text: 'name',
					value: 'id'
				},
				userInfo: {},
				form: {
					cellphone: "",
					resume_name: "",
					mobile: "",
					public_status: '',
					work_status: '',
					expect_salary: "",
					job_class_id: [],
					expect_province_id: '',
					expect_city_id: '',
					expect_district_id: '',
					expect_address: "",
					introduce: "",
					job_log: [{
						company: "",
						job_name: "",
						date: [
							"",""
						],
						start_date: "",
						end_date: "",
						industry: "",
						contact_name: "",
						contract_cellphone: "",
						content: "",
						achievement: ""
					}],
					certificates: []
				},
				mainIndex: 0,
				mainList: [{
						name: '请选择',
						value: '0'
					},
					{
						name: '公开',
						value: '1'
					}, {
						name: '保密',
						value: '2'
					}
				],

				jobTypeList: [],

				workStatusList: [{
						name: '请选择',
						value: 0
					},
					{
						name: '全职',
						value: 'full'
					},
					{
						name: '兼职',
						value: 'part'
					},
					{
						name: '实习',
						value: 'internship'
					}

				],
				workStatusIndex: 0,
				// startTime: "2024-01-01",
				// endTime: "2024-01-01",
			}
		},
		watch: {
			sysData: {
				handler(newValue, oldValue) {
					console.log("监听：：：", oldValue)
					if (!oldValue) {
						this.getSysData()
					}
				},
				immediate: true,
				deep: true
			},
			form: {
				handler(newValue, oldValue) {},
				immediate: true,
				deep: true
			}
		},
		computed: {},
		onLoad() {
			this.userFormData = uni.getStorageSync('userFormData')
			this.form = uni.getStorageSync('resumeFormData') != '' ? uni.getStorageSync('resumeFormData') : this.form
			console.log('第一页的数据', this.userFormData);
			let self = this;
			self.$nextTick(() => {
				self.getJobClassList()
				self.getCityList()
				// self.getSysData()
			})

		},
		methods: {
			nextTop() {
				console.log('返回上一页时填写的数据：', this.form)
				uni.setStorageSync('resumeFormData', this.form)

				uni.navigateBack();
			},
			async getResumeShow() {
				let params = {
					limit: this.limit,
					page: this.page,
					title: this.keyword,
					tag_id: [],
				}
				const {
					status_code,
					data
				} = await resumeShow(params)
				if (status_code == 200) {
					this.list = this.list.concat(data.data);
					this.more = data.more;
					this.status = this.more ? "loadmore" : "nomore"
				}
			},
			onchangeclass(e) {
				let data = e.detail.value
				this.form.job_class_id = data[data.length - 1]['value']
				console.log(this.form.job_class_id)
			},
			onchange(e) {
				console.log("工作地址：", e);
				let data = e.detail.value
				this.form.expect_province_id = data[0]['value']
				this.form.expect_city_id = data[1]['value']
				this.form.expect_district_id = data[2]['value']
				this.form.expect_address = data.map(item => item.text).join('')
				console.log("期望地址：", this.form.expect_address)
			},

			async getSysData() {
				const result = await getSysList()
				this.sysData = result;
				this.$store.commit('setSysData', result)
				console.log("系统数据！！！！！！！！！！！", this.sysData)
			},

			async getJobClassList() {
				const result = await getClassList()
				if (result.status_code == 200) {
					this.jobTypeList = result.data;
				}
			},
			async getCityList() {
				const result = await getCityList()
				if (result.status_code == 200) {
					this.items = result.data;
				}
				console.log(this.items)
			},
			changeMain(e) {
				this.mainIndex = e.detail.value
				this.form.public_status = this.mainList[this.mainIndex]['value']
			},

			changeSalary(e) {
				this.salaryIndex = e.detail.value
				console.log(this.salaryIndex, "索引")
				this.form.expect_salary = this.sysData['salary_range'][this.salaryIndex]['value']
			},

			changeWorkStatus(e) {
				this.workStatusIndex = e.detail.value
				this.form.work_status = this.workStatusList[this.workStatusIndex]['value']
			},

			bindStartDateChange(e) {
				console.log(e)
				// this.startTime = e.detail.value;
				this.form.job_log[0]['date'][0] = e.detail.value;
				this.form.job_log[0]['start_date'] = e.detail.value
			},
			bindEndDateChange(e) {
				// this.endTime = e.detail.value;
				this.form.job_log[0]['date'][1] = e.detail.value
				this.form.job_log[0]['end_date'] = e.detail.value
				console.log("日期：", this.form.job_log[0]['date'])
			},

			delCert(index) {
				this.certificates.splice(index, 1)
			},

			//上传荣誉证书
			uploadCert() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						// $dialog.loading('上传中')
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								console.log("图片信息：", result.data)
								if (result.status_code == 200) {
									// self.idCard = result.data.url
									// self.certForm.idCard_img = [result.data.id]
									// let distParams = {
									// 	file_id: result.data.id,
									// 	type: 'idcard'
									// }
									self.certificates.push({
										id: result.data.id,
										url: result.data.url
									})
								}
							}
						})
					}
				});
			},


			//下一步提交
			async next() {
				this.form = {
					...this.form,
					...this.userFormData
				}

				this.form.certificates = this.certificates.map(item => item.id)
                this.form.job_log = this.form.job_log.filter(item => item.company);
				console.log('提交的数据', this.form)
				// return
				let params = {
					...this.form
				}
				const result = await addResume(params)
				if (result.status_code == 200) {
					let loginInfo = await updateLoginInfo()
					if (loginInfo.status_code == 200) {
						this.$store.commit('setUserInfo', loginInfo.data)
						uni.removeStorageSync('resumeFormData')
						// uni.switchTab({
						// 	url: "/pages/index/index"
						// })
                        uni.navigateBack({
                            delta:2
                        })
						return uni.$u.toast('保存信息成功')
					} else {
						return uni.$u.toast('失败')
					}
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding: 0 0 256rpx 0;
	}

	.step {
		padding: 32rpx 0;
		border-radius: 0 0 24rpx 24rpx;
	}

	::v-deep .placeholderClass {
		font-weight: 400;
		font-size: 32rpx;
	}



	.wrap {
		padding: 0 32rpx;
		margin-top: 32rpx;

		.wrap-title {
			font-weight: 500;
			font-size: 32rpx;
			color: #333333;
			padding: 0 0 24rpx 0;

			.star {
				font-weight: 600;
				font-size: 22rpx;
				color: #FE4D4F;
				margin-left: 8rpx;
			}
		}

		.pic-list {
			display: flex;
			flex-wrap: wrap;

			.pic-item {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 200rpx;
				width: calc(33.3% - 32rpx);
				margin-bottom: 32rpx;
				margin-right: 32rpx;
				position: relative;
				border-radius: 16rpx;

				&>image {
					width: 100%;
					height: 100%;
					border-radius: 16rpx;
				}

				.zz {
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: rgba(0, 0, 0, 0.5);
					width: 100%;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
					z-index: 10;
					border-radius: 16rpx;

					.del {
						display: flex;
						align-items: center;
						border-radius: 16rpx;
						padding: 0 16rpx;
						height: 56rpx;
						background: rgba(255, 255, 255, 0.5);
						font-weight: 500;
						font-size: 28rpx;
						color: #FFFFFF;
					}
				}
			}

			.add {
				background-color: #FFFFFF;

				image {}
			}

		}

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			margin-bottom: 32rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.txt {
					padding: 24rpx 0;

				}

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.supTitle {
					font-weight: 500;
					font-size: 32rpx;
					color: #333333;
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					::v-deep uni-data-picker {
						width: 100%;
					}

					::v-deep .arrow-area {
						transform: rotate(-135deg);
					}

					::v-deep .input-arrow {
						width: 20rpx;
						height: 20rpx;
						border-left: 1px solid #606266;
						border-bottom: 1px solid #606266;
					}

					::v-deep .input-value-border {
						border: none;
					}

					::v-deep .input-value {
						padding: 0;
					}

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}

					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.footer {
		width: 100%;
		height: 224rpx;
		left: 0;
		bottom: 0;
		z-index: 10;
		position: fixed;
		background-color: #F5F5F7;

		.btns {
			display: flex;
			flex-direction: column;
			justify-content: center;
			height: 100%;
			padding: 0 32rpx;
			flex: 1;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 72rpx;
				border-radius: 36rpx;
				font-weight: 600;
				font-size: 28rpx;

				&:first-child {
					margin-bottom: 24rpx;
				}
			}

			.jump {
				background: #FFFFFF;
				color: #4F8CF0;
			}

			.next {
				background: #4F8CF0;
				color: #FFFFFF;
			}
		}
	}
</style>
