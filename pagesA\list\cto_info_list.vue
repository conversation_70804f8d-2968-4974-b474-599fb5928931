<template>
	<view class="">
		<view class="list">
			<view class="item" v-for="item in list" @click="goCtoDetails">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode=""></image>
				<view class="name">
					腾讯视频有限公司
				</view>
				<view class="arrow">
					<u-icon name="arrow-right"></u-icon>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
    import {
    	getHeadhuntersAuthEnterpriseList
    } from "../../config/api.js"
	export default{
		data(){
			return{
				list:[]
			}
		},
        onLoad() {
            this.getHeadhuntersAuthEnterpriseList()
        },
		methods:{
            async getHeadhuntersAuthEnterpriseList() {
            	let params = {}
            	const result = await getHeadhuntersAuthEnterpriseList(params)
            	if (result.status_code == 200) {
                    this.list = result.data
                console.log("res",result)
            	}
            },
			goCtoDetails(){
				uni.navigateTo({
					url:"/pagesA/details/ctoDetails"
				})
			}
		}
	}
</script>
<style>
	page{
		background-color: #f5f5f7;
	}
</style>
<style lang="less" scoped>

	.list{
		display: flex;
		flex-direction: column;
		padding: 32rpx;
		.item{
			display: flex;
			align-items: center;
			padding: 32rpx;
			margin-bottom: 24rpx;
			background-color: #FFFFFF;
			border-radius: 24rpx;
			&:last-child{
				margin-bottom: 0;
			}
			image{
				width: 72rpx;
				height: 72rpx;
			}
			.name{
				display: flex;
				flex: 1;
				padding: 0 24rpx;
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;
			}
		}
	}
</style>
