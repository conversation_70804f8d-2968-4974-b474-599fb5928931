<template>
	<view class="item" >
		<view class="item-up" @click="goQlmDetails">
			<image :src="item.member_info.image['path_url']" mode=""></image>
			<view class="info">
				<view class="user">
					<view class="userInfo">
						<view class="name">
							{{item.member_certification.name?item.member_certification.name:item.member_info.nick_name}}
						</view>
						<view class="cert" v-if="item.certification_status==1 || item.member.certification_status==1">
							已认证
						</view>
						<view class="cert cancel" v-if="item.certification_status==2 || item.member.certification_status==2">
							未认证
						</view>
					</view>
					<view class="status">
						<view class="dot line">
						</view>{{item.member_info.job_status_name}}
					</view>
				</view>
				<view class="tags">
					<view class="tag">
						{{item.member_info.sex_str}}
					</view>
					<view class="tag">
						{{item.member_info.age}}岁
					</view>
					<!-- <view class="tag">
						3年工作经验
					</view> -->
					<view class="tag">
						{{item.member_info.education_type_name}}
					</view>

				</view>
			</view>
		</view>


		<view class="item-down">
			<view class="addr">
				{{item.member_info.introduce||'暂无介绍'}}
			</view>
			<view class="btn" @click="communicate('member',item.id)">
				聊聊呗
			</view>
		</view>
	</view>
</template>

<script>
import {communicate} from "../common/common";

	export default {
		name: "qlmJobItem",
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {

			};
		},
		methods: {
      communicate,
			goQlmDetails() {
				uni.navigateTo({
					url: "/pagesA/details/qlmDetails?id=" + this.item.member_info.id
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		padding: 32rpx;
		margin-bottom: 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;

		.item-up {
			display: flex;
			padding-bottom: 28rpx;

			&>image {
				width: 104rpx;
				height: 104rpx;
				border-radius: 16rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				flex: 1;
				padding-left: 24rpx;

				.user {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.userInfo {
						display: flex;
						align-items: center;

						.name {
							font-weight: 600;
							font-size: 32rpx;
							color: #333333;
						}

						.cert {
							display: flex;
							align-items: center;
							margin-left: 28rpx;
							padding: 0 12rpx;
							height: 40rpx;
							background: rgba(87, 213, 28, 0.1);
							border-radius: 8rpx;
							font-weight: 600;
							font-size: 20rpx;
							color: #57D51C;
						}

						.cancel {
							background-color: #cccccc;
							color: #FFFFFF;
						}
					}

					.status {
						display: flex;
						align-items: center;
						font-weight: 400;
						font-size: 24rpx;
						color: #333333;

						.dot {
							width: 10rpx;
							height: 10rpx;
							border-radius: 50%;
							background: #999999;
							margin-right: 10rpx;
						}

						.line {
							background: #57D51C;
						}
					}
				}



				.tags {
					display: flex;

					.tag {
						display: flex;
						align-items: center;
						padding: 0 12rpx;
						font-weight: 400;
						font-size: 22rpx;
						color: #999999;
						// margin-right: 16rpx;
						border-right: 1px solid #999999;

						&:first-child {
							padding-left: 0;
						}

						&:last-child {
							border-right: none;
						}
					}
				}
			}

		}



		.item-down {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 28rpx 0 0 0;
			border-top: 1px solid #F5F5F5;

			.addr {
				width: 470rpx;
				// display: flex;
				// align-items: center;
				font-weight: 400;
				font-size: 12px;
				color: #333333;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.btn {
				// display: flex;
				// justify-content: center;
				// align-items: center;
				background-color: #4F8CF0;
				font-weight: 600;
				// padding: 0 24rpx;
				width: 144rpx;
				height: 56rpx;
				text-align: center;
				line-height: 56rpx;
				font-size: 24rpx;
				color: #FFFFFF;
				border-radius: 8rpx;
			}
		}
	}
</style>