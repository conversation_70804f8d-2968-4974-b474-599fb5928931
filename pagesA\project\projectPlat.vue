<template>
	<view class="home-index">
		<!-- <top-bar-status :class="['topClass', isScroll ? 'isScroll' : '']">
			<template #default>
				<view class="top_content">
					<view class="logo">
						<image src="../../static/images/index/logo.png" mode=""></image>
					</view>
					<view class="title">
						招聘会管理
					</view>
				</view>
			</template>
		</top-bar-status> -->
		<view class="content">
			<image src="https://api-test.zhaopinbei.com/images/plat.png" mode="widthFix"></image>
			<view class="list">
				<view class="item" v-for="(item,index) in list" :key="index" @click="go(item)">
					<image :src="item.icon" mode=""></image>
					<text>{{item.name}}</text>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import TopBarStatus from "../../components/topBarStatus.vue"
	export default {
		components: {
			TopBarStatus,
		},
		data() {
			return {
				isScroll: false,
				list: [{
						icon: '/static/images/projectPlat/listIcon.png',
						name: '项目列表',
						path: '/pagesA/project/list',
						
					},
					{
						icon: '/static/images/projectPlat/recordsIcon.png',
						name: '项目记录',
						path: '/pagesA/project/projectRecords'
					},
					{
						icon: '/static/images/projectPlat/signUpIcon.png',
						name: '报名记录',
						path: '/pagesA/project/signUp'
					},
					{
						icon: '/static/images/projectPlat/interviewIcon.png',
						name: '面试列表',
						path: '/pagesA/project/interviewList'
					},
					{
						icon: '/static/images/projectPlat/coordinationIcon.png',
						name: '协同列表',
						path: '/pagesA/list/cooperateList'
					},
					{
						icon: '/static/images/projectPlat/coordinationIcon.png',
						name: '协同记录',
						path: '/pagesA/cooperate/record'
					}
				],
				// company_list: [{
				// 		icon: '/static/images/plat/hireIcon.png',
				// 		name: '招聘会列表',
				// 		path: '/pagesA/list/company_jobfair_management_list',
						
				// 	},
				// 	{
				// 		icon: '/static/images/plat/signUpIcon.png',
				// 		name: '报名记录',
				// 		path: '/pagesA/list/signup_list'
				// 	}
				// ],
				// headhunters_list: [{
				// 		icon: '/static/images/plat/hireIcon.png',
				// 		name: '招聘会列表',
				// 		path: '/pagesA/headhunterList/headhunters_jobfair_management_list',
				// 	},
				// 	{
				// 		icon: '/static/images/plat/signUpIcon.png',
				// 		name: '报名记录',
				// 		path: '/pagesA/list/signup_list'
				// 	}
				// ],
			}
		},
		computed: {
			// roleType() {
			// 	return this.$store.state.roleType || uni.getStorageSync('roleType')
			// },
			// list() {
			// 	return this.roleType == 'company' ? this.company_list : this.headhunters_list
			// }
		},
		onPageScroll(e) {
			this.isScroll = e.scrollTop > 0
		},
		methods: {
			go(item) {
				uni.navigateTo({
					url: item.path
				})
			}
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding-bottom: 128rpx;
	}

	.topClass {
		position: inherit;
		width: 100%;
		z-index: 10;
		transition: all .5s;
		background: #FFFFFF;

		&.isScroll {
			position: fixed;
			top: 0;
			left: 0;
		}

		.top_content {
			display: flex;
			align-items: center;
			height: 100%;

			.logo {
				display: flex;
				position: absolute;

				image {
					width: 126rpx;
					height: 44rpx;
				}
			}

			.title {
				display: flex;
				flex: 1;
				justify-content: center;
				font-weight: 500;
				font-size: 34rpx;
				color: #000000;
			}
		}
	}

	.content {
		padding: 0 32rpx;

		&>image {
			width: 100%;
			margin: 32rpx 0;
		}
	}

	.list {
		display: flex;
		flex-wrap: wrap;
		overflow: hidden;
		margin-right: -32rpx;

		.item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: calc(25% - 32rpx);
			margin-right: 32rpx;
			background-color: #FFFFFF;
			margin-bottom: 32rpx;
			height: 148rpx;
			border-radius: 24rpx;

			image {
				width: 64rpx;
				height: 64rpx;
			}

			text {
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				margin-top: 12rpx;
			}
		}
	}
</style>