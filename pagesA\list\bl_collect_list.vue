<template>
	<view class="">
		<u-sticky bgColor="#FFFFFF">
			<view class="tabs">
				<u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" @click="changeTab"></u-tabs>
			</view>

		</u-sticky>

		<view class="list" v-if="list.length>0">
			<u-swipe-action>
				<u-swipe-action-item @click="handleSwipe(item,$event)" :options="options" v-for="(item,index) in list"
					:key="index">
					<block v-if="tabIndex==0">
						<obtain-item :item="item"></obtain-item>
					</block>
					<block v-if="tabIndex==1">
						<qlm-job-item :item="item"></qlm-job-item>
					</block>
				</u-swipe-action-item>
			</u-swipe-action>
		</view>
		<block v-else>
			<u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png">
			</u-empty>
		</block>
	</view>
</template>


<script>
	import {
		getCollectCompanyHeadhuntersList,
		getCollectCompanyMemberList,
		cancelCollectCompanyHeadhunters,
		cancelCollectCompanyMember
	} from "../../config/api.js"
	import QlmJobItem from "../../components/qlmJobItem.vue"
	import ObtainItem from "../../components/obtainItem.vue"
	import {
		collectMemberList,
		getCollectCompanyList
	} from "../../config/api.js"
	export default {
		components: {
			QlmJobItem,
			ObtainItem
		},
		data() {
			return {
				list: [],
				page: 1,
				limit: 10,
				options: [{
					text: '删除',
					style: {
						backgroundColor: '#FE4D4F',
						borderRadius: '24rpx',
						bottom: '32rpx',
						height: '98%',
						width: '148rpx',
						marginLeft: '24rpx',
						marginRight: '2rpx'
					}
				}],
				tabIndex: 0,
				tabs: [{
					name: '就业管家收藏'
				}, {
					name: '千里马收藏',
				}]
			}
		},
		onPageScroll(e) {},
		onLoad() {
			this.getCollectCompanyHeadhuntersList()
		},
		methods: {
			changeTab(e) {
				this.page = 1
				this.list = []
				this.tabIndex = e.index
				if (this.tabIndex == 0) {
					this.getCollectCompanyHeadhuntersList()
				} else if (this.tabIndex == 1) {
					this.getCollectCompanyMemberList()
				}
				this.tabIndex = e.index
			},

			//删除
			async handleSwipe(item, e) {
				let self = this;
				//表示点击了删除按钮
				if (e.index == 0) {
					uni.showModal({
						title: '确定要删除吗？',
						success: async (res) => {
							if (res.confirm) {
								console.log('用户点击确定');
								if (this.tabIndex == 0) {
									let params = {
										id: item.id
									}
									const {
										status_code,
										data
									} = await cancelCollectCompanyHeadhunters(params)
									if (status_code == 200) {
										self.page = 1
										self.getCollectCompanyHeadhuntersList()
										return uni.$u.toast('成功')
									}
								} else if (this.tabIndex == 1) {
									let params = {
										member_id: item.member_info.id
									}
									const {
										status_code,
										data
									} = await cancelCollectCompanyMember(params)
									if (status_code == 200) {
										self.page = 1
										self.getCollectCompanyMemberList()
										return uni.$u.toast('成功')
									}
								}

							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					})
				}
			},

			//就业管家收藏列表
			async getCollectCompanyHeadhuntersList() {
				let params = {
					page: this.page,
					limit: this.limit
				}
				const {
					status_code,
					message,
					data
				} = await getCollectCompanyHeadhuntersList(params)
				if (status_code == 200) {
					this.list = data.data;
				}
			},

			//求职者收藏列表
			async getCollectCompanyMemberList() {
				let params = {
					page: this.page,
					limit: this.limit
				}
				const {
					status_code,
					message,
					data
				} = await getCollectCompanyMemberList(params)
				if (status_code == 200) {
					this.list = data.data;
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f5;
	}
</style>
<style lang="less" scoped>
	.tabs {
		margin-bottom: 32rpx;
	}

	.list {
		padding: 0 32rpx;

		::v-deep .u-swipe-action-item__right {
			bottom: 32rpx;
			border-radius: 24rpx;
		}

		::v-deep .u-swipe-action-item__content {
			background: transparent;
		}
	}
</style>