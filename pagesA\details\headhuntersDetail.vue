<template>
  <view
    class="content"
    :style="{
      backgroundImage: 'url(https://api-test.zhaopinbei.com/images/headBg.png)',
      backgroundSize: 'cover',
      backgroundPosition: 'top',
      paddingTop: (titletop + titleheight + 6) * 2 + 'rpx !important',
      paddingBottom: titleheight * 2 + 'rpx !important',
    }"
  >
    <image
      class="back"
      :style="{ top: titletop + titleheight + 30 + 'rpx' }"
      src="https://api-test.zhaopinbei.com/storage/uploads/images/AiaBYluONHS4nW6LN71MddmwfH7ZeVlcTQxwRNoC.png"
      @click="back()"
    ></image>
    <!-- 顶部 -->
    <view class="topCon">
      <view class="infoCon">
        <image
          :src="detail.member_info ? detail.member_info.image.path_url : ''"
          mode=""
        ></image>
        <view class="info">
          <view class="name">{{ detail.member_certification.name }}</view>
          <view class="comp">{{ detail.company.name }}</view>
        </view>
      </view>
      <view class="tags">
        <block>
          <view
            class="tags"
            v-for="(labelItem, index) in detail.user_headhunter.label"
            :key="index"
            :item="labelItem"
          >
            <view class="tag">
              {{ labelItem }}
            </view>
          </view>
        </block>
      </view>
    </view>

    <view class="centerCon">
      <view class="auth">
        <image
          src="https://api-test.zhaopinbei.com/storage/uploads/images/MuUbXbr83bUs6FxI5DiePiU7hDmKyC1K2hD3jG3F.png"
          mode=""
        ></image>
        已实名认证
      </view>
      <view class="jobTags">
        <view class="title" style="margin-top: 20rpx; margin-bottom: 20rpx"
          >擅长行业</view
        >
        <view class="tags">
          <block>
            <view class="tags">
              <view class="tag"> 标签 </view>
            </view>
          </block>
        </view>
      </view>
      <view class="jobTags">
        <view class="title" style="margin-top: 20rpx; margin-bottom: 20rpx"
          >擅长职业</view
        >
        <view class="tags">
          <block>
            <view class="tags">
              <view class="tag"> 标签 </view>
            </view>
          </block>
        </view>
      </view>
      <view class="">
        <view
          class="titles"
          style="
            margin-top: 20rpx;
            margin-bottom: 20rpx;
            display: flex;
            align-items: center;
          "
        >
          <view class="title" style="">
            <text class=""> 个人介绍 </text>
          </view>
          <img
            @click="showZongjie = true"
            style="width: 32rpx; height: 32rpx; margin-left: 18rpx"
            src="https://api-test.zhaopinbei.com/storage/uploads/images/M73DSSf4x6zAJwx8jjaP0pf5TlEmQsqQwWbTQ2qV.png"
            alt=""
          />
        </view>
        <view class="">{{
          detail.user_headhunter.introduction || "暂无"
        }}</view>
      </view>
      <view class="">
        <view class="title" style="margin-top: 20rpx; margin-bottom: 20rpx"
          >擅长人群</view
        >
        <view class="tags">
          <block>
            <view class="tags">
              <view class="tag"> 标签 </view>
            </view>
          </block>
        </view>
      </view>
      <view class="">
        <view class="title" style="margin-top: 20rpx; margin-bottom: 20rpx"
          >我的领地</view
        >
        <view class="tags">
          <block>
            <view class="tags">
              <view class="tag"> 标签 </view>
            </view>
          </block>
        </view>
      </view>
    </view>

    <view class="bottomCon">
      <view class="">
        <view class="jobList">
          <view class="title">服务人才</view>
          <view class="title-right">
            <text>更多人才</text>
            <u-icon name="arrow-right" size="28rpx"></u-icon>
          </view>
        </view>
        <view class="comment">
          <view class="infoCom" style="margin-top: 32rpx">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"
              mode=""
            ></image>
            <view class="custom">
              <view class="infoCustom">
                <view class="name">李先生</view>
              </view>
              <view class="tags" style="margin-top: 0">
                <block>
                  <view class="tags">
                    <view class="tag"> 标签 </view>
                  </view>
                </block>
              </view>
            </view>
          </view>
          <view class="commentCon"> </view>
        </view>
      </view>
    </view>
    <view class="bottomCon">
      <view class="">
        <view class="jobList">
          <view class="title">服务企业</view>
          <view class="title-right">
            <text>更多企业</text>
            <u-icon name="arrow-right" size="28rpx"></u-icon>
          </view>
        </view>
        <view class="comment">
          <view class="infoCom" style="margin-top: 32rpx">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"
              mode=""
              style="border-radius: 16rpx"
            ></image>
            <view class="custom">
              <view class="infoCustom">
                <view class="name">搞定科技</view>
              </view>
              <view class="tags" style="margin-top: 0">
                <block>
                  <view class="tags">
                    <view class="tag"> 标签 </view>
                  </view>
                </block>
              </view>
            </view>
          </view>
          <view class="commentCon"> </view>
        </view>
      </view>
    </view>

    <view class="bottomCon">
      <view class="">
        <view class="jobList">
          <view class="title">我的发布</view>
        </view>
        <view class="comment">
          <view class="tabs-container">
            <u-tabs
              :current="tabsIndex"
              :list="tabsList"
              :activeStyle="{ color: '#4F8CF0', transform: 'scale(1)' }"
              :inactiveStyle="{ color: '#333', transform: 'scale(1)' }"
            ></u-tabs>
          </view>
          <view class="infoFabu" style="margin-top: 32rpx">
            <view class="custom">
              <view class="infoCustom">
                <view class="name">游戏UI设计-北京</view>
                <view class="money"> 12-50k </view>
              </view>
              <view class="tags" style="margin-top: 0">
                <block>
                  <view class="tags">
                    <view class="tag"> 标签 </view>
                  </view>
                </block>
              </view>
              <view class="jianjie">
                这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介
              </view>
            </view>
          </view>
          <view class="commentCon"> </view>
        </view>
      </view>
    </view>

    <view class="bottomCon">
      <view class="">
        <view class="jobList">
          <view class="title">我的任务</view>
        </view>
        <view class="comment">
          <view class="tabs-container">
            <u-tabs
              :current="tabsIndex"
              :list="tabsList"
              :activeStyle="{ color: '#4F8CF0', transform: 'scale(1)' }"
              :inactiveStyle="{ color: '#333', transform: 'scale(1)' }"
            ></u-tabs>
          </view>
          <view class="infoFabu" style="margin-top: 32rpx">
            <view class="custom">
              <view class="infoCustom">
                <view class="name">2.20职位任务</view>
                <view class="money"> 1000/人 </view>
              </view>
              <view class="renwu"> 职位任务 </view>
              <u-line></u-line>
              <view class=""> 发布人：张瑞显 </view>
              <view class=""> 发布时间：2025-02-20 14:06:31 </view>
              <view class="com-name">
                <img src="" alt="" />
                <view class="names"> 公司名称公司名称公司名称 </view>
                <view class="com-btn"> 进行中 </view>
              </view>
            </view>
          </view>
          <view class="commentCon"> </view>
        </view>
      </view>
    </view>

    <view class="bottomCon">
      <view class="">
        <view class="jobList">
          <view class="title"> 客户印象 </view>
          <view class="title-right" @click="yinxiangMore">
            <text>查看更多</text>
            <u-icon name="arrow-right" size="28rpx"></u-icon>
          </view>
        </view>
        <view class="tags">
          <block>
            <view class="tags">
              <view class="tag"> 标签 </view>
            </view>
          </block>
        </view>
      </view>

      <view class="">
        <view class="jobList">
          <view class="title" style="margin-top: 20rpx; margin-bottom: 20rpx"
            >评价</view
          >
          <view class="title-right" @click="showPingPopup = true">
            <text>查看更多</text>
            <u-icon name="arrow-right" size="28rpx"></u-icon>
          </view>
        </view>
        <view class="comment">
          <view class="infoCom">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"
              mode=""
            ></image>
            <view class="custom">
              <view class="infoCustom">
                <view class="name">匿名</view>
                <view class="time">近期评论</view>
              </view>
              <view class="">
                <u-rate :value="5" readonly activeColor="#F9AD14"></u-rate>
              </view>
            </view>
          </view>
          <view class="commentCon">
            <view class="con">
              个人介绍个人介绍个人介绍个人介绍
              个人介绍个人介绍个人介绍个人介绍个人介绍个人介绍
            </view>
          </view>
        </view>
      </view>
    </view>

    <view style="height: 200rpx"></view>
    <view class="sureBtnBox">
      <view class="btn" @click="communicate('user_headhunter', detail.id)">
      </view>
    </view>
    <!-- 客户印象弹出框 -->
    <u-popup
      :show="showImpressionPopup"
      mode="bottom"
      @close="showImpressionPopup = false"
      :round="16"
    >
      <view class="impression-popup">
        <view class="impression-popup-header">
          <text class="impression-title">客户印象</text>
          <u-icon
            name="close"
            size="28"
            @click="showImpressionPopup = false"
          ></u-icon>
        </view>
        <view class="impression-content">
          <view
            class="impression-item"
            v-for="(item, index) in impressionList"
            :key="index"
          >
            <text class="impression-name">{{ item.name }}</text>
            <text class="impression-count">{{ item.count }}</text>
          </view>
        </view>
      </view>
    </u-popup>

    <u-popup
      :show="showPingPopup"
      mode="bottom"
      @close="showPingPopup = false"
      :round="16"
    >
      <view class="impression-popup">
        <view class="impression-popup-header">
          <text class="impression-title">全部评价</text>
          <u-icon
            name="close"
            size="28"
            @click="showPingPopup = false"
          ></u-icon>
        </view>
        <view class="impression-content">
          <view class="comment">
            <view class="infoCom">
              <image
                src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"
                mode=""
              ></image>
              <view class="custom">
                <view class="infoCustom">
                  <view class="name">匿名</view>
                  <view class="time">近期评论</view>
                </view>
                <view class="">
                  <u-rate :value="5" readonly activeColor="#F9AD14"></u-rate>
                </view>
              </view>
            </view>
            <view class="commentCon">
              <view class="con">
                个人介绍个人介绍个人介绍个人介绍
                个人介绍个人介绍个人介绍个人介绍个人介绍个人介绍
              </view>
            </view>
          </view>
        </view>
      </view>
    </u-popup>

    <u-popup
      :show="showZongjie"
      mode="center"
      :round="16"
      @close="showZongjie = false"
    >
      <view
        class="impression-popup"
        style="background: #fff; width: 600rpx; min-height: auto"
      >
        <view class="impression-popup-header">
          <text class="impression-title">温馨提示</text>
          <u-icon name="close" size="28" @click="showZongjie = false"></u-icon>
        </view>
        <view class="impression-content">
          <view class="comment">
            <view class="infoCom" style="font-size: 24rpx; color: #777777">
              招聘呗为第三方交易平台及互联网信息服务提供者，平台所展示的案例详情、价格等信息皆由就业管家发布，其真实性、准确性和合法性均由就业管家负责。建议您谨慎核实，如发现违法、侵权信息，请立即向平台举报。
            </view>
            <view class="commentCon">
              <view class="con-btn" @click="showZongjie = false">
                我知道了
              </view>
            </view>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { headhunterDetail } from "../../config/common_api.js";
import AddressMap from "../../public_label/addressMap.vue";
import { isAuth } from "@/common/common.js";
import { headhunterJobs } from "../../config/common_api";
import {
  collectHeadhunter,
  clearCollectHeadhunter,
} from "../../config/member_api";
import { communicate } from "../../common/common";

export default {
  data() {
    return {
      id: 0,
      collect: false,
      detail: {},
      jobs: [],
      roleType: "",
      titleheight: 0,
      titletop: 0,
      iconTop: 0,
      tabsIndex: 0,
      tabsList: [
        {
          key: "1",
          name: "岗位",
        },
        {
          key: "2",
          name: "特推招聘",
        },
        {
          key: "0",
          name: "宣讲会",
        },
        {
          key: "3",
          name: "招聘会",
        },
        {
          key: "4",
          name: "岗位预定",
        },
      ],
      // 新增客户印象相关数据
      showImpressionPopup: false,
      showPingPopup: false,
      showZongjie: false,
      impressionList: [
        { name: "交付及时", count: "114" },
        { name: "联系及时", count: "103" },
        { name: "认真负责", count: "102" },
        { name: "增强信心", count: "114" },
        { name: "个人亮点挖掘", count: "114" },
        { name: "逻辑清晰", count: "114" },
        { name: "精通行业", count: "114" },
        { name: "重点突出", count: "114" },
        { name: "提供职业建议", count: "114" },
        { name: "提供面试建议", count: "114" },
      ],
    };
  },
  onLoad(options) {
    var _this = this;
    _this.id = options.id;
    _this.getHeadhunter();
    _this.roleType = uni.getStorageSync("roleType");
    this.getHeight();
  },

  methods: {
    getHeight() {
      let res = uni.getMenuButtonBoundingClientRect();
      this.titletop = res.top;
      this.titleheight = res.height;
      // console.log(res);
      // console.log("titletop的值", this.titletop);
      // console.log("titleheight的值", this.titleheight);
      // console.log("一半的titleheight的值", this.titleheight / 2);
    },
    goNew() {
      uni.navigateTo({
        url: "/pagesA/details/headhuntersDetail",
      });
    },
    communicate,
    goListDetails(id) {
      uni.navigateTo({
        url: "/pagesA/list/obtainItemDetails_qlm_list?id=" + id,
      });
    },
    companyDetails(id) {
      uni.navigateTo({
        url: "/pagesA/details/companyDetail?id=" + id,
      });
    },
    back() {
      uni.$u.route({
        type: "back",
      });
    },
    goJob(jobDetail) {
      uni.navigateTo({
        url: "/pagesA/details/memberJobDetails?id=" + jobDetail.job.id,
      });
    },
    getHeadhunter() {
      var _this = this;
      headhunterDetail({
        id: _this.id,
      }).then((response) => {
        _this.detail = response.data;
        _this.collect = response.data.collect_status == 1;

        var userId = _this.detail.id;

        if (_this.detail.id > 0) {
          headhunterJobs({
            send_user_id: userId,
            page: 1,
            limit: 5,
          }).then((response) => {
            _this.jobs = response.data.data;
          });
        }
      });
    },
    collectSelect() {
      // console.log('11111')
      var _this = this;

      if (!isAuth(["login"])) return;

      const res_userInfo = uni.getStorageSync("userInfo");

      var _roleType = res_userInfo.role_type;
      var _collectData = {
        id: _this.detail.id,
      };
      if (_this.collect) {
        clearCollectHeadhunter(_collectData).then((response) => {
          if (response.status_code == 200) {
            _this.collect = false;
          }
        });
      } else {
        collectHeadhunter(_collectData).then((response) => {
          if (response.status_code == 200) {
            _this.collect = true;
          }
        });
      }
    },
    yinxiangMore() {
      console.log(111);

      this.showImpressionPopup = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  position: relative;
}

view {
  box-sizing: border-box;
}

.sureBtnBox {
  width: 100%;
  height: 196rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  display: flex;
  justify-content: center;
  bottom: 0;
  left: 0;
  align-items: center;
  padding: 24rpx 32rpx 96rpx 32rpx;
}

.sureBtnBox_left {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 28rpx;
  color: #999999;
}

.yellow {
  color: #f9ad14 !important;
}

.startIcon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 8rpx;
}

.btn {
  width: 570rpx;
  height: 80rpx;
  background-image: url("https://api-test.zhaopinbei.com/storage/uploads/images/Ycpicwyb22B1rszQ6YwwTpgLPlMnwylS5un3FjmJ.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back {
  width: 32rpx;
  height: 32rpx;
  position: absolute;
  // top: 50rpx;
  position: fixed;
  left: 24rpx;
}

.infoFabu {
  .custom {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    .tag {
      background: rgba(255, 255, 255);
    }
  }
  .infoCustom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .name {
      font-size: 28rpx;
      color: #333333;
    }
    .money {
      font-size: 32rpx;
      color: #f98a14;
    }
    .renwu {
      font-size: 24rpx;
      color: #1690ff;
    }
  }
  .jianjie {
    font-size: 24rpx;
    color: #333333;
  }

  .com-name {
    display: flex;
    align-items: center;
    justify-content: space-between;
    img {
      width: 48rpx;
      height: 48rpx;
      background: #d9d9d9;
      border-radius: 8rpx;
    }
    .names {
      font-size: 24rpx;
      color: #999999;
    }
    .com-btn {
      width: 96rpx;
      height: 46rpx;
      background: rgba(79, 140, 240, 0.1);
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-size: 24rpx;
      color: #4f8cf0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

// 客户印象弹出框样式
.impression-popup {
  width: 100%;
  padding: 32rpx;
  background: #f5f5f7;
  border-radius: 16rpx;
  min-height: 50vh;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;
  }

  .impression-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }

  .impression-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .impression-item {
    padding: 0 16rpx;
    height: 56rpx;
    background-color: #fff;
    border-radius: 8rpx;
    margin-bottom: 16rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;

    .impression-name {
      font-size: 28rpx;
      color: #4e5c75;
      margin-right: 12rpx;
    }

    .impression-count {
      font-size: 24rpx;
      color: #f98a14;
    }
  }
  .comment {
    .infoCom {
      display: flex;
      image {
        width: 104rpx;
        height: 104rpx;
        border-radius: 50%;
        margin-right: 12rpx;
      }
      .custom {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .infoCustom {
          display: flex;
          align-items: center;
          .name {
            margin-right: 10rpx;
            font-size: 28rpx;
          }
          .time {
            color: #4f8cf0;
            font-size: 24rpx;
          }
        }
      }
    }
    .commentCon {
      display: flex;
      justify-content: flex-end;
      .con {
        width: 82%;
        color: #777777;
        font-size: 24rpx;
        display: -webkit-box; /* 将元素设置为弹性盒子 */
        -webkit-box-orient: vertical; /* 设置文本垂直排列 */
        -webkit-line-clamp: 2; /* 限制文本显示两行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }
      .con-btn {
        width: 134rpx;
        height: 52rpx;
        background: #2370ee;
        border-radius: 8rpx;
        font-size: 24rpx;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
@import "../../static/css/pagesA/details/headhuntersDetail.scss";
</style>
