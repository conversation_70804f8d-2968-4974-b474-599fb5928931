<template>
	<view class="recruitment-container">
		<view class="title">本次活动参选单位名录，如下：</view>
		<view class="job-item" v-for="(job, index) in jobList" :key="index">
			<view class="job-header">
				<view class="company-name">{{job.company}}高新招聘前端</view>
				<view class="salary">{{job.salary}}/天</view>
			</view>
			<view class="job-details">
				<view class="requirement">基本要求：{{job.requirement}}</view>
				<view class="time">
					<span>
						报名截止时间：{{job.deadline}}
					</span>
					<span>
						上岗时间：{{job.startTime}}
					</span>
				</view>
			</view>
		</view>
		<view class="footBox">
			<view class="sectionBox">

			</view>
			<view class="footer">
				<view class="footer-item">扫码报名</view>
				<view class="footer-item">聊一聊</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				jobList: [{
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					},
					{
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					}, {
						company: 'xx公司',
						salary: '500',
						requirement: '身高1.7cm、男女不限、能吃苦、工作时间8小时、工作地点北京xxx、薪水500 - 800每天',
						deadline: '2025-04-01',
						startTime: '2025-04-08'
					},
				]
			};
		}
	};
</script>

<style scoped>
	.recruitment-container {
		padding: 30rpx;
		background: rgba(245, 245, 247, 1);
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
	}

	.job-item {
		border: 2rpx solid #e5e5e5;
		border-radius: 10rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		background: rgba(255, 255, 255, 1);
	}

	.job-header {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;
	}

	.company-name {
		font-size: 28rpx;
	}

	.salary {
		font-size: 28rpx;
		color: rgba(249, 138, 20, 1);
	}

	.job-details {
		font-size: 24rpx;
		color: #666;
	}

	.requirement {
		margin-bottom: 10rpx;
	}

	.time {
		display: flex;
		justify-content: space-between;
		width: 622rpx;
		height: 34rpx;
		color: rgba(51, 51, 51, 1);
		font-size: 24rpx;
	}

	.sectionBox {
		width: 134rpx;
		height: 134rpx;
		background-color: rgba(217, 217, 217, 1);
	}

	.footBox {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 268rpx;
		height: 134rpx;
	}

	.footer {
		height: 134rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
		padding: 30rpx 0;
	}

	.footer-item {
		font-size: 24rpx;
		color: rgba(51, 51, 51, 1);
	}
</style>