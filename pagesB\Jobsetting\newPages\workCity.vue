<template>
	<!-- 选择城市 -->
	<view class="container">

	</view>
</template>

<script>
	export default {
		data() {
			return {

			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onShow() {},
		methods: {

		}
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #f5f5f5;
	}
</style>