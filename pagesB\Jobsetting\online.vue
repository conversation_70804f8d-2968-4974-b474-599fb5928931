<template>
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<u-form labelPosition="top" labelWidth="200">
				<view class="scroll-container">
					<view class="title-container">
						<text class="title">个人信息</text>
						<text class="title-link" @click="onRoutePage('personalInformation', {})">添加</text>
					</view>
					<view class="card-container">
						<view class="personal-container">
							<view class="personal-start">
								<image class="avatar" src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"></image>
								<view class="user">
									<text class="name">娃哈哈</text>
									<text class="detail">男 | 21岁 | 汉族 | 高中</text>
								</view>
							</view>
							<view class="personal-end">温馨提示：使用真实头像更加分哦</view>
						</view>
					</view>
					<view class="title-container">
						<text class="title">个人优势</text>
						<text class="title-link" @click="onRoutePage('personalStrength', {})">添加</text>
						<!-- <text class="title-link">保存</text> -->
					</view>
					<view class="card-container">
						<view class="strength-container">
							<u-textarea v-model="params.strength.content" border="none" height="100"
								placeholder="请输入个人优势"></u-textarea>
						</view>
					</view>
					<view class="title-container">
						<text class="title">求职期望</text>
						<text class="title-link" @click="onRoutePage('jobExpectation', {})">添加</text>
						<!-- <text class="title-link">保存</text> -->
					</view>
					<view class="card-container">
						<view class="expect-container">
							<u-form-item label="期望行业" borderBottom @click="onIndustryPage">
								<u-input v-model="params.job.content.industry" placeholder="请选择期望行业" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="期望职位" borderBottom>
								<u-input v-model="params.job.content.posts" placeholder="请输入期望职位"
									border="none"></u-input>
							</u-form-item>
							<u-form-item label="薪资要求" borderBottom @click="job.salaryShowPicker = true">
								<u-input v-model="job.salaryText" placeholder="请选择薪资要求" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="工作城市" borderBottom @click="onWorkCityPage">
								<u-input v-model="params.job.content.city" placeholder="请选择工作城市" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="其他感兴趣的城市" borderBottom>
								<u-input v-model="params.job.content.interestedCity" placeholder="请输入其他感兴趣的城市"
									border="none"></u-input>
							</u-form-item>
							<u-form-item label="期望职位详情">
								<u-input v-model="params.job.content.detail" placeholder="请输入期望职位详情"
									border="none"></u-input>
							</u-form-item>
						</view>
					</view>
					<view class="title-container">
						<text class="title">工作/实习经历</text>
						<text class="title-link" @click="onRoutePage('workExperience', {})">添加</text>
						<!-- <text class="title-link">保存</text> -->
					</view>
					<view class="card-container" v-for="v in params.work">
						<view class="work-container">
							<u-form-item label="公司名称" borderBottom>
								<u-input v-model="v.content.firmName" placeholder="请输入公司名称" border="none"></u-input>
							</u-form-item>
							<u-form-item label="所属行业" borderBottom>
								<u-input v-model="v.content.industry" placeholder="请输入所属行业" border="none"></u-input>
							</u-form-item>
							<u-form-item label="所属部门" borderBottom>
								<u-input v-model="v.content.department" placeholder="请输入所属部门" border="none"></u-input>
							</u-form-item>
							<u-form-item label="职位名称" borderBottom>
								<u-input v-model="v.content.position" placeholder="请输入职位名称" border="none"></u-input>
							</u-form-item>
							<u-form-item label="在职开始时间" borderBottom @click="onWorkStartTimeClick(v)">
								<u-input v-model="v.content.Time.startTime" placeholder="请选择在职开始时间" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="在职结束时间" borderBottom @click="onWorkEndTimeClick(v)">
								<u-input v-model="v.content.Time.endTime" placeholder="请选择在职结束时间" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="工作内容" borderBottom>
								<u-textarea v-model="v.content.jobContent" placeholder="请输入工作内容" autoHeight
									border="none"></u-textarea>
							</u-form-item>
							<u-form-item label="工作业绩">
								<u-textarea v-model="v.content.jobAchievements" placeholder="请输入工作业绩" autoHeight
									border="none"></u-textarea>
							</u-form-item>
						</view>
					</view>
					<view class="title-container">
						<text class="title">项目经历</text>
						<text class="title-link" @click="onRoutePage('projectExperience', {})">添加</text>
						<!-- <text class="title-link">保存</text> -->
					</view>
					<view class="card-container" v-for="v in params.project">
						<view class="project-container">
							<u-form-item label="项目名称" borderBottom>
								<u-input v-model="v.content.projectName" placeholder="请输入项目名称" border="none"></u-input>
							</u-form-item>
							<u-form-item label="项目角色" borderBottom>
								<u-input v-model="v.content.projectRole" placeholder="请输入项目角色" border="none"></u-input>
							</u-form-item>
							<u-form-item label="项目开始时间" borderBottom @click="onProjectStartTimeClick(v)">
								<u-input v-model="v.content.startTime" placeholder="请选择项目开始时间" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="项目结束时间" borderBottom @click="onProjectEndTimeClick(v)">
								<u-input v-model="v.content.endTime" placeholder="请选择项目结束时间" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="项目链接" borderBottom>
								<u-input v-model="v.content.projectLink" placeholder="请输入项目链接" border="none"></u-input>
							</u-form-item>
							<u-form-item label="项目描述" borderBottom>
								<u-textarea v-model="v.content.projectDescription" placeholder="请输入项目描述" autoHeight
									border="none"></u-textarea>
							</u-form-item>
							<u-form-item label="项目业绩">
								<u-textarea v-model="v.content.projectAchievements" placeholder="请输入项目业绩" autoHeight
									border="none"></u-textarea>
							</u-form-item>
						</view>
					</view>
					<view class="title-container">
						<text class="title">教育经历</text>
						<text class="title-link" @click="onRoutePage('educationalExperience', {})">添加</text>
						<!-- <text class="title-link">保存</text> -->
					</view>
					<view class="card-container" v-for="v in params.eductional">
						<view class="educat-container">
							<u-form-item label="学校名称" borderBottom>
								<u-input v-model="v.content.schoolName" placeholder="请输入学校名称" border="none"></u-input>
							</u-form-item>
							<u-form-item label="学制类型" borderBottom>
								<u-input v-model="v.content.educationType" placeholder="请输入学制类型"
									border="none"></u-input>
							</u-form-item>
							<u-form-item label="学历" borderBottom>
								<u-input v-model="v.content.degree" placeholder="请输入学历" border="none"></u-input>
							</u-form-item>
							<u-form-item label="专业" borderBottom>
								<u-input v-model="v.content.major" placeholder="请输入专业" border="none"></u-input>
							</u-form-item>
							<u-form-item label="开始时间" borderBottom @click="onEductionalStartTimeClick(v)">
								<u-input v-model="v.content.Time.startTime" placeholder="请选择开始时间" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="结束时间" borderBottom @click="onEductionalEndTimeClick(v)">
								<u-input v-model="v.content.Time.endTime" placeholder="请选择结束时间" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="专业排名" borderBottom>
								<u-input v-model="v.content.value" placeholder="请选择专业排名" readonly
									border="none"></u-input>
								<u-icon slot="right" name="arrow-right"></u-icon>
							</u-form-item>
							<u-form-item label="在校经历" borderBottom>
								<u-textarea v-model="v.content.schoolExperience" placeholder="请输入在校经历" autoHeight
									border="none"></u-textarea>
							</u-form-item>
							<u-form-item label="毕业设计/论文题目" borderBottom>
								<u-input v-model="v.content.graduationThesisTitle" placeholder="请输入专业"
									border="none"></u-input>
							</u-form-item>
							<u-form-item label="毕业设计/论文描述">
								<u-textarea v-model="v.content.graduationThesisDescription" placeholder="请输入毕业设计/论文描述"
									autoHeight border="none"></u-textarea>
							</u-form-item>
						</view>
					</view>
					<view class="title-container">
						<text class="title">自定义添加</text>
					</view>
					<view class="card-container">
						<view class="custom-container">
							<view class="custom-item">
								<view class="custom-start">
									<view class="title-area">
										<text class="title">资格证书</text>
										<view class="tag">推荐</view>
									</view>
									<view class="desc">尽可能选择有含金量的证书，证明你的专业程度</view>
								</view>
								<view class="custom-end">
									<image class="plus"
										src="https://api-test.zhaopinbei.com/storage/uploads/images/ZEkG7FK9D8uEIuk0Dpgj30UPB6b87gc5TnCiwTEA.png">
									</image>
								</view>
							</view>
							<view class="custom-item">
								<view class="custom-start">
									<view class="title-area">
										<text class="title">社团/组织经历</text>
										<view class="tag">推荐</view>
									</view>
									<view class="desc">体现活动策划、团队管理等学习之外的能力</view>
								</view>
								<view class="custom-end">
									<image class="plus"
										src="https://api-test.zhaopinbei.com/storage/uploads/images/ZEkG7FK9D8uEIuk0Dpgj30UPB6b87gc5TnCiwTEA.png">
									</image>
								</view>
							</view>
							<view class="custom-item">
								<view class="custom-start">
									<view class="title-area">
										<text class="title">志愿者经历</text>
									</view>
									<view class="desc">展示你的志愿者精神</view>
								</view>
								<view class="custom-end">
									<image class="plus"
										src="https://api-test.zhaopinbei.com/storage/uploads/images/ZEkG7FK9D8uEIuk0Dpgj30UPB6b87gc5TnCiwTEA.png">
									</image>
								</view>
							</view>
							<view class="custom-item">
								<view class="custom-start">
									<view class="title-area">
										<text class="title">专业技能</text>
									</view>
									<view class="desc">突出专业技能、特长展示</view>
								</view>
								<view class="custom-end">
									<image class="plus"
										src="https://api-test.zhaopinbei.com/storage/uploads/images/ZEkG7FK9D8uEIuk0Dpgj30UPB6b87gc5TnCiwTEA.png">
									</image>
								</view>
							</view>
						</view>
					</view>
				</view>
			</u-form>


			<u-picker ref="jobSalaryPicker" title="选择薪资范围" :show="job.salaryShowPicker" closeOnClickOverlay
				:columns="job.salaryColumns" @confirm="onJobSalaryPickerConfirm" @close="onJobSalaryPickerClose"
				@cancel="onJobSalaryPickerClose" @change="onJobSalaryPickerChange" immediateChange></u-picker>

			<u-datetime-picker ref="workStartTimePicker" v-model="work.startTimeTextModel" closeOnClickOverlay
				:show="work.startTimeShowPicker" mode="year-month" @confirm="onWorkStartTimePickerConfirm"
				@cancel="onWorkStartTimePickerClose" @close="onWorkStartTimePickerClose"></u-datetime-picker>

			<u-datetime-picker ref="workEndTimePicker" v-model="work.endTimeTextModel" closeOnClickOverlay
				:show="work.endTimeShowPicker" mode="year-month" @confirm="onWorkEndTimePickerConfirm"
				@cancel="onWorkEndTimePickerClose" @close="onWorkEndTimePickerClose"></u-datetime-picker>

			<u-datetime-picker ref="projectStartTimePicker" v-model="project.startTimeTextModel" closeOnClickOverlay
				:show="project.startTimeShowPicker" mode="year-month" @confirm="onProjectStartTimePickerConfirm"
				@cancel="onProjectStartTimePickerClose" @close="onProjectStartTimePickerClose"></u-datetime-picker>

			<u-datetime-picker ref="projectEndTimePicker" v-model="project.endTimeTextModel" closeOnClickOverlay
				:show="project.endTimeShowPicker" mode="year-month" @confirm="onProjectEndTimePickerConfirm"
				@cancel="onProjectEndTimePickerClose" @close="onProjectEndTimePickerClose"></u-datetime-picker>

			<u-datetime-picker ref="eductionalStartTimePicker" v-model="eductional.startTimeTextModel"
				closeOnClickOverlay :show="eductional.startTimeShowPicker" mode="year-month"
				@confirm="onEductionalStartTimePickerConfirm" @cancel="onEductionalStartTimePickerClose"
				@close="onEductionalStartTimePickerClose"></u-datetime-picker>

			<u-datetime-picker ref="eductionalEndTimePicker" v-model="eductional.endTimeTextModel" closeOnClickOverlay
				:show="eductional.endTimeShowPicker" mode="year-month" @confirm="onEductionalEndTimePickerConfirm"
				@cancel="onEductionalEndTimePickerClose" @close="onEductionalEndTimePickerClose"></u-datetime-picker>
		</scroll-view>

		<view class="btn-container">
			<view class="btn">确定</view>
		</view>
	</view>
</template>

<script>
	import {
		deleteStrength,
		getStrength
	} from '@/config';

	export default {
		data() {
			return {
				job: {
					salaryText: null,
					salaryShowPicker: false,
					salaryColumns: [
						['面议', '1k', '2k', '3k', '4k', '5k', '6k', '7k', '8k', '9k', '10k', '11k', '12k', '13k', '14k',
							'15k', '16k', '17k', '18k', '19k'
						],
						['面议'],
					],
				},

				work: {
					info: {},

					startTimeTextModel: Number(new Date()),
					startTimeShowPicker: false,

					endTimeTextModel: Number(new Date()),
					endTimeShowPicker: false,
				},

				project: {
					info: {},

					startTimeTextModel: Number(new Date()),
					startTimeShowPicker: false,

					endTimeTextModel: Number(new Date()),
					endTimeShowPicker: false,
				},
				eductional: {
					info: {},

					startTimeTextModel: Number(new Date()),
					startTimeShowPicker: false,

					endTimeTextModel: Number(new Date()),
					endTimeShowPicker: false,
				},

				params: {
					strength: {
						content: null
					},
					job: {
						content: {
							industry: null,
							posts: null,
							salary: {
								salaryBegin: null,
								salaryEnd: null
							},
							city: null,
							interestedCity: null,
							detail: null,
						}
					},
					work: [{
						content: {
							firmName: null,
							industry: null,
							department: null,
							position: null,
							Time: {
								startTime: null,
								endTime: null,
							},
							jobContent: null,
							jobAchievements: null,
							state: null,
						}
					}],
					project: [{
						content: {
							projectRole: null,
							projectName: null,
							startTime: null,
							endTime: null,
							projectLink: null,
							projectDescription: null,
							projectAchievements: null,
						}
					}],
					eductional: [{
						content: {
							schoolName: null,
							educationType: null,
							degree: null,
							major: null,
							Time: {
								startTime: null,
								endTime: null,
							},
							value: null,
							schoolExperience: null,
							graduationThesisTitle: null,
							graduationThesisDescription: null,
						}
					}]
				}
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onReady() {
			this.$refs.workStartTimePicker.setFormatter(this.formatter);
			this.$refs.workEndTimePicker.setFormatter(this.formatter);
			this.$refs.projectStartTimePicker.setFormatter(this.formatter);
			this.$refs.projectEndTimePicker.setFormatter(this.formatter);
		},
		methods: {
			formatTimestampToYearMonth(timestamp) {
				const date = new Date(timestamp);
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				return `${year}-${month}`;
			},
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			onRoutePage(url, params) {
				uni.$u.route({
					url: `/pagesB/Jobsetting/newPages/${url}`,
					params
				})
			},
			onBackStrengthParams(params) {
				const res = JSON.parse(params);
				this.params.strength = res;
			},
			onBackJobParams(params) {
				const res = JSON.parse(params);
				this.params.job = res;
				this.job.salaryText = `${res.content.salary.salaryBegin} - ${res.content.salary.salaryEnd}`
			},
			onJobSalaryPickerChange(event) {
				if (event.columnIndex === 0) {
					if (event.index === 0) {
						this.$refs.salaryPicker.setColumnValues(1, ['面议']);
						return;
					}
					if (event.index === 19) {
						this.$refs.salaryPicker.setColumnValues(1, ['20k']);
						return;
					}
					const columns = this.job.salaryColumns[0].filter((_, i) => i > event.index && i < event.index + 6);
					this.$refs.jobSalaryPicker.setColumnValues(1, columns);
				}
			},
			onJobSalaryPickerConfirm(event) {
				this.params.job.content.salary.salaryBegin = event.value[0];
				this.params.job.content.salary.salaryEnd = event.value[1];
				const index = event.indexs.reduce((prev, el) => (prev + el), 0);
				this.job.salaryText = index ? event.value.join(' - ') : event.value[0];
				this.onJobSalaryPickerClose();
			},
			onJobSalaryPickerClose() {
				this.job.salaryShowPicker = false;
			},
			onIndustryPage() {
				uni.$u.route({
					url: '/pagesB/Jobsetting/newPages/expectedIndustry'
				})
			},
			onWorkCityPage() {
				uni.$u.route({
					url: '/pagesB/Jobsetting/newPages/workCity'
				})
			},
			onBackWorkParams(params) {
				const res = JSON.parse(params);
				this.params.work = res;
			},
			onWorkStartTimeClick(v) {
				this.work.info = v;
				this.work.startTimeShowPicker = true;
				if (v.content.Time.startTime) {
					this.work.startTimeTextModel = Number(new Date(v.content.Time.startTime));
				}
			},
			onWorkEndTimeClick(v) {
				this.work.info = v;
				this.work.endTimeShowPicker = true;
				if (v.content.Time.endTime) {
					this.work.endTimeTextModel = Number(new Date(v.content.Time.endTime));
				}
			},
			onWorkStartTimePickerConfirm(event) {
				this.work.info.content.Time.startTime = this.formatTimestampToYearMonth(event.value);
				this.onWorkStartTimePickerClose();
			},
			onWorkStartTimePickerClose() {
				this.work.startTimeShowPicker = false;
			},
			onWorkEndTimePickerConfirm(event) {
				this.work.info.content.Time.endTime = this.formatTimestampToYearMonth(event.value);
				this.onWorkEndTimePickerClose();
			},
			onWorkEndTimePickerClose() {
				this.work.endTimeShowPicker = false;
			},
			onBackProjectParams(params) {
				const res = JSON.parse(params);
				this.params.project = res;
			},
			onProjectStartTimeClick(v) {
				this.project.info = v;
				this.project.startTimeShowPicker = true;
				if (v.content.startTime) {
					this.project.startTimeTextModel = Number(new Date(v.content.startTime));
				}
			},
			onProjectEndTimeClick(v) {
				this.project.info = v;
				this.project.endTimeShowPicker = true;
				if (v.content.endTime) {
					this.project.endTimeTextModel = Number(new Date(v.content.endTime));
				}
			},
			onProjectStartTimePickerConfirm(event) {
				this.project.info.content.startTime = this.formatTimestampToYearMonth(event.value);
				this.onProjectStartTimePickerClose();
			},
			onProjectStartTimePickerClose() {
				this.project.startTimeShowPicker = false;
			},
			onProjectEndTimePickerConfirm(event) {
				this.project.info.content.endTime = this.formatTimestampToYearMonth(event.value);
				this.onProjectEndTimePickerClose();
			},
			onProjectEndTimePickerClose() {
				this.project.endTimeShowPicker = false;
			},
			onBackEductionalParams(params) {
				const res = JSON.parse(params);
				this.params.eductional = res;
			},
			onEductionalStartTimeClick(v) {
				this.eductional.info = v;
				this.eductional.startTimeShowPicker = true;
				if (v.content.Time.startTime) {
					this.eductional.startTimeTextModel = Number(new Date(v.content.Time.startTime));
				}
			},
			onEductionalEndTimeClick(v) {
				this.eductional.info = v;
				this.eductional.endTimeShowPicker = true;
				if (v.content.Time.endTime) {
					this.eductional.endTimeTextModel = Number(new Date(v.content.Time.endTime));
				}
			},
			onEductionalStartTimePickerConfirm(event) {
				this.eductional.info.content.Time.startTime = this.formatTimestampToYearMonth(event.value);
				this.onEductionalStartTimePickerClose();
			},
			onEductionalStartTimePickerClose() {
				this.eductional.startTimeShowPicker = false;
			},
			onEductionalEndTimePickerConfirm(event) {
				this.eductional.info.content.Time.endTime = this.formatTimestampToYearMonth(event.value);
				this.onEductionalEndTimePickerClose();
			},
			onEductionalEndTimePickerClose() {
				this.eductional.endTimeShowPicker = false;
			},
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;

		.btn-container {
			background-color: #FFFFFF;
			padding-block-start: 24rpx;
			padding-inline: 64rpx;
			padding-block-end: calc(24rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			border-start-start-radius: 16rpx;
			border-start-end-radius: 16rpx;
			box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);

			.btn {
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				border-radius: 16rpx;
				text-align: center;
				color: #FFFFFF;
				padding-block: 20rpx;
				font-size: 28rpx;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				display: flex;
				flex-direction: column;
				gap: 24rpx;
				padding: 32rpx;

				.card-container {
					padding-block: 24rpx;
					padding-inline: 32rpx;
					border-radius: 24rpx;
					background-color: #FFFFFF;

					.custom-container {
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.custom-item {
							background-color: #F8F8F8;
							border-radius: 16rpx;
							padding-block: 24rpx;
							padding-inline: 32rpx;
							display: flex;
							align-items: center;
							justify-content: space-between;
							gap: 32rpx;

							.custom-start {
								display: flex;
								flex-direction: column;
								gap: 18rpx;

								.title-area {
									display: flex;
									align-items: center;
									gap: 12rpx;

									.title {
										color: #333333;
										font-size: 28rpx;
									}

									.tag {
										background-color: #E0EBFF;
										border-radius: 8rpx;
										color: #4F8CF0;
										font-size: 16rpx;
										padding-block: 4rpx;
										padding-inline: 6rpx;
									}
								}

								.desc {
									color: #666666;
									font-size: 24rpx;
									overflow: hidden;
									display: -webkit-box;
									-webkit-box-orient: vertical;
									-webkit-line-clamp: 1;
								}
							}

							.custom-end {
								.plus {
									width: 36rpx;
									height: 36rpx;
								}
							}
						}
					}

					.personal-container {
						display: flex;
						flex-direction: column;
						gap: 16rpx;

						.personal-end {
							color: #999999;
							font-size: 20rpx;
						}

						.personal-start {
							display: flex;
							align-items: center;
							gap: 24rpx;

							.user {
								display: flex;
								flex-direction: column;
								gap: 16rpx;

								.name {
									color: #333333;
									font-size: 28rpx;
								}

								.detail {
									color: #666666;
									font-size: 24rpx;
								}
							}

							.avatar {
								width: 90rpx;
								height: 90rpx;
								object-fit: contain;
								border-radius: 999rpx;
							}
						}
					}

					::v-deep .u-textarea {
						padding: 0 !important;
					}
				}

				.title-container {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					gap: 24rpx;

					.title {
						color: #333333;
						font-size: 32rpx;
						margin-inline-end: auto;
					}

					.title-link {
						color: #4F8CF0;
						font-size: 28rpx;
					}
				}
			}
		}
	}
</style>
