<template>
	<view class="register-one">
		<view class="wrap" v-for="(item,index) in formList" :key="index">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						学校名称<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入学校名称" fontSize='14px' clearable placeholderClass="placeholderClass" border="none"
							v-model="item.name" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item" >
					<view class="title">
						开始时间<text class="star">*</text>
					</view>
					<view class="in se">
						<picker mode="date" :value="item.start"  @change="bindStartDateChange(item,$event)">
							<view class="d-picker">{{item.start?item.start:"请选择开始时间"}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item" >
					<view class="title">
						结束时间<text class="star">*</text>
					</view>
					<view class="in se">
						<picker mode="date" :value="item.end"  @change="bindEndDateChange(item,$event)">
							<view class="d-picker">{{item.end?item.end:"请选择结束时间"}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</view>
		</view>
		
		<view class="btn" @click="add">
			新增一条教育经历
		</view>

		<view class="footer">
			<view class="next gray">
				注册
			</view>
		</view>

		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showStart: false,
				showEnd: false,
				tips: '',
				value: '',
				placeholderStyle: {
					'color': '#4F8CF0',
					'fontSize': '32rpx'
				},
				formList: [{
						id:1,
						name: '武汉大学',
						start: '2024-11-04',
						end: '2024-11-04'
					},
				]
			}
		},
		onLoad() {
			this.start = uni.$u.timeFormat(this.start, 'yyyy-mm-dd');
			this.end = uni.$u.timeFormat(this.end, 'yyyy-mm-dd');
		},
		methods: {
			bindStartDateChange(item,e){
				let id = item.id
				let index = this.formList.findIndex(v=>v.id==id)
				this.formList[index]['start'] = e.detail.value
			},
			bindEndDateChange(item,e){
				let id = item.id
				let index = this.formList.findIndex(v=>v.id==id)
				this.formList[index]['end'] = e.detail.value
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			change(e) {
				console.log('change', e);
			},
			add(){
				this.formList.push({
					id:this.formList.length+1,
					name: '',
					start: '',
					end: ''
				})
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
	}
</style>
<style lang="less" scoped>
	.register-one {
		display: flex;
		flex-direction: column;
		padding: 32rpx 32rpx 178rpx 32rpx;
	}

	.wrap {
		// padding: 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;
				margin-bottom: 32rpx;
				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;
					.star{
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}
					
					::v-deep picker{
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;
						.d-picker{
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}
	.btn{
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 24rpx;
		height: 64rpx;
		width: 260rpx;
		// margin-left: 32rpx;
		background: #4F8CF0;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
		border-radius: 8rpx;
	}
	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}

		.gray {
			background-color: #cccccc;
			color: #FFFFFF;
		}
	}
</style>