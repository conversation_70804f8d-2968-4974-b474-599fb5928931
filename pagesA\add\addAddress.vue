<template>
    <view class="">
        <view class="wrap" v-if="roleType=='headhunters'">
            <view class="inp">
                <view class="inp-item">
                    <view class="title">
                        关联公司
                        <text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker @change="changeComp" :value="compIndex" :range="compList" range-key="name">
                            <view class="d-picker" :style="{color:compIndex==0?'#c0c4cc':'#303133'}">
                                {{ compList[compIndex]['name'] }}
                            </view>
                        </picker>
                        <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>
            </view>
        </view>
        <view class="wrap">
            <view class="inp">
                <view class="inp-item">
                    <view class="title">
                        工作城市
                        <text class="star">*</text>
                    </view>
                    <view class="in se">
                        <uni-data-picker :map="map" placeholder="请选择工作地址" popup-title="请选择所在地区" :localdata="cityList"
                                         v-model="form.district_id" @change="onchange">
                        </uni-data-picker>
                    </view>
                </view>

                <view class="inp-item" @click="choosePosition">
                    <view class="title">
                        工作地址
                        <text class="star">*</text>
                    </view>
                    <view class="in se">
                        {{form.map_address?form.map_address:'请选择地址'}}
                        <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        楼层/单元室/门牌号
                        <text class="star">*</text>
                    </view>
                    <view class="in">
                        <u--input placeholder="请输入楼层/单元室/门牌号" border="none" fontSize="32rpx"
                                  v-model="form.address"></u--input>
                    </view>
                </view>
            </view>
        </view>

        <!-- <view class="wrap" v-if="roleType == 'member'">
            <view class="inp">
                <view class="default">
                    <view class="name">
                        设为默认地址
                    </view>
                    <u-switch v-model="form.is_default" :activeValue='1' :inactiveValue='2' size="18"></u-switch>
                </view>
            </view>
        </view> -->

        <view class="footer">
            <view class="next sure" @click="add">
                新建地址
            </view>
        </view>
    </view>
</template>

<script>
    import {
        saveAddress,
        getAddressDetails,
        getHeadhuntersAuthEnterpriseList
    } from "../../config/api.js"

    export default {
        data() {
            return {
                id: '',
                map: {
                    text: 'label',
                    value: 'value'
                },
                compList: [{
                    value: 0,
                    name: '请选择'
                }],
                sexIndex: 0,
                expect_address: '',
                compIndex: 0,
                form: {
                    company_id: "",
                    province_id: "",
                    city_id: "",
                    district_id: "",
                    address: "",
                    map_address: "",
                    lat: "",
                    lng: "",
                    remark: "",
                    is_default: 2
                }
            }
        },
        computed: {
            roleType() {
                return this.$store.state.roleType || uni.getStorageSync("roleType")
            },
            sysData() {
                return this.$store.state.sysData || uni.getStorageSync('sysData')
            },
            cityList() {
                return this.$store.state.cityList || uni.getStorageSync('cityList')
            }
        },

        onLoad(options) {
            let id = options.id
            this.id = id

            if (id > 0) {
                this.getAddressDetails()
            }

        },

        methods: {
            async getHeadhuntersAuthEnterpriseList() {
                let params = {}
                const result = await getHeadhuntersAuthEnterpriseList(params)
                if (result.status_code == 200) {

                    result.data.forEach((item, index) => {

                        this.compList.push({
                            value: item.authorize_company.id,
                            name: item.authorize_company.name,
                        })
                    });
                    this.compIndex = this.compList.findIndex(comp => comp.value == this.company_id);
                }
            },
            changeComp(e) {
                this.compIndex = e.detail.value
                this.form.company_id = this.compList[e.detail.value]['value']

            },
            onchange(e) {
                let data = e.detail.value;
                this.form.province_id = data[0]['value'];
                this.form.city_id = data[1]['value'];
                this.form.district_id = data[2]['value'];
                this.expect_address = data.map(item => item.text).join('');
            },

            //选择地图地址
            choosePosition() {
                let self = this;
                uni.chooseLocation({
                    success: function (res) {
                        self.form.lat = res.latitude
                        self.form.lng = res.longitude
                        self.form.map_address = res.address
                    }
                });
            },

            //添加编辑地址
            async add() {
                let params = null;
                if (this.id) {
                    params = {
                        id: this.id,
                        ...this.form
                    }
                } else {
                    params = {
                        ...this.form
                    }
                }
                saveAddress(params).then(response => {
                    uni.$u.toast(response.message);

                    if (response.status_code == '200') {
                        uni.setStorageSync(this.id > 0 ? 'editAddress' : 'storeAddress', response.data);
                        uni.navigateBack();
                    }
                });

            },

            //获取地址详情
            async getAddressDetails() {
                let params = {
                    id: this.id
                };
                const {
                    status_code,
                    data
                } = await getAddressDetails(params)
                if (this.roleType == "headhunters") {
                    this.company_id = data.company_id
                    this.getHeadhuntersAuthEnterpriseList()
                }

                this.expect_address = data.province_name + '/' + data.city_name + '/' + data.district_name

                const {
                    province_id,
                    city_id,
                    district_id,
                    address,
                    map_address,
                    lat,
                    lng,
                    remark,
                    is_default,

                } = data

                this.form = {
                    province_id,
                    city_id,
                    district_id,
                    address,
                    map_address,
                    lat,
                    lng,
                    remark,
                    is_default
                }
                console.log("回显的值：", this.form)
            }
        }
    }
</script>
<style lang="scss" src="../../static/css/pagesA/add/addAddress.scss"></style>