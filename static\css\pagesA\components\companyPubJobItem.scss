view {
    box-sizing: border-box;
}
.item {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 0 32rpx;
    margin-bottom: 32rpx;
    background-color: #fff;
    border-radius: 24rpx;
    height: 100%;
    .item-up {
        display: flex;
        flex-direction: column;
        padding: 30rpx 0;
        
        .title {
            font-weight: 600;
            font-size: 32rpx;
            color: #333333;
            
        }
        
        .item-up-one {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 30rpx 0;
            
            .name {
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
            }
            
            .money {
                font-weight: 600;
                font-size: 32rpx;
                color: #F98A14;
            }
        }
        
        .item-up-two {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
        }
    }
    
    .item-down {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        padding: 24rpx 0;
        
        .btns {
            display: flex;
            
            .btn {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-left: 24rpx;
                border-radius: 8rpx;
                font-weight: 600;
                padding: 0 24rpx;
                height: 56rpx;
                font-size: 24rpx;
                color: #FFFFFF;
                
                image {
                    width: 40rpx;
                    height: 40rpx;
                }
                
                &:first-child {
                    margin-left: 0;
                }
            }
            
            .agree {
                background-color: #4F8CF0;
                color: #FFFFFF;
            }
            
            .zd {
                background-color: #F5F5F7;
                color: #4F8CF0;
            }
            
            .refuse {
                background-color: #ccc;
                color: #FFFFFF;
            }
        }
        
        .reason {
            font-weight: 600;
            font-size: 24rpx;
            color: #FE4D4F;
        }
    }
    
    .flexRow {
        display: flex;
        flex-direction: row-reverse;
    }
    
    .status {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 0;
        top: 0;
        font-size: 28rpx;
        width: 132rpx;
        height: 62rpx;
        border-radius: 0 24rpx 0 24rpx;
    }
    
    .ing {
        background: #57D51C;
        color: #FFFFFF;
    }
    
    .wait {
        background: #4F8CF0;
        color: #FFFFFF;
    }
    
    .cg {
        background: #F9AD14;
        color: #FFFFFF;
    }
    
    .yxj {
        background: #cccccc;
        color: #FFFFFF;
    }
}