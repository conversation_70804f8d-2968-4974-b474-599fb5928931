.home-index {
    padding: 0 32rpx 160rpx 32rpx;
}

.userInfo {
    display: flex;
    padding: 32rpx 0;
    border-bottom: 1px solid #F5F5F7;
    
    .info {
        display: flex;
        flex-direction: column;
        flex: 1;
        
        .userName {
            display: flex;
            align-items: center;
            
            .name {
                font-weight: 600;
                font-size: 40rpx;
                color: #333333;
            }
            
            .dot {
                width: 12rpx;
                height: 12rpx;
                border-radius: 50%;
                background-color: #57D51C;
                margin-left: 16rpx;
            }
            
            .status {
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                margin-left: 16rpx;
            }
            
            image {
                width: 32rpx;
                height: 32rpx;
                margin-left: 16rpx;
            }
        }
        
        .tags {
            font-weight: 400;
            font-size: 24rpx;
            color: #999999;
            margin: 16rpx 0;
        }
        
        .items {
            display: flex;
            align-items: center;
            
            .item {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                
                // &:last-child {
                //     margin-left: 32rpx;
                // }
                
                text {
                    margin-left: 16rpx;
                }
            }
        }
    }
    
    & > image {
        width: 108rpx;
        height: 108rpx;
        
    }
}

.wrap {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #F5F5F7;
    padding: 32rpx 0;
    
    &:last-child {
        border-bottom: none;
    }
    
    .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .star {
            font-weight: 600;
            font-size: 22rpx;
            color: #FE4D4F;
            margin-left: 16rpx;
        }
        
        .name {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 32rpx;
            color: #333333;
            
            image {
                width: 32rpx;
                height: 32rpx;
                margin-left: 16rpx;
            }
        }
        
        .plus {
            image {
                width: 36rpx;
                height: 36rpx;
            }
        }
        
    }
    
    .content {
        display: flex;
        flex-direction: column;
        
        // align-items: center;
        .cont {
            display: flex;
            
            .ui {
                display: flex;
                flex-direction: column;
                flex: 1;
                
                .expert {
                    display: flex;
                    align-items: center;
                    margin-top: 40rpx;
                    
                    .type {
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #333333;
                    }
                    
                    .money {
                        font-weight: 600;
                        font-size: 28rpx;
                        color: #4F8CF0;
                        margin-left: 16rpx;
                    }
                }
                
                .pos {
                    display: flex;
                    align-items: center;
                    margin-top: 16rpx;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #666666;
                    
                    .addr {
                    }
                    
                    .type {
                        margin-left: 32rpx;
                    }
                }
            }
            
        }
        
        .sub-wrap {
            display: flex;
            flex-direction: column;
            margin-bottom: 32rpx;
            
            &:last-child {
                margin-bottom: 0;
            }
            
            .exper {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 40rpx;
                
                .name {
                    display: flex;
                    flex: 1;
                    font-weight: 500;
                    font-size: 32rpx;
                    color: #333333;
                }
                
                .time {
                    display: flex;
                    align-items: center;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #666666;
                    
                    text {
                        margin-right: 20rpx;
                    }
                }
            }
            
            .user-info {
                display: flex;
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
                margin-top: 16rpx;
                
                .mobile {
                    margin-left: 16rpx;
                }
            }
            
            .types {
                display: flex;
                margin-top: 16rpx;
                
                .type {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                    margin-right: 24rpx;
                }
            }
            
            .list {
                display: flex;
                flex-direction: column;
                margin-top: 16rpx;
                
                .item {
                    display: flex;
                    margin-bottom: 16rpx;
                    
                    &:last-child {
                        margin-bottom: 0;
                    }
                    
                    .dot {
                        width: 12rpx;
                        height: 12rpx;
                        border-radius: 50%;
                        background-color: #666666;
                        margin: 10rpx 24rpx;
                    }
                    
                    .js {
                        display: flex;
                        flex: 1;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #666666;
                    }
                }
            }
        }
        
        .jy-list {
            display: flex;
            flex-direction: column;
            margin-top: 40rpx;
            
            .jy-item {
                display: flex;
                flex-direction: column;
                margin-bottom: 32rpx;
                
                &:last-child {
                    margin-bottom: 0;
                }
                
                .name {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-weight: 500;
                    font-size: 32rpx;
                    color: #333333;
                }
                
                .time {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    margin-top: 16rpx;
                }
            }
        }
        
        .pic-list {
            display: flex;
            flex-wrap: wrap;
            
            .pic-item {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 200rpx;
                width: calc(33.3% - 32rpx);
                margin-top: 32rpx;
                margin-right: 32rpx;
                position: relative;
                border-radius: 24rpx;
                
                & > image {
                    width: 100%;
                    height: 100%;
                    border-radius: 24rpx;
                }
                
                .zz {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: rgba(0, 0, 0, 0.5);
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    left: 0;
                    top: 0;
                    z-index: 10;
                    border-radius: 24rpx;
                    
                    .del {
                        display: flex;
                        align-items: center;
                        border-radius: 16rpx;
                        padding: 0 16rpx;
                        height: 56rpx;
                        background: rgba(255, 255, 255, 0.5);
                        font-weight: 500;
                        font-size: 28rpx;
                        color: #FFFFFF;
                    }
                }
            }
            
            .add {
                background-color: #F5F5F7;
                
                image {
                }
            }
            
        }
    }
    
    .intro {
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        margin-top: 40rpx;
    }
}

.footer {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 140rpx;
    width: 100%;
    left: 0;
    bottom: 0;
    background: #FFFFFF;
    font-weight: 600;
    font-size: 28rpx;
    border-radius: 24rpx 24rpx 0 0;
    z-index: 999;
    
    .btns {
        display: flex;
        width: 90%;
        
        .btn {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
            height: 80rpx;
            color: #333333;
            border-radius: 16rpx;
        }
        
        .cancel {
            background: rgba(254, 77, 79, 0.1);
            color: #FE4D4F;
        }
        
        .zx {
            color: #FFFFFF;
            background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        }
    }
}