<template>
    <view class="">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
               <!-- <view class="tabs">
                    <u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" :current="tabIndex" @click="changeTab"></u-tabs>
                </view> -->
                <view class="search-wrap">
                    <u-search placeholder="请输入职位名称或千里马姓名" bgColor="#FFFFFF" :showAction="false"
                              v-model="keyword"></u-search>
                </view>

                <view class="filters">
                    <view class="filter">
                        期望薪资
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>

                </view>
            </view>
        </u-sticky>
        <view class="list">
            <company-signup-item v-for="(item,index) in list" :key="index" :item="item" :tabIndex="tabIndex"
                                 @cancelSignup="cancelSignup" @inviteInterview="inviteInterview"></company-signup-item>
        </view>
        <u-popup :show="showReason" :round="10" bgColor="#F5F5F5" mode="center" closeOnClickOverlay @close="closeReason"
                 @open="openReason">
            <view class="credit" style="width: 70vw;">
                <view class="title comp">
                    拒绝原因
                </view>
                <view class="content">
                    <u--textarea v-model="reason" placeholder="请输入内容"></u--textarea>
                </view>

                <view class="agree" @click="sureCancel">
                    确定
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
    import {
        getCompanySignupList,
        cancelCompanySignup,
    } from "../../config/api.js"

    import {headhunterJobActiveReportList} from "../../config/headhunterList_api";

    import CompanySignupItem from '../components/companySignupItem.vue'

    export default {
        components: {
            CompanySignupItem
        },

        data() {
            return {
                reason: "",
                showReason: false,
                currentItem: {},
                page: 1,
                limit: 10,
                status: 'loadmore',
                more: false,
                tabIndex: 1,
                tabs: [{
                    name: '普通报名',
                }, {
                    name: '活动报名',
                }, {
                    name: '代招报名'
                }],
                list: []
            }
        },
        onLoad() {
            this.getList()
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getList()
            } else {
                this.status = 'nomore'
            }
        },
        methods: {
            inviteInterview(item) {
                this.currentItem = item
                console.log(this.currentItem)
                uni.navigateTo({
                    url: "/pagesA/add/inviteInterview?report_id=" + item.id + "&job_id=" + item.job_id + "&member_id=" + item.member_id
                })
            },
            openReason() {
                this.showReason = true
            },

            closeReason() {
                this.reason = ''
                this.showReason = false
            },
            cancelSignup(item) {
                this.showReason = true
                this.currentItem = item
            },
            changeTab(op) {
                this.page = 1;
                this.list = [];
                this.tabIndex = op.index
                this.getList();
            },

            getList() {
                // this.page = 1;
                // this.list = [];
                // 需要增加参数进行切换work_type_id?所对应的123？

                console.log('TAB',this.tabIndex);
                switch (this.tabIndex) {
                    case 0:
                        this.getCompanySignupList();
                        break;
                    case 1:
                        this.headhunterJobActiveReportList();
                        break;
                }
            },
            async sureCancel() {
                let params = {
                    report_id: this.currentItem.id,
                    cancel_reason: this.reason
                }
                const {
                    status_code,
                    data,
                    message
                } = await cancelCompanySignup(params)
                this.closeReason()
                uni.$u.toast(message)
                if (status_code == 200) {
                    this.getCompanySignupList()
                }
            },
            async getCompanySignupList() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    report_status: ''
                }
                const {
                    status_code,
                    data,
                    message
                } = await getCompanySignupList(params)
                if (status_code == 200) {
                    this.list = this.list.concat(data.data);
                    // 返回false代表没有下一页
                    this.more = data.more;
                    this.status = this.more ? "loadmore" : "nomore"
                    console.log("记录：", this.list)
                }
            },
            async headhunterJobActiveReportList() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    report_status: ''
                }
                const {
                    status_code,
                    data,
                    message
                } = await headhunterJobActiveReportList(params)
                if (status_code == 200) {
                    this.list = this.list.concat(data.data);
                    // 返回false代表没有下一页
                    this.more = data.more;
                    this.status = this.more ? "loadmore" : "nomore"
                    console.log("记录：", this.list)
                }
            }
        }
    }
</script>
<style>
    page {
        background: #f5f5f7;
    }
</style>
<style lang="less" scoped>
    .header {
        .tabs {
            background: #FFFFFF;
            border-radius: 0 0 24rpx 24rpx;
        }

        .search-wrap {
            padding: 0 32rpx;
            margin: 22rpx 0;
        }

        .filters {
            display: flex;
            align-items: center;
            padding: 0 32rpx 32rpx 32rpx;

            .filter {
                display: flex;
                align-items: center;
                height: 48rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 0 12rpx;
                margin-right: 12rpx;
                border-radius: 8rpx;
                background-color: #FFFFFF;

                image {
                    width: 24rpx;
                    height: 24rpx;
                }
            }

        }
    }

    .list {
        padding: 0 32rpx;
    }

    .credit {
        display: flex;
        flex-direction: column;
        padding: 40rpx 48rpx;

        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .tip {
                image {
                    width: 48rpx;
                    height: 48rpx;
                }
            }
        }

        .comp {
            justify-content: center;
        }

        .content {
            display: flex;
            flex-direction: column;
            margin-top: 40rpx;

            .sub-title {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                color: #000000;
            }
        }

        .agree {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 44rpx;
            height: 88rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #FFFFFF;
            background: #4F8CF0;
            margin-top: 32rpx;
        }
    }

</style>