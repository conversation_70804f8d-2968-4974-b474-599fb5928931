<template>
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="user-container" @click="onRoutePage('personalInformation')">
					<view class="user-start">
						<view class="title-box">
							<text class="title">娃哈哈</text>
							<view class="sub-title__box">
								<view class="dot"></view>
								<text>在职</text>
							</view>
						</view>
						<view class="detail">男·24岁·汉族·本科</view>
						<view class="contact-box">
							<view class="phone-box">
								<image class="image"
									src="https://api-test.zhaopinbei.com/storage/uploads/images/PImkkGjvin8hI2fSQlZ6r26T0qvtoyvjukN0ZIde.png">
								</image>
								<text>17762277667</text>
							</view>
							<view class="wx-box">
								<image class="image"
									src="https://api-test.zhaopinbei.com/storage/uploads/images/MLwqm8ELTwOL0bZEnfeXrVsNJY10weMp6IHMHKj1.png">
								</image>
								<text>Lorem, ipsum.</text>
							</view>
						</view>
					</view>
					<view class="user-end">
						<image class="avatar" src="https://picsum.photos/800/600"></image>
					</view>
				</view>
				<u-line></u-line>
				<view class="strength-container">
					<view class="title-container" @click="onRoutePage('personalStrength')">
						<text>个人优势</text>
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/dMDIpORYc96JLP5Ks4YZknmvAmjSCmwaDxwziTkS.png">
						</image>
					</view>
					<view class="content">
						Lorem ipsum dolor sit amet consectetur adipisicing elit. Quaerat labore totam molestiae
						voluptates itaque esse facilis vitae? Debitis, corporis aut.
					</view>
				</view>
				<u-line></u-line>
				<view class="job-container">
					<view class="title-container" @click="onRoutePage('jobExpectation')">
						<text>期望职位</text>
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/dMDIpORYc96JLP5Ks4YZknmvAmjSCmwaDxwziTkS.png">
						</image>
					</view>
					<view class="content">
						<view class="content-start">
							<text class="title">UI设计师</text>
							<text class="sub-title">9-12K</text>
						</view>
						<view class="content-end">
							<text>河南省郑州市金水区</text>
							<text>全职</text>
						</view>
					</view>
				</view>
				<u-line></u-line>
				<view class="work-container">
					<view class="title-container" @click="onRoutePage('workExperience')">
						<text>实习/工作经历</text>
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/dMDIpORYc96JLP5Ks4YZknmvAmjSCmwaDxwziTkS.png">
						</image>
					</view>
					<view class="content">
						<view class="item">
							<view class="item-start">
								<view class="title">科技能力技术公司</view>
								<text class="sub-title">2024-6 - 2025-3</text>
							</view>
							<view class="item-center">UI设计师</view>
							<view class="item-end">
								<view class="substance">
									<view class="dot"></view>
									<view class="text">
										<text style="margin-inline-end: 12rpx;">内容: </text>
										<text>
											Lorem ipsum dolor sit amet consectetur adipisicing elit. Animi quisquam
											quasi molestiae quos eligendi fugiat tempora deserunt quo praesentium quam,
											mollitia facilis obcaecati assumenda dolorum ipsum deleniti nisi ea? A natus
											ipsam non vel? Blanditiis inventore velit at debitis cum.
										</text>
									</view>
								</view>
								<view class="substance">
									<view class="dot"></view>
									<view class="text">
										<text style="margin-inline-end: 12rpx;">业绩: </text>
										<text>
											Lorem ipsum dolor sit amet consectetur adipisicing elit. Animi quisquam
											quasi molestiae quos eligendi fugiat tempora deserunt quo praesentium quam,
											mollitia facilis obcaecati assumenda dolorum ipsum deleniti nisi ea? A natus
											ipsam non vel? Blanditiis inventore velit at debitis cum.
										</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<u-line></u-line>
				<view class="project-container">
					<view class="title-container" @click="onRoutePage('projectExperience')">
						<text>项目经历</text>
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/dMDIpORYc96JLP5Ks4YZknmvAmjSCmwaDxwziTkS.png">
						</image>
					</view>
					<view class="content">
						<view class="item">
							<view class="item-start">
								<view class="title">招聘呗</view>
								<text class="sub-title">2024-6 - 2025-3</text>
							</view>
							<view class="item-center">UI设计师</view>
							<view class="item-end">
								<view class="substance">
									<view class="dot"></view>
									<view class="text">
										<text style="margin-inline-end: 12rpx;">内容: </text>
										<text>
											Lorem ipsum dolor sit amet consectetur adipisicing elit. Animi quisquam
											quasi molestiae quos eligendi fugiat tempora deserunt quo praesentium quam,
											mollitia facilis obcaecati assumenda dolorum ipsum deleniti nisi ea?
										</text>
									</view>
								</view>
								<view class="substance">
									<view class="dot"></view>
									<view class="text">
										<text style="margin-inline-end: 12rpx;">业绩: </text>
										<text>
											Lorem ipsum dolor sit amet consectetur adipisicing elit. Animi quisquam
											quasi molestiae quos eligendi fugiat tempora deserunt quo praesentium quam,
											mollitia facilis obcaecati assumenda dolorum ipsum deleniti nisi ea?
										</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				<u-line></u-line>
				<view class="educat-container">
					<view class="title-container" @click="onRoutePage('educationalExperience')">
						<text>教育经历</text>
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/dMDIpORYc96JLP5Ks4YZknmvAmjSCmwaDxwziTkS.png">
						</image>
					</view>
					<view class="content">
						<view class="item">
							<image class="image" src="https://picsum.photos/800/600"></image>
							<view class="item-detail">
								<text class="title">南昌工学院</text>
								<view class="sub-title__box">
									<text>本科</text>
									<text>会计</text>
								</view>
							</view>
							<text class="time">2021-9 - 2023-7</text>
						</view>
					</view>
				</view>
				<u-line></u-line>
				<view class="custom-container">
					<view class="title-container">
						<text>自定义添加</text>
					</view>
					<view class="content">
						<view class="custom-item">
							<view class="custom-start">
								<view class="title-area">
									<text class="title">资格证书</text>
									<view class="tag">推荐</view>
								</view>
								<view class="desc">尽可能选择有含金量的证书，证明你的专业程度</view>
							</view>
							<view class="custom-end">
								<image class="plus"
									src="https://api-test.zhaopinbei.com/storage/uploads/images/ZEkG7FK9D8uEIuk0Dpgj30UPB6b87gc5TnCiwTEA.png">
								</image>
							</view>
						</view>
						<view class="custom-item">
							<view class="custom-start">
								<view class="title-area">
									<text class="title">社团/组织经历</text>
									<view class="tag">推荐</view>
								</view>
								<view class="desc">体现活动策划、团队管理等学习之外的能力</view>
							</view>
							<view class="custom-end">
								<image class="plus"
									src="https://api-test.zhaopinbei.com/storage/uploads/images/ZEkG7FK9D8uEIuk0Dpgj30UPB6b87gc5TnCiwTEA.png">
								</image>
							</view>
						</view>
						<view class="custom-item">
							<view class="custom-start">
								<view class="title-area">
									<text class="title">志愿者经历</text>
								</view>
								<view class="desc">展示你的志愿者精神</view>
							</view>
							<view class="custom-end">
								<image class="plus"
									src="https://api-test.zhaopinbei.com/storage/uploads/images/ZEkG7FK9D8uEIuk0Dpgj30UPB6b87gc5TnCiwTEA.png">
								</image>
							</view>
						</view>
						<view class="custom-item">
							<view class="custom-start">
								<view class="title-area">
									<text class="title">专业技能</text>
								</view>
								<view class="desc">突出专业技能、特长展示</view>
							</view>
							<view class="custom-end">
								<image class="plus"
									src="https://api-test.zhaopinbei.com/storage/uploads/images/ZEkG7FK9D8uEIuk0Dpgj30UPB6b87gc5TnCiwTEA.png">
								</image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		methods: {
			onRoutePage(url) {
				uni.$u.route({
					url: `/pagesB/Jobsetting/newPages/${url}`,
				})
			},
			onBackStrengthParams(params) {
				const res = JSON.parse(params);
			},
			onBackJobParams(params) {
				const res = JSON.parse(params);
			},
			onBackWorkParams(params) {
				const res = JSON.parse(params);
			},
			onBackProjectParams(params) {
				const res = JSON.parse(params);
			},
			onBackEductionalParams(params) {
				const res = JSON.parse(params);
			},
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		display: flex;
		flex-direction: column;

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				display: flex;
				flex-direction: column;
				gap: 24rpx;
				padding-inline: 32rpx;
				padding-block-start: 32rpx;
				padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
				padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

				.custom-container {
					display: flex;
					flex-direction: column;
					gap: 32rpx;

					.content {
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.custom-item {
							background-color: #F8F8F8;
							border-radius: 16rpx;
							padding-block: 24rpx;
							padding-inline: 32rpx;
							display: flex;
							align-items: center;
							justify-content: space-between;
							gap: 32rpx;

							.custom-start {
								display: flex;
								flex-direction: column;
								gap: 18rpx;

								.title-area {
									display: flex;
									align-items: center;
									gap: 12rpx;

									.title {
										color: #333333;
										font-size: 28rpx;
									}

									.tag {
										background-color: #E0EBFF;
										border-radius: 8rpx;
										color: #4F8CF0;
										font-size: 16rpx;
										padding-block: 4rpx;
										padding-inline: 6rpx;
									}
								}

								.desc {
									color: #666666;
									font-size: 24rpx;
									overflow: hidden;
									display: -webkit-box;
									-webkit-box-orient: vertical;
									-webkit-line-clamp: 1;
								}
							}

							.custom-end {
								.plus {
									width: 36rpx;
									height: 36rpx;
								}
							}
						}
					}
				}

				.educat-container {
					display: flex;
					flex-direction: column;
					gap: 32rpx;

					.item {
						display: flex;
						align-items: center;
						gap: 16rpx;

						.time {
							color: #666666;
							font-size: 20rpx;
							margin-inline-start: auto;
							align-self: flex-start;
							line-height: 48rpx;
						}

						.item-detail {
							display: flex;
							flex-direction: column;
							align-items: flex-start;
							gap: 16rpx;

							.sub-title__box {
								color: #666666;
								font-size: 24rpx;
							}

							.title {
								color: #333333;
								font-size: 32rpx;
							}
						}

						.image {
							width: 90rpx;
							height: 90rpx;
							object-fit: contain;
							border-radius: 999rpx;
						}
					}
				}

				.work-container,
				.project-container {
					display: flex;
					flex-direction: column;
					gap: 32rpx;

					.content {
						.item {
							display: flex;
							flex-direction: column;
							gap: 16rpx;

							.item-end {
								display: flex;
								flex-direction: column;
								gap: 16rpx;

								.substance {
									display: flex;
									align-items: flex-start;
									gap: 16rpx;

									.text {
										color: #666666;
										font-size: 24rpx;
										overflow: hidden;
										display: -webkit-box;
										-webkit-box-orient: vertical;
										-webkit-line-clamp: 3;
									}

									.dot {
										margin-block-start: 16rpx;
										width: 8rpx;
										height: 8rpx;
										border-radius: 999rpx;
										background-color: #666666;
									}
								}
							}

							.item-center {
								color: #333333;
								font-size: 28rpx;
							}

							.item-start {
								display: flex;
								align-items: center;
								justify-content: space-between;
								gap: 32rpx;

								.title {
									color: #333333;
									font-size: 32rpx;
									overflow: hidden;
									display: -webkit-box;
									-webkit-box-orient: vertical;
									-webkit-line-clamp: 1;
								}

								.sub-title {
									color: #666666;
									font-size: 20rpx;
								}
							}
						}
					}
				}

				.job-container {
					display: flex;
					flex-direction: column;
					gap: 32rpx;

					.content {
						display: flex;
						flex-direction: column;
						gap: 16rpx;

						.content-start {
							display: flex;
							align-items: center;
							gap: 18rpx;
							font-size: 28rpx;

							.title {
								color: #333333;
							}

							.sub-title {
								color: #4F8CF0;
							}
						}

						.content-end {
							display: flex;
							align-items: center;
							gap: 34rpx;
							color: #666666;
							font-size: 20rpx;
						}
					}
				}

				.strength-container {
					display: flex;
					flex-direction: column;
					gap: 32rpx;

					.content {
						color: #333333;
						font-size: 24rpx;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 4;
					}
				}


				.title-container {
					display: flex;
					align-items: center;
					justify-content: space-between;
					color: #333333;
					font-size: 32rpx;

					.image {
						width: 32rpx;
						height: 32rpx;
						object-fit: contain;
					}
				}

				.user-container {
					display: flex;
					justify-content: space-between;

					.user-end {
						align-self: flex-start;

						.avatar {
							width: 108rpx;
							height: 108rpx;
							object-fit: contain;
							border-radius: 999rpx;
						}
					}

					.user-start {
						display: flex;
						flex-direction: column;
						gap: 20rpx;

						.contact-box {
							display: flex;
							align-items: center;
							gap: 30rpx;
							color: #333333;
							font-size: 24rpx;

							.image {
								width: 32rpx;
								height: 32rpx;
								object-fit: contain;
							}

							.phone-box,
							.wx-box {
								display: flex;
								align-items: center;
								gap: 14rpx;
							}
						}

						.detail {
							color: #999999;
							font-size: 24rpx;
						}

						.title-box {
							display: flex;
							align-items: center;
							gap: 24rpx;
							color: #333333;

							.title {
								font-size: 32rpx;
							}

							.sub-title__box {
								display: flex;
								align-items: center;
								gap: 8rpx;
								font-size: 24rpx;

								.dot {
									width: 8rpx;
									height: 8rpx;
									background-color: #57D51C;
									border-radius: 999rpx;
								}
							}
						}
					}
				}
			}
		}
	}
</style>