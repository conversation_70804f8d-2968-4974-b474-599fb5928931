const AUTH_BASE = 'common-v2/auth'
// export const login = `${AUTH_BASE}/cellphone-login`
import {
	request
} from "./request"
//切换身份

// 上传
export const uploadApi = (data) => {
	return request({
		url: `upload-v2/upload/upload`,
		data,
		upload: true,
		common: 'common'
	})
}
// 附件简历列表
export const resumeList = (data) => {
	return request({
		url: `upload-v2/upload/resumeList`,
		data,
		method: 'post',
		common: 'common'
	})
}

// 删除附件简历
export const deleteResume = (data) => {
	return request({
		url: `upload-v2/upload/deleteResume`,
		data,
		method: 'post',
		common: 'common'
	})
}


export const switchLogin = (data) => {
	return request({
		url: `common-v2/auth/switch-login`,
		data,
		method: 'post',
		common: 'common'
	})
}

//猎头注册
export const headhuntersCert = (data) => {
	return request({
		url: `common-v2/auth/register/headhunter-store`,
		data,
		method: 'post',
		common: 'common'
	})
}


export const logout = (data) => {
	return request({
		url: `common-v2/auth/logout`,
		data,
		method: 'post',
		common: 'common'
	})
}

//伯乐设置职位
export const setJobClass = (data) => {
	return request({
		url: `common-v2/auth/register/set-job-class`,
		data,
		method: 'post',
		common: 'common'
	})
}

//伯乐-企业信息
export const getCompanyInfo = (data) => {
	return request({
		url: `common-v2/company/show`,
		data,
		method: 'post',
		common: 'common'
	})
}


//行业列表
// export const getIndustryList = (data) => {
// 	return request({
// 		url: `common-v1/industry/class/index`,
// 		data,
// 		method: 'post'
// 	})
// }

export const getIndustryList = (data) => {
	return request({
		url: `common-v2/industry/index`,
		data,
		method: 'post',
		common: 'common'
	})
}


//企业福利
export const getWelfareList = (data) => {
	return request({
		url: `common-v2/tag/index`,
		data,
		method: 'post',
		common: 'common'
	})
}


//招聘会列表
export const getJobfairList = (data) => {
	return request({
		url: `common-v2/job/active/index`,
		data,
		method: 'post',
		common: 'common'
	})
}


//修改职位类型
export const saveTourist = (data) => {
	return request({
		url: `common-v2/user/user/job-class`,
		data,
		method: 'post',
		common: 'common'
	})
}


//职位分类
export const getClassList = (data) => {
	return request({
		url: `common-v2/job/class`,
		data,
		method: 'post',
		common: 'common'
	})
}

//就业类型
export const getWorkType = (data) => {
	return request({
		url: `common-v2/job/work-type`,
		data,
		method: 'post',
		common: 'common'
	})
}


// 获取省市数据
export const getCityList = (data) => {
	return request({
		url: `common-v1/area/tree`,
		data,
		method: 'post',
		common: 'common'
	})
}

//千里马-获取职位类型数据
export const getJobClassList = (data) => {
	return request({
		url: `common-v1/job/class/index`,
		data,
		method: 'post',
		common: 'common'
	})
}


//伯乐-获取职位类型数据
export const getCompanyJobClassList = (data) => {
	return request({
		url: `common-v2/job/class`,
		data,
		method: 'post',
		common: 'common'
	})
}


//千里马-获取系统数据
export const getSysList = (data) => {
	return request({
		url: `common-v1/system/const-list`,
		data,
		method: 'post',
		common: 'common'
	})
}


//登录
export const quickLogin = (data) => {
	return request({
		url: `common-v2/auth/wx-mini-login`,
		data,
		method: 'post',
		common: 'common'
	})
}


//登录
export const login = (data) => {
	return request({
		url: `common-v2/auth/cellphone-login`,
		data,
		method: 'post',
		common: 'common'
	})
}

//图片上传
export const uploadImg = (data) => {
	return request({
		url: `common-v1/file/upload/base64`,
		data,
		method: 'post',
		common: 'common'
	})
}

//识别图片接口
export const distImg = (data) => {
	return request({
		url: `common-v1/file/ocr/put-file`,
		data,
		method: 'post',
		common: 'common'
	})
}

//千里马注册
export const registerQlm = (data) => {
	return request({
		url: `common-v2/auth/register/store`,
		data,
		method: 'post',
		common: 'common'
	})
}

//千里马-实名认证
export const cert = (data) => {
	return request({
		url: `common-v2/auth/member/store`,
		data,
		method: 'post',
		common: 'common'
	})
}


//企业标签
export const getCompanyTags = (data) => {
	return request({
		url: `common-v2/tag/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

//企业认证
export const companyCert = (data) => {
	return request({
		url: `/company/store`,
		data,
		method: 'post'
	})
}

//企业编辑
export const editCompany = (data) => {
	return request({
		url: `common-v2/company/store`,
		data,
		method: 'post',
		common: 'common'
	})
}


//更新登录信息
export const updateLoginInfo = (data) => {
	return request({
		url: `common-v2/auth/query`,
		data,
		method: 'post',
		common: 'common'
	})
}

//千里马-添加简历
export const addResume = (data) => {
	return request({
		url: `/member/resume/store`,
		data,
		method: 'post'
	})
}

//千里马-简历列表
export const getResumeList = (data) => {
	return request({
		url: `/member/resume/index`,
		data,
		method: 'post'
	})
}

//千里马-简历详情
export const getResumeDetails = (data) => {
	return request({
		url: `/member/resume/show`,
		data,
		method: 'post'
	})
}

//千里马-公开简历
export const openResume = (data) => {
	return request({
		url: `/member/resume/open`,
		data,
		method: 'post'
	})
}

//千里马-设置为主简历
export const setMainResume = (data) => {
	return request({
		url: `/member/resume/public-status`,
		data,
		method: 'post'
	})
}

//千里马-删除简历
export const delResume = (data) => {
	return request({
		url: `/member/resume/destroy`,
		data,
		method: 'post'
	})
}


//千里马-企业列表
export const getEnterpriseList = (data) => {
	return request({
		url: `common-v2/company/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

//千里马-企业详情
export const getEnterpriseDetails = (data) => {
	return request({
		url: `common-v2/company/web-show`,
		data,
		method: 'post',
		common: 'common'
	})
}

//千里马-收藏企业
export const collectEnterprise = (data) => {
	return request({
		url: `/collect/company/store`,
		data,
		method: 'post'
	})
}

//千里马-取消收藏企业
export const cancelCollectEnterprise = (data) => {
	return request({
		url: `/collect/company/destroy`,
		data,
		method: 'post'
	})
}


//千里马-地址列表
export const getAddressList = (data) => {
	return request({
		url: `/address/address/index`,
		data,
		method: 'post'
	})
}


//千里马-默认地址
export const setDefaultAddress = (data) => {
	return request({
		url: `/address/address/default-store`,
		data,
		method: 'post'
	})
}

//千里马-保存地址
export const saveAddress = (data) => {
	return request({
		url: `/address/address/store`,
		data,
		method: 'post'
	})
}

//千里马-删除地址
export const delAddress = (data) => {
	return request({
		url: `/address/address/destroy`,
		data,
		method: 'post'
	})
}

//千里马-地址详情
export const getAddressDetails = (data) => {
	return request({
		url: `/address/address/show`,
		data,
		method: 'post'
	})
}


//千里马-面试记录
export const getInterviewList = (data) => {
	return request({
		url: `/interview/index`,
		data,
		method: 'post'
	})
}

//千里马-面试审核
export const interviewApprove = (data) => {
	return request({
		url: `/interview/audit`,
		data,
		method: 'post'
	})
}

//千里马-面试-签到
export const interviewSignup = (data) => {
	return request({
		url: `/interview/sign`,
		data,
		method: 'post'
	})
}

//千里马-面试-详情
export const getInterviewDetails = (data) => {
	return request({
		url: `/interview/show`,
		data,
		method: 'post'
	})
}


//千里马-首页-职位列表
export const getRecomendList = (data) => {
	return request({
		url: `common-v2/job/indexs`,
		data,
		method: 'post',
		common: 'common'
	})
}

//千里马-优秀企业-职位列表
export const getComJobList = (data) => {
	return request({
		url: `common-v2/job/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

// 千里马-招聘会-招聘会职位
export const activeJobList = (data) => {
	return request({
		url: `common-v2/job/active/jobs`,
		data,
		method: 'post',
		common: 'common'
	})
}

//千里马-首页-职位列表
export const getJobDetails = (data) => {
	return request({
		url: `common-v2/job/show`,
		data,
		method: 'post',
		common: 'common'
	})
}


//千里马-收藏列表-职位列表
export const getCollectJobList = (data) => {
	return request({
		url: `/collect/job`,
		data,
		method: 'post'
	})
}

//千里马-收藏列表-企业列表
export const getCollectCompanyList = (data) => {
	return request({
		url: `/collect/company`,
		data,
		method: 'post'
	})
}

//千里马-收藏列表-就业管家
export const getCollectHeadhunterList = (data) => {
	return request({
		url: `/collect/headhunter/index`,
		data,
		method: 'post'
	})
}

//千里马-收藏职位
export const memberCollectJob = (data) => {
	return request({
		url: `/collect/job/store`,
		data,
		method: 'post'
	})
}

//千里马-取消收藏职位
export const memberCancelCollectJob = (data) => {
	return request({
		url: `/collect/job/destroy`,
		data,
		method: 'post'
	})
}


//千里马-报名记录
export const getSignupList = (data) => {
	return request({
		url: `/job/report/index`,
		data,
		method: 'post'
	})
}

//千里马-报名
export const signup = (data) => {
	return request({
		url: `/job/report/store`,
		data,
		method: 'post'
	})
}

//千里马-取消报名
export const cancelSignup = (data) => {
	return request({
		url: `/job/report/cancel`,
		data,
		method: 'post'
	})
}

//用户详情
export const getUserDetails = (data) => {
	return request({
		url: `common-v2/member/member-info/show`,
		data,
		method: 'post',
		common: 'common'
	})
}
//个人信息修改
export const editUserInfo = (data) => {
	return request({
		url: `common-v2/member/member-info/store`,
		data,
		method: 'post',
		common: 'common'
	})
}


// 伯乐-首页-千里马列表
export const getCompanyMemberList = (data) => {
	return request({
		url: `common-v2/member/member-resume/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

// 伯乐-首页-千里马详情
export const getCompanyMemberDetails = (data) => {
	return request({
		url: `common-v2/member/member-resume/show`,
		data,
		method: 'post',
		common: 'common'
	})
}
// 简历详情（id查看）
export const memberResumeIdShow = (data) => {
	return request({
		url: `common-v2/member/member-resume/id-show`,
		data,
		method: 'post',
		common: 'common'
	})
}

// 伯乐-收藏千里马列表
export const getCollectCompanyMemberList = (data) => {
	return request({
		url: `/collect/member`,
		data,
		method: 'post'
	})
}

// 伯乐-取消收藏千里马
export const cancelCollectCompanyMember = (data) => {
	return request({
		url: `/collect/member/destroy`,
		data,
		method: 'post'
	})
}

// 伯乐-收藏千里马
export const collectCompanyMember = (data) => {
	return request({
		url: `/collect/member/store`,
		data,
		method: 'post'
	})
}


// 伯乐-收藏就业管家
export const getCollectCompanyHeadhuntersList = (data) => {
	return request({
		url: `/collect/headhunter/index`,
		data,
		method: 'post'
	})
}


// 伯乐-取消收藏就业管家
export const cancelCollectCompanyHeadhunters = (data) => {
	return request({
		url: `/collect/headhunter/destroy`,
		data,
		method: 'post'
	})
}

// 伯乐-收藏就业管家
export const collectCompanyHeadhunters = (data) => {
	return request({
		url: `/collect/headhunter/store`,
		data,
		method: 'post'
	})
}


// 伯乐-职位管理列表
export const getCompanyJobList = (data) => {
	return request({
		url: `/job/index`,
		data,
		method: 'post'
	})
}

// 抄送记录
export const jobCopyIndex = (data) => {
	return request({
		url: `/job/copy-index`,
		data,
		method: 'post',
	})
}

//伯乐-职位详情
export const getCompanyJobDetails = (data) => {
	return request({
		url: `/job/show`,
		data,
		method: 'post'
	})
}

//伯乐-保存职位
export const addCompanyJob = (data) => {
	return request({
		url: `/job/store`,
		data,
		method: 'post'
	})
}

//伯乐-曝光职位
export const exposeCompanyJob = (data) => {
	return request({
		url: `/job/exposed`,
		data,
		method: 'post'
	})
}

//伯乐-置顶职位
export const topCompanyJob = (data) => {
	return request({
		url: `/job/top`,
		data,
		method: 'post'
	})
}

//伯乐-职位上下架
export const upDownCompanyJob = (data) => {
	return request({
		url: `/job/recruiting`,
		data,
		method: 'post'
	})
}


//伯乐-删除职位
export const delCompanyJob = (data) => {
	return request({
		url: `/job/destroy`,
		data,
		method: 'post'
	})
}


//伯乐-授权就业管家列表
export const getCompanyAuthEmployList = (data) => {
	return request({
		url: `/company/authorize`,
		data,
		method: 'post'
	})
}


//伯乐-招聘会管理列表
export const getCompanyJobfairList = (data) => {
	return request({
		url: `/job/active/report`,
		data,
		method: 'post'
	})
}


//伯乐-员工管理-管理员-列表
export const getCompanyStaffList = (data) => {
	return request({
		url: `/user/index`,
		data,
		method: 'post'
	})
}

//伯乐-员工管理-管理员-保存
export const addCompanyStaff = (data) => {
	return request({
		url: `/user/store`,
		data,
		method: 'post'
	})
}

//伯乐-员工管理-管理员-删除
export const delCompanyStaff = (data) => {
	return request({
		url: `/user/destroy`,
		data,
		method: 'post'
	})
}


//伯乐-员工管理-角色-列表
export const getCompanyRoleList = (data) => {
	return request({
		url: `/user/role`,
		data,
		method: 'post'
	})
}


//伯乐-地址列表
export const getCompanyAddressList = (data) => {
	return request({
		url: `/address/address/index`,
		data,
		method: 'post'
	})
}


//伯乐-默认地址
export const setDefaultCompanyAddress = (data) => {
	return request({
		url: `/address/address/default-store`,
		data,
		method: 'post'
	})
}

//伯乐-保存地址
export const saveCompanyAddress = (data) => {
	return request({
		url: `/address/address/store`,
		data,
		method: 'post'
	})
}

//伯乐-删除地址
export const delCompanyAddress = (data) => {
	return request({
		url: `/address/address/destroy`,
		data,
		method: 'post'
	})
}

//伯乐-地址详情
export const getCompanyAddressDetails = (data) => {
	return request({
		url: `/address/address/show`,
		data,
		method: 'post'
	})
}

//伯乐-报名记录

export const getCompanySignupList = (data) => {
	return request({
		url: `/job/report/index`,
		data,
		method: 'post'
	})
}

//伯乐-取消报名
export const cancelCompanySignup = (data) => {
	return request({
		url: `/job/report/cancel`,
		data,
		method: 'post'
	})
}

//伯乐-邀请面试
export const inviteInterview = (data) => {
	return request({
		url: `/interview/store`,
		data,
		method: 'post'
	})
}


//伯乐-面试列表
export const getCompanyInterviewList = (data) => {
	return request({
		url: `/interview/index`,
		data,
		method: 'post'
	})
}

//公用-面试列表
export const commonInterviewList = (data) => {
	return request({
		url: `common-v2/interview/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

//伯乐-取消面试
export const companyCancelInterview = (data) => {
	return request({
		url: `/interview/cancel`,
		data,
		method: 'post'
	})
}

//伯乐-面试详情
export const getCompanyInterviewDetails = (data) => {
	return request({
		url: `/interview/show`,
		data,
		method: 'post'
	})
}


//就业管家-授权企业列表
export const getHeadhuntersAuthEnterpriseList = (data) => {
	return request({
		url: `/company/index`,
		data,
		method: 'post'
	})
}

//就业管家-授权企业详情
export const getHeadhuntersAuthEnterpriseDetails = (data) => {
	return request({
		url: `/company/show`,
		data,
		method: 'post'
	})
}

//就业管家-设置授权企业员工
export const setAuthEnterpriseStaff = (data) => {
	return request({
		url: `/company/set`,
		data,
		method: 'post'
	})
}

//变更详情
export const updateButler = (data) => {
	return request({
		url: `/company/user-authorizes/show`,
		data,
		method: 'post'
	})
}

export const memberChangeShow = (data) => {
	return request({
		url: `/member/authorize/change-show`,
		data,
		method: 'post'
	})
}

export const memberAuthorizeRelieve = (data) => {
	return request({
		url: `/member/authorize/relieve`,
		data,
		method: 'post'
	})
}

export const userAuthorizesAudit = (data) => {
	return request({
		url: `/company/user-authorizes/audit`,
		data,
		method: 'post'
	})
}

export const memberAuthorizeAuditChange = (data) => {
	return request({
		url: `/member/authorize/audit-change`,
		data,
		method: 'post'
	})
}


//就业管家-取消授权
export const cancelAuth = (data) => {
	return request({
		url: `/company/cancel`,
		data,
		method: 'post'
	})
}

//就业管家-委托授权
export const entrustAuth = (data) => {
	return request({
		url: `/company/audit-store`,
		data,
		method: 'post'
	})
}


//就业管家-招聘会管理-列表
export const getHeadhuntersJobfairList = (data) => {
	return request({
		url: `/job/active/index`,
		data,
		method: 'post'
	})
}

//就业管家-发布招聘会
export const saveHeadhuntersJobfair = (data) => {
	return request({
		url: `/job/active/store`,
		data,
		method: 'post'
	})
}

//就业管家-删除招聘会
export const delHeadhuntersJobfair = (data) => {
	return request({
		url: `/job/active/destroy`,
		data,
		method: 'post'
	})
}


//就业管家-招聘会详情
export const getHeadhuntersJobfairDetails = (data) => {
	return request({
		url: `/job/active/show`,
		data,
		method: 'post'
	})
}

//就业管家-授权应聘人列表
export const getAuthApplicantList = (data) => {
	return request({
		url: `/member/authorize`,
		data,
		method: 'post'
	})
}


//就业管家-员工树结构
export const getUserTree = (data) => {
	return request({
		url: `/user/tree`,
		data,
		method: 'post'
	})
}


//我的钱包-充值消费记录
export const getRechargeList = (data) => {
	return request({
		url: `common-v2/account/point/records`,
		data,
		method: 'post',
		common: 'common'
	})
}

//我的钱包-服务包列表
export const getServicePackageList = (data) => {
	return request({
		url: `common-v2/package/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

//我的钱包-钱包信息
export const getPackageInfo = (data) => {
	return request({
		url: `common-v2/account/point/index`,
		data,
		method: 'post',
		common: 'common'
	})
}


//我的钱包-合同职位包充值
export const recharge = (data) => {
	return request({
		url: `common-v2/package/payment`,
		data,
		method: 'post',
		common: 'common'
	})
}

//我的钱包-积分充值
export const rechargePoint = (data) => {
	return request({
		url: `common-v2/package/account-payment`,
		data,
		method: 'post',
		common: 'common'
	})
}

//查询微信用户授权信息
export const getWechatUserAuthInfo = (data) => {
	return request({
		url: `common-v2/wechat/user-info/show`,
		data,
		method: 'post',
		common: 'common'
	})
}

//查询微信用户信息
export const getWechatUserInfo = (data) => {
	return request({
		url: `common-v2/wechat/user-info/get`,
		data,
		method: 'post',
		common: 'common'
	})
}

//职位=常见问题
export const getQuestionList = (data) => {
	return request({
		url: `common-v2/job/question`,
		data,
		method: 'post',
		common: 'common'
	})
}
//账号下（企业|运营商）列表
export const getCompanyList = (data) => {
	return request({
		url: `common-v2/auth/company`,
		data,
		method: 'post',
		common: 'common'
	})
}
export const resetPassword = (data) => {
	return request({
		url: `common-v2/auth/reset-password`,
		data,
		method: 'post',
		common: 'common'
	})
}

// 保存就业管家信息
export const saveButlerDetails = (data) => {
	return request({
		url: `/user/user-headhunter/store`,
		data,
		method: 'post',
	})
}

// 获取就业管家信息
export const getButlerDetails = (data) => {
	return request({
		url: `/user/user-headhunter/show`,
		data,
		method: 'post'
	})
}


// 获取省市
export const getAllCity = (data) => {
	return request({
		url: `common-v1/area/city-list`,
		data,
		method: 'post',
		common: 'common'
	})
}

//千里马-招聘会详情
export const getQLMJobfairDetails = (data) => {
	console.log(data, 111);
	return request({
		url: `common-v2/job/active/show`,
		data,
		method: 'post',
		common: 'common'
	})
}

//就业管家列表
export const staffList = (data) => {
	return request({
		url: `common-v2/headhunter/user/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

// 合同列表
export const contractIndex = (data) => {
	return request({
		url: `/contract/index`,
		data,
		method: 'post'
	})
}

// 合同详情
export const contractShow = (data) => {
	return request({
		url: `/contract/show`,
		data,
		method: 'post'
	})
}

// 关键字列表
export const getKeyword = (data) => {
	return request({
		url: `/contract/keyword`,
		data,
		method: 'post'
	})
}

// 保存关键字
export const keyWordStore = (data) => {
	return request({
		url: `/contract/keyword/store`,
		data,
		method: 'post'
	})
}
// 关键字详情
export const keyWordShow = (data) => {
	return request({
		url: `/contract/keyword/show`,
		data,
		method: 'post'
	})
}
// 删除关键字
export const keyWordDestroy = (data) => {
	return request({
		url: `/contract/keyword/destroy`,
		data,
		method: 'post'
	})
}
// 保存印章
export const sealStore = (data) => {
	return request({
		url: `/contract/seal/store`,
		data,
		method: 'post'
	})
}

// 印章列表
export const contractSeal = (data) => {
	return request({
		url: `/contract/seal`,
		data,
		method: 'post'
	})
}

// 印章详情
export const sealShow = (data) => {
	return request({
		url: `/contract/seal/show`,
		data,
		method: 'post'
	})
}

// 删除印章
export const sealDestroy = (data) => {
	return request({
		url: `/contract/seal/destroy`,
		data,
		method: 'post'
	})
}

// 招聘会参与企业
export const getJobFairCompany = (data) => {
	return request({
		url: `common-v2/job/active/company`,
		data,
		method: 'post',
		common: 'common'
	})
}


// 就业管家千里马列表
export const getHeadhunterUserMember = (data) => {
	return request({
		url: `common-v2/headhunter/user/members`,
		data,
		method: 'post',
		common: 'common'
	})
}
//就业管家给企业发起合同
export const postContract = (data) => {
	return request({
		url: `/contract/store`,
		data,
		method: 'post',
	})
}