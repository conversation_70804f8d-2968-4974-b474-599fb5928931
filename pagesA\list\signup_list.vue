<template>
	<view class="">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="tabs">
					<u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)',
						fontSize:'32rpx'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)',
						fontSize:'24rpx'
					}" :list="tabs" @click="tabChange()"></u-tabs>
				</view>
				<view class="filters">
					<!-- <view class="filter" @click="goPage">
						{{userCity.name?userCity.name:'全国'}}
						<image src="/static/images/index/down.png" mode=""></image>
					</view> -->
					<view class="search-wrap">
						<u-search placeholder="请输入职位名称" bgColor="#FFFFFF" v-model="page.form.job_title" actionText="搜索"
							@custom="searchCli" @clear="clear"></u-search>
					</view>
				</view>
			</view>
		</u-sticky>

		<!-- 筛选项 -->
		<view class="filterss">
			<view class="filter" v-if="roleType=='member'" @click="filterInterview">
				<view class="d-picker">{{filterInterviewText}}</view>
				<image src="/static/images/index/down.png" mode=""></image>
			</view>
		</view>

		<view class="list" v-if="page.data.length > 0">
			<sign-up-item v-for="(item,index) in page.data" :key="index" :itemIndex="index" :item="item"
				@cancelSignup="cancelSignup"></sign-up-item>
			<Pages :status="page.status"></Pages>
		</view>
		<view v-else class="no-data-container">
			<image src="https://api-test.zhaopinbei.com/storage/uploads/images/seSCfATYwcGhiKSeVL4lpUa7gxtuL8aSgnW5C9k7.png" mode=""></image>
			<view class="button" @tap="showDialog = true">
				前往职位列表->
			</view>
		</view>
		<view v-if="showDialog" class="custom-dialog">
			<view class="dialog-content">
				<view class="dialog-title">选择报名方式</view>
				<view class="dialog-buttons">
					<button @tap="handleSignup('job')">普通报名</button>
					<button @tap="handleSignup('job_active')">招聘会报名</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Pages from "../../components/pages.vue";
	import SignUpItem from "../components/signUpItem.vue"

	import {
		getSignupList,
		cancelSignup
	} from "../../config/api.js"

	export default {
		components: {
			SignUpItem,
			Pages,
		},
		data() {
			return {
				tabs: [{
						name: '普通报名',
						value: 'job',
					}, {
						name: '活动报名', //招聘会报名
						value: 'job_active',
					},
					{
						name: '就业管家推荐',
						value: 'recommend',
					}
				],
				userCity: '',
				page: {
					form: {
						page: 1,
						limit: 10,
						title: '',
						city_id: '',
						model_type: 'job',
					},
					data: [],
					more: false,
					status: 'loadmore',
				},
				showDialog: false,
				filterInterviewText: '报名状态',
				roleType: ''
			}
		},
		onLoad() {
			this.getSignupList()
			this.userCity = uni.getStorageSync('userCity');
			this.roleType = uni.getStorageSync('roleType')
		},
		onShow() {
			// 用户选择的城市从本地获取name、id
			if (this.userCity.name != uni.getStorageSync('userCity').name) {
				this.userCity = uni.getStorageSync('userCity');
				this.searchCli()
			}
		},
		//触底加载更多
		onReachBottom() {
			if (this.page.more) {
				this.page.status = 'loading';
				this.page.form.page++;
				this.getSignupList();
			}
		},

		methods: {
			filterInterview() {
				uni.showActionSheet({
					itemList: ['全部', '已报名', '已取消'], // 显示的选项
					success: (res) => {
						console.log('000', res);
						// 更新筛选状态文本
						this.filterInterviewText = res.tapIndex == 0 ? '全部' : res.tapIndex == 1 ? '已报名' :
							'已取消';
						// 更新筛选参数
						this.page.form.info = res.tapIndex == 0 ? '' : res.tapIndex == 1 ? '已报名' : '已取消';
						// 重新加载列表
						this.initPage();
						this.getSignupList();
					},
				});
			},
			initPage() {
				this.page.form.page = 1;
				this.page.data = [];
				this.page.status = 'loadmore';
			},
			goPage() {
				uni.navigateTo({
					url: '/pagesA/components/selectCitys'
				})
			},
			searchCli() {
				this.initPage();
				this.getSignupList()
			},
			clear() {
				this.page.form.job_title = '';
				this.initPage();
				this.searchCli()
			},
			tabChange(e) {
				this.filterInterviewText='报名状态'
				// 从这里提取筛选项
				this.initPage();
				this.page.form.model_type = this.tabs[e.index]['value'];
				console.log(this.page.form.model_type);
				// 报名记录列表
				this.getSignupList()
			},
			// 报名记录
			async getSignupList() {
				var _this = this;
				getSignupList(this.page.form).then(response => {
					if (response.status_code == '200') {
						let data = response.data.data;
						// 根据筛选条件过滤数据
						if (this.page.form.info) {
							data = data.filter(item => item.report_status_name === this.page.form.info);
						}
						console.log('筛选后的数据', data); // 打印筛选后的数据
						_this.page.data = _this.page.data.concat(data);
						_this.page.more = response.data.more || false;
						_this.page.status = _this.page.more ? 'loadmore' : 'nomore';
					}
				});
			},

			//取消报名
			cancelSignup(changeItem) {
				var item = changeItem.item;
				var itemIndex = changeItem.index;

				let self = this;
				uni.showModal({
					title: '是否取消该职位报名',
					editable: true,
					placeholderText: '请输入取消原因',
					success: async function(res) {
						if (res.confirm) {
							if (!res.content) {
								uni.$u.toast('请输入原因')
								return
							}
							let params = {
								report_id: item.id,
								model_id: item.job_id,
								cancel_reason: res.content,
							}
							const {
								status_code,
								data
							} = await cancelSignup(params)
							if (status_code == 200) {
								self.page.data.splice(itemIndex, 1, data);
								uni.$u.toast('成功')
							}

						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				})
			},
			handleSignup(type) {
				this.showDialog = false; // 关闭弹窗
				if (type === 'job') {
					uni.navigateTo({
						url: "/pagesA/list/job_list?tabIndex=0"
					})
				} else if (type === 'job_active') {
					uni.navigateTo({
						url: "/pagesA/list/job_fair_list"
					})
				}
			}
		}
	}
</script>
<style lang="scss" src="../../static/css/pagesA/list/signup_list.scss"></style>
