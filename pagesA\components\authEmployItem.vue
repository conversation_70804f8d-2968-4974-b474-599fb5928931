<template>
	<view class="item">
		<view class="item-up">
			<image src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode=""></image>
			<view class="info">
				<view class="user">
					<view class="userInfo">
						<view class="name">
							{{ item.company.name }}
						</view>
					</view>
				</view>
				<view class="flags">
					<view class="flag">学创北京</view>
					<view class="flag">A轮</view>
					<view class="flag">5000人</view>
				</view>

				<view class="tags">
					<view class="tag">世界500强</view>
					<view class="tag">上市公司</view>
					<view class="tag">游戏大厂</view>
				</view>
			</view>
		</view>

		<view class="item-down">
			<view class="addr">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png" mode=""></image>
				<view class="name">地址地址地址地址地址地址地址地址</view>
			</view>
			<view class="status">已签署</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'authEmployItem',
	props: {
		item: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {};
	},
};
</script>

<style lang="less" scoped>
.item {
	display: flex;
	flex-direction: column;
	padding: 32rpx;
	margin-bottom: 32rpx;
	background-color: #ffffff;
	border-radius: 24rpx;

	.item-up {
		display: flex;
		padding-bottom: 32rpx;
		// background-color: red;
		& > image {
			width: 144rpx;
			height: 144rpx;
		}

		.info {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			flex: 1;
			padding-left: 24rpx;

			.user {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.userInfo {
					display: flex;
					align-items: center;

					.name {
						font-weight: 600;
						font-size: 32rpx;
						color: #333333;
					}
				}
			}

			.flags {
				display: flex;

				.flag {
					display: flex;
					align-items: center;
					padding: 0 12rpx;
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
					border-right: 1px solid #999999;

					&:first-child {
						padding-left: 0;
					}

					&:last-child {
						border-right: none;
					}
				}
			}

			.tags {
				display: flex;

				.tag {
					display: flex;
					align-items: center;
					background: #f6f6f6;
					border-radius: 8rpx;
					height: 46rpx;
					padding: 0 12rpx;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin-right: 16rpx;
				}
			}
		}
	}

	.item-down {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-top: 32rpx;
		border-top: 1px solid #f5f5f7;

		.addr {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 24rpx;
			color: #666666;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 12rpx;
			}
		}

		.status {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 50rpx;
			padding: 0 12rpx;
			background: rgba(249, 173, 20, 0.1);
			border-radius: 8rpx;
			font-weight: 600;
			font-size: 20rpx;
			color: #f9ad14;
		}
	}
}
</style>
