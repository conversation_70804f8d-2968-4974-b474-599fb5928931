<template>
    <view class="home-index">
        <view class="userInfo">
            <view class="info">
                <view class="userName">
                    <view class="name">
                        {{details.member_info.nick_name}}
                    </view>
                    <view class="dot" v-if="details.member_info.job_status_name">

                    </view>
                    <view class="status" v-if="details.member_info.job_status_name">
                        {{details.member_info.job_status_name}}
                    </view>
                </view>

                <view class="tags">
                    {{details.member_info.sex_str}}·{{details.member_info.age}}岁·{{details.member_info.nation}}·{{details.member_info.education_type_name}}
                </view>

                <view class="items">
                    <view class="item">
                        <u-icon name="phone" size="28rpx" color="#333333"></u-icon>
                        <text>{{details.member.cellphone}}</text>
                    </view>
                    <!-- <view class="item">
                        <u-icon name="email" size="28rpx" color="#333333"></u-icon>
                        <text><EMAIL></text>
                    </view> -->
                </view>
            </view>
            <image :src="details.member_info.image.thumbnail_path_url" mode=""></image>
        </view>
        <view class="wrap">
            <view class="title">
                <view class="name">
                    <text>教育经历</text>
                </view>
            </view>

            <view class="content">
                <view class="jy-list">
                    <view class="jy-item" v-for="(edu,eduInd) in details.member_info.education_log" :key="edu.id">
                        <view class="name">
                            {{edu.school}}
                        </view>
                        <view class="time">
                            {{edu.start}}-{{edu.end}}
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="wrap">
            <view class="title">
                <view class="name">
                    <text>求职期望</text>
                </view>
        
            </view>
            <view class="content">
                <view class="cont">
                    <view class="ui">
                        <view class="expert">
                            <view class="type" v-if="details.public_resume_job_class.name">
                                {{details.public_resume_job_class.name}}
                            </view>
                            <view class="money" v-if="details.public_resume.expect_salary_k">
                                {{details.public_resume.expect_salary_k}}
                            </view>
                        </view>
                        <view class="pos">
                            <view class="addr" v-if="details.public_resume.expect_address">
                                {{details.public_resume.expect_address}}
                            </view>
                            <view class="type" v-for="(types,ind) in details.public_resume_work_types" :key="types.id" v-if="details.member_resume_work_types.length>0">
                                {{types.name}}
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="wrap">
            <view class="title">
                <view class="name">
                    <text>自我介绍</text>
                </view>
            </view>
            <view class="content" v-if="details.member_info.introduce">
                <view class="intro">
                    {{details.member_info.introduce}}
                </view>
            </view>
        </view>

        <view class="wrap">
            <view class="title">
                <view class="name">
                    <text>工作经历</text>
                </view>
            </view>
            <view class="content">
                <view class="sub-wrap" v-for="(job,jobInd) in details.member_resume.job_log" :key="index">
                    <view class="exper">
                        <view class="name">
                            {{job.company}}
                        </view>
                        <view class="time">
                            <text>{{job.start_date}}-{{job.end_date}}</text>
                        </view>
                    </view>
                    <view class="user-info">
                        <text class="user-name">{{job.contact_name}}</text>
                        <text class="mobile">{{job.contract_cellphone}}</text>
                    </view>

                    <view class="types">
                        <view class="type">
                            {{job.industry}}
                        </view>
                        <view class="type">
                            {{item.job_name}}
                        </view>
                    </view>

                    <view class="list">
                        <view class="item">
                            <view class="dot">
                            </view>
                            <view class="js">
                                业绩：{{job.achievement}}
                            </view>
                        </view>

                        <view class="item">
                            <view class="dot">
                            </view>
                            <view class="js">
                                业绩：{{job.content}}
                            </view>
                        </view>
                    </view>
                </view>

            </view>
        </view>


        <view class="wrap">
            <view class="title">
                <view class="name">
                    <text>资格证书</text>
                </view>
            </view>

            <view class="content">
                <view class="pic-list">
                    <view class="pic-item" v-for="imgItem in details.member_resume.certificates">
                        <image :src="imgItem.thumbnail_path_url" mode=""></image>
                    </view>
                </view>
            </view>
        </view>

        <view class="edit" @click="edit">
            <u-icon name="edit-pen" color="#FFFFFF" size="14"></u-icon>
            <text>编辑简历</text>
        </view>

        <view class="footer">
            <view class="btns">
                <!--<view class="btn cancel">-->
                <!--解除授权申请-->
                <!--</view>-->
                <view class="btn cancel" v-if="details.but.audit_status == 1" @click="cancelSubmit">
                    解除授权审核
                </view>
                <view class="btn cancel" v-if="details.but.send_audit_status == 1" @click="relieve">
                    解除授权审核
                </view>
                <view class="btn zx" style="margin-left: 32rpx;">
                    立即联系
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import {
        authorizeShow,
        auditMemberAuthorizeHandel,
    } from "../../config/headhunterList_api.js"

    import {
        memberAuthorizeRelieve
    } from "../../config/api";

    export default {
        data() {
            return {
                id: '',
                details: ''
            }
        },
        onLoad(options) {
            this.id = options.id
            this.authorizeShow()
        },
        methods: {
            relieve() {
                var _this = this;

                if (_this.details.but.send_audit_status != 1) {
                    return;
                }

                var _this = this;
                uni.showModal({
                    title: '解除授权审核', // 标题
                    // editable: true, // 开启输入框
                    // placeholderText: '请输入审核意见', // 输入框提示语
                    confirmText: "确定",
                    cancelText: "取消",
                    success: (res) => {
                        if (res.confirm) {

                            memberAuthorizeRelieve({
                                id: _this.id,
                            }).then(response => {
                                uni.$u.toast(response.message);
                                if (response.status == '200') {
                                    this.getAuthorize();
                                }
                            });
                        } else if (res.cancel) {

                        }
                    }
                })

            },

            cancelSubmit() {
                // 弹窗输入
                var _this = this;
                uni.showModal({
                    title: '解除授权审核', // 标题
                    editable: true, // 开启输入框
                    placeholderText: '请输入审核意见', // 输入框提示语
                    confirmText: "同意",
                    cancelText: "驳回",
                    success: (res) => {
                        if (res.confirm) {
                            // 如果没有输入内容
                            if (!res.content) {
                                return uni.$u.toast('请输入原因')
                            }
                            // 后续处理
                            auditMemberAuthorizeHandel({
                                remark: res.content,
                                id: _this.id,
                                authorize_audit_status: 1,
                            }).then(response => {
                                uni.$u.toast(response.message);
                                uni.navigateBack();
                            });
                        } else if (res.cancel) {

                            // 后续处理
                            auditMemberAuthorizeHandel({
                                remark: res.content,
                                id: _this.id,
                                authorize_audit_status: 3,
                            }).then(response => {
                                uni.$u.toast(response.message);
                                uni.navigateBack();
                            });
                        }
                    }
                })

            },
            //简历详情
            async authorizeShow() {
                let params = {
                    id: this.id
                }
                const res = await authorizeShow(params)
                if (res.status_code == 200) {
                    console.log("res", res)
                    this.details = res.data
                }
            },

            edit() {
                uni.navigateTo({
                    url: "/pagesA/add/HeadhunterAddResume?member_id=" + this.details.member_id,
                })
            }
        }
    }
</script>
<style lang="scss" scoped>
    @import "../../static/css/pagesA/details/authApplicantDetails.scss";
</style>