.flex {
	display: flex;
}

.PM {
	font-family: 'PingFang SC-Medium';
}

.PR {
	font-family: 'PingFang SC-Regular';
}

.PS {
	font-family: 'PingFang SC-Semibold';
}

.flex-1 {
	flex: 1;
}

.flex-col {
	flex-direction: column;
}

.flex-wrap {
	flex-wrap: wrap;
}

.flex-nowarp {
	flex-wrap: nowrap;
}

.justify-center {
	justify-content: center;
}

.justify-between {
	justify-content: space-between;
}

.justify-end {
	justify-content: end;
}

.justify-start {
	justify-content: start;
}

.items-center {
	align-items: center;
}

.items-start {
	align-items: start;
}

.items-end {
	align-items: end;
}

.self-end {
	align-self: flex-end;
}

.self-start {
	align-self: flex-start;
}

.bg-gray {
	background-color: #F5F5F7;
}

.bg-gray-500 {
	background-color: #D9D9D9;
}

.bg-whiter {
	background-color: #FFFFFF;
}

.bg-blue-200 {
	background-color: #EBF3FF;
}

.bg-blue {
	background-color: #4F8CF0;
}

.font-bold {
	font-weight: bold;
}

.text-blue {
	color: #4F8CF0;
}

.text-white {
	color: #FFFFFF;
}

.text-black {
	color: #333333;
}

.text-gray {
	color: #999999;
}

.text-origin {
	color: #F98A14;
}

.w-full {
	width: 100%;
}

.h-full {
	height: 100vh;
}

.text-2 {
	font-size: 16rpx;
}

.text-3 {
	font-size: 24rpx;
}

.text-4 {
	font-size: 32rpx;
}

.rounded-lg {
	border-radius: 12rpx;
}

.rounded-xl {
	border-radius: 24rpx;
}

.gap-5 {
	gap: 40rpx;
}

.gap-4 {
	gap: 32rpx;
}

.gap-3 {
	gap: 24rpx;
}

.gap-2 {
	gap: 16rpx;
}

.gap-1 {
	gap: 8rpx;
}

.rounded-100 {
	border-radius: 100%;
}

.p-5 {
	padding: 40rpx;
}

.p-4 {
	padding: 32rpx;
}

.p-3 {
	padding: 24rpx;
}

/* 横轴编剧 */
.px-5 {
	padding-left: 40rpx;
	padding-right: 40rpx;
}

.px-4 {
	padding-left: 32rpx;
	padding-right: 32rpx;
}

.px-3 {
	padding-left: 24rpx;
	padding-right: 24rpx;
}

/* 侧轴编剧 */
.py-5 {
	padding-top: 40rpx;
	padding-bottom: 40rpx;
}

.py-4 {
	padding-top: 32rpx;
	padding-bottom: 32rpx;
}

.py-3 {
	padding-top: 24rpx;
	padding-bottom: 24rpx;
}

/* 左边距 */
.pl-5 {
	padding-left: 40rpx;
}

.pl-4 {
	padding-left: 32rpx;
}

.pl-3 {
	padding-left: 24rpx;
}

/* 右边距 */
.pr-5 {
	padding-right: 40rpx;
}

.pr-4 {
	padding-right: 32rpx;
}

.pr-3 {
	padding-right: 24rpx;
}

/* 顶部内边距类 */
.pt-5 {
	padding-top: 40rpx;
}

.pt-4 {
	padding-top: 32rpx;
}

.pt-3 {
	padding-top: 24rpx;
}

/* 底部内边距类 */
.pb-5 {
	padding-bottom: 40rpx;
}

.pb-4 {
	padding-bottom: 32rpx;
}

.pb-3 {
	padding-bottom: 24rpx;
}

.overflow-y-auto {
	overflow-y: auto;
}

.ov-t {
	white-space: nowrap;
	/* 强制文本不换行 */
	overflow: hidden;
	/* 溢出内容隐藏 */
	text-overflow: ellipsis;
	/* 溢出部分显示省略号 */
}

.border-b {
	border-bottom: 2rpx solid #F5F5F7;
}

.ov-t-multi {
	display: -webkit-box;
	/* 必须结合的属性 */
	-webkit-box-orient: vertical;
	/* 必须结合的属性 */
	-webkit-line-clamp: 2;
	/* 控制显示的行数 */
	overflow: hidden;
	text-overflow: ellipsis;
}