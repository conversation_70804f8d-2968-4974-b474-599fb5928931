<template>
	<view class="home-index">
		<view class="swiper">
			<image :src="details.job_active.image.path_url" mode=""></image>
		</view>

		<view class="wrap">
			<view class="active-info">
				<view class="title">
					<view class="name">
						{{ details.job_active.title }}
					</view>
					<view
						:class="[
							'status',
							details.job_active.active_status == 'started'
								? 'ing'
								: details.job_active.active_status == 'end'
								? 'over'
								: details.job_active.status == 3
								? 'bm'
								: details.job_active.status == 4
								? 'wks'
								: '',
						]">
						{{ details.job_active.active_status_name }}
					</view>
				</view>
				<view class="time">活动发布时间：{{ details.job_active.created_at }}</view>
				<view class="notice">
					<u-notice-bar bgColor="#F5F5F7" color="#333333" :text="notice"></u-notice-bar>
				</view>
			</view>

			<view class="sub-wrap" v-if="roleType != 'company'">
				<view class="title">
					<view class="">参与企业</view>
					<view class="title-right" @click="toCom(details)">
						<text>更多企业</text>
						<u-icon name="arrow-right" size="28rpx"></u-icon>
					</view>
				</view>
				<!-- <view class="companyBox" v-if="roleType!='company'">
			        <view class="companyChild" v-for="(item,index) in companyList.slice(0, 4)" :key="index" :item="item">
						<image :src="item.company_info.logo.path_url" mode="" class="companyImg"></image>
			            <view class="company">
			            	<view class="companyName">{{ item.company.name}}</view>
							<view class="companyBtn" @click="companyDetail(item.company.id)">详情</view>
			            </view>
			        </view>
			    </view>
			    <view class="companyBox" v-else>
			        <view class="companyChild" v-for="(item,index) in companyList" :key="index" :item="item">
			            <image :src="item.company_info.logo.path_url" mode="" class="companyImg"></image>
			            <view>{{ item.company.name}}</view>
			            <view class="companyBtn">详情</view>
			        </view>
			    </view> -->
			</view>

			<view class="sub-wrap" v-if="roleType != 'company'">
				<view class="title">
					<view class="">招聘会职位</view>
					<view class="title-right" @click="toComJobList(details)">
						<text>更多职位</text>
						<u-icon name="arrow-right" size="28rpx"></u-icon>
					</view>
				</view>

				<!-- <view class="companyBox" v-if="roleType!='company'">
			        <view class="companyChild" v-for="(item,index) in jobList.slice(0, 4)" :key="index" :item="item">
						<image :src="item.company_info.logo.path_url" mode="" class="companyImg"></image>
			            <view class="company">
			            	<view class="companyName">{{ item.job.title}}</view>
							<view class="companyBtn" @click="companyDetail(item.company.id)">详情</view>
			            </view>
			        </view>
			    </view>
			    <view class="companyBox" v-else>
			        <view class="companyChild" v-for="(item,index) in companyList" :key="index" :item="item">
			            <image :src="item.company_info.logo.path_url" mode="" class="companyImg"></image>
			            <view>{{ item.company.name}}</view>
			            <view class="companyBtn">详情</view>
			        </view>
			    </view> -->
			</view>

			<view class="sub-wrap">
				<view class="title">活动信息</view>
				<view class="desc">
					{{ details.job_active.intro }}
				</view>
			</view>

			<view class="sub-wrap">
				<view class="title">活动时间</view>
				<view class="times">
					<view class="time">
						{{ details.job_active.start }}
					</view>
					<view class="line">----</view>
					<view class="time">
						{{ details.job_active.end }}
					</view>
				</view>
			</view>

			<view class="sub-wrap">
				<view class="title">活动地点</view>
				<view class="pos" @click="openNavigation(details.address[0])">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png" mode=""></image>
					{{ details.address[0].address }}
				</view>
				<view class="map">
					<map style="width: 100%; height: 300rpx" :latitude="latitude" :longitude="longitude" :markers="covers"></map>
				</view>
			</view>
		</view>

		<view class="footer" v-if="roleType != 'member'">
			<view class="btns">
				<view class="btn zx" @click.stop="communicate('user_headhunter', details.job_active.user_id)">咨询主办方</view>
				<view class="btn agree" @click="signup" v-if="details.but.report_but == 1 && details.report_status == 2">立即报名</view>
				<!-- <view class="btn agree" @click="" v-if="roleType=='headhunters'">
				    代公司报名
				</view> -->
				<!-- <view class="btn agree" v-if="details.but.report_but==2">
                    已报名
                </view> -->
				<!-- <view class="btn agree" @click="pubJob">
                    发布职位
                </view> -->
				<view class="btn over" v-if="details.job_active.active_status_name == '已结束'">
					{{ details.job_active.active_status_name }}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { communicate, isAuth } from '../../common/common';
import { getQLMJobfairDetails } from '../../config/api.js';
export default {
	data() {
		return {
			jobActiveId: 0,
			roleType: '',
			details: {},
			page: 1,
			limit: 10,
			notice: '*****公司已加入本次招聘会，欢迎各位同学踊跃参加！',
			latitude: 39.909,
			longitude: 116.39742,
			covers: [
				{
					latitude: 39.909,
					longitude: 116.39742,
				},
			],
			companyList: [],
			jobList: [],
		};
	},
	onLoad(options) {
		this.jobActiveId = options.id;
		this.roleType = uni.getStorageSync('roleType');
	},
	onShow() {
		this.getHeadhuntersJobfairDetails();
		// this.getJobFairCompany();
		// this.getJobList()
	},
	computed: {
		roleType() {
			console.log('当前用户的身份：', uni.getStorageSync('roleType'));
			return this.$store.state.roleType || uni.getStorageSync('roleType');
		},
		userInfo() {
			return this.$store.state.userInfo || uni.getStorageSync('userInfo');
		},
	},
	methods: {
		// 分享功能
		onShareAppMessage(res) {
			const pages = getCurrentPages(); //获取当前页面的参数
			// pages[0].$page.fullPath//当前页面路径及页面参数
			if (res.from === 'button') {
				// 来自页面内分享按钮
				console.log(res.target);
			}
			return {
				title: this.details.job_active.title,
				path: pages[pages.length - 1].$page.fullPath,
			};
		},
		// 导航方法
		openNavigation(address) {
			// console.log('22223333', address)
			wx.openLocation({
				latitude: parseFloat(address.lat), // 纬度，浮点数，范围为-90~90
				longitude: parseFloat(address.lng), // 经度，浮点数，范围为-180~180
				name: address.map_address || '面试地点', // 位置名
				scale: 18, // 地图缩放级别
			});
		},
		communicate,
		companyDetail(companyId) {
			uni.navigateTo({
				url: '/pagesA/details/companyDetail?id=' + companyId,
			});
		},
		//立即报名
		signup() {
			if (!isAuth(['login', 'info', 'auto']) && roleType == 'company') return;
			var _this = this;
			uni.navigateTo({
				url: '/pagesA/add/activeSignup?id=' + _this.details.job_active.id,
			});
		},

		//报名以后可以发布职位
		pubJob() {
			uni.navigateTo({
				url: '/pagesA/add/pubJobOne',
			});
		},

		//获取招聘会详情
		async getHeadhuntersJobfairDetails() {
			let params = {
				id: this.jobActiveId,
			};
			const { status_code, data, message } = await getQLMJobfairDetails(params);
			if (status_code == 200) {
				this.details = data;
				console.log('this.details', this.details);
				//重新定位地图
				this.longitude = data.address[0].lng;
				this.latitude = data.address[0].lat;
				this.covers[0]['latitude'] = data.address[0].lat;
				this.covers[0]['longitude'] = data.address[0].lng;
			}
		},

		//获取招聘会参与企业
		// async getJobFairCompany() {
		//     let params = {
		//         id: this.jobActiveId,
		//     }
		//     const {
		//         status_code,
		//         data,
		//         message
		//     } = await getJobFairCompany(params)
		//     if (status_code == '200') {
		//         this.companyList = data;
		//     }
		// },

		// 获取招聘会职位列表
		// async getJobList() {
		// 	let params = {
		// 		limit: this.limit,
		// 		page: this.page,
		// 		id: this.jobActiveId
		// 	}
		// 	const {
		// 		status_code, data
		// 	} = await activeJobList(params)
		// 	if (status_code == 200) {
		// 		this.jobList = this.jobList.concat(data.data);
		// 		this.more = data.more
		// 	}
		// },

		// 更多企业
		toCom() {
			console.log(111);
			uni.navigateTo({
				url: '/pagesA/list/join_company-list?id=' + this.jobActiveId,
			});
		},

		// 更多职位
		toComJobList() {
			console.log(111);
			uni.navigateTo({
				url: '/pagesA/list/active_job_list?id=' + this.jobActiveId,
			});
		},
	},
};
</script>

<style lang="scss" src="../../static/css/pagesA/details/blActiveDetails.scss"></style>
