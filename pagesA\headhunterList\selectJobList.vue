<template>
	<view id="app">
		<!-- 多选 -->
		<!-- <view :class="['listBox',{ selected: isSelected(item.id) }]" v-for="(item,index) in list" :key="item.id"
			@click="selectedJog(item)">
			{{item.title}}
		</view>

		<view class="footer">
			<view class="next sure" @click="add">
				确定
			</view>
		</view> -->
		<view :class="['listBox', { selected: selectedItem && selectedItem.id === item.id }]"
			v-for="(item, index) in list" :key="item.id" @click="selectItem(item)">
			{{ item.title }}
		</view>

		<view class="footer">
			<view class="next sure" @click="add">
				确定
			</view>
		</view>

	</view>
</template>

<script>
	import {
		getCompanyJobList
	} from "../../config/api.js"

	export default {
		data() {
			return {
				title: '',
				page: 1,
				limit: 10,
				list: [],
				selectedItems: [], // 多选
				selectedItem: null, // 用于存储单选项
				statusList: [{
					value: 'all',
					name: '全部',
				}, {
					value: 'active',
					name: '招聘中',
				}, {
					value: 'draft',
					name: '草稿',
				}, {
					value: 'off_shelf',
					name: '已下架',
				}],
				statusIndex: 0
			}
		},

		onLoad() {
			this.getCompanyJobList()
		},
		onShow() {},

		methods: {
			selectItem(item) {
				// 如果当前选中的项与点击的项相同，则取消选中
				if (this.selectedItem && this.selectedItem.id === item.id) {
					this.selectedItem = null;
				} else {
					// 否则，将当前项设置为选中项
					this.selectedItem = item;
				}
			},
			// 多选
			// selectedJog(item) {
			// 	const index = this.selectedItems.findIndex(selected => selected.id === item.id);
			// 	if (index >= 0) {
			// 		this.selectedItems.splice(index, 1);
			// 	} else {
			// 		this.selectedItems.push({
			// 			id: item.id,
			// 			title: item.title
			// 		});
			// 	}
			// },
			isSelected(id) {
				return this.selectedItems.some(item => item.id === id);
			},
			add() {
				if (this.selectedItem) {
					uni.setStorageSync('selectedItems', {id:this.selectedItem.id,title:this.selectedItem.title})
				}
				// 多选存本地
				// uni.setStorageSync('selectedItems', this.selectedItems)
				uni.navigateBack()
			},
			//获取列表
			async getCompanyJobList() {
				let params = {
					page: this.page,
					limit: this.limit,
					status: '', //审核状态：1-通过，2-待审核，3-驳回
					list_status: this.statusList[this.statusIndex][
						'value'
					], //列表状态：默认：alll，全部： all	当前登录账号， 所有已发布的职位	招聘中 active	当前账号发布职位中， 状态为 已上架的职位	下架 off_shelf	当前账号发布职位中， 状态为 已下架的职位	草稿 draft	当前账号发布职位中， 提交状态为草稿的职位
				}
				const {
					status_code,
					data
				} = await getCompanyJobList(params)
				if (status_code == 200) {
					this.list = data.data
				}
			},
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	view {
		box-sizing: border-box;
	}

	#app {
		width: 100%;
		padding: 32rpx;
	}

	.listBox {
		width: 100%;
		padding: 24rpx;
		margin-bottom: 24rpx;
		background: #fff;
		border-radius: 24rpx;
		border: 2rpx solid transparent;
	}

	.listBox.selected {
		border: 2rpx solid #4F8CF0;
		/* Change border color when selected */
	}

	.footer {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		height: 120rpx;
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 24rpx 24rpx 0 0;
		z-index: 10;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #FFFFFF;
			height: 88rpx;
			width: 90%;
			border-radius: 16rpx;
		}

		.sure {
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	}
</style>