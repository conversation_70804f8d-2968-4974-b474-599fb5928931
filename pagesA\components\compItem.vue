<template>
	<view class="item" @click="goCompDetails">
		<view class="item-up">
			<image :src="item.company_info.logo.path_url" mode=""></image>
			<view class="info">
				<view class="user">
					<view class="userInfo">
						<view class="name">
							{{item.company.name.length<10?item.company.name:item.company.name.substr(0,12)+'...'}}
						</view>
					</view>
				</view>
				<view class="flags">
					<view class="flag">
						{{item.company.short_name}}
					</view>
					<view class="flag">
						{{item.company_info.financing_type_name}}
					</view>
					<view class="flag">
						{{item.company_info.size_type_name}}
					</view>
				</view>

				<view class="tags">
					<view class="tag" v-for="(sub,idx) in item.tags" :key="idx">
						{{sub.title}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "compItem",
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {

			};
		},
		methods: {
			goCompDetails() {
				uni.navigateTo({
					url: "/pagesA/details/companyDetail?id=" + this.item.company.id
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		padding: 32rpx;
		margin-bottom: 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;

		.item-up {
			display: flex;

			&>image {
				width: 144rpx;
				height: 144rpx;
				border-radius: 16rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				flex: 1;
				padding-left: 24rpx;

				.user {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.userInfo {
						display: flex;
						align-items: center;

						.name {
							font-weight: 600;
							font-size: 32rpx;
							color: #333333;
						}
					}
				}

				.flags {
					display: flex;

					.flag {
						display: flex;
						align-items: center;
						padding: 0 12rpx;
						font-weight: 400;
						font-size: 22rpx;
						color: #999999;
						border-right: 1px solid #999999;

						&:first-child {
							padding-left: 0;
						}

						&:last-child {
							border-right: none;
						}
					}
				}

				.tags {
					display: flex;

					.tag {
						display: flex;
						align-items: center;
						background: #F6F6F6;
						border-radius: 8rpx;
						height: 46rpx;
						padding: 0 12rpx;
						font-weight: 400;
						font-size: 22rpx;
						color: #666666;
						margin-right: 10rpx;

						&:last-child {
							margin-right: 0;
						}
					}
				}
			}
		}
	}
</style>