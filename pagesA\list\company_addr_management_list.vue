<template>
	<view class="main">
		<view class="list">
			<u-swipe-action>
				<u-swipe-action-item :options="options" v-for="(item,index) in list" :name="index" :key="item.id"
					@click="handleSwipe(item,$event)">
					<!-- <addr-item :item="item" @setDefaultAddress="setDefaultAddress" :key="index"></addr-item> -->
					<company-addr-item :item="item"  :key="index"></company-addr-item>
				</u-swipe-action-item>
			</u-swipe-action>
		</view>

		<view class="footer">
			<view class="next sure" @click="add">
				新建地址
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCompanyAddressList,
		delCompanyAddress,
		setDefaultCompanyAddress
	} from "../../config/api.js"
	import CompanyAddrItem from '../components/companyAddrItem.vue'
	export default {
		components: {
			CompanyAddrItem
		},
		data() {
			return {
				page: 1,
				limit: 10,
				options: [{
					text: '删除',
					style: {
						backgroundColor: '#FE4D4F',
						borderRadius: '24rpx',
						bottom: '32rpx',
						height: '100%',
						width: '150rpx',
						marginLeft: '24rpx'
					}
				}],

				list: []
			}
		},

		watch: {
			list: {
				handler(newValue, oldValue) {},
				immediate: true,
				deep: true
			}
		},

		onShow() {
			this.getCompanyAddressList()
		},

		methods: {
			add() {
				uni.navigateTo({
					url: "/pagesA/add/addCompanyAddress"
				})
			},
			//地址列表
			async getCompanyAddressList() {
				let params = {
					page: this.page,
					limit: this.limit
				}
				const {
					status_code,
					data
				} = await getCompanyAddressList(params)
				if (status_code == 200) {
					this.list = data.data;
					console.log(this.list, "数据长度")
				}

			},
			//删除
			async handleSwipe(item, e) {
				let self = this;
				//表示点击了删除按钮
				if (e.index == 0) {
					uni.showModal({
						title: '确定要删除该地址吗？',
						success: async (res) => {
							if (res.confirm) {
								console.log('用户点击确定');
								let params = {
									id: item.id
								}
								const {
									status_code,
									data
								} = await delCompanyAddress(params)
								if (status_code == 200) {
									self.page = 1
									self.getCompanyAddressList()
									return uni.$u.toast('成功')
								}
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					})
				}
			},
			//设置默认地址
			async setDefaultCompanyAddress(op) {
				let params = {
					id: op.id
				}
				const {
					status_code,
					data,
					message
				} = await setDefaultCompanyAddress(params)
				if (status_code == 200) {
					this.page = 1
					this.getCompanyAddressList()
					return uni.$u.toast('成功')
				}

			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.main {
		padding-bottom: 120rpx;
	}

	.list {
		padding: 32rpx 32rpx 0 32rpx;

		::v-deep .u-swipe-action-item__right {
			bottom: 32rpx;
			border-radius: 24rpx;
		}

		::v-deep .u-swipe-action-item__content {
			background: transparent;
		}
	}

	.footer {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		height: 120rpx;
		width: 100%;
		background-color: #FFFFFF;
		z-index: 10;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			font-size: 28rpx;
			font-weight: 600;
			border-radius: 16rpx;
		}

		.sure {
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	}
</style>