<template>
    <view id="app">
        <view class="content" v-for="(item,index) in list">
            <view class="title blueCla">记录{{index+1}}</view>
            <view class="contentChild">
                <view class="title">职位名称</view>
                <view class="contentText">{{item.job.title}}</view>
            </view>
            <view class="contentChild">
                <view class="title">面试时间</view>
                <view class="contentText">{{item.interviewed_at}}</view>
            </view>
            <view class="contentChild">
                <view class="title">联系人</view>
                <view class="contentText">{{item.contact_name}}</view>
            </view>
            <view class="contentChild">
                <view class="title">联系人电话</view>
                <view class="contentText">{{item.contact_cellphone}}</view>
            </view>
            <view class="contentChild">
				<!-- 无地图不加导航 -->
                <view class="title">面试地址</view>
                <view class="contentText">{{item.addresses[0].map_address}}</view>
            </view>
            <view class="contentChild" v-if="item.remark">
                <view class="title">面试备注</view>
                <view class="contentText">{{item.remark}}</view>
            </view>
<!--            <view class="contentChild">
                <view class="title">状态</view>
                <view class="contentText">这里放状态</view>
            </view> -->
			
            <view class="contentChild">
                <view class="title">面试结果</view>
                <view class="contentText">{{item.interview_result.result_status_name}}</view>
            </view>
        </view>
    </view>
</template>

<script>
    import {shareInterview} from '../../config/common_api.js'
    export default {
        data() {
            return {
                share_id:'',
                list:[],
                page:1,
                limit:10,
                status:'loading',
                
            }
        },
        onLoad(option) {
            this.share_id = option.id
            this.shareInterview(this.share_id)
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.shareInterview()
            } else {
                this.status = 'nomore'
            }
        },
        methods: {
            async shareInterview() {
                let params = {
                    page:this.page,
                    limit:this.limit,
                    share_id:this.share_id*1
                }
                const res = await shareInterview(params)
                console.log("res",res)
                this.list = this.list.concat(res.data.data);
                // // 返回false代表没有下一页
                this.more = res.data.more;
                this.status = this.more ? "loadmore" : "nomore"
            }
        },
    }
</script>
<style>
    page {
        background: #f5f5f7;
    }
</style>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }
    #app {
        width: 100%;
        padding: 32rpx;
    }
    
    .content {
        width: 100%;
        padding: 24rpx;
        background: #fff;
        border-radius: 24rpx;
        margin-bottom: 24rpx;
    }
    
    .contentChild {
        border-bottom: 2rpx solid #ccc;
        padding: 16rpx 0;
    }
    
    .title {
        font-weight: bold;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 8rpx;
    }
    
    .contentText {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }
    
    .blueCla {
        color: #4F8CF0 !important;
    }
</style>