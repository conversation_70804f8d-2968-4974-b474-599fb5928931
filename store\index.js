import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

const store = new Vuex.Store({
    state: {
        roleType: "", //千里马=member  伯乐=company  就业管家=headhunters
        selected: 0, //tabbar索引
        userInfo: null, //用户信息
        sysData: null, //系统选项数据
        cityList: null, //省市区数据
        provinceCityList: null, //省市数据
        companyAuthorize: null, // 授权企业
        nineObj: {
            flag: "", //身份：student-学生，work-我是职场人
            type: "",
            industry_id: [] //行业id
        },
        pubJobOne: null,
        pubJobTwo: null,
        register_headhunters_one: null,
        collaborator: [], //协作人
        executor: [], //执行人
        ws_url: '',
        selectedCity: {}, //选中的城市
        chatHistory: {}, //聊天记录
        tabChat: uni.getStorageSync('tabChat') || '' // 从本地存储中获取初始值
    },
    mutations: {
        setTabChat(state, newValue) {
            state.tabChat = newValue;
            uni.setStorageSync('tabChat', newValue); // 更新本地存储
        },
        //登录用户信息
        setUserInfo(state, userInfo) {
            state.userInfo = userInfo
            uni.setStorageSync('userInfo', userInfo)
        },
        setWsUrl(state, wsUrl) {
            state.ws_url = wsUrl
            uni.setStorageSync('ws_url', wsUrl)
        },
        //设置token
        setToken(state, token) {
            state.token = token
            uni.setStorageSync('token', token)
        },
        //设置角色类型
        setRoleType(state, type) {
            state.roleType = type
            uni.setStorageSync('roleType', type)
        },
        //设置系统用户数据
        setSysData(state, data) {
            state.sysData = data
            uni.setStorageSync('sysData', data)
        },
        //设置系统用户数据
        setCityList(state, data) {
            state.cityList = data
            uni.setStorageSync('cityList', data)
        },
        setProvinceCityList(state, data) {
            state.provinceCityList = data
            uni.setStorageSync('provinceCityList', data)
        },
        setCompanyAuthorize(state, data) {
            state.companyAuthorize = data;
        },
        //更新选中的selected
        setSelected(state, index) {
            state.selected = index
        },

        //发布职位第一步
        setPubJobOne(state, data) {
            state.pubJobOne = data
        },


        //猎头注册第一步
        setHeadhuntersRegOne(state, data) {
            state.register_headhunters_one = data
        },

        //设置协作人
        setCollaborator(state, arr) {
            state.collaborator = arr;
        },

        //设置执行人
        setExecutor(state, arr) {
            state.executor = arr
        },
        // 选中的城市
        setSelectedCity(state, cityData) {
            state.selectedCity = cityData;
        },
        // 当前聊天记录
        setChatHistory(state, chatHistory) {
            state.chatHistory = chatHistory;
            uni.setStorageSync('chatHistory', chatHistory)

        },
    },
    actions: {
        //相当于异步的操作,不能直接改变state的值，只能通过触发mutations的方法才能改变
    }
})
export default store
