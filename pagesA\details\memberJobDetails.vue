<template>
    <view class="main">
        <view class="wrap">
            <view class="job-info">
                <view class="job-name">
                    <text class="job">{{details.title}}</text>
                    <text class="money">
                        {{details.salary_info_str}}
                    </text>
                </view>
                <view class="tags">
                    <view class="tag">
                        <image src="/static/images/index/addr.png" mode=""></image>
                        <text>{{details.addresses[0].city_name}}·{{details.addresses[0].district_name}}</text>
                    </view>
                    <view class="tag">
                        <image src="/static/images/index/package.png" mode=""></image>
                        <text>{{details.experience_name}}</text>
                    </view>
                    <view class="tag">
                        <image src="/static/images/index/doctor.png" mode=""></image>
                        <text>{{details.education_str}}</text>
                    </view>
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="pub-info">
                <image
                    :src="details.send_user_member_info.image['path_url']?details.send_user_member_info.image['path_url']:'https://api-test.zhaopinbei.com/storage/uploads/images/41R1LgOXldsXlyAjelHeNXsb0SuBPKE7ANcgobdG.png'"
                    mode=""></image>
                <view class="users">
                    <view class="username">
                        {{details.send_user_member_certification.name}}
                    </view>
                    <view class="address">
                        {{details.company_name}}
                    </view>
                </view>
            </view>
        </view>

        <view class="wrap">
            <view class="sub-wrap">
                <view class="sub-title">
                    就业方式
                </view>
                <view class="desc">
                    <text style="margin-right: 16rpx;" v-for="(item,index) in details.work_types"
                        :key="index">{{item.name}} </text>
                </view>
            </view>
            <view class="sub-wrap">
                <view class="sub-title">
                    职位内容
                </view>
                <view class="desc">
                    {{details.job_describe}}
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <address-map title="职位地址" :addresses="details.addresses"></address-map>
        </view>

        <view class="wrap" @click="goCompanyPage(details.company_id)">
            <view class="title">
                公司信息
            </view>
            <view class="compony-info">
                <image :src="details.company_info.logo.thumbnail_path_url" mode=""></image>
                <view class="info">
                    <view class="name">
                        {{details.company_name}}
                    </view>
                    <view class="tags">
                        <view class="tag">
                            {{details.company_info.size_type_name}}
                        </view>
                        <view class="tag">
                            {{details.company_info.financing_type_name}}
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- <view class="wrap" v-show="roleType=='member'">
            <view class="title">
                职位问答
            </view>
            <view class="answers">
                <view class="answer" v-for="(item,index) in questionList" :key="index">
                    <view class="ans">
                        <image src="/static/images/index/ans.png" mode=""></image>
                        <text>{{item.content}}</text>
                    </view>
                    <block v-if="item.childes && item.childes.length>0">
                        <view class="qus" v-for="(sub,idx) in item.childes" :key="idx">
                            <image src="/static/images/index/qus.png" mode=""></image>
                            <text>{{sub.content}}</text>
                        </view>
                    </block>
                </view>
            </view>
            <view class="more" @click="loadMore">
                更多 <image src="/static/images/index/down.png" mode=""></image>
            </view>
        </view> -->

        <view class="footer">
            <block>
                <view class="favor ysc" v-if="details.collect_status==1" @click="memberCancelCollectJob">
                    <u-icon name="star-fill" color="#F9AD14"></u-icon>
                    已收藏
                </view>
                <view class="favor" v-if="details.collect_status==2" @click="memberCollectJob">
                    <u-icon name="star-fill" color="#999999"></u-icon>
                    收藏
                </view>
                <view class="btns">

                    <view class="btn" v-if="details.report_status==2" @click="openResume">
                        立即报名
                    </view>
                    <view class="btn cancel" v-if="details.report_status==1" @click="openReason">
                        取消报名
                    </view>
                    <view class="btn agree" @click="communicate('job',details.id)">
                        聊聊呗
                    </view>
                </view>
            </block>
        </view>


        <u-popup :show="showResume" :round="10" bgColor="#F5F5F5" mode="bottom" closeOnClickOverlay @close="closeResume"
            @open="openResume">
            <view class="credit">
                <view class="title comp">
                    请选择发送简历
                </view>
                <view class="content">
                    <view class="btns">
                        <view :class="['btn',resumeIndex==index?'active':'']" v-for="(item,index) in resumeList"
                            :key="index" @click="changeResume(index)">
                            <text class="">{{item.member_resume.resume_name}}</text>
                        </view>
                    </view>
                </view>

                <view class="agree" @click="signup">
                    确定
                </view>
            </view>
        </u-popup>

        <u-popup :show="showReason" :round="10" bgColor="#F5F5F5" mode="center" closeOnClickOverlay @close="closeReason"
            @open="openReason">
            <view class="credit" style="width: 70vw;">
                <view class="title comp">
                    取消原因
                </view>
                <view class="content">
                    <u--textarea v-model="reason" placeholder="请输入内容"></u--textarea>
                </view>

                <view class="agree" @click="cancelSignup">
                    确定
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
    import {
        isAuth
    } from '@/common/common.js'
import {communicate} from "../../common/common.js";
    import {
        getJobDetails,
        getQuestionList,
        memberCancelCollectJob,
        memberCollectJob,
        getResumeList,
        signup,
        cancelSignup,
        getSysList
    } from "../../config/api.js"
    import AddressMap from "../../public_label/addressMap.vue";
    import {
        authChat
    } from "../../config/common_api";
    export default {
        components: {
            AddressMap
        },
        data() {
            return {
                reason: '',
                more: false,
                page: 1,
                limit: 10,
                resumeIndex: 0,
                resumeList: [],
                questionList: [],
                id: '',
                // compIndex: 1,
                showReason: false,
                showResume: false,
                latitude: 39.909,
                longitude: 116.39742,
                model_type: 'job',
                model_id: '',
                covers: [{
                    id: 1,
                    latitude: 39.909,
                    longitude: 116.39742,
                    // iconPath: '../../../static/location.png'
                }],
                details: {},
                model_user_id: ''
            }
        },
        computed: {
            roleType() {
                return this.$store.state.roleType || uni.getStorageSync('roleType')
            }
        },
        onLoad(options) {
            if(uni.getStorageSync('sharePage')) {
                uni.removeStorageSync('sharePage')
            }
            this.id = options.id
            this.model_type = options.model_type ? options.model_type : 'job'
            this.model_id = options.model_id ? options.model_id : this.id
            this.model_user_id = options.model_user_id ? options.model_user_id : ''
            this.getJobDetails()
            this.getQuestionList()
            if(!uni.getStorageSync('sysData')) {
                this.getSysData()
            }

        },
        methods: {
            // 分享功能
            onShareAppMessage(res) {
                const pages = getCurrentPages();//获取当前页面的参数
                // pages[0].$page.fullPath//当前页面路径及页面参数
            	if (res.from === 'button') { // 来自页面内分享按钮
            		console.log(res.target)
            	}
            	return {
            		title: this.details.title,
            		path: pages[pages.length - 1].$page.fullPath,
            	}
            },
            //系统选项数据
            async getSysData() {
                const result = await getSysList()
                // this.sysData = result;
                this.$store.commit('setSysData', result);
            },
            selectPeople() {
                uni.navigateTo({
                    url: '/pagesA/list/signUpPeopleList?id=' + this.id
                })
            },
            goCompanyPage(id) {
                uni.navigateTo({
                    url: '/pagesA/details/companyDetail?id=' + id
                })
            },
            edit() {
                uni.navigateTo({
                    url: "/pagesA/add/pubJobOne?id=" + this.id
                })
            },

            openResume() {
                if (!uni.getStorageSync('roleType')) {
                   return uni.showModal({
                        title: '是否前去登录',
                        success: async function(res) {
                            if (res.confirm) {
                                const pages = getCurrentPages();
                                uni.setStorageSync('sharePage',pages[pages.length - 1].$page.fullPath)
                                uni.reLaunch({
                                    url: '/pages/login/login'
                                })
                                return
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                                return
                            }
                        }
                    })

                }
                if (!isAuth(["login", "info"])) return
                if (this.roleType == 'headhunters') {
                    this.selectPeople()
                    return
                }
                this.getResumeList()
            },

            closeResume() {
                this.showResume = false
            },

            changeResume(index) {
                this.resumeIndex = index
            },

            openReason() {
                this.showReason = true
            },

            closeReason() {
                this.reason = ''
                this.showReason = false
            },
            communicate,
            // communicate(type,id) {
            //     if (!isAuth(["login", "info", "auth"])) return

            //     let params = {
            //         type: 'job',
            //         id: this.id
            //     }
            //     const {
            //         status_code,
            //         message,
            //         data
            //     } = await authChat(params)
            //     if (status_code == 200) {
            //       console.log('创建聊天',data)
            //       // uni.navigateTo({
            //       //   url: `/pages/message/message?name=${item.name}&avatar=${item.avatar}&chatId=${item.chat_id}`
            //       // })
            //     }
            // },
            //收藏
            async memberCollectJob() {
                if (!isAuth(["login"])) return

                let params = {
                    id: this.id
                }
                const {
                    status_code,
                    message
                } = await memberCollectJob(params)
                if (status_code == 200) {
                    uni.$u.toast(message || '成功')
                    this.getJobDetails()
                }
            },

            //取消收藏
            async memberCancelCollectJob() {
                let params = {
                    id: this.id
                }
                const {
                    status_code,
                    message
                } = await memberCancelCollectJob(params)
                if (status_code == 200) {
                    uni.$u.toast(message || '成功')
                    this.getJobDetails()
                }
            },

            //获取简历
            async getResumeList() {
                const res_userInfo = uni.getStorageSync('userInfo')
                if (res_userInfo == '') return
                if (!isAuth(['info','resume'])) return
                let params = {
                    limit: 10,
                    page: 1,
                    type: 'resume'
                }

                const {
                    status_code,
                    data,
                    message
                } = await getResumeList(params)
                if (status_code == 200) {
                    this.resumeList = data.data;
                    this.showResume = true
                }
            },

            //报名
            async signup() {

                // if(uni.getStorageSync('roleType')) {
                //     uni.setStorageSync('sharePath')
                //     uni.reLaunch({
                //         url:''
                //     })
                // }
                // if (!isAuth(['login', 'info', 'resume'])) return
                console.log("this.resumeList",this.resumeList)
                let params = {
                    job_id: this.id,
                    member_resume_id: this.resumeList[this.resumeIndex].member_resume.id,
                    model_type: this.model_type,
                    model_id: this.model_id,
                    model_user_id: this.model_user_id
                }
                const {
                    status_code,
                    message,
                    data
                } = await signup(params)
                this.closeResume()
                if (status_code == 200) {
                    uni.$u.toast('报名成功')
                    this.getJobDetails()
                } else {
                    uni.$u.toast(message || '失败')
                }
            },


            //取消报名
            async cancelSignup() {
                let params = {
                    report_id: this.details.job_report.id,
                    cancel_reason: this.reason,
                    model_type: this.model_type,
                    model_id: this.model_id,
                }
                const {
                    status_code,
                    message,
                    data
                } = await cancelSignup(params)
                this.closeReason()
                if (status_code == 200) {
                    uni.$u.toast(message || '成功')
                    this.getJobDetails()
                } else {
                    uni.$u.toast(message || '失败')
                }
            },

            async getJobDetails() {
                let params = {
                    id: this.id
                }
                const {
                    status_code,
                    data
                } = await getJobDetails(params)
                if (status_code == 200) {
                    this.details = data

                    if (this.details.report_status == 2) {
                        // this.getResumeList()
                    }

                    this.covers[0]['latitude'] = this.details['addresses'][0]['lat']
                    this.covers[0]['longitude'] = this.details['addresses'][0]['lng']

                    this.latitude = this.details['addresses'][0]['lat']
                    this.longitude = this.details['addresses'][0]['lng']
                }
            },


            loadMore() {
                if (this.more) {
                    this.page++
                    this.getQuestionList()
                }
            },

            //常见问题
            async getQuestionList() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    id: this.id
                }
                const {
                    status_code,
                    data,
                    message
                } = await getQuestionList(params)
                if (status_code == 200) {
                    this.more = data.more
                    this.questionList = this.questionList.concat(data.data);
                }
            },
        }
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    .main {
        padding: 32rpx 32rpx 140rpx 32rpx;
    }

    .wrap {
        display: flex;
        flex-direction: column;
        background: #FFFFFF;
        padding: 0 32rpx;
        border-radius: 24rpx;
        margin-bottom: 24rpx;

        .job-info {
            display: flex;
            flex-direction: column;
            padding: 24rpx 0;

            .job-name {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .job {
                    font-weight: 500;
                    font-size: 32rpx;
                    color: #333333;
                }

                .money {
                    font-weight: 500;
                    font-size: 32rpx;
                    color: #F98A14;
                }
            }

            .tags {
                display: flex;
                align-items: center;
                margin-top: 24rpx;

                .tag {
                    display: flex;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #999999;
                    margin-right: 24rpx;

                    image {
                        width: 28rpx;
                        height: 28rpx;
                        margin-right: 6rpx;
                    }
                }
            }
        }

        .pub-info {
            display: flex;
            padding: 24rpx 0;

            image {
                width: 72rpx;
                height: 72rpx;
                border-radius: 50%;
            }

            .users {
                display: flex;
                flex-direction: column;
                padding-left: 16rpx;

                .username {
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #333333;
                }

                .address {
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #999999;
                    margin-top: 10rpx;
                }
            }
        }

        .sub-wrap {
            display: flex;
            flex-direction: column;

            .sub-title {
                font-weight: 500;
                font-size: 32rpx;
                color: #333333;
                padding: 24rpx 0;
            }

            .desc {
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                line-height: 40rpx;
                margin-bottom: 24rpx;
            }

            .pos {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;

                text {
                    margin-left: 10rpx;
                }
            }

            .map {
                padding: 24rpx 0;
            }
        }

        .title {
            padding: 24rpx 0 0 0;
            font-weight: 500;
            font-size: 32rpx;
            color: #333333;
        }

        .compony-info {
            display: flex;
            padding: 24rpx 0;

            image {
                width: 72rpx;
                height: 72rpx;
                border-radius: 50%;
            }

            .info {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                padding-left: 16rpx;

                .name {
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #333333;
                }

                .tags {
                    display: flex;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #999999;

                    .tag {
                        border-right: 1px solid #999999;
                        padding: 0 12rpx;

                        &:first-child {
                            padding-left: 0;
                        }

                        &:last-child {
                            border-right: none;
                        }
                    }
                }
            }
        }

        .answers {
            display: flex;
            flex-direction: column;

            .answer {
                display: flex;
                flex-direction: column;
                padding: 24rpx 0;
                border-bottom: 1px solid #F5F5F7;

                &:last-child {
                    border-bottom: none;
                }

                .ans {
                    display: flex;
                    align-items: center;
                    font-weight: 400;
                    font-size: 14px;
                    color: #333333;

                    image {
                        width: 28rpx;
                        height: 28rpx;
                        margin-right: 32rpx;
                    }
                }

                .qus {
                    display: flex;
                    align-items: center;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #999999;
                    margin-top: 20rpx;

                    image {
                        width: 28rpx;
                        height: 28rpx;
                        margin-right: 32rpx;
                    }
                }
            }
        }

        .down {
            display: flex;
            justify-content: center;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;

            image {
                width: 32rpx;
                height: 32rpx;
            }
        }

        .more {
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
            padding-bottom: 32rpx;

            image {
                width: 32rpx;
                height: 32rpx;
            }
        }
    }

    .footer {
        position: fixed;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 140rpx;
        width: 100%;
        left: 0;
        bottom: 0;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 28rpx;

        .favor {
            display: flex;
            align-items: center;
            color: #999999;
            margin-left: 32rpx;
        }

        .ysc {
            color: #F9AD14;
        }

        .btns {
            display: flex;
            flex: 1;
            padding: 0 32rpx;

            .btn {
                display: flex;
                justify-content: center;
                align-items: center;
                flex: 1;
                padding: 0 72rpx;
                height: 80rpx;
                background: #F5F5F7;
                color: #333333;
                border-radius: 16rpx;

                &:first-child {
                    margin-right: 20rpx;
                }

            }

            .edit {
                color: #4F8CF0;
                background: rgba(79, 140, 240, 0.1);
            }

            .down {
                background: linear-gradient(135deg, #F0544F 0%, #EE1E1E 100%);
                color: #FFFFFF;
            }

            .cancel {
                background: rgba(254, 77, 79, 0.1);
                color: #FE4D4F;
            }

            .agree {
                background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
                color: #FFFFFF;
            }
        }
    }

    .credit {
        display: flex;
        flex-direction: column;
        padding: 40rpx 48rpx;

        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .logo-content {
                display: flex;

                image {
                    width: 48rpx;
                    height: 48rpx;
                    margin-right: 16rpx;
                }

                text {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 500;
                    font-size: 15px;
                    color: rgba(0, 0, 0, 0.9);
                }
            }

            .tip {
                image {
                    width: 48rpx;
                    height: 48rpx;
                }
            }
        }

        .comp {
            justify-content: center;
        }

        .content {
            display: flex;
            flex-direction: column;
            margin-top: 40rpx;

            .sub-title {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                color: #000000;
            }

            .desc {
                display: flex;
                font-weight: 400;
                margin-top: 16rpx;
                font-size: 24rpx;
                color: #999999;
            }

            .btns {
                display: flex;
                flex-direction: column;
                margin-top: 16rpx;

                .btn {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    background: #FFFFFF;
                    height: 140rpx;
                    border-radius: 16rpx;

                    &:last-child {
                        margin-top: 24rpx;
                    }

                    text {
                        font-weight: 400;
                        font-size: 32rpx;
                        color: rgba(0, 0, 0, 0.9);
                    }

                    .bind {
                        font-size: 24rpx;
                        color: #07C160;
                    }
                }

                .active {
                    color: #4F8CF0;
                    border: 1rpx solid #4F8CF0;
                }
            }
        }

        .other {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 60rpx 0;
            color: #576B95;
            font-weight: 500;
            font-size: 24rpx;
        }

        .agree {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 44rpx;
            height: 88rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #FFFFFF;
            background: #4F8CF0;
            margin-top: 32rpx;
        }
    }
</style>
