<template>
  <view class="container">
    <button class="show-resume-btn" @click="showResumePopup">显示简历弹窗</button>
    
    <!-- 引入简历弹窗组件 -->
    <resume-popup 
      :show="showPopup" 
      :resumeData="resumeData" 
      @close="closeResumePopup"
      @audit="handleAudit"
    ></resume-popup>
  </view>
</template>

<script>
import ResumePopup from '@/components/resume-popup.vue';

export default {
  components: {
    ResumePopup
  },
  data() {
    return {
      showPopup: false,
      resumeData: {
        avatar: '/static/images/avatar-placeholder.png',
        name: '王哈哈',
        age: '22岁',
        education: '24应届生',
        degree: '本科',
        school: '离校-随时到岗',
        job: '设计（行业）',
        salary: '5-7K',
        location: '郑州',
        advantage: '个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势',
        expectPosition: '全职职位',
        jobDetail: '设计（行业）',
        company: '北京学创联盟（北京）有限公司',
        position: 'UI设计师',
        workContent: '内容：内容内容内容内容内容内容',
        projectCompany: '北京学创联盟（北京）有限公司',
        projectPosition: 'UI设计师',
        projectContent: '内容：内容内容内容内容内容内容',
        schoolLogo: '/static/images/avatar-placeholder.png',
        schoolName: '郑州大学',
        schoolYear: '2021-2025',
        major: '会计',
        educationLevel: '本科'
      }
    };
  },
  methods: {
    showResumePopup() {
      this.showPopup = true;
    },
    closeResumePopup() {
      this.showPopup = false;
    },
    handleAudit() {
      uni.showToast({
        title: '审核操作',
        icon: 'none'
      });
    }
  }
};
</script>

<style lang="scss">
.container {
  padding: 40rpx;
}

.show-resume-btn {
  width: 100%;
  height: 90rpx;
  background-color: #4080ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}
</style>
