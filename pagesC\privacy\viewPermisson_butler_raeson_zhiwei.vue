<template>
	<view class="warp">
		<view class="title">
			选择不看就业管家职位的理由
		</view>
		<view class="box">
			<view class="inner" v-for="item in list" :key="item" @click="handel(item)">
				{{item}}
			</view>
		</view>
		<u-modal :show="show" title="请填写不看就业管家职位的理由" @cancel="cancel" @confirm="confirm" confirmColor="#576B95"
			:showCancelButton="true">
			<view class="slot-content">
				<u--input style="width: 540rpx;" placeholder="输入您的理由" type="textarea" border="none" v-model="value"
					@change="change" :maxlength="15" :showWordLimit="true" inputAlign="left"></u--input>
			</view>
		</u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [
					'已经有服务的就业管家', '就业管家职位不合适', '就业管家回复慢', '就业管家专业度不高', '之前就业管家体验不好', '不需要就业管家介绍工作',
					'其他'
				],
				show: false,
				value: ''
			}
		},
		methods: {
			handel(item) {
				console.log(item);
				if (item === '其他') {
					this.show = true
				} else {
					uni.navigateBack({
						success() {
							uni.showToast({
								icon: 'none',
								title: '不看就业管家职位',
							})
						}
					})
				}
			},
			cancel() {
				this.show = false
			},
			confirm() {
				this.show = false
				uni.navigateBack({
					success() {
						uni.showToast({
							icon: 'none',
							title: '你已成功对就业管家隐藏',
						})
					}
				})
			},
			change() {

			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		height: 100vh;
		margin: 32rpx;
		background: #fff;

		title {
			font-size: 32rpx;
			color: #333333;
			margin: 32rpx 0;
		}

		.box {
			display: flex;
			flex-wrap: wrap;
			margin-top: 32rpx;

			.inner {
				font-size: 24rpx;
				color: #333333;
				padding: 12rpx;
				margin-right: 24rpx;
				background: #F5F5F7;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				margin-bottom: 24rpx;
			}
		}

	}
</style>