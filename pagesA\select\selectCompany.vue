<template>
    <view id="app">
        <!-- <u-sticky bgColor="#F5F5F5">
             <view class="header">
                 <view class="search-wrap">
                     <u-search placeholder="请输入姓名或手机号" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
                 </view>
             </view>
         </u-sticky>
         <view style="height: 32rpx;"></view> -->
        <view class="content" v-for="(item, index) in page.data" :key="item.id" @click="toggleSelect(item)"
              :class="{ selected: isSelected(item) }">
            <view>{{ item.authorize_company.name }}</view>
        </view>
        <Pages :status="page.status"></Pages>
        <view style="height: 196rpx;"></view>
        <view @click="selected" class="btn">
            <view class="btnCla">选择完成</view>
        </view>
    </view>
</template>

<script>

    import {getHeadhuntersAuthEnterpriseList} from "../../config/api";

    export default {
        data() {
            return {
                page: {
                    form: {
                        page: 1,
                        limit: 10,
                        status: 1,
                        cancel_status: 2,
                    },
                    status: 'loadmore',
                    more: false,
                    data: [],
                },
                selectedItems: [], // 用于存储选中的项
                type: 2,
            }
        },

        //触底加载更多
        onReachBottom() {
            if (this.page.more) {
                this.status = 'loading'
                this.page.form.page++;
                this.getList()
            }
        },
        computed: {},
        onLoad() {
            this.getList()
        },
        methods: {
            // 在这里拿到所需要的值
            selected() {
                this.$store.commit('setCompanyAuthorize', this.selectedItems);
                uni.navigateBack();
            },
            initPage() {
                this.page.data = [];
                this.page.form.page = 1;
                this.page.status = 'loadmore';
            },
            getList() {
                getHeadhuntersAuthEnterpriseList(this.page.form).then(response => {
                    if (response.status_code == '200') {
                        this.page.more = response.data.more
                        this.page.data = this.page.data.concat(response.data.data);
                        this.page.status = this.page.more ? 'loadmore' : 'nomore';
                    }
                });
            },
            toggleSelect(item) {
                if (this.type == 2) { // 根据进入页面的type判断是多选还是单选
                    const index = this.selectedItems.findIndex(selected => selected.id === item.id);
                    if (index > -1) {
                        this.selectedItems.splice(index, 1); // 取消选择
                    } else {
                        this.selectedItems.push(item); // 添加选择
                    }
                }
            },
            isSelected(item) {
                return this.selectedItems.some(selected => selected.id === item.id);
            }
        }
    }
</script>

<style lang="scss">
    @import "../../static/css/pagesA/select/selectCompany";
</style>