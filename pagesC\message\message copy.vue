<template>
	<view id="app">
		<view style="position: sticky;top: 0;z-index: 99999;">
			<!-- 顶部职位信息 -->
			<view class="positonBox">
				<view>{{job_status_name}}</view>
				<view :class="['positionIcon',typeClass]">
					{{personTypeName}}
				</view>
			</view>
			<!-- 顶部按钮 -->
			<view class="topBtnBox">
				<view class="topBtnBox_child" @click="sendInterview"
					v-if="(selfType=='company'||selfType=='headhunters')&&personType=='member'">
					<image src="/static/images/chat/interviewIcon.png" mode="" class="interviewIcon"></image>
					<view>发起面试</view>
				</view>
				<view class="topBtnBox_child" @click="exchangePhone">
					<image src="/static/images/chat/phoneIcon.png" mode="" class="interviewIcon">
					</image>
					<view>交换联系方式</view>
				</view>
				<view class="topBtnBox_child" v-if="personType=='company'||personType=='headhunters'"
					@click="companyDetails">
					<image src="/static/images/chat/companyDetailsImg.png" mode="" class="interviewIcon">
					</image>
					<view>公司详情</view>
				</view>
				<view class="topBtnBox_child" @click="sendContract"
					v-if="(selfType=='company'||selfType=='headhunters')&&personType!=='member'&&personType!=='headhunters'">
					<image src="/static/images/chat/contractIcon.png" mode="" class="interviewIcon"></image>
					<view>签署授权合同</view>
				</view>
				<view class="topBtnBox_child" v-if="selfType=='member'" @click="getResumeList">
					<image src="/static/images/chat/sendResumeImg.png" mode="" class="interviewIcon"></image>
					<view>发送简历</view>
				</view>
				<view class="topBtnBox_child" v-if="selfType=='member'&&personType=='headhunters'"
					@click="goDetail(user_id)">
					<image src="/static/images/chat/huntInfo.svg" mode="" class="interviewIcon"></image>
					<view>管家信息</view>
				</view>
				<!-- <view class="topBtnBox_child" v-if="selfType=='member'&&personType=='headhunters'">
                    <image src="/static/images/chat/entrustmentImg.png" mode="" class="interviewIcon"
                        ></image>
                    <view>委托简历</view>
                </view> -->
				<view class="topBtnBox_child" v-if="personType=='member'" @click="seeResume">
					<image src="/static/images/chat/lookResume.png" mode="" class="interviewIcon"></image>
					<view>查看简历</view>
				</view>
				<view class="topBtnBox_child" v-if="selfType=='member'&&personType=='headhunters'"
					@tap="showDialog = true">
					<image src="/static/images/chat/invite.svg" mode="" class="interviewIcon"></image>
					<view>委托邀约</view>
				</view>
			</view>
		</view>

		<!-- 聊天内容 -->
		<scroll-view class="chat" scroll-y :scroll-into-view="scrollToView" @scrolltoupper="getHistory()"
			scroll-with-animation>
			<view class="chat-main" :style="{paddingBottom:inputh+'px'}">
				<view class="chat-ls" v-for="(item,index) in chatHistory" :key="index" :id="'msg'+ index">
					<view class="chat-time" v-if="item.createTime != ''">{{changeTime(item.createTime)}}</view>
					<!-- 接口返回的left和right进行区分 -->
					<view class="msg-m msg-left" v-if="item.show_type ==  'left'">
						<!-- 对方头像 -->
						<image class="user-img" :src="item.avatar" mode=""></image>
						<view class="message" v-if="item.TextType == 'text'">
							<!-- 文字 -->
							<view class="msg-text">{{item.sendText.text}}</view>
						</view>
						<view class="message" v-if="item.TextType == 'image'" @tap="previewImg(item.sendText.text)">
							<!-- 图像 -->
							<image :src="item.sendText.text" class="msg-img" mode="widthFix"></image>
						</view>
						<!-- @tap="playVoice(item.sendText.voice)" -->
						<view class="message" v-if="item.TextType == 'video'">
							<!-- 音频 -->
							<video :src="item.sendText.text" controls :autoplay="false" :muted="false" :loop="false"
								class="msg-img" auto-pause-if-open-native></video>
						</view>
						<view class="message" v-if="item.TextType == 3" @tap="openLocation(item.sendText)">
							<!-- 位置 -->
							<view class="msg-map">
								<view class="map-name">{{item.sendText.name}}</view>
								<view class="map-address">{{item.sendText.address}}</view>
								<!-- 如果map不起作用，就可以直接用一张图片去替代 -->
								<!-- <map class="map" :longitude="item.sendText.longitude" :latitude="item.sendText.latitude"
  :markers="covers(item.sendText)"></map> -->
							</view>
						</view>
						<view class="message" v-if="item.TextType == 'member_contract'"
							@tap="showContractDetails(item)">
							<view class="cardContractLeft">
								<view class="cardContractTop">
									<image src="../../static/images/chat/contractImg.png" mode="" class="contractImg">
									</image>
									<view class="contractName">{{item.sendText.name}}</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>

						<!-- 面试邀约 -->
						<view class="message" v-if="item.TextType == 'invitation'" @tap="showInterviewDetails(item)">
							<view class="cardContractLeft">
								<view class="cardContractTop">
									<image src="../../static/images/chat/interviewCard.png" mode="" class="contractImg">
									</image>
									<view class="contractName">您有一份面试邀约</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>
						<!-- 简历 -->
						<view class="message" v-if="item.TextType == 'resume'" @tap="showResumeDetials(item)">
							<view class="cardContractLeft">
								<view class="cardContractTop">
									<image src="../../static/images/chat/resumeIcon.png" mode="" class="contractImg">
									</image>
									<view class="contractName">{{item.sendText.text}}</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>
						<!-- word文档 -->
						<view class="message" v-if="item.TextType == 6" @tap="">
							<view class="cardContractLeft">
								<view class="cardContractTop">
									<image src="../../static/images/chat/wordCard.png" mode="" class="contractImg">
									</image>
									<view class="contractName">word文档版简历名称word文档版</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>
						<!-- pdf版 -->
						<view class="message" v-if="item.TextType == 7" @tap="">
							<view class="cardContractLeft">
								<view class="cardContractTop">
									<image src="../../static/images/chat/pdfWord.png" mode="" class="contractImg">
									</image>
									<view class="contractName">pdf版简历名称pdf版</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>
						<!-- 交换联系方式 -->
						<view class="message" v-if="item.TextType == 'cellphone'" @tap="showCellPhone">
							<view class="cardContractLeft">
								<view class="cardContractTop">
									<image src="../../static/images/chat/phoneCard.png" mode="" class="contractImg">
									</image>
									<view class="contractName">是否交换联系方式</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>

						<!-- 职位卡片 -->
						<view class="message" v-if="item.TextType == 10" @tap="showCellPhone">
							<view class="cardContractLeft">
								<view class="cardContractTop">
									<!-- <image src="../../static/images/chat/phoneCard.png" mode="" class="contractImg">
                                    </image> -->
									<view class="contractName">职位名称</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>

					</view>
					<view class="msg-m msg-right" v-if="item.show_type == 'right'">
						<!-- 用户自己的头像 -->
						<image class="user-img" :src="item.avatar"></image>
						<view class="message" v-if="item.TextType == 'text'">
							<view class="msg-text">{{item.sendText.text}}</view>
						</view>
						<view class="message" v-if="item.TextType == 'image'" @tap="previewImg(item.sendText.text)">
							<image :src="item.sendText.text" class="msg-img" mode="widthFix"></image>
						</view>
						<!-- @tap="playVoice(item.sendText.voice)" -->
						<view class="message" v-if="item.TextType == 'video'">
							<!-- 音频 -->
							<video :src="item.sendText.text" controls :autoplay="false" :muted="false" :loop="false"
								class="msg-img" auto-pause-if-open-native></video>
						</view>
						<view class="message" v-if="item.TextType == 3" @tap="openLocation(item.sendText)">
							<!-- 位置 -->
							<view class="msg-map">
								<view class="map-name">{{item.sendText.name}}</view>
								<view class="map-address">{{item.sendText.address}}</view>
								<!-- <map class="map" :longitude="item.sendText.longitude" :latitude="item.sendText.latitude"
  :markers="covers(item.sendText)"></map> -->
							</view>
						</view>
						<!-- 发送合同  v-if="item.TextType == 4" -->
						<view class="message" v-if="item.TextType == 'member_contract'"
							@tap="showContractDetails(item)">
							<view class="cardContract">
								<view class="cardContractTop">
									<image src="../../static/images/chat/contractImg.png" mode="" class="contractImg">
									</image>
									<view class="contractName">{{item.sendText.name}}</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>

						<!-- 面试邀约 -->
						<view class="message" v-if="item.TextType == 'invitation'" @tap="showInterviewDetails(item)">
							<view class="cardContract">
								<view class="cardContractTop">
									<image src="../../static/images/chat/interviewCard.png" mode="" class="contractImg">
									</image>
									<view class="contractName">您发起了一份面试邀约</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>
						<!-- 简历 -->
						<view class="message" v-if="item.TextType == 'resume'" @tap="showResumeDetials(item)">
							<view class="cardContract">
								<view class="cardContractTop">
									<image src="../../static/images/chat/resumeIcon.png" mode="" class="contractImg">
									</image>
									<view class="contractName">{{item.sendText.text}}</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>
						<!-- word文档 -->
						<view class="message" v-if="item.TextType == 6" @tap="">
							<view class="cardContract">
								<view class="cardContractTop">
									<image src="../../static/images/chat/wordCard.png" mode="" class="contractImg">
									</image>
									<view class="contractName">word文档版简历名称word文档版</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>
						<!-- pdf版 -->
						<view class="message" v-if="item.TextType == 7" @tap="">
							<view class="cardContract">
								<view class="cardContractTop">
									<image src="../../static/images/chat/pdfWord.png" mode="" class="contractImg">
									</image>
									<view class="contractName">pdf版简历名称pdf版</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>
						<!-- 交换联系方式 -->
						<view class="message" v-if="item.TextType == 'cellphone'" @tap="">
							<view class="cardContract">
								<view class="cardContractTop">
									<image src="../../static/images/chat/phoneCard.png" mode="" class="contractImg">
									</image>
									<view class="contractName">交换联系方式</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>

						<!-- 发送个职位卡片 -->
						<view class="message" v-if="item.TextType == 10" @tap="">
							<view class="cardContract">
								<view class="cardContractTop">
									<!-- <image src="../../static/images/chat/phoneCard.png" mode="" class="contractImg">
                                    </image> -->
									<view class="contractName">职位名称</view>
								</view>
								<view class="contractBtn">点击查看</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view id="bottom1"></view>
		</scroll-view>
		<!-- 面试详情 -->
		<InterviewDetails v-if="showPopup2" :show="showPopup2" :roleType="roleType" :currentItem="currentItem"
			@response="handleResponse" @close="showPopup2 = false" />
		<!-- 发送简历 -->
		<resumePop v-if="showResumePopup" :show="showResumePopup" :resumeList="resumeList" @confirm="handleConfirm"
			@close="showResumePopup = false" />
		<!-- 切换职位 -->
		<Popup :visible="showPopup1" :data="jobList" @close="showPopup1 = false" @select="handleSelect" />
		<submit @inputs="inputs" @heights="heights" @showPopup="showJobList"></submit>

		<!-- 委托邀约 -->
		<view v-if="showDialog" class="custom-dialog">
			<view class="dialog-content">
				<view class="dialog-title">委托邀约</view>
				<view class="dialog-buttons">
					<button @tap="handleSignup('job')">同意</button>
					<button @tap="handleSignup('job_active')">拒绝</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import dateTime from '@/common/dateTime.js';
	import submit from '@/components/chat/submit.vue';
	import Popup from '@/components/Popup.vue';
	import resumePop from '@/components/chat/resumePop.vue';
	import InterviewDetails from '@/components/chat/interviewDetails.vue';
	import {
		chatList,
		messageRecord,
		sendMessage
	} from "../../config/common_api";
	import {
		isAuth
	} from '@/common/common.js'
	import {
		getHeadhuntersJobfairList,
		inviteInterview,
		postContract,
		getCompanyJobList,
		getResumeList
	} from "../../config/api";
	//音频播放
	const innerAudioContext = uni.createInnerAudioContext();
	export default {
		data() {
			return {
				roleType: '',
				selfType: '',
				personType: '',
				personTypeName: '',
				typeClass: '',
				job_status_name: '',
				chat_id: '',
				member_id: '',
				user_id: '',
				client_id: '',
				friendName: "xpq",
				avatar: "xpq",
				// mag: [],
				msg: [],
				// 反转数据接收
				unshiftmsg: [],
				imgMsg: [],
				scrollToView: '',
				oldTime: new Date(),
				inputh: '80',
				page: 1,
				limit: 8,
				more: true,
				interviewform: '', // 面试表单
				showPopup2: false, // 面试弹窗展示隐藏
				currentItem: {

				},
				contractform: '', // 合同表单
				showPopup1: false, // 切换职位展示隐藏
				jobList: [], // 切换职位数据
				showResumePopup: false, //简历弹窗
				resumeList: [], // 简历列表
				showDialog: false, // 委托邀约弹窗
			}
		},
		computed: {
			userInfo() {
				return this.$store.state.userInfo || uni.getStorageSync('userInfo')
			},
			chatHistory() {
				return this.$store.state.chatHistory || uni.getStorageSync('chatHistory')
			}
		},
		watch: {
			chatHistory: {
				handler(newValue, oldValue) {
					if (this.page == 1) {
						this.goBottom(); // 当 chatHistory 改变时调用 goBottom 方法
					}
				},
				deep: true, // 深度监听数组
				immediate: true // 可选，初始化时也会调用一次
			}
		},
		// onShow() {
		//     // 数组倒叙 主要是应对后端传过来的数据
		//     for (var i = 0; i < this.msg.length; i++) {
		//         //时间间隔处理
		//         if (i < this.msg.length - 1) { //这里表示头部时间还是显示一下
		//             let t = dateTime.spaceTime(this.oldTime, this.msg[i].createTime);
		//             if (t) {
		//                 this.oldTime = t;
		//             }
		//             this.msg[i].createTime = t;
		//         }
		//         // 获取图片，为下面的预览做准备
		//         if (this.msg[i].TextType == 1) {
		//             this.imgMsg.unshift(this.msg[i].sendText)
		//         }
		//         this.unshiftmsg.unshift(this.msg[i]);
		//     }
		//     // 跳转到最后一条数据 与前面的:id进行对照
		//     this.$nextTick(() => {
		//         this.scrollToView = 'msg' + (uni.getStorageSync('chatHistory').length - 1)
		//         console.log("获取id", this.scrollToView)
		//     })
		// },
		onLoad(options) {
			console.log("options", options)
			this.chat_id = options.chatId
			this.member_id = options.member_id
			this.user_id = options.user_id
			this.avatar = this.userInfo.member_info.image.thumbnail_path_url
			this.name = options.name
			this.personTypeName = options.personTypeName;
			this.typeClass = options.typeClass;
			this.personType = options.personType;
			this.selfType = options.selfType;
			this.job_status_name = options.job_status_name
			this.client_id = uni.getStorageSync('connect_client_id')
			this.company_id = options.company_id;
			this.messageRecord()
			uni.setNavigationBarTitle({
				title: options.name
			});
			if (options.type && options.type == 'contract') {
				console.log('触发发送合同')
				this.sendContractMssages()
			}
			this.roleType = uni.getStorageSync('roleType');
			if (uni.getStorageSync('roleType') != 'member') {
				// this.loadData()
			}
		},
		onShow() {
			if (uni.getStorageSync('chatInterview')) {
				this.interviewform = uni.getStorageSync('chatInterview');
				uni.removeStorageSync('chatInterview')
				this.sendInterviewMssages()
			}
			if (uni.getStorageSync('contract')) {
				this.contractform = uni.getStorageSync('contract');
				uni.removeStorageSync('contract')
				this.sendContractMssages()
			}
		},
		components: {
			submit,
			InterviewDetails,
			Popup,
			resumePop
		},
		methods: {
			// 查看简历
			seeResume() {
				uni.navigateTo({
					url: '/pagesA/details/qlmDetails?id=' + this.member_id
				})
			},
			showResumeDetials(e) {
				uni.navigateTo({
					url: '/pagesA/details/chatResume?id=' + e.sendText.id
				})
			},
			// 获取简历列表
			async getResumeList() {
				if (!isAuth(['resume'])) return
				const res = await getResumeList({
					page: 1,
					limit: 3
				})
				if (res.status_code == 200) {
					if (res.data.data.length > 0) {
						this.resumeList = res.data.data;
						this.showResumePopup = true;
					} else {
						uni.navigateTo({
							url: "/pagesA/add/addResume"
						})
					}

				}
				console.log('获取简历列表', res)
			},
			// 选择简历
			handleConfirm(selectedResume) {
				console.log('选中的简历:', selectedResume);
				// 在这里进行相应的处理，比如发送简历等
				this.sendMessage({
					text: selectedResume.member_resume.resume_name,
					id: selectedResume.member_resume.id
				}, 'resume', 'resume', selectedResume.member_resume.id)
			},
			// 获取职位信息
			async loadData() {
				const res = await getCompanyJobList()
				console.log("获取职位", res)
				this.jobList = res.data
			},
			showJobList(bol) {
				this.showPopup1 = bol
			},
			// 点击职位
			handleSelect(item) {
				console.log('选中的项:', item); // 处理选中的数据
			},
			// 查看合同详情
			showContractDetails(item) {
				console.log("item", item)
				uni.navigateTo({
					url: '/pagesA/details/chatContractDetails?id=' + item.sendText.contractId
				})
			},
			// 查看面试详情
			showInterviewDetails(item) {
				console.log("item", item)
				this.showPopup2 = true
				this.currentItem = item
			},
			// 委托邀约
			invite() {
				console.log('111')
			},
			// 邀请面试弹窗
			handleResponse(response) {
				console.log('用户选择:', response);
				// 处理用户选择
			},
			goDetail(user_id) {
				uni.navigateTo({
					url: '/pagesA/details/obtainItemDetails?id=' + user_id
				})
			},
			getHistory() {
				// 防止重复加载
				// this.more是没有下一页数据了，打断继续请求
				if (this.loading || !this.more) {
					return;
				}

				this.loading = true;
				setTimeout(() => { // 模拟数据加载
					this.page++;
					this.messageRecord()
				}, 500); // 假设数据请求时间为500ms
			},
			//聊天记录
			async messageRecord() {
				let params = {
					chat_id: this.chat_id,
					page: this.page,
					limit: this.limit
				}
				const {
					status_code,
					message,
					data
				} = await messageRecord(params)
				if (status_code == 200) {
					this.loading = false;
					this.more = data.more;
					let chatHistory = data.data
					let arrData = []
					if (this.page == 1) {
						arrData = []
					} else {
						arrData = uni.getStorageSync('chatHistory')
					}
					const getHistoryArr = [];
					for (let i = 0; i < chatHistory.length; i++) {
						let dataNew = {
							"sendName": this.name,
							"avatar": chatHistory[i].right_user.avatar,
							"sendText": chatHistory[i].content,
							"createTime": chatHistory[i].created_at,
							"updateTime": chatHistory[i].updated_at,
							"chatmState": 1,
							"show_type": chatHistory[i].show_type,
							"TextType": chatHistory[i].type
						}
						if (chatHistory[i].show_type == 'left') {
							dataNew.avatar = chatHistory[i].left_user.avatar
						}
						if (this.page == 1) {
							arrData.push(dataNew)
						} else {
							getHistoryArr.push(dataNew)
						}
					}
					if (this.page != 1) {
						console.log("getHistoryArr", getHistoryArr)
						arrData.unshift(...getHistoryArr)
					}
					console.log('聊天记录的重组', arrData)
					this.$store.commit('setChatHistory', arrData)
					// this.$nextTick(() => {
					//         this.scrollToView = 'msg' + (arrData.length - 1)
					//         console.log("获取id", this.scrollToView)
					//     })
					if (this.page == 1) {
						this.goBottom()
					}
				}
			},
			changeTime(date) {
				return dateTime.dateTime1(date);
			},
			async sendInterviewMssages() {

				const {
					status_code,
					message,
					data
				} = await inviteInterview(this.interviewform)

				if (status_code == 200) {
					this.interviewform.interview_id = data.id
					this.sendMessage(this.interviewform, 'invitation', 'invitation', data.id)
				} else {
					console.log('发送面试失败', message)
					uni.showToast({
						title: message,
						icon: 'none',
						success: () => {

						}
					})
				}
			},
			async sendInterview() {
				uni.navigateTo({
					url: `/pagesA/add/chatInterview?name=${this.name}&avatar=${this.avatar}&chatId=${this.chat_id}&member_id=${this.member_id}&user_id=${this.user_id}`,
				})
			},
			//交换手机号
			exchangePhone() {
				this.sendMessage({
					text: '申请交换手机号'
				}, 'cellphone')
			},
			async sendContractMssages() {


				const {
					status_code,
					message,
					data
				} = await postContract(this.contractform)
				console.log("合同详情", data)
				this.contractform.contractId = data.id;
				if (status_code == 200) {
					this.sendMessage(this.contractform, 'member_contract', 'member_contract', data.id)
				} else {
					console.log('发起合同', message)
					uni.showToast({
						title: message,
						icon: 'none',
						success: () => {

						}
					})
				}
			},
			//发起合同
			async sendContract() {
				uni.navigateTo({
					url: `/pagesA/add/chatContract?name=${this.name}&avatar=${this.avatar}&chatId=${this.chat_id}&member_id=${this.member_id}&user_id=${this.user_id}`,
				})
			},
			// 进行图片的预览
			previewImg(e) {
				// let index = 0;
				// for (let i = 0; i < this.imgMsg.length; i++) {
				//     if (this.imgMsg[i] == e) {
				//         index = i;
				//     }
				// }
				// console.log("index", index)
				// 预览图片
				uni.previewImage({
					current: 1,
					urls: [e],
					longPressActions: {
						itemList: ['发送给朋友', '保存图片', '收藏'],
						success: function(data) {
							console.log('图片预览');
						},
						fail: function(err) {
							console.log(err.errMsg);
						}
					}
				});
			},
			//音频播放
			playVoice(e) {
				innerAudioContext.src = e;
				innerAudioContext.onPlay(() => {
					console.log('开始播放');
				});
			},
			//地图定位
			covers(e) {
				let map = [{
					latitude: e.latitude,
					longitude: e.longitude,
					// iconPath: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png'
				}]
				return (map);
			},
			//跳转地图信息
			openLocation(e) {
				uni.openLocation({
					latitude: e.latitude,
					longitude: e.longitude,
					name: e.name,
					address: e.address,
					success: function() {
						console.log('success');
					}
				});
			},
			signContract() {
				let data = {
					"sendName": "゛时光い",
					"receviceName": "xpq",
					"sendText": '',
					"createTime": new Date(),
					"updateTime": new Date(),
					"chatmState": 1,
					"TextType": 4
				};
				this.unshiftmsg.push(data);
			},
			//公用的发送消息方法
			sendMessage(content, type, model_type, model_id) {
				// const client_id = uni.getStorageSync('connect_client_id');
				// if (!client_id) {
				// 	console.error("client_id 为空，无法发送消息");
				// 	return;
				// }

				let sendData = {
					"chat_id": this.chat_id,
					"client_id": this.client_id,
					"model_type": model_type,
					"model_id": model_id,
					"content": content,
					"type": type
				}
				// 后续再给服务器发
				sendMessage(sendData).then((res) => {
					console.log('接口发送消息后返回的信息', res.data)
				})
				//时间间隔处理
				let data = {
					"sendName": this.name,
					"avatar": this.avatar,
					"sendText": content,
					"createTime": new Date(),
					"updateTime": new Date(),
					"chatmState": 1,
					"show_type": 'right',
					"TextType": type
				};
				this.chatHistory.push(data);
				console.log('发送消息后渲染', this.chatHistory)
				// 跳转到最后一条数据 与前面的:id进行对照
				this.$nextTick(function() {
					const targetId = 'msg' + (this.chatHistory.length - 1);
					this.scrollToView = targetId;
				})
				// if (e.type == 1) {
				//   this.imgMsg.push(e.message);
				// }
			},
			//接受输入内容
			inputs(e) {
				console.log("e", e.type)
				let model_type = '';
				let model_id = 0;
				if (e.type == 'image' || e.type == 'text' || e.type == 'video') {
					model_type = '';
					model_id = 0;
				}
				this.sendMessage({
					text: e.message,
				}, e.type, model_type, model_id)
			},
			//输入框高度
			heights(e) {
				console.log("高度:", e)
				// + 100
				this.inputh = e;
				// this.goBottom();
			},
			// 滚动到底部
			goBottom() {
				this.scrollToView = '';
				this.$nextTick(function() {
					this.scrollToView = 'msg' + (this.chatHistory.length - 1)
				})
			},
			companyDetails() {
				uni.navigateTo({
					url: '/pagesA/details/companyDetail?id=' + this.company_id
				})
			}
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style scoped lang="scss">
	view {
		box-sizing: border-box;
	}

	#app {
		width: 100%;
	}

	.positonBox {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 500;
		font-size: 20rpx;
		color: #FFFFFF;
		background: #4192EA;

		.positionIcon {
			border-radius: 56rpx 56rpx 56rpx 56rpx;
			font-weight: 400;
			font-size: 20rpx;
			color: #FFFFFF;
			text-align: center;
			padding: 0 12rpx;
			margin-left: 8rpx;
		}

		.memberColor {
			background: #81F21E;
		}

		.companyColor {
			background: #FFD044;
		}

		.headhunterColor {
			background: #16EEF5;
		}
	}

	.topBtnBox {
		width: 100%;
		height: 134rpx;
		background: #4192EA;
		border-radius: 0 0 24rpx 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
		margin-bottom: 24rpx;

		.topBtnBox_child {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-weight: 400;
			font-size: 20rpx;
			color: #FFFFFF;

			.interviewIcon {
				width: 40rpx;
				height: 40rpx;
				margin-bottom: 8rpx;
			}
		}

	}

	.custom-dialog {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	}

	.dialog-content {
		padding: 40rpx;
		background-color: #fff;
		border-radius: 20rpx;
		width: 600rpx;
		text-align: center;
	}

	.dialog-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 40rpx;
		color: #333;
	}

	.dialog-buttons {
		display: flex;
		justify-content: space-between;
	}

	.dialog-buttons button {
		flex: 1;
		margin: 0 20rpx;
		// padding: 12rpx 0;
		width: 300rpx;
		background-color: #4f8cf0;
		color: white;
		border-radius: 10rpx;
		text-align: center;
		font-size: 28rpx;
	}

	.dialog-buttons button:active {
		background-color: #3a6bb0;
	}

	.chat {
		height: 85vh;

		.chat-main {
			padding-left: 32rpx;
			padding-right: 32rpx;
			padding-top: 20rpx;
			// padding-bottom: 120rpx;  //获取动态高度
			display: flex;
			flex-direction: column;
		}

		.chat-ls {
			.chat-time {
				font-size: 24rpx;
				color: rgba(39, 40, 50, 0.3);
				line-height: 34rpx;
				padding: 10rpx 0rpx;
				text-align: center;
			}

			.msg-m {
				display: flex;
				padding: 20rpx 0;

				.user-img {
					flex: none;
					width: 80rpx;
					height: 80rpx;
					border-radius: 20rpx;
				}

				.message {
					flex: none;
					max-width: 480rpx;
				}

				.msg-text {
					font-size: 32rpx;
					color: rgba(39, 40, 50, 1);
					line-height: 44rpx;
					padding: 18rpx 24rpx;
				}

				.msg-img {
					max-width: 400rpx;
					border-radius: 20rpx;
					margin: 0 24rpx;
				}

				.msg-map {
					background: #fff;
					width: 464rpx;
					height: 284rpx;
					overflow: hidden;

					.map-name {
						font-size: 32rpx;
						color: rgba(39, 40, 50, 1);
						line-height: 44rpx;
						padding: 18rpx 24rpx 0 24rpx;
						//下面四行是单行文字的样式
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
						overflow: hidden;
					}

					.map-address {
						font-size: 24rpx;
						color: rgba(39, 40, 50, 0.4);
						padding: 0 24rpx;
						//下面四行是单行文字的样式
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
						overflow: hidden;
					}

					.map {
						padding-top: 8rpx;
						width: 464rpx;
						height: 190rpx;
					}
				}

				.voice {
					// width: 200rpx;
					min-width: 100rpx;
					max-width: 400rpx;
				}

				.voice-img {
					width: 28rpx;
					height: 36rpx;
				}
			}

			.msg-left {
				flex-direction: row;

				.msg-text {
					word-break: break-all;
					/* 允许数字和长单词在任何位置断开 */
					margin-left: 16rpx;
					background-color: #fff;
					border-radius: 0rpx 20rpx 20rpx 20rpx;
				}

				.ms-img {
					margin-left: 16rpx;
				}

				.msh-map {
					margin-left: 16rpx;
					border-radius: 0rpx 20rpx 20rpx 20rpx;
				}

				.voice {
					text-align: right;

				}

				.voice-img {
					float: left;
					transform: rotate(180deg);
					width: 28rpx;
					height: 36rpx;
					padding-bottom: 4rpx;
				}
			}

			.msg-right {
				flex-direction: row-reverse;

				.msg-text {
					word-break: break-all;
					/* 允许数字和长单词在任何位置断开 */
					margin-right: 16rpx;
					background-color: #A0DDFF;
					border-radius: 20rpx 0rpx 20rpx 20rpx;
				}

				.ms-img {
					margin-right: 16rpx;
				}

				.msh-map {
					margin-left: 16rpx;
					border-radius: 20rpx 0rpx 20rpx 20rpx;
				}

				.voice {
					text-align: left;

				}

				.voice-img {
					float: right;
					padding: 4rpx;
					width: 28rpx;
					height: 36rpx;
				}
			}
		}
	}

	.cardContractLeft {
		width: 492rpx;
		height: 226rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 32rpx;
		transform: translateX(24rpx);
	}

	.cardContract {
		width: 492rpx;
		height: 226rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 32rpx;
		transform: translateX(-32rpx);
	}

	.cardContractTop {
		display: flex;
		align-items: center;
	}

	.contractImg {
		width: 72rpx;
		height: 72rpx;
		margin-right: 16rpx;
	}

	.contractName {
		width: 340rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #333333;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.contractBtn {
		width: 100%;
		height: 66rpx;
		background: #4F8CF0;
		border-radius: 16rpx;
		text-align: center;
		line-height: 66rpx;
		font-weight: 400;
		font-size: 24rpx;
		color: #FFFFFF;
		margin-top: 24rpx;
	}
</style>
