<template>
	<view class="item" @click="go">
		<view class="name">
			合同标题合同标题
		</view>
		<view class="start">
			<text>合同创建时间</text>
			<text>2024/01/01 19:00:00</text>
		</view>
		<view class="start">
			<text>合同截止时间</text>
			<text>2024/01/01 19:00:00</text>
		</view>
		<view class="statusBtn">
			<view :class="['status',item.status==1?'sure':'wait']">
				{{item.status==1?'已签署':'待签署'}}
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name:'signRecordItem',
		props:{
			item:{
				type:Object,
				default:()=>{}
			}
		},
		data(){
			return{
				
			}
		},
		methods:{
			// go(){
			// 	uni.navigateTo({
			// 		url:"/pagesA/details/contractDetails"
			// 	})
			// }
		}
	}
</script>

<style lang="less" scoped>
	.item{
		display: flex;
		flex-direction: column;
		padding: 24rpx 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;
		margin-bottom: 32rpx;
		.name{
			font-weight: 500;
			font-size: 28rpx;
			color: #333333;
		}
		.start{
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			margin-top: 16rpx;
		}
		
		.statusBtn{
			display: flex;
			align-items: center;
			margin-top: 16rpx;
			.status{
				display: flex;
				align-items: center;
				height: 40rpx;
				padding: 0 12rpx;
				font-weight: 600;
				font-size: 20rpx;
				border-radius: 8rpx;
			}
			
			.sure{
				background: rgba(87,213,28,0.1);
				color: #57D51C;
			}
			.wait{
				background: rgba(79,140,240,0.1);
				color: #4F8CF0;
			}
		}
	}
</style>