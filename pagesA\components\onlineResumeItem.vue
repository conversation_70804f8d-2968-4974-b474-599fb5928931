<template>
    <view class="item">
        <view class="up" @click="go">
            <image :src="type==0?'https://api-test.zhaopinbei.com/storage/uploads/images/5Vb9rePKPy5xj8diaa2Ko4VLl6oJSU33d8qVJ8dx.png':'../static/images/fold.png'" mode=""></image>
            <view class="info">
                <view class="name">
                    {{type == 0 ? item.member_resume.resume_name:item.original_name}}
                </view>
                <view class="time">
                    修改时间：{{type == 0 ?item.member_resume.updated_at:item.updated_at}}
                </view>
            </view>
        </view>
        <view class="down">
            <view class="desc">
                是否为主简历：
                <u-switch v-model="public_status" :activeValue='1' :inactiveValue='2' size="20"
                          @change.stop="change(item,$event)"></u-switch>
            </view>
            <view class="dot" @click.stop="more(index,item)">
                <u-icon name="more-dot-fill"></u-icon>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        name: 'onlineResume',
        props: {
            index: {
                type: Number,
            },
            item: {
                type: Object,
                default: () => {
                }
            },
            type: {
                type: Number,
                default: 0
            }
        },
        data() {
            return {
                public_status: ''
            }
        },
        mounted() {
            if(this.type==0) {
            this.public_status = this.item.member_resume.public_status
            }
        },
        methods: {
            // 更多选项
            more(index,item) {
                this.$emit('more', {
                    item: item,
                    index: index,
                    show: true
                })
            },
            // 点击查看
            go() {
                if(this.type==0) {
                    uni.navigateTo({
                    url: "/pagesA/details/resumeDetails?id=" + this.item.member_resume.id
                    })
                } else {
                     // 文件预览
                const filePath = this.item.path;
                uni.showLoading({ title: '准备文件中...' });

                // 先下载文件到本地
                uni.downloadFile({
                    url: filePath,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            // 打开文档预览
                            uni.openDocument({
                                filePath: res.tempFilePath,
                                showMenu: true, // 显示右上角菜单支持分享
                                fileType: this.item.mime_type,
                                success: () => uni.hideLoading(),
                                fail: (err) => {
                                    uni.hideLoading();
                                    uni.showToast({ title: '不支持该格式预览', icon: 'none' });
                                }
                            });
                        }
                    },
                    fail: (err) => {
                        uni.hideLoading();
                        uni.showToast({ title: '文件下载失败', icon: 'none' });
                    }
                });
                }
            },
            change(item, v) {
                item.public_status = item.public_status == 1 ? 2 : 1
                this.$emit('setMainResume', {
                    item,
                    value: v
                })
            }
        }
    }
</script>

<style lang="scss">
    @import "../../static/css/pagesA/components/onlineResumeItem";
</style>
