<template>
	<view class="warp">
		<u--textarea v-model="value2" height="160" placeholder="说点什么~" maxlength="2000" border="bottom"
			count></u--textarea>
		<view class="upload-box">
			<u-upload :fileList="fileList1" name="1" multiple="true" :maxCount="9"
					  @afterRead="afterRead"
					  @delete="deletePic"
			></u-upload>
		</view>
		<view class="huati" @click="addhuati">
			<img src="/static/images/publishCenter/dynamic_icon2.png" alt="" />
			<text>添加话题</text>
			<u-modal :show="show" :title="title" :content='content' @confirm="confirm">
				<u-input v-model="inputValue" placeholder="请以#开头，#结尾!"></u-input>
			</u-modal>
		</view>
		<view class="bg-1" v-if="huatiList.length>0">
			<view class="d-1" v-for="(item,index) in huatiList">
				<text>{{item.name}}</text>
				<img src="/static/images/publishCenter/dynamic_icon2.png" alt="" @click="delClick(item)"/>
			</view>
			<!--<u-toast ref="uToast"></u-toast>-->
		</view>
		<view class="tips">
			*发布即代表您已阅读并同意 <text class="inn">《招聘呗创作者平台用户协议》</text>
		</view>
		<view class="btn" @click="fabuClick">
			发布文章
		</view>
	</view>
	<!--<u-notify message="Hi uView" :show="show"></u-notify>-->

</template>

<script>
	import {
		uploadImgs,
		addArticle,
	} from "../../config/api.js"
	export default {
		components:{

		},
		data() {
			return {
				value2: '',
				fileList1: [],
				show:false,
				title:'添加话题',
				content:'',
				inputValue:'',
                huatiList:[],
			}
		},
		onLoad(item){
			this.userType = uni.getStorageSync('roleType');
			this.company_id = uni.getStorageSync('userInfo')?.company.id;
			this.member_id = uni.getStorageSync('userInfo')?.member_info.member_id;
		},
		methods: {
            // 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1);
			},

			uploadFile(filePath) {
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: 'https://api-test.zhaopinbei.com/common-v2/articles/upload',
						name: 'file',
						filePath,
						formData: {},
						success: res => {
							resolve(JSON.parse(res.data));
						}
					});
				})
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
				let lists = [].concat(event.file);
				let fileListLen = this[`fileList${event.name}`].length;
				console.log(event)
				const fileUrls = event.file.map(el => el.url);
                console.log(fileUrls)
				const requests = fileUrls.map(el => this.uploadFile(el));
                const res = await  Promise.all(requests);
                console.log(res)
				res.forEach((item,index)=>{
					this.fileList1.push(item.data.url);
				})
				console.log(this.fileList1)
			},

            // 判断是否为图片文件（可根据需要调整）
			/*isImage(url) {
				return /\.(jpeg|jpg|gif|png|svg)$/.test(url);
			}*/
			//添加话题
			addhuati(){
				if(this.huatiList.length>=1){
					uni.showToast({
						icon: 'success',
						title: '最多添加1个话题',
						duration: 1000
					});
					return false;
				}
				this.inputValue = '';
				this.show = true;
			},
			//删除话题
			delClick(){
				this.huatiList.forEach((item,index)=>{
					this.huatiList.splice(index, 1);
				})
			},
			//关闭模态框
			confirm(){
				this.show = false;
				const obj = {};
				obj.name = this.inputValue;
				this.huatiList.push(obj);
			},
			//发布
			fabuClick(){
              const ls = {
				  name:this.inputValue,
				  content:this.value2,//textareaValue.value = formLis.value.name + '\n' + textareaValue.value
				  //content:aaa,
				  cover:this.fileList1.join(','),
				  form:1,//1文章 2动态 3提问 4发布视频
				  type:1,//1正文 2草稿 iocnFlag.value?2:1
			  }
				if(this.userType=='company'){//伯乐
					ls.member_id = this.company_id;
				}else if(this.userType=='member'){//千里马
					ls.member_id = this.member_id;
				}else if(this.userType=='headhunters'){//就业管家

				}
				addArticle(ls).then(({ status_code, data }) => {
					if (status_code == '200') {
						console.log('111数据', data)
						uni.navigateTo({
							url: './index',
						});
						//$emit('handleSelect','2-1')
					}
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 32rpx;

		.upload-box {
			margin-top: 24rpx;
		}

		.huati {
			margin-top: 24rpx;
			background: #f0f0f0;
			width: 160rpx;
			height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 20rpx;
			font-size: 24rpx;

			img {
				width: 28rpx;
				height: 28rpx;
				margin-right: 6rpx;
			}

		}
	    .bg-1{
			.d-1{
				display: inline-block;
				background: #eaece8e8;
				color:#989697;
				padding: 5rpx;
				border-radius: 20rpx;
				font-size: 24rpx;
				margin: 7rpx;
			img {
				width: 28rpx;
				height: 28rpx;
				margin-right: 6rpx;
			    }
	        }
		}
		.tips {
			margin-top: 24rpx;
			font-size: 20rpx;
			color: #999;

			.inn {
				color: #4F8CF0;
			}
		}

		.btn {
			width: 686rpx;
			height: 80rpx;
			background: #4F8CF0;
			font-size: 28rpx;
			color: #FFFFFF;
			text-align: center;
			line-height: 80rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			position: fixed;
			bottom: 100rpx;
		}
	}
</style>
