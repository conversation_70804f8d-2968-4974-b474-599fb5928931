<template>
    
    <view class="popup" v-if="visible">
        <view class="popup-content">
            <input v-model="searchQuery" @input="onSearch" placeholder="查询您想更换的职位" class="ipt"/>
            <scroll-view class="list" scroll-y @scrolltolower="loadMore" :style="{height: listHeight + 'rpx'}">

                <view v-for="item in paginatedData" :key="item.id" class="list-item" @click="selectItem(item)">
                    {{ item.job_describe }}</view>
                <view v-if="loading" class="loading">加载中...</view>
            </scroll-view>
            <button @click="close" class="btn">取消切换</button>
        </view>
    </view>
</template>

<script>
    export default {
        props: {
            visible: Boolean,
            data: Array
        },
        data() {
            return {
                searchQuery: '',
                currentPage: 1,
                itemsPerPage: 10,
                loading: false,
                listHeight: 600 // 自定义的列表高度
            };
        },
        computed: {
            filteredData() {
                return this.data.filter(item => item.job_describe.includes(this.searchQuery));
            },
            paginatedData() {
                const start = (this.currentPage - 1) * this.itemsPerPage;
                return this.filteredData.slice(0, start + this.itemsPerPage); // 只显示到当前页数的数据
            },
        },
        methods: {
            // 输入搜索
            onSearch() {
                this.currentPage = 1;
            },
            loadMore() {
                // 防止重复加载
                if (this.loading || this.paginatedData.length >= this.filteredData.length) {
                    return;
                }

                this.loading = true;
                setTimeout(() => { // 模拟数据加载
                    this.currentPage++;
                    this.loading = false;
                }, 500); // 假设数据请求时间为500ms
            },
            selectItem(item) {
                this.$emit('select', item); // 通过事件传递所选项数据
                this.close(); // 关闭弹窗
            },
            close() {
                this.$emit('close');
            },
        },
    };
</script>

<style scoped lang="scss">
    .popup {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        // border-top: 2rpx solid #ccc;
        z-index: 99999999999;
    }

    .popup-content {
        padding: 20rpx;
    }

    .list {
        max-height: 600rpx;
        /* 自定义高度 */
        overflow-y: auto;
        /* 确保可以滚动 */
    }

    .list-item {
        padding: 24rpx 0;
    }

    .loading {
        text-align: center;
        padding: 24px;
    }
    
    .btn {
        background: #4F8CF0;
        font-weight: 600;
        font-size: 34rpx;
        color: #FFFFFF;
    }
    
    .ipt {
        width: 100%;
        height: 56rpx;
        background: #f5f5f6;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
        padding: 0 24rpx;
        box-sizing: border-box;
    }
</style>