<template>
	<view>
		<u-popup :show="showRourist" :round="10" bgColor="transparent" mode="center" closeOnClickOverlay>
			<view class="rule-container" v-if="nextIndex === 0">
				<view class="rule-pop">
					<view class="rule-desc" @click="openRule">
						请阅读并同意招聘呗，
						<text class="rule-link">《用户服务协议》</text>
						和
						<text class="rule-link">《隐私协议》</text>
						允许招聘呗管理平台账号信息。
					</view>
					<view class="btns">
						<view class="btn refuse" @click="refuse">返回</view>
						<view class="btn agree" @click="agree">选择身份</view>
						<!-- <button open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" class="btn agree">
							手机授权登录
						</button> -->
					</view>
				</view>
			</view>

			<view v-if="nextIndex === 1">
				<view class="cardTitle">选择身份</view>
				<view :class="tabIndex == 'member' ? 'cardBox cardBox1 active' : 'cardBox cardBox1'" @click="changeCard('member')">
					<image
						src="https://api-test.zhaopinbei.com/storage/uploads/images/jAfxcbrUljqX3kVonUX7C3eqePCloRBP9BewbEDp.png"
						mode=""
						class="cardImg1"></image>
					<view class="cardBoxText">
						<view class="cardBoxText_til">我要求职</view>
						<view class="cardBoxText_text">你的未来，从这里开始！！</view>
					</view>
				</view>

				<view :class="tabIndex == 'company' ? 'cardBox cardBox2 active' : 'cardBox cardBox2'" @click="changeCard('company')">
					<image
						src="https://api-test.zhaopinbei.com/storage/uploads/images/s73PfxstAVk658qwTi3xNQP0BMBzGZ80QT5iLuKa.png"
						mode=""
						class="cardImg2"></image>
					<view class="cardBoxText">
						<view class="cardBoxText_til">我要招聘</view>
						<view class="cardBoxText_text">让招聘变得快捷、便捷！</view>
					</view>
				</view>
				<view :class="tabIndex == 'headhunters' ? 'cardBoxHead  active' : 'cardBoxHead'" @click="changeCard('headhunters')">
					我是就业管家
				</view>
				<!-- <view class="trueBtnCla" @click="loginBtn">确认</view> -->
				<button open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" class="trueBtnCla">手机授权登录</button>
			</view>

			<view v-if="nextIndex === 2">
				<view class="cardTitle">选择求职身份</view>
				<view :class="flag == 'student' ? 'cardBox cardBox3 active' : 'cardBox cardBox3'" @click="changeTab('student')">
					<image
						src="https://api-test.zhaopinbei.com/storage/uploads/images/JcxrAcryJL92lwRCkhJQktIn4ai7x6IbN67LCo8P.png"
						mode=""
						class="cardImg3"></image>
					<view class="cardBoxText">
						<view class="cardBoxText_til">我是学生</view>
						<view class="cardBoxText_text">梦想启航，校园招聘季来袭！</view>
					</view>
				</view>

				<view :class="flag == 'work' ? 'cardBox cardBox3 active' : 'cardBox cardBox3'" @click="changeTab('work')">
					<image
						src="https://api-test.zhaopinbei.com/storage/uploads/images/u6WZRkJtUruGrMlPCkiQYTGQhD2fjUZKv6JHyvdU.png"
						mode=""
						class="cardImg4"></image>
					<view class="cardBoxText">
						<view class="cardBoxText_til">我是职场人</view>
						<view class="cardBoxText_text">为卓越而招聘，期待你的加入！</view>
					</view>
				</view>
				<view class="trueBtnCla" @click="go">确认</view>
			</view>

			<selectJobs ref="selectJobsRef" @childEvent="handleChildEvent"></selectJobs>
		</u-popup>
	</view>
</template>

<script>
import selectJobs from '../components/selectJobs.vue';
import { quickLogin } from '../config/api.js';
export default {
	name: 'tourist',
	components: {
		selectJobs,
	},
	data() {
		return {
			showRourist: false,
			tabIndex: 'member',
			nextIndex: 0,
			codeList: '',
			flag: '',
		};
	},
	onShow() {},
	watch() {},
	methods: {
		handleChildEvent(data) {
			this.showRourist = data;
			this.$emit('childDestroyed', 1); // 发射事件通知父组件，准备销毁
		},
		changeCard(ind) {
			this.tabIndex = ind;
		},
		open() {
			this.showRourist = true;
			uni.login({
				success: loginRes => {
					this.codeList = loginRes.code;
					console.log('11111111', loginRes);
				},
				fail: str => {
					uni.showToast({
						title: '请确认您的微信是否登录',
						icon: 'none',
					});
				},
			});
		},
		openRule() {},
		agree() {
			this.nextIndex = 1;
			// uni.navigateTo({
			// 	url:"/pagesA/tourist/select_tourist_one"
			// })
		},
		async getPhoneNumber(e) {
			let detail = e.detail;
			if (detail.errMsg === 'getPhoneNumber:ok') {
				let code = detail.code;

				let params = {
					code: this.codeList,
					encryptedData: detail.encryptedData,
					encryptedCode: detail.code,
					iv: detail.iv,
					user_code: code,
					type: this.tabIndex,
				};
				const { data, status_code, message } = await quickLogin(params);
				console.log(data);
				const { role_type, token, member, job_class_count } = data;

				if (status_code == 200) {
					this.$store.commit('setToken', token);
					this.$store.commit('setRoleType', role_type);
					this.$store.commit('setUserInfo', data);
					if (role_type == 'headhunters') {
						this.showRourist = false;
						this.$emit('childDestroyed', 1);
						return;
					}
					if (job_class_count < 1) {
						if (role_type == 'member') {
							this.nextIndex = 2;
							return;
						}
						this.go();
						return;
					}
					// this.nextIndex = 2
				}
				this.showRourist = false;
				this.$emit('childDestroyed', 1);
			} else {
				console.log('用户拒绝授权');
			}
		},
		changeTab(index) {
			this.flag = index;
		},
		go() {
			this.nextIndex = 3;
			// console.log("22222",this.$refs.selectJobsRef)
			this.$refs.selectJobsRef.open(this.flag);
			// uni.navigateTo({
			// 	url: "/pagesA/tourist/selectJob?flag=" + this.flag
			// })
		},
		refuse() {
			this.showRourist = false;
		},
	},
};
</script>

<style lang="less" scoped>
.rule-container {
	padding: 0 24rpx;

	.rule-pop {
		display: flex;
		flex-direction: column;
		background: #ffffff;
		padding: 48rpx;
		border-radius: 20rpx;
		opacity: 0.9;

		.rule-desc {
			font-weight: 400;
			font-size: 28rpx;
			color: #000000;

			.rule-link {
				color: #4f8cf0;
			}
		}

		.btns {
			display: flex;
			justify-content: center;
			margin-top: 24rpx;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 1;
				height: 60rpx;
				border-radius: 16rpx;
				border: 1px solid #4f8cf0;
				font-size: 28rpx;

				&:first-child {
					margin-right: 24rpx;
				}
			}

			.refuse {
				color: #fe4d4f;
				border: 1px solid #fe4d4f;
			}

			.agree {
				background: #4f8cf0;
				color: #ffffff;
			}
		}
	}
}

.cardTitle {
	width: 100%;
	height: 56rpx;
	// font-family: PingFang SC, PingFang SC;
	font-weight: 600;
	font-size: 40rpx;
	color: #ffffff;
	line-height: 47rpx;
	text-stroke: 2rpx #4f8cf0;
	text-align: center;
	font-style: normal;
	text-transform: none;
	-webkit-text-stroke: 2rpx #4f8cf0;
	margin-bottom: 32rpx;
}

.cardBox {
	width: 686rpx;
	height: 240rpx;
	background: #ffffff;
	border-radius: 24rpx 24rpx 24rpx 24rpx;
	margin-bottom: 32rpx;
	box-sizing: border-box;
	border: 8rpx solid transparent;
}

.cardBoxHead {
	width: 686rpx;
	height: 96rpx;
	background: #ffffff;
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	text-align: center;
	line-height: 96rpx;
	font-weight: 600;
	font-size: 34rpx;
	color: #4f8cf0;
	border: 8rpx solid transparent;
	margin-bottom: 32rpx;
}

.cardBox1 {
	padding: 10rpx 32rpx;
	padding-right: 56rpx !important;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.cardBox2 {
	padding: 10rpx 56rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.cardBox3 {
	padding: 10rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.active {
	border: 8rpx solid #4f8cf0;
}

.cardImg1 {
	width: 234rpx;
	height: 220rpx;
}

.cardImg2 {
	width: 188rpx;
	height: 220rpx;
}

.cardImg3 {
	width: 210rpx;
	height: 220rpx;
}

.cardImg4 {
	width: 244rpx;
	height: 220rpx;
}

.cardBoxText {
	flex: 1;
	text-align: center;
}

.cardBoxText_til {
	font-weight: 600;
	font-size: 40rpx;
	color: #000000;
}

.cardBoxText_text {
	font-weight: 400;
	font-size: 24rpx;
	color: #999999;
	margin-top: 8rpx;
}

.trueBtnCla {
	width: 442rpx;
	height: 96rpx;
	background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
	border-radius: 16rpx 16rpx 16rpx 16rpx;

	font-weight: 600;
	font-size: 34rpx;
	color: #ffffff;
	text-align: center;
	line-height: 96rpx;
	margin: 0 auto;
}
</style>
