<template>
    <view class="home-index">
        <view class="task-info">
            <view class="task">
                <view class="name">
                    {{details.title}}
                </view>
                <view class="per">
                    {{details.unit_total}}/每位
                </view>
            </view>
            <view class="desc">
                <view class="time">
                    {{details.start_at}}-{{details.end_at}}
                </view>
                <view :class="['type',details.type=='job'?'blue':details.type=='custom'?'yellow':details.type=='job_active'?'green':'']">
                    {{details.type_name}}
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="info">
                <view class="username">
                    <!--  -->
                    <image :src="details.user_member_info.image.thumbnail_path_url" mode=""></image>
                    <view class="name">
                        {{details.user_member_certification.name}}
                    </view>
                </view>
                <view class="phone">
                    {{details.user_member_certification.mobile_no}}
                </view>
            </view>
        </view>

        <view class="item-wrap">
            <view class="item" v-if="taskJobList.title" @click="goDetails">
                <view class="name gl">
                    关联职位
                </view>

                <view class="number ms">
                    {{taskJobList.title}}
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="item">
                <view class="name">
                    任务总进度
                </view>

                <view class="number">
                    {{details.need_count}}/{{details.count}}
                </view>
            </view>
        </view>


        <view class="list">
            <view class="item" v-for="(item,index) in taskUserList" :key="item.id" @click="goPeopleDetail(item.id)">
                <view class="info">
                    <image :src="item.member_info.image.thumbnail_path_url" mode=""></image>
                    <view class="userInfo">
                        <view class="name">
                            {{item.member_certification.name?item.member_certification.name:item.member_info.nick_name}}
                        </view>
                        <view class="phone">
                            {{item.member.cellphone}}
                        </view>
                    </view>
                </view>
                <u-line length="100%" color="#F5F5F7"></u-line>
                <view class="desc">
                    <view class="rs">
                        已招人数
                    </view>
                    <view class="num">
                        {{item.promotions_count}}
                    </view>
                </view>
            </view>
        </view>


        <view class="footer" v-if="details.active_status!='end'">
            <view class="btns" @click="closeTask(details.id)">
                <view class="btn zx">
                    结束任务
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import {getTaskListDetail, getTaskUser, taskJobs,taskClose} from '../../config/headhunterList_api.js'

    export default {
        data() {
            return {
                details: '',
                page: 1,
                limit: 10,
                taskUserList: [],
                taskJobList: '',
                id:''
            }
        },
        onLoad(option) {
            this.id = option.id;
            console.log("option", option)
            this.getTaskListDetail(option.id)
            this.getTaskUser(option.id)
            this.taskJobs(option.id)
        },
        methods: {
            // 分享功能
            onShareAppMessage(res) {
                const pages = getCurrentPages();//获取当前页面的参数
                // pages[0].$page.fullPath//当前页面路径及页面参数
            	if (res.from === 'button') { // 来自页面内分享按钮
            		console.log(res.target)
            	}
            	return {
            		title: this.details.title,
            		path: '/pagesA/details/taskDetails?id='+this.id,
            	}
            },
            // 结束任务
            async closeTask(id) {
                let params = {
                    id
                }
                
                const res = await taskClose(params)
                if(res.status_code=='200') {
                    this.details.active_status = res.data.active_status;
                    uni.showToast({
                        title: '任务已结束',
                        icon: 'none'
                    })
                }
            },
            // 获取关联职位
            async taskJobs(id) {
                let params = {
                    id
                }
                const res = await taskJobs(params)
                this.taskJobList = res.data[0]
                console.log("关联职位", res)
            },
            // 获取任务详情
            async getTaskListDetail(id) {
                let params = {
                    id
                }
                const res = await getTaskListDetail(params)
                this.details = res.data
                console.log("res", res)
            },
            // 领取任务人列表
            async getTaskUser(id) {
                let params = {
                    task_id: id * 1,
                    limit: this.limit,
                    page: this.page
                }
                const res = await getTaskUser(params)
                this.taskUserList = res.data.data
                console.log("res", this.taskUserList)
            },
            goPeopleDetail(id) {
                uni.navigateTo({
                    url: '/pagesA/details/jyManaTaskDetailsPeople?id=' + id
                })
            },
            goDetails() {
                const pages = getCurrentPages(); //获取当前页面的参数
                const model_user_id = uni.getStorageSync('userInfo').login_user.id;
                // if (uni.getStorageSync('roleType') != 'member') return
                uni.navigateTo({
                    url: `/pagesA/details/memberJobDetails?id=${this.taskJobList.id}&model_user_id=${model_user_id}&model_id=${this.details.id}&model_type=task`
                })
            }
        }
    }
</script>
<style>
    page {
        background-color: #f5f5f7;
    }
</style>
<style lang="less" scoped>
    .home-index {
        padding: 32rpx 32rpx 170rpx 32rpx;
    }

    .task-info {
        display: flex;
        flex-direction: column;
        background-color: #FFFFFF;
        padding: 32rpx;
        border-radius: 24rpx;

        .task {
            display: flex;
            justify-content: space-between;

            .name {
                display: flex;
                flex: 1;
                font-weight: 500;
                font-size: 32rpx;
                color: #333333;
            }

            .per {
                font-weight: 600;
                font-size: 32rpx;
                color: #F98A14;
                margin-left: 16rpx
            }
        }

        .desc {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 16rpx 0;

            .time {
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
            }

            .type {
                font-weight: 600;
                font-size: 24rpx;
            }

            .yellow {
                color: #F9AD14;
            }

            .blue {
                color: #1690FF;
            }

            .green {
                color: #57D51C;
            }
        }

        .info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 32rpx;

            .username {
                display: flex;
                align-items: center;
                font-weight: 500;
                font-size: 28rpx;
                color: #333333;

                image {
                    width: 48rpx;
                    height: 48rpx;
                    margin-right: 32rpx;
                }
            }

            .phone {
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
            }
        }
    }

    .item-wrap {
        display: flex;
        flex-direction: column;
        margin: 32rpx 0;
        border-radius: 24rpx;
        background-color: #FFFFFF;

        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 104rpx;
            padding: 0 32rpx;

            .name {
                font-weight: 400;
                font-size: 28rpx;
                color: #333333;
            }

            .number {
                font-weight: 600;
                font-size: 28rpx;
                color: #4F8CF0;
            }

            .gl {
                font-weight: 500;
                font-size: 28rpx;
                color: #999999;
            }

            .ms {
                font-weight: 500;
                font-size: 28rpx;
                color: #333333;
            }
        }

    }

    .list {
        display: flex;
        // flex-direction: column;
        flex-wrap: wrap;
        justify-content: space-between;

        .item {
            display: flex;
            flex-direction: column;
            width: calc(45% - 24rpx);
            padding: 24rpx;
            background: #FFFFFF;
            border-radius: 24rpx;
            margin-bottom: 24rpx;

            .info {
                display: flex;
                padding-bottom: 16rpx;

                image {
                    width: 80rpx;
                    height: 80rpx;
                }

                .userInfo {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    flex: 1;
                    padding-left: 16rpx;

                    .name {
                        font-weight: 500;
                        font-size: 28rpx;
                        color: #333333;
                    }

                    .phone {
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #999999;
                    }
                }
            }

            .desc {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 16rpx;

                .rs {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #999999;
                }

                .num {
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #333333;
                }
            }
        }
    }

    .footer {
        position: fixed;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 140rpx;
        width: 100%;
        left: 0;
        bottom: 0;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 28rpx;
        border-radius: 24rpx 24rpx 0 0;

        .btns {
            display: flex;
            width: 90%;

            .btn {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 80rpx;
                color: #333333;
                border-radius: 16rpx;
            }

            .zx {
                color: #FFFFFF;
                background: linear-gradient(135deg, #F0544F 0%, #EE1E1E 100%);
            }
        }
    }
</style>