<template>
	<view class="container">
		<view class="context">
			<!-- 输入区域 -->
			<view class="input-area">
				<view class="section">
					<input class="input" placeholder="请输入工作内容" v-model="inputValue" maxlength="1000" />
				</view>
				<view class="box">
					<view class="text-box">
						<text :class="inputValue.length == 0 ? 'active':'count'">
							{{inputValue.length}}
						</text>
						<text class="count-bottom">
							/3000</text>
					</view>
					<view class="Model">
						<view class="model-left">
							<img src="/static/jobsetting/无序列表<EMAIL>" alt=""
								style="width: 40rpx; height: 40rpx;" />
							<img src="/static/jobsetting/无序列表<EMAIL>" alt=""
								style="width: 40rpx; height: 40rpx;" />
						</view>
						<view style="display: flex; align-items: center;" @click="toggle('bottom')">
							<img src="/static/jobsetting/范例<EMAIL>" alt="" style="width: 40rpx; height: 40rpx;" />
							<text style="color: rgba(79, 140, 240, 1); font-size: 28rpx;">范例</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<uni-popup ref="popup" background-color="#fff">
			<scroll-view scroll-y="true" class="scroll-Y">
				<view class="popup-box">
					<view class="popup-context">
						<view class="popup-header">
							<view class="popup-title" :class="isTitleClicked ? 'popup-active': 'popup-title' "
								@click="selectTitle">
								优质范例
							</view>
							<view class="popup-subtitle" :class="isSubtitleClicked ? 'popup-active': 'popup-subtitle' "
								@click="selectSubtitle">
								内容模板
							</view>
						</view>
						<view class="popup-close" @click="closeTip">×</view>
					</view>
					<view class="popup-content" v-if="isTitleClicked">
						<view class="recommendation">
							<view class="recommendation-desc">为你推荐相似求职者的优质简历内容参考，均经过专业HR和招聘业务方认定</view>
						</view>
						<view class="resume-example" v-for="item in resume" :key='item.style'>
							<view class="resume-box">
								<view style="display: flex; justify-content: space-between; align-items: center; width: 214rpx; height: 60rpx;
									margin-bottom: 24rpx;">
									<view class="resume-icon"></view>
									<view class="resume-title">{{item.style}}</view>
								</view>
								<view class="resume-text">
									<view class="resume-detail">
										{{item.content}}
										<text class="resume-collapse" @click="toggleCollapse">收起</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<view class="popup-content" v-if="isSubtitleClicked">
						<view class="popup-desc">提供多种内容结构模板，让写简历更高效</view>
						<view class="template-list">
							<view class="template-item" v-for="(item, index) in templateList" :key="index">
								<view class="template-title">

									<img src="/static/jobsetting/三点.png" alt=""
										style="width: 32rpx; height: 32rpx; margin-right: 16rpx;" />
									<text class="template-name">{{item.name}}</text>
								</view>
								<view class="template-content">
									<view class="template-desc">{{item.desc}}</view>
									<view class="template-params">
										<view class="param" v-for="(param, pIndex) in item.params" :key="pIndex">
											{{param}}
										</view>
									</view>
									<button class="use-btn" @click="useTemplate(item)">{{item.useBtnText}}</button>
								</view>
							</view>
						</view>
					</view>


				</view>

			</scroll-view>
		</uni-popup>
		<!-- 完成按钮 -->
		<view class="footer">
			<button class="confirm-btn" @click="finish">完成</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				inputValue: '',
				noticehide: true,
				hidePicker: false,
				resume: [{
						style: '人力资源1',
						content: '人力资源管理硕士，获得CIPD证书，代表人力资源领域最高水平。通过英语四六级，雅思7分，考取计算机二级access证书，ACCA通过6门。七年班长，三年党组委，学生会主席团，寒招实践团宣传组组长，一年级辅导员。硕士三年学业成绩专业第一，全国大学生人力资源大赛特等奖。在XX、XX、XX、XX，共五段人力资源实习经历',
					},
					{
						style: '人力资源2',
						content: '人力资源管理硕士，获得CIPD证书，代表人力资源领域最高水平。通过英语四六级，雅思7分，考取计算机二级access证书，ACCA通过6门。七年班长，三年党组委，学生会主席团，寒招实践团宣传组组长，一年级辅导员。硕士三年学业成绩专业第一，全国大学生人力资源大赛特等奖。在XX、XX、XX、XX，共五段人力资源实习经历',
					},
					{
						style: '人力资源3',
						content: '人力资源管理硕士，获得CIPD证书，代表人力资源领域最高水平。通过英语四六级，雅思7分，考取计算机二级access证书，ACCA通过6门。七年班长，三年党组委，学生会主席团，寒招实践团宣传组组长，一年级辅导员。硕士三年学业成绩专业第一，全国大学生人力资源大赛特等奖。在XX、XX、XX、XX，共五段人力资源实习经历',
					},
				],
				templateList: [{
					name: '通用格式',
					desc: '通用介绍句式，可面向大多岗位',
					params: ['工作能力', '掌握技能', '拥有证书', '性格优点'],
					useBtnText: '使用'
				}, {
					name: 'STAR法则',
					desc: '用STRAT法则梳理工作内容，清晰又突出',
					params: ['业务背景', '业务目标', '具体工作内容', '最终获得成果'],
					useBtnText: '使用'
				}, {
					name: '负责业务...',
					desc: '体现对业务的思考和个人发挥价值',
					params: ['· 业务目标拆解', '· 落地方案实施 ', ' · 个人能力体现 ', '· 工作业绩总结'],
					useBtnText: '使用'
				}, {
					name: '工作内容...',
					desc: '体现性格亮点与对行业兴趣',
					params: ['· 负责领域 ', '· 参与工作事项', '· 具备业务能力 ', ' · 业绩介绍'],
					useBtnText: '使用'
				}],
				isTitleClicked: true,
				isSubtitleClicked: false
			}
		},
		methods: {
			closeTip() {
				this.$refs.popup.close();
			},
			finish() {
				// 完成按钮点击后的逻辑，比如提交数据等
				console.log('完成操作，输入值为：', this.inputValue);
			},
			showPicker() {
				this.hidePicker = true
			},
			toggle(type) {
				this.type = type
				// open 方法传入参数 等同在 uni-popup 组件上绑定 type属性
				this.$refs.popup.open(type)
			},
			selectTitle() {
				this.isTitleClicked = true;
				this.isSubtitleClicked = false;
				console.log();
			},
			selectSubtitle() {
				this.isSubtitleClicked = true;
				this.isTitleClicked = false;
			},
		},
	};
</script>

<style scoped>
	.container {
		padding: 20rpx 0rpx;
		background-color: #fff;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.context {
		flex: 1;
	}

	.scroll-Y {
		height: 774rpx;
	}

	.tip-bar {
		background-color: #ffd6b3;
		padding: 10rpx 15rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.tip-text {
		color: #f60;
		font-size: 28rpx;
		margin-left: 16rpx;
	}

	.close-icon {
		color: #ccc;
		font-size: 32rpx;
	}

	.input-area {
		margin-top: 20rpx;
		padding: 32rpx;
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 1173rpx;
	}

	.label {
		font-size: 32rpx;
		color: #333;
	}

	.input {
		width: 100%;
		font-size: 32rpx;
		border: none;
		padding: 10rpx 0;
	}

	.section {
		/* margin-bottom: 30rpx; */
		width: 686rpx;
		padding: 32rpx 0rpx;
		/* border-bottom: 1rpx solid rgba(230, 230, 230, 1); */
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		flex: 1;
	}

	.count {
		color: rgba(79, 140, 240, 1);
		font-size: 28rpx;
		float: right;
		margin-top: 24rpx;
	}

	.count-bottom {
		color: rgba(153, 153, 153, 1);
		font-size: 28rpx;
		margin-top: 24rpx;
	}

	.text-box {
		display: flex;
		justify-content: flex-end;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-image: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.Model {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.model-left {
		width: 104rpx;
		height: 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.popup-content {
		/* width: 750rpx;
		height: 774rpx; */
		display: flex;
		flex-direction: column;
		align-items: center;

	}

	.popup-box {
		padding: 24rpx 32rpx;
	}

	.popup-context {
		display: flex;
		justify-content: space-between;
	}

	.popup-title {
		font-size: 32rpx;
		/* font-weight: bold; */
	}

	.popup-subtitle {
		font-size: 28rpx;
		color: #666;
		margin-left: 42rpx;
	}

	.popup-active {
		background-image: url('https://api-test.zhaopinbei.com/storage/uploads/images/sySQp2NnCVbjDxzMArI8JLcNwIZRVDHGn1KKKS5N.png');
		background-size: auto;
		font-size: 32rpx;
		font-weight: bold;
		color: rgba(51, 51, 51, 1);
	}

	.popup-close {
		font-size: 36rpx;
		cursor: pointer;
	}

	.popup-content {
		/* padding: 30rpx; */
		display: flex;
		justify-content: space-between;
	}

	.recommendation {
		margin-bottom: 30rpx;
	}

	.recommendation-desc {
		font-size: 24rpx;
		color: #999;
	}

	.resume-example {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		width: 686rpx;
		height: 364rpx;
		padding: 24rpx;
		border-radius: 12rpx;
		background-color: rgba(245, 245, 245, 1);
	}

	.resume-icon {
		width: 30px;
		height: 30px;
		background-color: #ddd;
		border-radius: 50%;
		margin-right: 10px;
	}

	.resume-text {
		flex: 1;
	}

	.resume-title {
		font-size: 14px;
		margin-bottom: 5px;
	}

	.resume-detail {
		font-size: 26rpx;
		line-height: 1.5;
	}

	.resume-collapse {
		color: #1aad19;
		cursor: pointer;
	}

	.popup-footer {
		text-align: center;
		padding: 15px;
	}

	.save-button {
		width: 100%;
		padding: 10px;
		background-color: #007aff;
		color: #fff;
		border: none;
		border-radius: 5px;
		font-size: 14px;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
	}

	.active {
		color: red;
		margin-top: 24rpx;
		font-size: 28rpx;
	}

	.popup-desc {
		font-size: 28rpx;
		color: #999;
		margin-top: 32rpx;
		margin-bottom: 24rpx;
		align-self: flex-start;
	}

	.template-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.template-item {
		width: 283rpx;
		margin-bottom: 32rpx;
		background-color: #F5F7FA;
		padding: 24rpx;
		border-radius: 16rpx;
		box-shadow: 0 0 12px rgba(0, 0, 0, 0.13);
	}

	.template-title {
		display: flex;
		align-items: center;
	}

	.template-icon {
		width: 60px;
		height: 60rpx;
		background-color: #007AFF;
		margin-bottom: 20rpx;
	}

	.template-name {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
	}

	.template-desc {
		font-size: 20rpx;
		color: #666;
		margin-bottom: 20rpx;
	}

	.template-params {
		margin-bottom: 20rpx;
		align-self: flex-start;
	}

	.param {
		font-size: 20rpx;
		color: #999;
		display: block;
	}

	.use-btn {
		background-color: #007AFF;
		color: white;
		border: none;
		font-size: 24rpx;
		border-radius: 50rpx;
		width: 128rpx;
		height: 62rpx;
		margin: 0px;
	}

	.template-content {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.resume-box {
		display: flex;
		flex-direction: column;
		align-self: center;
		height: 280rpx;
	}
</style>
