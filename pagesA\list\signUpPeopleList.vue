<template>
    <view id="app">
        <view v-for="item in list" :key="item.member_id"
            :class="['personCla', { selected: selectedIds.includes(item.member_id) }]"
            @click="toggleSelection(item.member_id)">
            {{ item.member_info.nick_name }}
        </view>
        <Pages :status="status"></Pages>
        <view style="height: 196rpx;"></view>
        <view class="bottomBox">
            <view class="btn redBtn" @click="addBtn(1)">取消</view>
            <view class="btn greyBtn" @click="addBtn(2)">清空</view>
            <view class="btn blueBtn" @click="addBtn(3)">确定</view>
        </view>
    </view>
</template>

<script>
    import {communicate} from "../../common/common.js";
    import {
        signUpJob
    } from '../../config/headhunterList_api.js'
    import {
        getAuthApplicantList
    } from "../../config/api.js"
    import Pages from '../../components/pages.vue'
    export default {
        components: {
            Pages
        },
        data() {
            return {
                id: '',
                page: 1,
                limit: 10,
                status: 'loadmore',
                more: false,
                list: [],
                selectedIds: []
            };
        },
        onLoad(option) {
            this.id = option.id
            this.getAuthApplicantList()
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getAuthApplicantList()
            } else {
                this.status = 'nomore'
            }
        },
        methods: {
            
            async getAuthApplicantList() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    // open_status:1,//是否公开简历
                    // cellphone:this.keyword,
                    // rule:'modify',
                    auth_type: this.auth_type
                }
                const {
                    status_code,
                    data,
                    message
                } = await getAuthApplicantList(params)
                if (status_code == 200) {
                    this.list = this.list.concat(data.data);
                }
            },
            addBtn(num) {
                if (num == 1) {
                    uni.navigateBack()
                }
                if (num == 2) {
                    this.selectedIds = []
                }
                if (num == 3) {
                    this.signUp()
                }
            },
            async signUp() {
                if (this.selectedIds.length == 0) {
                    return uni.showToast({
                        title: '请选择报名人',
                        icon: 'none'
                    });
                }
                const report_data = this.selectedIds.map(id => ({
                    member_id: id,
                    member_resume_id: 0,
                    job_id: this.id,
                    model_user_id: 0
                }));

                let params = {
                    model_type:'job',
                    report_data:report_data
                }

                const res = await signUpJob(params)
                if (res.status_code == 200) {
                    uni.showToast({
                        title: '报名成功',
                        icon: 'none'
                    });
                } else {
                    uni.showToast({
                        title: res.message,
                        icon: 'none'
                    });
                    if(res.message.includes("尚未授权")) {
                        communicate('job',this.id)
                    }
                }

            },
            toggleSelection(id) {
                const index = this.selectedIds.indexOf(id);
                if (index > -1) {
                    // 如果已经选中，则取消选择
                    this.selectedIds.splice(index, 1);
                } else {
                    // 如果未选中，则添加选择
                    this.selectedIds.push(id);
                }
            }
        }
    }
</script>
<style>
    page {
        background: #f5f5f7;
    }
</style>
<style scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .personCla {
        padding: 24rpx;
        border: 2rpx solid #ccc;
        margin-bottom: 12px;
        border-radius: 24rpx;
    }

    .selected {
        border-color: blue;
    }

    .bottomBox {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 196rpx;
        background: #FFFFFF;
        box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
        display: flex;
        padding: 24rpx 32rpx 92rpx 32rpx;
        justify-content: space-between;
    }

    .btn {
        width: 216rpx;
        height: 80rpx;
        text-align: center;
        line-height: 80rpx;
        color: #FFFFFF;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
    }

    .greyBtn {
        background: #ccc;
    }

    .redBtn {
        background: linear-gradient(135deg, #F0544F 0%, #EE1E1E 100%);
    }

    .blueBtn {
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
    }
</style>