<template>
	<!-- 个人权利行使 -->
	<view class="container">
		<u-navbar bgColor="transparent" placeholder :autoBack="true" />
		<scroll-view class="scroll-view" :scroll-y="true">
			<view class="scroll-container">
				<view class="title">个人权利行使11</view>

				<view class="card-container">
					<u-cell-group v-for="v in list" :title="v.title" :border="false" :key="v.title">
						<u-cell v-for="_v in v.children" :key="_v.key" :title="_v.title"
							:titleStyle="{fontSize: '24rpx', color: '#333333'}" isLink></u-cell>
					</u-cell-group>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					title: '访问、修改个人信息',
					children: [{
							key: '1',
							title: '访问和修改姓名',
						},
						{
							key: '2',
							title: '访问和修改性别',
						},
						{
							key: '3',
							title: '访问和修改手机号',
						}
					]
				}, {
					title: '删除个人信息',
					children: [{
						key: '4',
						title: '删除个人信息',
					}]
				}, {
					title: '改变授权范围',
					children: [{
							key: '5',
							title: '改变或撤回敏感信息权限',
						},
						{
							key: '6',
							title: '拒绝接受推送',
						},
						{
							key: '7',
							title: '隐藏求职行为数据',
						}
					]
				}]
			}
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-cell {
		.u-cell__body {
			padding-inline: 0 !important;
		}
	}

	::v-deep .u-cell-group {
		.u-cell-group__title {
			padding-inline: 0 !important;
			font-size: 28rpx;
			color: #333333;
		}
	}

	.container {
		height: 100vh;
		background-color: #F5F5F7;
		background-image: url(https://api-test.zhaopinbei.com/storage/uploads/images/w33HtRxPH6VQJa8L98BqkNjMbGEbKh115A3BdGH3.png);
		background-size: contain;
		background-repeat: no-repeat;
		display: flex;
		flex-direction: column;

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 64rpx;
				padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
				padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

				.title {
					color: #333333;
					font-size: 32rpx;
				}

				.card-container {
					background-color: #FFFFFF;
					border-radius: 24rpx;
					padding-block: 24rpx;
					padding-inline: 24rpx;
				}
			}
		}
	}
</style>