<template>
   <view>
       <u-popup :show="show" round="10" mode="center" @close="handleClose">
           <view>
               <view class="wrap">
                   <view class="title">面试详情</view>
                   <view class="items">
                       <view class="item">
                           <text class="lab">面试人：</text>
                           <text class="name">{{currentItem.sendText.contact_name}}</text>
                       </view>
                       <view class="item">
                           <text class="lab">报名渠道：</text>
                           <text class="name">活动报名</text>
                       </view>
                       <view class="item">
                           <text class="lab">联系电话：</text>
                           <text class="name">{{currentItem.sendText.contact_cellphone}}</text>
                       </view>
                       <view class="item">
                           <text class="lab">面试时间：</text>
                           <text class="name">{{currentItem.sendText.interviewed_at}}</text>
                       </view>
                       <view class="item">
                           <text class="lab">面试地址：</text>
                           <text class="name">{{currentItem.sendText.address.map_address}}{{currentItem.sendText.address.address}}</text>
                       </view>
                       <view class="item">
                           <text class="lab">面试备注：</text>
                           <text class="name">{{currentItem.sendText.remark}}</text>
                       </view>
                        
                       <view class="item" v-if="roleType=='member'">
                           <view @click="handleResponse(currentItem,2)" class="btn1">拒绝</view>
                           <view @click="handleResponse(currentItem,1)" class="btn">同意</view>
                       </view>
                   </view>
               </view>
           </view>
       </u-popup>
   </view>
</template>

<script>
    import {
        isAuth
    } from '@/common/common.js'
    import {

        interviewApprove,
    } from "../../config/api.js"
    export default {
        name:'InterviewDetails',
        props: {
            show: {
                type: Boolean,
                required: true
            },
            currentItem: {
                type: Object,
                required: true
            },
            roleType: {
                type: String,
                required: true
            },
        },
        methods: {
            handleClose() {
                this.$emit('close');
            },
            handleResponse(response) {
                
                // this.$emit('response', response);
                // this.handleClose();
            },
            //审核
            handleResponse(op,status) {
                console.log("op",op);
                let self = this;
                var _item = op.sendText;
                var _status = status;
                
                if (!isAuth(['auth'])) return
                uni.showModal({
                    title: _status == 1 ? '是否同意该面试' : '是否取消该面试',
                    editable: _status == 1 ? false : true,
                    placeholderText: '请输入取消原因',
                    success: async function(res) {
                        if (res.confirm) {
                            const {
                                status_code,
                                data,
                                message
                            } = await interviewApprove({
                                interview_id: _item.interview_id,
                                status: _status,
                                remark: _status == 1 ? '同意' : res.content
                            });
                            if (status_code == '200') {
                                uni.$u.toast('成功')
                                // self.page.data.splice(_index, 1, data);
                                this.handleClose();
                            }
                            if (status_code == '501') {
                                uni.$u.toast('请实名认证')
                                setTimeout(function() {
                                    uni.navigateTo({
                                        url: '/pages/my/cert'
                                    })
                                }, 3000);
            
                            }
            
                        } else if (res.cancel) {}
                    }
                })
            
            
            },
        }
    }
</script>

<style lang="less" scoped>
    .wrap {
        display: flex;
        flex-direction: column;
        width: 600rpx;

        .title {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 92rpx;
            background: rgba(79, 140, 240, 0.1)
        }

        .items {
            display: flex;
            flex-direction: column;

            .item {
                display: flex;
                justify-content: space-between;
                padding: 24rpx;

                .lab {
                    display: flex;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                }

                .name {
                    flex: 1;
                    flex-wrap: wrap;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                }
                
                .btn {
                    width: 202rpx;
                    height: 66rpx;
                    background: #4F8CF0;
                    border-radius: 16rpx 16rpx 16rpx 16rpx;
                    text-align: center;
                    line-height: 66rpx;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;
                }
                
                .btn1 {
                    width: 202rpx;
                    height: 66rpx;
                    background: #FE4D4F;
                    border-radius: 16rpx 16rpx 16rpx 16rpx;
                    text-align: center;
                    line-height: 66rpx;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;
                }
            }
        }
    }
</style>