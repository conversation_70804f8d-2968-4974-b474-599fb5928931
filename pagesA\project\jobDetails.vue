<template>
	<view id="app">

		<view class="topBox">
			<view class="topBox_title">
				<view class="topBox_title_text">销售销售销售销售销售销售销售</view>
				<view class="topBox_title_num">20-15K</view>
			</view>

			<view class="requireBox">
				<view class="requireChild">
					<image src="/static/images/project/position1Img.png" mode="" class="iconImg"></image>
					<view class="requireText">郑州·金水区</view>
				</view>
				<view class="requireChild">
					<image src="/static/images/project/jobYear.png" mode="" class="iconImg"></image>
					<view class="requireText">3-5年</view>
				</view>
				<view class="requireChild">
					<image src="/static/images/project/qualificationImg.png" mode="" class="iconImg"></image>
					<view class="requireText">本科</view>
				</view>
			</view>

			<view class="hrBox"></view>

			<view class="topBox_bottom">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode="" class="peopleHead"></image>
				<view class="topBox_bottom_text">
					<view class="topBox_bottom_text_name">发布人姓名</view>
					<view class="topBox_bottom_text_company">发布人公司</view>
				</view>
			</view>

		</view>


		<view class="detailsBox">
			<view class="titleText">就业方式</view>
			<view class="titleText_type">实习</view>
			<view class="titleText">职位内容</view>
			<view class="details_text">
				职位内容职位内容职位内容职位内容职位内容职位内容职位内容职位内容职位内容职位内容职位内容职位内容职位内容
			</view>
			<view class="titleText">职位地址</view>
			<view class="posBox">
				<image src="/static/images/project/position2Img.png" mode="" class="positionIcon"></image>
				<view class="positionCla">郑州金水区郑州金水区郑州金水区郑州金水区郑州金水</view>
			</view>
			<map :latitude="latitude" :longitude="longitude" :markers="covers" class="mapCla"></map>

		</view>

		<view class="companyDetail">
			<view class="titleText">公司信息</view>
			<view class="companyDetail_box">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode="" class="companyImg"></image>
				<view class="companyDetail_right">
					<view class="companyDetail_right_one">公司名称</view>
					<view class="companyDetail_right_two">2000人 | 天使轮</view>
				</view>
			</view>
		</view>

		<!-- <view class="jobQuestionBox">
			<view class="titleText">职位问答</view>
			<view class="hrBox"></view>
			<view class="questionChild">
				<view class="questionChild_red">
					<image src="/static/images/project/redIcon.png" mode="" class="redIcon"></image>
					<view class="questionText">问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题</view>
				</view>
				<view class="questionChild_red">
					<image src="/static/images/project/greenIcon.png" mode="" class="redIcon"></image>
					<view class="answerText">问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题问题</view>
				</view>
			</view>
		</view> -->


	</view>
</template>

<script>
	export default {
		data() {
			return {
				latitude: 39.909,
				longitude: 116.39742,
				covers: [{
					id: 1,
					latitude: 39.909,
					longitude: 116.39742,
					width: '30', // 标记点图标宽度
					height: '35' // 标记点图标高度
				}]
			}
		},
		methods: {

		}
	}
</script>

<style scoped>
	view {
		box-sizing: border-box;
	}

	#app {
		width: 100%;
		padding: 32rpx;
		min-height: 100vh;
		height: auto;
		background: #F5F5F7;
	}

	.topBox {
		width: 100%;
		height: auto;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 24rpx 32rpx;
		margin-bottom: 22rpx;
	}

	.topBox_title {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.topBox_title_text {
		font-weight: 500;
		font-size: 32rpx;
		color: #333333;
	}

	.topBox_title_num {
		font-weight: 500;
		font-size: 32rpx;
		color: #4F8CF0;
	}

	.requireBox {
		display: flex;
		margin-top: 24rpx;
	}

	.requireChild {
		display: flex;
		margin-right: 24rpx;
	}

	.iconImg {
		width: 28rpx;
		height: 28rpx;
	}

	.requireText {
		font-weight: 400;
		font-size: 20rpx;
		color: #999999;
	}

	.hrBox {
		width: 622rpx;
		height: 2rpx;
		background: #F5F5F7;
		margin: 24rpx 0;
	}

	.topBox_bottom {
		display: flex;
	}

	.peopleHead {
		width: 72rpx;
		height: 72rpx;
		border-radius: 36rpx;
		margin-right: 16rpx;
	}

	.topBox_bottom_text {
		height: 72rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.topBox_bottom_text_name {
		font-weight: 500;
		font-size: 24rpx;
		color: #333333;
	}

	.topBox_bottom_text_company {
		font-weight: 400;
		font-size: 20rpx;
		color: #999999;
	}

	.detailsBox {
		width: 100%;
		/* height: 1102rpx; */
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 24rpx 32rpx;
		margin-bottom: 22rpx;
	}

	.titleText {
		font-weight: bold;
		font-size: 32rpx;
		color: #333333;
		margin: 24rpx 0;
	}

	.hrBox {

	}

	.titleText_type {
		font-weight: 400;
		font-size: 28rpx;
		color: #333333;
	}

	.details_text {
		font-weight: 400;
		font-size: 24rpx;
		color: #333333;
		line-height: 28rpx;
		padding-bottom: 24rpx;
		border-bottom: 2rpx solid #F5F5F7;
	}

	.posBox {
		display: flex;
		margin-bottom: 24rpx;
	}

	.positionIcon {
		width: 34rpx;
		height: 34rpx;
		margin-right: 8rpx;
	}

	.positionCla {
		font-weight: 400;
		font-size: 24rpx;
		color: #333333;
	}

	.mapCla {
		width: 100%;
		height: 296rpx;
	}

	.companyDetail_box {
		display: flex;
		align-items: center;
	}

	.companyDetail {
		width: 100%;
		height: auto;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 24rpx 32rpx;
		margin-bottom: 22rpx;
	}

	.companyImg {
		width: 72rpx;
		height: 72rpx;
		background: #D9D9D9;
		border-radius: 36rpx;
		margin-right: 16rpx;
	}

	.companyDetail_right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.companyDetail_right_one {
		font-weight: 500;
		font-size: 24rpx;
		color: #333333;
		margin-bottom: 10rpx;
	}

	.companyDetail_right_two {
		font-weight: 400;
		font-size: 20rpx;
		color: #999999;
	}

	.jobQuestionBox {
		width: 100%;
		height: 586rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 18rpx 32rpx;
	}

	.questionChild {
		width: 100%;
		padding: 26rpx 0;

		border-bottom: 2rpx solid #F5F5F7;
	}

	.questionChild_red {
		display: flex;
		align-items: center;
		margin-bottom: 32rpx;
	}

	.redIcon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 16rpx;
	}

	.questionText {
		flex: 1;
		font-weight: 400;
		font-size: 28rpx;
		color: #333333;
		white-space: nowrap;         /* 不换行 */
		  overflow: hidden;           /* 超出部分隐藏 */
		  text-overflow: ellipsis;    /* 超出部分用省略号表示 */
	}

	.answerText {
		flex: 1;
		font-weight: 400;
		font-size: 28rpx;
		color: #999999;
		white-space: nowrap;         /* 不换行 */
		  overflow: hidden;           /* 超出部分隐藏 */
		  text-overflow: ellipsis;    /* 超出部分用省略号表示 */
	}


</style>
