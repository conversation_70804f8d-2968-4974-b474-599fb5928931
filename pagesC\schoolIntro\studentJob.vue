<template>
	<view class="container ">
		<view class="flex flex-col p-3 bg-gray gap-3 overflow-y-auto">
			<view class="job">
				<view class="header">
					<view class="title flex justify-between">
						<text class="h2 font-bold PM">游戏UI设计师</text>
						<text class="p PM font-bold">12-20K</text>
					</view>
					<view class="main flex gap-3">
						<view class="main-col flex gap-1 items-center">
							<uni-icons type="location-filled" size="14" color="#999999"></uni-icons>
							<text>郑州·金水区</text>
						</view>
						<view class="main-col flex gap-1 items-center">
							<img src="../../static/new/公文包1.png" alt="" srcset="" />
							<text class="">3-5年</text>
						</view>
						<view class="main-col flex gap-1 items-center">
							<img src="../../static/new/exduction.png" alt="" srcset="" />
							<text class="">本科</text>
						</view>
					</view>
					<view class="border-box w-full bg-gray"></view>
					<view class="avatar-box flex gap-2">
						<view class="avatar  rounded-100">
							<!-- 头像 -->
							<!-- <img src="" alt="" /> -->
						</view>
						<view class="flex flex-col gap-1">
							<text class="text-3 font-bold PM">发布人姓名</text>
							<text class="PR id-name" style="font-size: 20rpx;">发布人公司发布人公司发布人公司</text>
						</view>
					</view>
				</view>
			</view>
			<view class="desc flex-1 bg-whiter rounded-xl flex flex-col gap-3 py-3 px-4">
				<text class="font-bold text-blcak text-4">职位详情</text>
				<view class=" flex gap-2 flex-wrap">
					<u-tag v-for="item in jobdesc" :key="item.id" :text="item.val" bgColor="#F5F5F5" color="#666666"
						borderColor="#F5F5F5"></u-tag>
				</view>
				<view class="responseList flex flex-col gap-5">
					<view class="resposedesc flex flex-col gap-2">
						<text>岗位职责：</text>
						<text>1. 负责游戏各功能界面的设计，包括风格、布局、色彩、字体等搭配；</text>
						<text>2. 负责游戏ICON、LOGO、图标、字体等元素的设计及制作；</text>
						<text>3. 配合策划与程序团队，持续跟进设计实现效果，提升工作品质；</text>
						<text>4. 参与游戏界面优化，提出界面规划建议，把控最终界面实现效果；</text>

					</view>
					<view class="resposedesc flex flex-col gap-2">
						<text>任职要求：</text>
						<text>1. 美术或设计相关专业，本科及以上学历；</text>
						<text>2. 3年以上界面设计工作经验，有王者项目或类似风格项目经验者优先；</text>
						<text>3. 深刻理解用户界面的人机交互、操作逻辑及整体美观设计；</text>
						<text>4. 具备成熟的平面构成、色彩构成认知，优秀的审美能力及较强的设计能力；</text>
						<text>5. 热爱游戏，工作认真负责，具备良好的沟通协调能力和团队合作精神。</text>
					</view>
				</view>
				<view class="border-box w-full bg-gray"></view>
				<view class="flex flex-col gap-3">
					<text class="PM text-4 font-bold">职位地址</text>
					<view class="flex gap-1">
						<u-icon name="map-fill" color="#4F8CF0" size="17"></u-icon>
						<text class="text-3 text-blcak PR">郑州金水区郑州金水区郑州金水区郑州金水区郑州金水</text>
					</view>
					<view class="map-box w-full bg-gray-500 rounded-xl">
						<!-- 地图模块 -->
					</view>
				</view>
			</view>
			<view class="company bg-whiter rounded-xl flex flex-col gap-3 px-4 py-3">
				<text class="text-4 font-bold PM">公司信息</text>
				<view class="avatar-box flex gap-2">
					<view class="avatar  rounded-100">
						<!-- <img src="" alt="" /> -->
					</view>
					<view class="flex flex-col gap-1">
						<text class="text-3 font-bold PM">公司名称</text>
						<text class="PR id-name" style="font-size: 20rpx;">2000人｜天使轮</text>
					</view>
				</view>
			</view>
			<view class="bg-whiter rounded-xl flex flex-col gap-3 px-4 py-3">
				<text class="PM text-4 text-blcak font-bold">职位问答</text>
				<view class="border-box w-full bg-gray"></view>
				<view class="flex flex-col" v-for="i in 3" :key="i">
					<view class="flex flex-col gap-3 border-b pb-3">
						<view class="flex gap-3 items-center">
							<image src="../../static/new/w1.png" mode="" style="width: 28rpx; height: 28rpx;"></image>
							<text class="PR font-bold text-blcak ov-t"
								style="font-size: 28rpx;">问题问题问题问题问题问题问题问题问题问题问题问题</text>
						</view>
						<view class="flex gap-3 items-center ">
							<image src="../../static/new/w2.png" mode="" style="width: 28rpx; height: 28rpx;"></image>
							<text class="PR font-bold text-gray ov-t"
								style="font-size: 28rpx;">答案答案答案答案答案答案答案答案答案答案答案答案</text>
						</view>
					</view>
				</view>
				<view class="flex items-center justify-center gap-1">
					<text class="PR text-blcak" style="font-size: 28rpx;">查看更多</text>
					<u-icon name="arrow-down-fill" color="#666666" size="16"></u-icon>
				</view>
			</view>

			<view class="rounded-xl bg-whiter rounded-xl px-4 py-3 flex flex-col gap-3">
				<view class="flex justify-between items-center">
					<view class="flex gap-2 items-center">
						<text class="font-bold text-4 PM text-blcak ">已报名</text>
						<text class="text-3 text-black PR">(已有100人报名)</text>
					</view>
					<view class="flex gap-3">
						<text class="text-3 text-gray">查看更多</text>
						<u-icon name="arrow-right" color="#777777"></u-icon>
					</view>
				</view>
				<view class="flex justify-between flex-wrap gap-3">
					<view class="avatar-box flex gap-2 items-center" style="min-width: 45%;" v-for="i in 6" :key="i">
						<view class="avatar  rounded-100">
							<!-- <img src="" alt="" /> -->
						</view>
						<text class="text-4 font-bold PM">王*彤</text>
					</view>
				</view>
			</view>

			<view class="flex flex-col gap-3 px-4 py-3 rounded-xl bg-whiter">
				<text class="text-4 text-black font-bold">其他职位</text>
				<view class="flex flex-col gap-2 p-4" v-for="i in 3" :key="i">
					<view class="flex justify-between items-center">
						<text class="text-black PM font-bold" style="font-size: 28rpx;">游戏UI设计-北京</text>
						<text class="PM font-bold text-origin">12-50k</text>
					</view>
					<view class=" flex gap-2 flex-wrap">
						<u-tag text="世界500强" bgColor="#F5F5F5" color="#666666" borderColor="#F5F5F5"></u-tag>
						<u-tag text="上市公司" bgColor="#F5F5F5" color="#666666" borderColor="#F5F5F5"></u-tag>
						<u-tag text="游戏大厂" bgColor="#F5F5F5" color="#666666" borderColor="#F5F5F5"></u-tag>
					</view>
					<text class="text-3 text-black PR ov-t-multi">
						这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介
					</text>
				</view>
			</view>
			<view class="flex flex-col gap-3 ">
				<view class="flex gap-2">
					<img src="../../static/new/safety.png" style="width: 32rpx; height: 32rpx;" alt="" />
					<text class="text-3 text-black PR">招聘呗安全提示</text>
				</view>
				<view class="text-3 text-black PR">
					招聘呗严禁用人单位和招聘者用户做出任何损害求职者合法权益的违法违规行为，包括但不限于扣押求职者证据、收取求职者财务、向求职者集资、让求职者入股、诱导求职者异地入职、异地参加培训、违法违规使用求职者简历等，一旦发现此类行为，
					<text class="text-blue">请立即举报</text>
				</view>
			</view>
		</view>
		<!-- 底部弹窗 -->

		<view class=" bg-whiter rounded-xl px-4 py-3 flex justify-between"
			style="height: 196rpx; box-shadow: 0rpx -4rpx 32rpx 0rpx #000000 5%;">
			<view class="flex items-center gap-1">
				<u-icon name="heart" size="20"></u-icon>
				<text>关注</text>
			</view>
			<view class="flex gap-3 items-center">
				<view v-if="!action"
					class="py-3 flex justify-center items-center bg-blue-200 rounded-xl text-blue font-bold PS"
					style="min-width: 256rpx;" @click="openPopup">
					立即报名
				</view>
				<view v-else class="py-3 flex justify-center items-center bg-gray rounded-xl text-gray font-bold PS"
					style="min-width: 256rpx;" @click="action = false">
					取消报名
				</view>
				<view class="rounded-xl py-3 flex justify-center items-center text-white font-bold PS"
					style="min-width: 256rpx; background: linear-gradient(to bottom, #4F8CF0, #1E6DEE);">
					聊聊呗
				</view>
			</view>
		</view>
		<uni-popup ref="bottomPopup" style=" position: absolute;" type="bottom">
			<view class="popup-content bg-gray rounded-xl flex flex-col justify-between" style="height: 760rpx;">
				<!-- <text>请输入您的简历链接：</text>
				<input v-model="resumeUrl" placeholder="https://example.com/resume.pdf" />
				<button @click="sendResume">发送简历</button> -->
				<text class="PM text-black pt-4 flex justify-center font-bold" style="font-size: 28rpx;">请选择发送简历</text>
				<view class="flex flex-col gap-3" style="padding: 48rpx 24rpx;">
					<view class="bg-whiter rounded-xl p-4 flex gap-4" v-for="i in 3" :key="i">
						<view class="bg-gray-500" style="width: 72rpx; height: 72rpx;">
							<img src="" alt="" />
						</view>
						<view class="flex flex-col gap-1">
							<text class="PM text-black" style="font-size: 28rpx;">简历名称.pdf</text>
							<text class="PR text-3" style="color: #999999;">更新于2025.03.14 14:06</text>
						</view>
					</view>
				</view>
				<view class="bg-blue py-3 flex justify-center text-white text-4 PR" @click="complete"
					style="width: 440rpx; margin: 0 auto; border-radius: 54rpx; margin-bottom: 40rpx;">
					投递
				</view>
			</view>
		</uni-popup>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				jobdesc: [{
						id: 1,
						val: '不接受居家办公'
					},
					{
						id: 2,
						val: '交互设计经验'
					},
					{
						id: 3,
						val: '1-3年UI设计经验'
					},
					{
						id: 4,
						val: '接受实习/在校生投递'
					},
					{
						id: 5,
						val: 'PS'
					},
					{
						id: 6,
						val: '接受无相关经验'
					}
				],
				action: false,
			}
		},
		methods: {
			openPopup() {
				this.$refs.bottomPopup.open(); // 打开底部弹窗
				this.action = true
			},
			complete() {
				this.$refs.bottomPopup.close(); // 关闭弹窗
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		display: flex;
		flex-direction: column;

		.job {
			display: flex;
			flex-direction: column;
			gap: 50rpx;
			padding: 24rpx 30rpx;
			border-radius: 24rpx;
			background-color: #FFFFFF;

			.header {
				display: flex;
				width: 100%;
				flex-direction: column;
				gap: 24rpx;

				.title {
					font-size: 32rpx;

					.h2 {
						color: #333333;
					}

					.p {
						color: #F98A14;
					}
				}

				.main {
					.main-col {
						img {
							width: 28rpx;
							height: 28rpx;
						}

					}
				}
			}

		}

		.desc {
			.responseList {
				.resposedesc {
					font-size: 28rpx;
					color: #333333;
				}
			}

			.map-box {
				min-height: 296rpx;
			}
		}
	}

	.border-box {
		height: 2rpx;
	}

	.id-name {
		color: #999999;
		font-size: 20rpx;
	}

	.avatar-box {
		.avatar {
			width: 72rpx;
			height: 72rpx;
			background-color: #D9D9D9;
		}
	}

	::v-deep .vue-ref {
		padding-bottom: 0rpx !important;
	}
</style>

<style lang="scss" scoped>
	@import url("../../static/css/pagesC/schoolIntro/commonCss.css");
</style>