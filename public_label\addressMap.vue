<!--这个是企业卡片的样式-->
<template>
  <view class="sub-wrap">
    <view class="sub-title" style="margin-bottom: 16rpx; margin-top: 14rpx;" v-if="title != ''">
      {{ title }}
    </view>
    <view class="pos" style="margin-bottom: 12rpx;">
      <!-- <u-icon name="map-fill" size="28rpx"></u-icon> -->
      <text @click="openNavigation(addresses[0])" style="font-size: 24rpx;">
	  {{addresses[0].map_address}}</text>
    </view>
    <view class="map">
      <map style="width: 100%; height: 300rpx;" :latitude="addresses[0].lat" :longitude="addresses[0].lng"
           :markers="covers">
      </map>
    </view>
  </view>
</template>

<script>
export default {
  name: "addressMap",
  props:{
    addresses: {
      type: Array,
      default: () => {}
    },
    title:{
      type: String
    }
  },
  data() {
    return {
      covers: [{
        id: 1,
        latitude: 39.909,
        longitude: 116.39742,
        // iconPath: '../../../static/location.png'
      }],
    }
  },
  methods:{
	  // 导航方法
	  openNavigation(address) {
	  	// console.log('22223333', address)
	  	wx.openLocation({
	  		latitude: parseFloat(address.lat), // 纬度，浮点数，范围为-90~90
	  		longitude: parseFloat(address.lng), // 经度，浮点数，范围为-180~180
	  		name: address.map_address || '面试地点', // 位置名
	  		scale: 18 // 地图缩放级别
	  	})
	  },
  },
  watch: {
    addresses(row) {
      this.covers[0].latitude = row[0].lat
      this.covers[0].longitude = row[0].lng
      console.log('this.covers',this.covers)
    }
  }
}
</script>
<style>
page {
  background: #F5F5F7;
}
</style>
<style lang="less" scoped>
.main {
  padding: 32rpx 32rpx 140rpx 32rpx;
}

.wrap {
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  padding: 0 32rpx;
  border-radius: 24rpx;
  margin-bottom: 24rpx;

  .job-info {
    display: flex;
    flex-direction: column;
    padding: 24rpx 0;

    .job-name {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .job {
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
      }

      .money {
        font-weight: 500;
        font-size: 32rpx;
        color: #4F8CF0;
      }
    }

    .tags {
      display: flex;
      align-items: center;
      margin-top: 24rpx;

      .tag {
        display: flex;
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        margin-right: 24rpx;

        image {
          width: 28rpx;
          height: 28rpx;
          margin-right: 6rpx;
        }
      }
    }
  }

  .pub-info {
    display: flex;
    padding: 24rpx 0;

    image {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
    }

    .users {
      display: flex;
      flex-direction: column;
      padding-left: 16rpx;

      .username {
        font-weight: 500;
        font-size: 24rpx;
        color: #333333;
      }

      .address {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        margin-top: 10rpx;
      }
    }
  }

  .sub-wrap {
    display: flex;
    flex-direction: column;

    .sub-title {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      padding: 24rpx 24rpx;
    }

    .desc {
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
      line-height: 40rpx;
      margin-bottom: 24rpx;
    }

    .pos {
      display: flex;
      align-items: center;
      font-weight: 400;
      color: #333333;

      text {
        margin-left: 10rpx;
      }
    }

    .map {
      padding: 24rpx 0;
    }
  }

  .title {
    padding: 24rpx 0 0 0;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
  }

  .compony-info {
    display: flex;
    padding: 24rpx 0;

    image {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
    }

    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding-left: 16rpx;

      .name {
        font-weight: 500;
        font-size: 24rpx;
        color: #333333;
      }

      .tags {
        display: flex;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;

        .tag {
          border-right: 1px solid #999999;
          padding: 0 12rpx;

          &:first-child {
            padding-left: 0;
          }

          &:last-child {
            border-right: none;
          }
        }
      }
    }
  }

  .answers {
    display: flex;
    flex-direction: column;

    .answer {
      display: flex;
      flex-direction: column;
      padding: 24rpx 0;
      border-bottom: 1px solid #F5F5F7;

      &:last-child {
        border-bottom: none;
      }

      .ans {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #333333;

        image {
          width: 28rpx;
          height: 28rpx;
          margin-right: 32rpx;
        }
      }

      .qus {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        margin-top: 20rpx;

        image {
          width: 28rpx;
          height: 28rpx;
          margin-right: 32rpx;
        }
      }
    }
  }

  .down {
    display: flex;
    justify-content: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;

    image {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .more {
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    padding-bottom: 32rpx;

    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.footer {
  position: fixed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 140rpx;
  width: 100%;
  left: 0;
  bottom: 0;
  background: #FFFFFF;
  font-weight: 600;
  font-size: 28rpx;

  .favor {
    display: flex;
    align-items: center;
    color: #999999;
    margin-left: 32rpx;
  }

  .ysc {
    color: #F9AD14;
  }

  .btns {
    display: flex;
    flex: 1;
    padding: 0 32rpx;

    .btn {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      padding: 0 72rpx;
      height: 80rpx;
      background: #F5F5F7;
      color: #333333;
      border-radius: 16rpx;

      &:first-child {
        margin-right: 20rpx;
      }

    }

    .edit {
      color: #4F8CF0;
      background: rgba(79, 140, 240, 0.1);
    }

    .down {
      background: linear-gradient(135deg, #F0544F 0%, #EE1E1E 100%);
      color: #FFFFFF;
    }

    .cancel {
      background: rgba(254, 77, 79, 0.1);
      color: #FE4D4F;
    }

    .agree {
      background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
      color: #FFFFFF;
    }
  }
}

.credit {
  display: flex;
  flex-direction: column;
  padding: 40rpx 48rpx;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .logo-content {
      display: flex;

      image {
        width: 48rpx;
        height: 48rpx;
        margin-right: 16rpx;
      }

      text {
        font-family: PingFang SC, PingFang SC;
        font-weight: 500;
        font-size: 15px;
        color: rgba(0, 0, 0, 0.9);
      }
    }

    .tip {
      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

  .comp {
    justify-content: center;
  }

  .content {
    display: flex;
    flex-direction: column;
    margin-top: 40rpx;

    .sub-title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 28rpx;
      color: #000000;
    }

    .desc {
      display: flex;
      font-weight: 400;
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #999999;
    }

    .btns {
      display: flex;
      flex-direction: column;
      margin-top: 16rpx;

      .btn {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #FFFFFF;
        height: 140rpx;
        border-radius: 16rpx;

        &:last-child {
          margin-top: 24rpx;
        }

        text {
          font-weight: 400;
          font-size: 32rpx;
          color: rgba(0, 0, 0, 0.9);
        }

        .bind {
          font-size: 24rpx;
          color: #07C160;
        }
      }

      .active {
        color: #4F8CF0;
        border: 1rpx solid #4F8CF0;
      }
    }
  }

  .other {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 60rpx 0;
    color: #576B95;
    font-weight: 500;
    font-size: 24rpx;
  }

  .agree {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 44rpx;
    height: 88rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #FFFFFF;
    background: #4F8CF0;
    margin-top: 32rpx;
  }
}
</style>