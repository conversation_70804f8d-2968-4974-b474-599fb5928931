<template>
	<view class="wrap" @click.stop="goDetails">
		<view class="item">
			<view class="item-up">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/41R1LgOXldsXlyAjelHeNXsb0SuBPKE7ANcgobdG.png" mode=""></image>
				<view class="info">
					<view class="user">
						<view class="userInfo">
							<view class="name">
								就业管家姓名
							</view>
						</view>
					</view>
					<view class="flag">
						学创联盟（北京）网络科技有限公司
					</view>
				</view>
			</view>

			<view class="item-down">
				<view class="tags">
					<view class="tag" v-for="v in 3">
						管家标签
					</view>
				</view>
			</view>

			<!-- <view class="arrow" v-if="item.status==2">
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view> -->
		</view>
		 <!-- v-if="item.status==1" -->
		<block>
			<u-line color="#F5F5F7" length="100%"></u-line>
			<view class="item flexRow">
				<view class="item-up">
					<image src="/static/images/my/hz.png" mode=""></image>
					<view class="info">
						<view class="user">
							<view class="userInfo">
								<view class="name">
									学创联盟（北京）网络... <image src="/static/images/plat/vip.png" mode=""></image>
								</view>
							</view>
						</view>
						<view class="desc">
							<view class="desc-item">
								0-20人
							</view>
							<view class="desc-item">
								建筑业
							</view>
						</view>
					</view>
				</view>

				<view class="item-down" @click.stop>
					<view class="dot">
						<u-icon name="more-dot-fill" @click.stop="more"></u-icon>
					</view>
				</view>
			</view>
		</block>

	</view>
</template>

<script>
	export default {
		name:"commissionItem",
		props:{
			item:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {

			};
		},
		methods:{
			goDetails() {
				uni.navigateTo({
					url:'/pagesA/details/obtainItemDetails'
				})
			},
			more(item){
				this.$emit('more',{
					item,
					show:true
				})
			}
		}
	}
</script>


<style lang="less" scoped>
	.wrap{
		display: flex;
		flex-direction: column;
		padding: 0 32rpx;
		margin-bottom: 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;
	}
	.item {
		display: flex;
		flex-direction: column;
		position: relative;
		padding: 24rpx 0;
		.item-up {
			display: flex;
			flex: 1;
			&>image {
				width: 96rpx;
				height: 96rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				flex: 1;
				padding-left: 24rpx;

				.user {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.userInfo {
						display: flex;
						align-items: center;

						.name {
							display: flex;
							align-items: center;
							font-weight: 600;
							font-size: 32rpx;
							color: #333333;
							image{
								margin-left: 16rpx;
								width: 32rpx;
								height: 32rpx;
							}
						}
					}
				}

				.desc {
					display: flex;
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
					margin-top: 16rpx;

					.desc-item {
						border-right: 1px solid #999999;
						padding: 0 12rpx;

						&:first-child {
							padding-left: 0;
						}

						&:last-child {
							border-right: none;
						}
					}
				}

				.flag{
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}



		.item-down {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 20rpx;
			.tags {
				display: flex;

				.tag {
					display: flex;
					align-items: center;
					background: #F6F6F6;
					border-radius: 8rpx;
					height: 46rpx;
					padding: 0 12rpx;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin-right: 16rpx;
				}
			}

			.dot{
				transform: rotate(90deg);
			}
		}

		.arrow{
			position: absolute;
			right: 0;
			top: 50%;
		}
	}

	.flexRow{
		display: flex;
		flex-direction: row;
	}
</style>
