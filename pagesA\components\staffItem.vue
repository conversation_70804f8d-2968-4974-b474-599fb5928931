<template>
    <view class="item" @click="edit(item)">
        <image :src="item.member_info.image.path_url||'https://api-test.zhaopinbei.com/storage/uploads/images/41R1LgOXldsXlyAjelHeNXsb0SuBPKE7ANcgobdG.png'" mode=""></image>
        <view class="info">
            <view class="user">
                <view class="name">
                    {{ item.member.certification_status==1 ? item.member_certification.name : item.name }}
                </view>
                <view :class="['status',item.member.certification_status==1?'yrz':'wrz']">
                    {{item.member.certification_status==1 ? '已认证':'未认证'}}
                </view>
            </view>
            <view class="phone">
                手机号：{{ item.member.cellphone }}
            </view>
        </view>
        <view class="arrow" v-if="item.disable_status!=1">
            <u-icon name="arrow-right"></u-icon>
        </view>
        <view class="reason" v-else>
            账号已注销
        </view>
    </view>
</template>

<script>
    export default {
        name: "staffItem",
        props: {
            item: {
                type: Object,
                default: () => {
                }
            },
            itemIndex: {
                type: Number,
            },
        },
        data() {
            return {};
        },
        methods: {
            edit(item) {
                uni.navigateTo({
                    url: "/pagesA/add/editStaff?id=" + item.id + "&index=" + this.itemIndex,
                })
            }
        }
    }
</script>

<style lang="scss" src="../../static/css/pagesA/components/staffItem.scss"></style>
