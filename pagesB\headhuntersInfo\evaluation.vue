<template>
  <view class="warp">
    <view class="inner">
      <view class="tips">
        <view class="title"> 填写我的评价有什么用？ </view>
        <view class="cont">
          添加主营业务，方便千里马快速了解公司。平台也将根据你所填写的业务，更好的为你推荐符合要求的千里马。
        </view>
      </view>
      <u-tabs
        :current="tabsIndex"
        :list="tabsList"
        :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
        :inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }"
        lineWidth="0"
        @click="onTabsItemClick"
      ></u-tabs>
      <view class="line"> </view>
      <view class="list">
        <view class="item" v-for="item in taglist" :key="item">
          <text class="name">{{ item.name }}</text>
          <text>{{ item.num }}</text>
        </view>
      </view>
      <view class="list-box">
        <view class="item" v-for="item in list" :key="item">
          <u-avatar :src="item.ava" size="80rpx"></u-avatar>
          <view class="right">
            <view class="name">
              {{ item.name }} <text class="time">近期评价</text>
            </view>
            <view class="rate">
              <u-rate
                :count="5"
                active-color="#F9AD14"
                v-model="item.star"
              ></u-rate>
            </view>
            <view class="box-cont">
              真的是特别好，公牛品牌质量过硬，客服小姐姐服务好，耐心细心。安装师傅很专业，大热天从九点安装到下午两点才结束去吃饭。很辛苦。感谢客服和安装服务。很辛苦。感谢客服和安装服务。
            </view>
          </view>
        </view>
      </view>
    </view>
    <view style="height: 220rpx"></view>
    <view class="btn-warp">
      <view class="btn"> 保存 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tabsIndex: 0,
      tabsList: [{ name: "千里马评价" }, { name: "伯乐评价" }],
      type: "1",
      taglist: [
        { name: "提供职业建议", num: 113 },
        { name: "联系及时", num: 103 },
        { name: "热情周到", num: 102 },
        { name: "增强信心", num: 132 },
        { name: "个人亮点挖掘", num: 113 },
        { name: "精通行业", num: 113 },
        { name: "及时交付", num: 113 },
        { name: "人脉广泛", num: 113 },
        { name: "认真负责", num: 113 },
        { name: "专业性强", num: 113 },
        { name: "摆渡人", num: 113 },
        { name: "提供面试建议", num: 113 },
      ],
      list: [
        {
          name: "匿名",
          ava: "",
          star: 5,
        },
        {
          name: "匿名",
          ava: "",
          star: 5,
        },
        {
          name: "匿名",
          ava: "",
          star: 4,
        },
        {
          name: "匿名",
          ava: "",
          star: 4,
        },
      ],
    };
  },
  onLoad: function (options) {},
  methods: {},
};
</script>

<style lang="less" scoped>
.inner {
  padding: 32rpx;

  .tips {
    padding: 40rpx;
    background: linear-gradient(180deg, #f2f8ff 0%, #ffffff 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 2rpx solid rgba(215, 233, 255, 1);
    margin-bottom: 32rpx;

    .title {
      font-size: 24rpx;
      color: #333333;
      margin-bottom: 24rpx;
    }

    .cont {
      font-size: 20rpx;
      color: #8d9aaa;
    }
  }

  .line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28rpx;

    .tit {
      font-size: 32rpx;
      color: #000000;
    }

    .add {
      font-size: 28rpx;
      color: #4f8cf0;
    }
  }

  .list {
    display: flex;
    flex-wrap: wrap;

    .item {
      display: flex;
      align-items: center;
      gap: 14rpx;
      padding: 10rpx 24rpx;
      background: #eef4ff;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-size: 24rpx;
      color: #4f8cf0;
      margin-right: 24rpx;
      margin-bottom: 24rpx;
      .name {
        color: #4e5c75;
      }
    }
  }
}
.list-box {
  display: flex;
  flex-direction: column;

  .item {
    display: flex;
    align-items: center;
    gap: 32rpx;
    margin-bottom: 24rpx;
    margin-top: 24rpx;

    .right {
      flex: 1;
      .name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
        .time {
          font-size: 24rpx;
          color: #4f8cf0;
          margin-left: 24rpx;
        }
      }
      .rate {
        margin: 12rpx 0;
      }

      .box-cont {
        font-size: 24rpx;
        color: #777777;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .del {
      width: 32rpx;
      height: 32rpx;
    }
  }
  .btm {
    margin-bottom: 45rpx;
  }
}
.btn-warp {
  position: fixed;
  bottom: 0;
  width: 750rpx;
  height: 196rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;

  .btn {
    width: 686rpx;
    height: 80rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    line-height: 80rpx;
    margin-top: 24rpx;
  }
}
</style>
