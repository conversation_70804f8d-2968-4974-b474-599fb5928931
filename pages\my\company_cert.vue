<template>
	<view class="register-one">
		<block v-if="step==1">
			<view class="wrap">
				<view class="inp">
					<!-- <view class="avatar">
						<view class="inp-item">
							<view class="title fb">
								企业头像<text class="star">*</text>
							</view>
							<view class="in lab">
								请选择系统默认头像
							</view>
						</view>
						<view class="pic">
							<image src="https://api-test.zhaopinbei.com/storage/uploads/images/DjpYAiYx76keAZZls68bXyVPGHjYpURJJxA9YHcU.png" mode=""></image>
						</view>
					</view> -->

					<view class="inp-item">
						<view class="title">
							企业名称<text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入企业名称" fontSize="32rpx" clearable
								:placeholderStyle="placeholderStyle" border="none" v-model="form.name"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							社会统一代码<text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入社会统一代码" fontSize="32rpx" clearable
								:placeholderStyle="placeholderStyle" border="none" v-model="form.credit_id"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							联系人手机号<text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入手机号" fontSize="32rpx" clearable
								:placeholderStyle="placeholderStyle" border="none"
								v-model="form.contact_cellphone"></u--input>
						</view>
					</view>
                    <view class="inp-item">
                    	<view class="title">
                    		验证码<text class="star">*</text>
                    	</view>
                    	<view class="in">
                    		<!-- <u--input placeholder="请输入验证码" fontSize="32rpx" clearable
                    			:placeholderStyle="placeholderStyle" border="none"
                    			v-model="form.yzm"></u--input> -->
                                <!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
                                <!-- #ifndef APP-NVUE -->
                                <u-input placeholder="请输入验证码" placeholderStyle="#222" v-model="form.code" clearable fontSize="28rpx" border="none">
                                <!-- #endif -->
                                    <!-- #ifdef APP-NVUE -->
                                    <u--input placeholder="请输入验证码" placeholderStyle="#222" v-model="form.code" clearable fontSize="28rpx" border="none">
                                    <!-- #endif -->
                                        <template slot="suffix">
                                            <u-code ref="uCode" @change="codeChange" seconds="20" changeText="X秒重新获取"></u-code>
                                            <u-button @tap="getCode" :text="tips" type="success" size="mini"></u-button>
                                        </template>
                                <!-- #ifndef APP-NVUE -->
                                </u-input>
                                <!-- #endif -->
                                <!-- #ifdef APP-NVUE -->
                                </u--input>
                                <!-- #endif -->
                    	</view>
                    </view>

					<view class="inp-item">
						<view class="title">
							联系人邮箱<text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入邮箱" fontSize="32rpx" clearable
								:placeholderStyle="placeholderStyle" border="none"
								v-model="form.contact_email"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							企业简称
						</view>
						<view class="in">
							<u--input placeholder="请输入企业简称" fontSize="32rpx" clearable
								:placeholderStyle="placeholderStyle" border="none" v-model="form.short_name"></u--input>
						</view>
					</view>
				</view>
				<view class="pic">
					<view class="title">
						营业执照<text class="star">*</text>
					</view>

					<view class="pic-list">
						<block v-if="business_license_img">
							<view class="pic-item">
								<image :src="business_license_img" mode=""></image>
							</view>
						</block>

						<view class="pic-item" @click="uploadBusinessLicense">
							<image src="../../static/images/index/up.png" mode=""></image>
						</view>
					</view>
				</view>
			</view>
		</block>

		<block v-if="step==2">
			<view class="wrap">
				<view class="inp">
					<view class="inp-item">
						<view class="title">
							是否法人本人认证
						</view>
						<view class="in se">
							<picker @change="changeHerSelf" :value="herSelfIndex" :range="herSelfList" range-key="name">
								<view class="d-picker" :style="{color:herSelfIndex==0?'#c0c4cc':'#303133'}">
									{{herSelfList[herSelfIndex]['name']}}
								</view>
							</picker> <u-icon name="arrow-right"></u-icon>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							法人姓名<text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入法人姓名" fontSize="32rpx" :placeholderStyle="placeholderStyle"
								border="none" v-model="form.legal_name"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							法人身份证号<text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入法人身份证号" fontSize="32rpx" :placeholderStyle="placeholderStyle"
								border="none" v-model="form.legal_id_card"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							法人手机号<text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入手机号" fontSize="32rpx" :placeholderStyle="placeholderStyle"
								border="none" v-model="form.legal_cellphone"></u--input>
						</view>
					</view>

					<view class="inp-item" >
						<view class="title">
							所在地区<text class="star">*</text>
						</view>
						<view class="in se">
							<!-- <picker @change="bindPickerChange" :value="eduIndex" :range="edu">
								<view class="d-picker">{{edu[eduIndex]}}</view>
							</picker> <u-icon name="arrow-right"></u-icon> -->

							<uni-data-picker :map="map" placeholder="请选择所在地区" popup-title="请选择所在地区"
								:localdata="cityList" @change="onchange">
							</uni-data-picker>
						</view>
					</view>

					<view class="inp-item" @click="choosePosition">
						<view class="title">
							详细地址<text class="star">*</text>
						</view>

						<view class="in se">
							{{form.address.address?form.address.address:'请选择地址'}}
							<u-icon name="arrow-right"></u-icon>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							门牌号<text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入门牌号" fontSize="32rpx" :placeholderStyle="placeholderStyle"
								border="none" v-model="form.address.address_info"></u--input>
						</view>
					</view>
				</view>

				<view class="pic">
					<view class="title">
						法人身份证正反面<text class="star">*</text>
					</view>

					<view class="pic-list">
						<view class="pic-item" @click="uploadIdcardZM">
							<image :src="idcard_front_img?idcard_front_img:'../../static/images/index/up.png'" mode="">
							</image>
						</view>
						<view class="pic-item" @click="uploadIdcardFM">
							<image :src="idcard_back_img?idcard_back_img:'../../static/images/index/up.png'" mode="">
							</image>
						</view>
					</view>
				</view>

				<view class="pic">
					<view class="title">
						上传资质<text class="star" v-if="roleType=='headhunters'">*</text>
					</view>

					<view class="pic-list">
						<view class="pic-item" @click="uploadZZ">
							<image :src="zz_img?zz_img:'../../static/images/index/up.png'" mode=""></image>
						</view>
					</view>
				</view>
			</view>
		</block>


		<view class="footer">
			<view class="next sure" v-if="step==1" @click="next">
				下一步
			</view>
			<block v-if="step==2">
				<view class="next gray" @click="prev">
					上一步
				</view>

				<view class="next sure" @click="sure">
					提交
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	import {
		companyCert,
		uploadImg,
		updateLoginInfo
	} from "../../config/api.js"
	export default {
		data() {
			return {
                tips: '',
				map: {
					text: 'label',
					value: 'value'
				},
				step: 1,
				value: '',
				herSelfIndex: 0,
				herSelfList: [{
					value: 0,
					name: '请选择'
				}, {
					value: 'self',
					name: '本人'
				}, {
					value: 'other',
					name: '非本人'
				}],
				placeholderStyle: {
					'fontSize': '32rpx'
				},
				zz_img: '',
				business_license_img: '',
				idcard_front_img: '',
				idcard_back_img: '',
				form: {
					name: '', //企业名称
					credit_id: '', //社会统一代码
					contact_cellphone: "", //联系人手机号
					contact_email: "", //电子邮箱，比如 <EMAIL>
					short_name: "", //企业简称
					business_license: [], //营业执照
					auth_type: "", //登录类型：self-本人
                    yzm:'', // 验证码
					card_img_f: [
						2
					], //法人身份证照片正面
					card_img_b: [
						3
					], //法人身份证照片反面
					legal_name: "", //企业名称
					legal_id_card: "", //法人身份证号码
					legal_cellphone: "", //法人手机号
					address: {
						lat: "",
						lng: "",
						province_id: "",
						city_id: "",
						district_id: "",
						address: "",
						address_info: ""
					} //地址
				}
			}
		},
		computed: {
			roleType() {
				return this.$store.state.roleType || uni.getStorageSync('roleType')
			},
			cityList() {
				return this.$store.state.cityList || uni.getStorageSync('cityList')
			}
		},
		onLoad(options) {
			this.type = options.type
			this.start = uni.$u.timeFormat(this.start, 'yyyy-mm-dd');
			this.end = uni.$u.timeFormat(this.end, 'yyyy-mm-dd');
		},
		methods: {
            codeChange(text) {
                this.tips = text;
            },

            getCode() {
                if (this.$refs.uCode.canGetCode) {
                    // 模拟向后端请求验证码
                    uni.showLoading({
                        title: '正在获取验证码'
                    })
                    setTimeout(() => {
                        uni.hideLoading();
                        // 这里此提示会被this.start()方法中的提示覆盖
                        uni.$u.toast('验证码已发送');
                        // 通知验证码组件内部开始倒计时
                        this.$refs.uCode.start();
                    }, 2000);
                } else {
                    uni.$u.toast('倒计时结束后再发送');
                }
            },
			changeHerSelf(e) {
				this.herSelfIndex = e.detail.value
				this.form['auth_type'] = this.herSelfList[this.herSelfIndex]['value']
			},
			onchange(e) {
				console.log("工作地址：", e);
				let data = e.detail.value
				this.form.address.province_id = data[0]['value']
				this.form.address.city_id = data[1]['value']
				this.form.address.district_id = data[2]['value']
				// this.form.address.address = data.map(item => item.text).join('')
			},

			//选择地图地址
			choosePosition() {
				console.log(1)
				let self = this;
				uni.chooseLocation({
					success: function(res) {
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
						self.form.address.lat = res.latitude
						self.form.address.lng = res.longitude
						self.form.address.address = res.address
					}
				});
			},
			//上传营业执照
			uploadBusinessLicense() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								if (result.status_code == 200) {
									self.business_license_img = result.data.url
									self.form.business_license = [result.data.id]
								} else {
                                    uni.$u.toast(result.message)
                                }
							}
						})
					}
				});
			},

			//上传身份证正面
			uploadIdcardZM() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								if (result.status_code == 200) {
									self.idcard_front_img = result.data.url
									self.form.card_img_f = [result.data.id]
								} else {
                                    uni.$u.toast(result.message)
                                }
							}
						})
					}
				});
			},

			//上传身份证反面
			uploadIdcardFM() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						// $dialog.loading('上传中')
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								if (result.status_code == 200) {
									self.idcard_back_img = result.data.url
									self.form.card_img_b = [result.data.id]
								} else {
                                    uni.$u.toast(result.message)
                                }
							}
						})
					}
				});
			},

			//上传资质
			uploadZZ() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						// $dialog.loading('上传中')
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								if (result.status_code == 200) {
									self.zz_img = result.data.url
									// self.form.business_license = [result.data.id]
								} else {
                                    uni.$u.toast(result.message)
                                }
							}
						})
					}
				});
			},

			next() {
				this.step = 2
			},
			prev() {
				this.step = 1
			},
			async sure() {
                if(!uni.$u.test.mobile(this.form.contact_cellphone)) {
                    return uni.$u.toast('手机号格式不正确')
                }
                if(this.form.auth_type == 0 || !this.form.auth_type) {
                    return uni.$u.toast('是否法人本人认证')
                }
				let params = {
					...this.form
				}
				const {
					status_code,
					data
				} = await companyCert(params)
				if (status_code == 200) {
					let loginInfo = await updateLoginInfo()
					if (loginInfo.status_code == 200) {
						this.$store.commit('setUserInfo', loginInfo.data)
						uni.switchTab({
							url: "/pages/index/index"
						})
						return uni.$u.toast('保存信息成功')

					} else {
                        uni.$u.toast(loginInfo.message)
                    }
				}
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
		/* background-image: url('/static/images/login/bg.png');
		background-position: 100% 100%;
		background-size: 100% 100%;
		background-repeat: no-repeat; */
		font-family: PingFang SC, PingFang SC;
	}

	/* .placeholderClass{
		font-weight: 400;
		font-size: 32rpx;
		color: red;
	} */
</style>
<style lang="less" scoped>
	.register-one {
		display: flex;
		flex-direction: column;
		padding-bottom: 220rpx;
	}

	.wrap {
		padding: 30rpx;

		.pic {
			display: flex;
			flex-direction: column;
			margin-bottom: 32rpx;

			.title {

				font-weight: 500;
				font-size: 32rpx;
				color: #333333;
				margin: 16rpx 0;

				.star {
					font-weight: 600;
					font-size: 22rpx;
					color: #FE4D4F;
					margin-left: 8rpx;
				}
			}

			.pic-list {
				display: flex;
				flex-wrap: wrap;

				.pic-item {
					margin-right: 24rpx;

					&:last-child {
						margin-right: 0;
					}

					image {
						width: 200rpx;
						height: 180rpx;
					}
				}
			}
		}

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			margin-bottom: 32rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}



				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					::v-deep uni-data-picker {
						width: 100%;
					}

					::v-deep .arrow-area {
						transform: rotate(-135deg);
					}

					::v-deep .input-arrow {
						width: 20rpx;
						height: 20rpx;
						border-left: 1px solid #606266;
						border-bottom: 1px solid #606266;
					}

					::v-deep .input-value-border {
						border: none;
					}

					::v-deep .input-value {
						padding: 0;
					}

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}

					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}

				.fb {
					font-size: 32rpx;
				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 24rpx;
		height: 64rpx;
		width: 260rpx;
		// margin-left: 32rpx;
		background: #4F8CF0;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
		border-radius: 8rpx;
	}

	.footer {
		display: flex;
		justify-content: space-around;
		flex-direction: column;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 220rpx;
		// background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}

		.gray {
			background-color: #cccccc;
			color: #FFFFFF;
		}

		.sure {
			background-color: #4F8CF0;
			color: #FFFFFF;
		}
	}
</style>
