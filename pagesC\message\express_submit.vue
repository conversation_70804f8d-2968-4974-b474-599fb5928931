<template>
	<view class="container">
		<u-toast ref="uToast"></u-toast>
		<view class="textarea-container">
			<u-textarea v-model="params.content" placeholder="输入您的常用语回复，请不要填写QQ,微信等联系方式或广告信息，否则系统将封禁您的账号"
				:adjustPosition="false" border="none" maxlength="200" height="400"></u-textarea>
		</view>
		<view class="sticky-container">
			<view class="template-container" @click="onTemplatePopupOpen">
				<image class="image" src="/static/new/小眼睛***********"></image>
				<text class="text">常用语优质模板</text>
				<view class="length">
					<text class="left">{{params.content.length}}</text>
					<text class="right">/200</text>
				</view>
			</view>
			<view class="btn-container">
				<view class="btn" @click="onSubmit">完成</view>
			</view>
		</view>

		<u-popup :show="templatePopup" :round="12" mode="bottom" :safeAreaInsetBottom="false"
			@close="onTemplatePopupClose">
			<view class="popup-container__template">
				<view class="tabs-container">
					<view class="item">
						<text class="text">自我介绍范例</text>
						<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/VjsHJaEaXBBVabRlZcwOaaKXyiLPNq1pAckyd5qO.png"></image>
					</view>
					<view class="item is_active">
						<text class="text">自我介绍模版</text>
						<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/VjsHJaEaXBBVabRlZcwOaaKXyiLPNq1pAckyd5qO.png"></image>
					</view>
				</view>

				<template v-if="true">
					<text class="message">为您提供不同技能侧重的自我介绍模板，点击使用即可根据模板快速生成文案</text>
					<view class="template-container">
						<view class="item" v-for="_ in 20">
							<view class="item-start">
								<image class="image" src="/static/new/menu.png"></image>
								<text>硬件研发</text>
							</view>
							<view class="item-center">对口专业/专业知识软件工具业研发方向知名企业</view>
							<view class="item-end">使用</view>
						</view>
					</view>
				</template>

				<template v-if="false">
					<view class="type-container">
						<view class="item is_active">硬件开发</view>
						<view class="item">硬件开发</view>
						<view class="item">硬件开发</view>
					</view>

					<view class="list-container">
						<view class="item" v-for="_ in 5">
							<view class="item-start">
								<image class="avater" src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"></image>
								<text class="name">前端工程师</text>
							</view>
							<view class="item-text">
								伯乐您好，我叫XX，本科毕业于北航能源与动力专业，有4年热设计工程师经验。目前在长安汽车担任热设计工程师，有车载控制热设计、水冷系统设计项目经验。掌握热仿真软
								件、FLotherm、IcePak等软件工具，可独立完成散热发案设计、测试、分析工作，支持结构、PCB设计。我想应聘这个职位，期待可以进一步沟通。
							</view>
						</view>
					</view>
				</template>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		interviewHelloWordAdd,
		interviewHelloWordUpdate
	} from '@/config';

	export default {
		data() {
			return {
				templatePopup: false,

				params: {
					id: null,
					content: null,
					type: null,
				}
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onLoad(options) {
			this.params.id = options.id;
			this.params.content = options.content;
			this.params.type = options.type;
		},
		methods: {
			async onSubmit() {
				const event = this.params.id ? interviewHelloWordUpdate : interviewHelloWordAdd;
				const res = await event({
					...this.params,
					...this.userTypeParams
				});
				if (res.status_code !== '200') return;
				this.$refs.uToast.show({
					message: res.message,
					complete: () => {
						uni.$u.route({
							type: 'back'
						});
					}
				})
			},
			onTemplatePopupOpen() {
				this.templatePopup = true;
			},
			onTemplatePopupClose() {
				this.templatePopup = false;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.popup-container__template {
		height: calc(100vh - 160rpx);
		padding: 32rpx;
		border-start-start-radius: 24rpx;
		border-start-end-radius: 24rpx;
		display: flex;
		flex-direction: column;
		gap: 40rpx;

		.template-container {
			display: grid;
			grid-template-columns: repeat(2, 1fr);
			gap: 32rpx;
			overflow-y: auto;

			.item {
				padding: 24rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;
				border: 1rpx #e6e6e6 solid;
				border-radius: 8rpx;

				.item-start {
					display: flex;
					align-items: center;
					gap: 16rpx;
					color: #333333;
					font-size: 28rpx;

					.image {
						width: 40rpx;
						height: 40rpx;
					}
				}

				.item-center {
					color: #434343;
					font-size: 26rpx;
					overflow: hidden;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 5;
				}

				.item-end {
					width: 128rpx;
					height: 66rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #ffffff;
					font-size: 24rpx;
					border-radius: 999rpx;
					background-color: #4f8cf0;
					margin-inline: auto;
					margin-block-start: 40rpx;
				}
			}
		}

		.message {
			font-size: 24rpx;
			color: #999999;
		}

		.list-container {
			flex: 1;
			overflow-y: auto;
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding-block: 20rpx;

			.item {
				padding: 24rpx;
				box-shadow: 0rpx 0rpx 12rpx 0rpx rgba(0, 0, 0, 0.1);
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-start {
					display: flex;
					align-items: center;
					gap: 16rpx;
					color: #333333;
					font-size: 28rpx;

					.avater {
						width: 60rpx;
						height: 60rpx;
						border-radius: 999rpx;
					}
				}

				.item-text {
					color: #434343;
					font-size: 26rpx;
				}
			}
		}

		.type-container {
			display: flex;
			align-items: center;
			justify-content: space-around;

			.item {
				width: 200rpx;
				height: 68rpx;
				color: #333333;
				font-size: 32rpx;
				border: 1rpx #e6e6e6 solid;
				border-radius: 999rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.is_active {
				border: 1rpx #4f8cf0 solid;
				color: #4f8cf0;
				background-color: #e8f3ff;
			}
		}

		.tabs-container {
			display: flex;
			align-items: flex-end;
			gap: 40rpx;

			.item {
				position: relative;
				color: #666666;

				.text {
					font-size: 32rpx;
					position: relative;
					z-index: 2;
				}

				.image {
					width: 168rpx;
					height: 25rpx;
					position: absolute;
					display: none;
					bottom: 0;
					left: 0;
					z-index: 1;
				}
			}

			.is_active {
				color: #000000;

				.image {
					display: block;
				}
			}
		}
	}

	.container {
		height: 100vh;

		.textarea-container {
			padding: 24rpx;
		}

		.sticky-container {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			border-block-start: 1rpx #e6e6e6 solid;

			.btn-container {
				background-color: #ffffff;
				padding-inline: 32rpx;
				padding-block-start: 24rpx;
				padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
				border-start-start-radius: 24rpx;
				border-start-end-radius: 24rpx;
				box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);

				.btn {
					padding-block: 20rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					background-image: linear-gradient(to right, #4f8cf0, #1e6dee);
					border-radius: 24rpx;
					color: #ffffff;
					font-size: 28rpx;
				}
			}

			.template-container {
				padding-inline: 32rpx;
				padding-block: 24rpx;
				display: flex;
				align-items: center;
				gap: 16rpx;

				.image {
					width: 40rpx;
					height: 40rpx;
				}

				.text {
					color: #333333;
					font-size: 28rpx;
				}

				.length {
					display: flex;
					align-items: center;
					font-size: 20rpx;
					margin-inline-start: auto;

					.left {
						color: #4f8cf0;
					}

					.right {
						color: #333333;
					}
				}
			}
		}
	}
</style>
