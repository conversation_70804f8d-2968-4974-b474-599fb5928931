<template>
	<!-- 登录设备管理 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="title">登录设备管理</view>
				<view class="sub-title">以下最近登录过您账号的设备情况，若您发现非本人操作，请及时删除，以保障您的账号安全</view>
				<u-cell-group :border="false">
					<u-cell>
						<template #title>
							<view class="item-box">
								<view class="item-start">
									<text class="title">IPHONE||PRO15</text>
									<text class="sub-title">当前设备</text>
								</view>
								<view class="item-end">2025-04-08 20:43:58</view>
							</view>
						</template>
					</u-cell>
				</u-cell-group>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-cell {
		.u-cell__body {
			padding-inline: 0 !important;
		}
	}

	::v-deep .u-cell-group {
		.u-cell-group__title {
			padding-inline: 0 !important;
		}
	}

	.container {
		height: 100vh;
		background-color: #FFFFFF;
		display: flex;
		flex-direction: column;

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
				padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

				.item-box {
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.item-start {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.title {
							color: #333333;
							font-size: 28rpx;
						}

						.sub-title {
							color: #999999;
							font-size: 24rpx;
						}
					}

					.item-end {
						color: #999999;
						font-size: 24rpx;
					}
				}

				.title {
					color: #333333;
					font-size: 32rpx;
				}

				.sub-title {
					color: #777777;
					font-size: 24rpx;
				}
			}
		}
	}
</style>