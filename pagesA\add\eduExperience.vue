<template>
	<view class="home-index">
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						学校名称
					</view>
					<view class="in">
						<u--input placeholder="请输入学校名称" placeholderClass="placeholderClass" clearable  border="none" v-model="value"
							@change="change"></u--input>
					</view>
				</view>
				
				
				
				<view class="inp-item">
					<view class="title">
						开始时间
					</view>
					<view class="in se">
						<picker mode="date" :value="startTime"  @change="bindStartDateChange">
							<view class="d-picker">{{startTime?startTime:"请选择开始时间"}}</view>
						</picker>
						 <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				
				<view class="inp-item">
					<view class="title">
						结束时间
					</view>
					<view class="in se">
						<picker mode="date" :value="endTime"  @change="bindEndDateChange">
							<view class="d-picker">{{endTime?endTime:"请选择结束时间"}}</view>
						</picker>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</view>
		</view>
		
		
		
		<view class="footer">
			<view class="next pub" @click="next">
				保存
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return{
				startTime: "2024-01-01",
				endTime:"2024-01-01",
			}
		},
		methods:{
			bindStartDateChange(e){
				console.log(e)
				this.startTime = e.detail.value
			},
			bindEndDateChange(e){
				this.endTime = e.detail.value
			}
		}
	}
</script>
<style>
	page{
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index{
		padding-bottom: 170rpx;
	}
	.wrap {
		padding: 30rpx;
	
		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			margin-bottom: 32rpx;
			.avatar {
				display: flex;
				align-items: center;
	
				.pic {
					padding: 0 30rpx 0 0;
	
					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}
	
			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;
				.txt{
					padding: 24rpx 0;
				}
				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;
					.star{
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}
				
				.supTitle{
					font-weight: 500;
					font-size: 32rpx;
					color: #333333;
				}
	
				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;
					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}
					
					::v-deep picker{
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;
						.d-picker{
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}
	
				.se {
					color: #999;
				}
	
				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}
	
	.footer {
		display: flex;
		justify-content: space-around;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 16rpx 16rpx 0 0;
	
		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}
		.save{
			border: 1px solid #4F8CF0;
			color: #4F8CF0;
		}
		.pub {
			background: #4F8CF0;
			border: 1px solid #4F8CF0;
			color: #FFFFFF;
		}
	}
</style>