<template>
	<view>
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="tabs">
					<u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" @click="topTabChange()"></u-tabs>
				</view>
				
				<view class="searchBox">
					<u-search placeholder="请输入姓名或手机号" bgColor="#FFFFFF" showAction @custom="custom" v-model="keyword"></u-search>
					<view class="tabsBox">
						<u-tabs :list="list1" lineColor="transparent" :activeStyle="{
						color: '#fff',
						background:'#4F8CF0',
						padding:'8rpx 24rpx',
						borderRadius:'128rpx',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#333333',
						background:'#FFFFFF',
						padding:'8rpx 24rpx',
						borderRadius:'128rpx',
						transform: 'scale(1.05)'
					}" ></u-tabs>
					</view>
				</view>
			</view>
		</u-sticky>
		
		<view id="app">
			<view class="content">
				<view class="contentBox">
					<view class="userNameBox">
						<view>人才姓名</view>
						<view class="phoneNum">176333333333</view>
					</view>
					<view class="emailBox"><EMAIL></view>
					<view class="companyName">公司名称</view>
					<view class="notes">备注</view>
					<view class="hrBox"></view>
					
					<view class="btnBox">
						<view class="btn edit">编辑</view>
						<view class="btn del">删除</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 调用的通讯录内部 -->
		<!-- <block  v-if="list && topTabsIndex==1">
			<inner-item :list="list" :current="curNameId" :type="type" :flag="flag"
				@tree-node-click="nodeClick" @selected='selected'></inner-item>
		</block> -->
		
		<view style="height: 196rpx;"></view>
		
		<view class="bottomBox">
			<view class="bottomBox_btn" @click="goPage()">新增人才</view>
		</view>
		
		
	</view>
</template>

<script>
	import InnerItem from "../components/innerItem.vue"
	export default {
		components: {
			InnerItem
		},
		data() {
			return {
				tabs: [{
					name: '我的人才库',
				}, {
					name: '下游人才库',
				}],
				list1: [{
					name: '全部',
				}, {
					name: '分组名称',
				}, {
					name: '分组名称',
				}, {
					name: '分组名称',
				}, {
					name: '分组名称',
				}, {
					name: '分组名称',
				}, {
					name: '分组名称',
				}, {
					name: '分组名称',
				}],
				topTabsIndex:0
			}
		},
		methods: {
			goPage() {
				uni.navigateTo({
					url:'/pagesA/talentPool/addTalentPool'
				})
			},
			topTabChange(e) {
				this.topTabsIndex = e.index;
			}
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	view {
		box-sizing: border-box;
	}
	#app {
		width: 100%;
		padding: 0 32rpx;
	}
	.tabs {
		padding: 32rpx;
		background: #FFFFFF;
	}

	.searchBox {
		padding: 32rpx;
	}
	
	.tabsBox {
		padding-top: 24rpx;
	}
	
	.content {
		width: 100%;
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 32rpx;
	}
	
	.contentBox {
		width: 100%;
		padding: 32rpx;
		background: #F5F5F7;
		border-radius: 24rpx;
		margin-bottom: 24rpx;
	}
	
	.userNameBox {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-weight: 600;
		font-size: 32rpx;
		color: #333333;
		margin-bottom: 16rpx;
	}
	
	.phoneNum {
		font-weight: 400;
		font-size: 28rpx;
		color: #999999;
	}
	
	.emailBox {
		font-weight: 500;
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 16rpx;
	}
	
	.companyName {
		font-weight: 500;
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 16rpx;
	}
	
	.notes {
		font-weight: 400;
		font-size: 24rpx;
		color: #CCCCCC;
	}
	
	.hrBox {
		width: 558rpx;
		height: 2rpx;
		background: #FFFFFF;
		margin: 16rpx 0;
	}
	
	.btnBox {
		display: flex;
	}
	
	.btn {
		padding: 12rpx 32rpx;
		color: #FFFFFF;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		margin-right: 24rpx;
	}
	
	.edit {
		background: #F9AD14;
	}
	
	.del {
		background: #FE4D4F;
	}
	
	.bottomBox {
		width: 100%;
		height: 196rpx;
		background: #FFFFFF;
		box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0,0,0,0.05);
		border-radius: 24rpx 24rpx 0 0;
		position: fixed;
		bottom: 0;
		left: 0;
		padding: 24rpx 32rpx 92rpx 32rpx ;
	}
	
	.bottomBox_btn {
		width: 100%;
		height: 80rpx;
		background: linear-gradient( 135deg, #4F8CF0 0%, #1E6DEE 100%);
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		text-align: center;
		line-height: 80rpx;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
	}
</style>