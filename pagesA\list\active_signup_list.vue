<template>
	<view class="">
		<u-sticky bgColor="#F5F5F5">
			<view class="tabs">
				<u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
					color: '#4F8CF0',
					fontWeight: 'bold',
					transform: 'scale(1.05)'
				}" :inactiveStyle="{
					color: '#999999',
					transform: 'scale(1)'
				}" :list="tabs" @click="changeTab"></u-tabs>
			</view>
		</u-sticky>
		<view class="list">
			<active-signup-item v-for="(item,index) in list" :key="index" :item="item"></active-signup-item>
		</view>
	</view>
</template>

<script>
	import ActiveSignupItem from '../components/activeSignupItem.vue'
	export default {
		components: {
			ActiveSignupItem
		},
		data() {
			return {
				tabIndex:0,
				tabs:[
					{
						name:'全部'
					},
					{
						name:'已报名'
					},
					{
						name:'已通过'
					},
					{
						name:'已驳回'
					}
				],
				list:[
					{
						status:1
					},
					{
						status:2
					},
					{
						status:3
					}
				]
			}
		},
		methods:{
			changeTab(e){
				this.tabIndex = e.index
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.tabs{
		background-color: #FFFFFF;
		border-radius: 0 0 24rpx 24rpx;
	}
	.list {
		padding: 32rpx;
	}
</style>