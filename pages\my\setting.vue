<template>
	<view class="home-index">
		<view class="list">
			<view class="item" @click="goSecurity">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/Ha48awxXalvLjvravoOJ0qhkge6jf6xJs9qejwpq.png" mode=""></image>
					<text>账号与安全中心</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
			<!--<view class="item" @click="goHeadhuntersInfo">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/Ha48awxXalvLjvravoOJ0qhkge6jf6xJs9qejwpq.png" mode=""></image>
					<text>管家信息</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>-->
			<view class="item">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/1pZsugLw3xbS3Gtv3FXHRY8pIIpQRD9gb66chlpI.png" mode=""></image>
					<text>切换账号</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
			<view class="item">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/ClWIRCz4ZQS9KTXA2rjl07Mr9Pda3VT0CWTmTdu7.png" mode=""></image>
					<text>通知与提醒</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>

			<view class="item" @click="goPrivacy">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/UWZWPWPDxTiKmYwGKs3X5ZwGqVLqnLTfjFvqWIXN.png" mode=""></image>
					<text>隐私保护</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
			<view class="item" @click="clearStorage">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/pM3IP2ae6MxB7tvZcDYAeBkb7NCQvUINE4BJ1K7k.png" mode=""></image>
					<text>清除缓存</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
			<view class="item" @click="goPrivate">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/uxYbpkeJWG8nnakwqZFtuvVuE2NkfqyfUUYBsbpU.png" mode=""></image>
					<text>隐私与规则</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
			<view class="item" @click="goAccount">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/xmALm4AEhmQQRJg9ptRYuP9blfmrkw8TibfAenK5.png" mode=""></image>
					<text>账号管理</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>

			<view class="item" @click="goAccount">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/1gMS52mKqevrFL1ErdpP20BWhUXYH6XWxTzh30PP.png" mode=""></image>
					<text>我的地址</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
			<view class="item" @click="goComplaint">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/1gMS52mKqevrFL1ErdpP20BWhUXYH6XWxTzh30PP.png" mode=""></image>
					<text>我的投诉</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
			<view class="item" @click="goFeedback">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/9kuwPnUnnkOqVLTyRREg5e5iC8wgvvFmNvKubTWv.png" mode=""></image>
					<text>用户反馈</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
			<view class="item">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/XrSPY01Y3IsrobqF0vcBqizjhogj3GvNHJf63drf.png" mode=""></image>
					<text>关于我们</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>

			<view class="item" @click="logout">
				<view class="item-left">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/RXSH9bUwuf6lnhA1MuzwhD7B10ZBVhrYrR9a3SA1.png" mode=""></image>
					<text>退出登录</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		logout
	} from "../../config/api.js"
	export default {
		data() {
			return {

			}
		},
		methods: {
			clearStorage() {
				uni.showModal({
					title: '确认',
					content: '确定要清除缓存吗？',
					success: (res) => {
						if (res.confirm) {
							uni.clearStorageSync();
							uni.reLaunch({
								url: '/pages/index/index'
							});
						} else if (res.cancel) {
							console.log('用户取消了清除存储');
						}
					}
				});
			},
			goSecurity() {
				uni.navigateTo({
					url: "/pagesB/personal/index"
				})
			},
			/*goHeadhuntersInfo() {
				uni.navigateTo({
					url: "/pagesA/headhuntersInfo/index"
				})
			},*/
			goFeedback() {
				uni.navigateTo({
					url: "/pagesA/personal/feedback"
				})
			},
			goPrivate() {
				uni.navigateTo({
					url: "/pages/my/private"
				})
			},
			goAccount() {
				uni.navigateTo({
					url: "/pagesA/personal/accountManagement"
				})
			},
			goPrivacy() {
				uni.navigateTo({
					url: "/pagesC/privacy/index"
				})
			},
			async logout() {
				const {
					status_code,
					data,
					message
				} = await logout()
				if (status_code == 200) {
					this.$store.commit('setToken', '')
					this.$store.commit('setRoleType', 'member')
					this.$store.commit('setUserInfo', null)
					// uni.setStorageSync('token','')
					// uni.setStorageSync('roleType','member')
					// uni.setStorageSync('userInfo',null)
					uni.clearStorageSync()
					uni.reLaunch({
						url: "/pages/login/login"
					})
				}
			},
			goComplaint() {
				uni.navigateTo({
					url: "/pagesC/complaint/index"
				})
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding: 32rpx;
	}

	.list {
		display: flex;
		flex-direction: column;
		background: #FFFFFF;
		padding: 0 48rpx;
		border-radius: 24rpx;
		z-index: 10;

		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 100rpx;

			.item-left {
				display: flex;
				align-items: center;
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;

				image {
					width: 32rpx;
					height: 32rpx;
				}

				text {
					margin-left: 24rpx;
				}
			}
		}
	}

	.logout {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100rpx;
		background: #4F8CF0;
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: 600;
		margin-top: 80rpx;
		border-radius: 24rpx;
	}
</style>
