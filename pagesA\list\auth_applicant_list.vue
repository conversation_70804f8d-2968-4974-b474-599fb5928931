<template>
    <view class="">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="tabs">
                    <u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" @click="changeTab"></u-tabs>
                </view>
                <view class="search-wrap">
                    <u-search placeholder="请输入应聘人手机号" bgColor="#FFFFFF" :showAction="false"
                        v-model="keyword"></u-search>
                </view>
                <view class="filters">
                    <view class="filter">
                        <picker @change="changeOpen" :value="isOpenIndex" :range="isOpenList" range-key="name">
                            <view class="d-picker">{{isOpenList[isOpenIndex]['name']}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                </view>
            </view>
        </u-sticky>

        <view class="list">
            <auth-applicant-item v-for="(item,index) in list" :key="index" :item="item"
                @selectComp="selectComp"></auth-applicant-item>
        </view>
        <Pages :status="status"></Pages>

        <u-popup :show="showComp" :round="10" bgColor="#F5F5F5" mode="bottom" closeOnClickOverlay @close="closeComp">
            <view class="credit">
                <view class="title comp">
                    请选择推送企业
                </view>
                <view class="content">
                    <view class="btns">
                        <view :class="['btn',compIndex==1?'active':'']" @click="changeComp(1)">
                            <text class="">学创联盟（北京）网络科技公司</text>
                        </view>
                        <view :class="['btn',compIndex==2?'active':'']" @click="changeComp(2)">
                            <text>学创联盟（天津）网络科技公司</text>
                        </view>
                    </view>
                </view>

                <view class="agree" @click="">
                    确定
                </view>
            </view>
        </u-popup>
        <view style="height: 120rpx;"></view>
        <view class="footer">
            <view class="next sure" @click="go">
                添加应聘人
            </view>
        </view>
    </view>
</template>

<script>
    import {
        getAuthApplicantList
    } from "../../config/api.js"
    import AuthApplicantItem from '../components/authApplicantItem.vue'
    import Pages from "../../components/pages.vue";
    export default {
        components: {
            AuthApplicantItem,
            Pages,
        },
        data() {
            return {
                page: 1,
                limit: 10,
                status: 'loadmore',
                more: false,
                keyword: '',
                showComp: false,
                currentItem: {},
                compIndex: 1,
                isOpenIndex: 0,
                isOpenList: [{
                        value: 0,
                        name: '是否公开简历'
                    },
                    {
                        value: 1,
                        name: '是'
                    }, {
                        value: 2,
                        name: '否'
                    }
                ],
                tabIndex: 0,
                tabs: [{
                        name: '全部'
                    },
                    {
                        name: '签署授权'
                    },
                    {
                        name: '手动授权'
                    }
                ],
                list: [],
                auth_type:''
            }
        },
        onLoad() {
            this.getAuthApplicantList()
        },
        //触底加载更多
        onReachBottom() {
        	if (this.more) {
        		this.status = 'loading'
        		this.page++
                this.getAuthApplicantList()
        	} else {
        		this.status = 'nomore'
        	}
        },
        methods: {
            go() {
                uni.navigateTo({
                    url: '/pagesA/headhunterList/authApplication/personalCenter'
                })
            },
            changeOpen(e) {
                this.isOpenIndex = e.detail.value
            },
            changeTab(e) {
                this.tabIndex = e.index
                if(this.tabIndex==1) {
                    this.auth_type ='sign'
                }
                if (this.tabIndex==2) {
                    this.auth_type = 'recommend'
                }
                this.page = 1;
                this.list = [];
                this.getAuthApplicantList()
            },
            selectComp(op) {
                uni.navigateTo({
                    // url:'/pagesA/list/pushCompany?id='+op.member_id+'&resume_id='+op.member_certification.id
                    url:`/pagesA/list/pushCompany?id=${op.item.member_id}&resume_id=0`
                })
                // this.currentItem = op.item;
                // this.showComp = op.show;
            },
            changeComp(index) {
                this.compIndex = index
            },
            closeComp() {
                this.showComp = false
            },
            // this.isOpenList[this.isOpenIndex]['value']
            async getAuthApplicantList() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    // open_status:1,//是否公开简历
                    // cellphone:this.keyword,
                    // rule:'modify',
                    auth_type:this.auth_type
                }
                const {
                    status_code,
                    data,
                    message
                } = await getAuthApplicantList(params)
                if (status_code == 200) {
                    this.list = this.list.concat(data.data);
                    // 返回false代表没有下一页
                    this.more = data.more;
                    this.status = this.more ? "loadmore" : "nomore"
                }
            }
        }
    }
</script>
<style>
    page {
        background-color: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    .list {
        padding: 0 32rpx;
    }

    .header {
        padding: 0 0 32rpx 0;

        .tabs {
            background-color: #FFFFFF;
            border-radius: 0 0 24rpx 24rpx;
        }

        .search-wrap {
            margin-top: 32rpx;
            padding: 0 32rpx;
        }

        .filters {
            display: flex;
            margin-top: 32rpx;
            padding: 0 32rpx;

            .filter {
                display: flex;
                align-items: center;
                height: 48rpx;
                background-color: #FFFFFF;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 0 12rpx;
                margin-right: 12rpx;
                border-radius: 8rpx;

                image {
                    width: 24rpx;
                    height: 24rpx;
                }
            }
        }
    }

    .credit {
        display: flex;
        flex-direction: column;
        padding: 40rpx 48rpx;

        // background: #F5F5F7;
        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .logo-content {
                display: flex;

                image {
                    width: 48rpx;
                    height: 48rpx;
                    margin-right: 16rpx;
                }

                text {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 500;
                    font-size: 15px;
                    color: rgba(0, 0, 0, 0.9);
                }
            }

            .tip {
                image {
                    width: 48rpx;
                    height: 48rpx;
                }
            }
        }

        .comp {
            justify-content: center;
        }

        .content {
            display: flex;
            flex-direction: column;
            margin-top: 40rpx;

            .sub-title {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                color: #000000;
            }

            .desc {
                display: flex;
                font-weight: 400;
                margin-top: 16rpx;
                font-size: 24rpx;
                color: #999999;
            }

            .btns {
                display: flex;
                flex-direction: column;
                margin-top: 16rpx;

                .btn {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    background: #FFFFFF;
                    height: 140rpx;
                    border-radius: 16rpx;

                    &:last-child {
                        margin-top: 24rpx;
                    }

                    text {
                        font-weight: 400;
                        font-size: 32rpx;
                        color: rgba(0, 0, 0, 0.9);
                    }

                    .bind {
                        font-size: 24rpx;
                        color: #07C160;
                    }
                }

                .active {
                    color: #4F8CF0;
                    border: 1rpx solid #4F8CF0;
                }


            }


        }

        .other {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 60rpx 0;
            color: #576B95;
            font-weight: 500;
            font-size: 24rpx;
        }

        .agree {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 44rpx;
            height: 88rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #FFFFFF;
            background: #4F8CF0;
            margin-top: 32rpx;
        }
    }

    .footer {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: fixed;
        left: 0;
        bottom: 0;
        height: 120rpx;
        width: 100%;
        background-color: #FFFFFF;
        z-index: 10;

        .next {
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 600;
            font-size: 28rpx;
            color: #FFFFFF;
            height: 88rpx;
            width: 90%;
            border-radius: 16rpx;
        }

        .sure {
            background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
            color: #FFFFFF;
        }
    }
</style>