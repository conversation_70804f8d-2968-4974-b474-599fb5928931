// webSocket.js

const websoket = {};
const socketState = {
    isConnected: false, // 连接状态
    isReconnecting: false, // 是否正在重连
    reconnectAttempts: 0, // 当前重连次数
    maxReconnectAttempts: 6, // 最大重连次数
    reconnectInterval: 3000, // 重连间隔，单位为毫秒
};

let isInitializing = false; // 初始化锁
let heartbeatTimer = null; // 心跳检测定时器

/**
 * 初始化 WebSocket 连接
 */
const webSoketInit = () => {
    const wsUrl = uni.getStorageSync('ws_url'); // 动态读取 WebSocket URL
    console.log('WebSocket URL:', wsUrl);

    /**
     * 重连函数
     */
    const reconnect = () => {
        if (socketState.reconnectAttempts < socketState.maxReconnectAttempts) {
            socketState.reconnectAttempts += 1;
            console.log(`尝试第 ${socketState.reconnectAttempts} 次重连...`);
            setTimeout(() => {
                initWebSocket();
            }, socketState.reconnectInterval);
        } else {
            console.log("达到最大重连次数，停止重连");
        }
    };

    /**
     * WebSocket 初始化函数
     */
    const initWebSocket = () => {
        if (isInitializing) return; // 防止重复初始化
        isInitializing = true;

        const wsUrl = uni.getStorageSync('ws_url'); // 动态读取最新值
        console.log('WebSocket URL:', wsUrl);
        websoket.value = uni.connectSocket({
            url: wsUrl,
            success: () => {
                console.log('WebSocket连接任务创建成功');
                console.log('websoket.value:', websoket.value); // 调试日志
            },
            fail: () => {
                console.log('WebSocket连接失败');
                isInitializing = false; // 解锁
                uni.showToast({
                    title: 'WebSocket连接失败',
                    icon: 'error',
                    duration: 2000,
                });
                reconnect(); // 连接失败时尝试重连
            },
        });
        // 绑定 WebSocket 事件
        websoket.value.onOpen((res) => {
            console.log('WebSocket已打开', res);
            console.log('WebSocket对象:', websoket.value); // 打印 WebSocket 对象
            socketState.isConnected = true;
            socketState.isReconnecting = false;
            socketState.reconnectAttempts = 0;
            isInitializing = false; // 解锁
            startHeartbeat(); // 启动心跳检测
        });

        websoket.value.onClose(() => {
            console.log('WebSocket已经被关闭了');
            stopHeartbeat(); // 停止心跳检测
            socketState.isConnected = false;
            reconnect(); // 关闭时尝试重连
        });

        websoket.value.onError((err) => {
            console.error('WebSocket发生错误:', err);
            socketState.isConnected = false;
            reconnect(); // 错误时尝试重连
        });

        websoket.value.onMessage((res) => {
            console.log('接收到后端发送消息', res.data);
            handleMessage(res.data); // 消息处理
        });
    };

    // 初始化 WebSocket 连接
    initWebSocket();
};

/**
 * 心跳检测：启动定时器
 */
const startHeartbeat = () => {
    heartbeatTimer = setInterval(() => {
        if (websoket.value && websoket.value.readyState === 1) {
            console.log('发送心跳包...');
            websoket.value.send({
                data: JSON.stringify({ type: 'ping' }),
            });
        } else {
            clearInterval(heartbeatTimer);
            console.log('WebSocket未连接，停止心跳检测');
        }
    }, 30000); // 每30秒发送一次心跳
};

/**
 * 心跳检测：停止定时器
 */
const stopHeartbeat = () => {
    clearInterval(heartbeatTimer);
    console.log('心跳检测已停止');
};
// websoket.value.onMessage((res) => {
//     console.log('接收到后端发送消息:', res.data); // 调试日志
//     handleMessage(res.data); // 消息处理
// });
/**
 * 消息处理逻辑
 */
const handleMessage = (message) => {
    try {
        const data = JSON.parse(message);
        switch (data.type) {
            case "ping":
                handlePing(data);
                break;
            case "999":
                handleOtherLogin(data);
                break;
            case "onConnect":
                handleConnectClientId(data);
                break;
            case "message":
                handleMessageContent(data);
                break;
            default:
                console.log("未知消息类型:", data.type);
                break;
        }
    } catch (error) {
        console.error("解析消息失败:", error);
    }
};

/**
 * 处理 ping 消息
 */
const handlePing = (data) => {
    console.log('收到心跳响应:', data);
    websoket.value.send({
        data: JSON.stringify({ type: 'pong' }),
    });
};

/**
 * 处理其他地方登录的消息
 */
const handleOtherLogin = (data) => {
    console.log('其他地方登录了:', data);
    uni.showToast({
        title: '您的账号已在其他地方登录',
        icon: 'none',
        duration: 3000,
    });
};

/**
 * 处理 client_id 缓存
 */
const handleConnectClientId = (data) => {
    uni.setStorageSync('connect_client_id', data?.client_id);
    console.log("接收到了 client_id 并缓存", data?.client_id);
};

/**
 * 处理普通消息内容
 */
const handleMessageContent = (data) => {
    console.log("收到了新的消息", data.content);
    let dataNew = {
        sendName: "",
        avatar: data.content.right_user.avatar,
        sendText: data.content.content,
        createTime: data.content.created_at,
        updateTime: data.content.updated_at,
        chatmState: 1,
        show_type: data.content.show_type,
        TextType: data.content.type
    };
    if (data.content.show_type == 'left') {
        dataNew.avatar = data.content.left_user.avatar;
    }
    let chatHistory = store.state.chatHistory || uni.getStorageSync('chatHistory') || [];
    chatHistory.push(dataNew);
    store.commit('setChatHistory', chatHistory);
};

/**
 * 获取 WebSocket 对象
 */
const getWebSocket = () => {
    return websoket;
};

/**
 * 关闭 WebSocket 连接
 */
const closeSocket = () => {
    if (websoket.value && [0, 1].includes(websoket.value.readyState)) {
        websoket.value.close({
            success(res) {
                socketState.isConnected = false;
                socketState.isReconnecting = false;
                socketState.reconnectAttempts = 0;
                console.log('关闭成功', res);
            },
            fail(err) {
                console.log('关闭失败', err);
            },
        });
    } else {
        console.log("WebSocket未连接或已关闭");
    }
};

/**
 * 发送消息
 */
const sendMessage = (message) => {
    const client_id = uni.getStorageSync('connect_client_id');
    if (!client_id) {
        console.error("client_id 为空，无法发送消息");
        return;
    }

    if (websoket.value && websoket.value.readyState === 1) {
        console.log('发送消息:', message);
        websoket.value.send({
            data: JSON.stringify({
                ...message,
                client_id: client_id // 确保 client_id 正确传递
            }),
        });
    } else {
        console.log("WebSocket未连接或已关闭，无法发送消息");
    }
};

export {
    getWebSocket,
    websoket,
    webSoketInit,
    closeSocket,
    sendMessage,
    socketState, // 导出连接状态对象
};