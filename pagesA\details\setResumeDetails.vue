<template>
	<view class="home-index">
		<view class="userInfo">
			<view class="info">
				<view class="userName">
					<view class="name">
                        <input type="text" v-model="form.name"/>
					</view>
					<view class="dot">

					</view>
					<view class="status">
						{{details.member_info.job_status_name}}
					</view>
					<!-- <image src="/pagesA/static/images/edit.png" mode="" @click="goResumePersonal"></image> -->
				</view>

				<view class="tags">
					{{details.member_info.sex==1?'男':details.member_info.sex==2?'女':'保密'}}·{{details.member_info.age}}岁·{{details.member_info.nation}}族·{{details.member_info.education_type_name}}
				</view>

				<view class="items">
					<view class="item">
						<u-icon name="phone" size="28rpx" color="#333333"></u-icon>
						<text>{{details.member_certification.mobile_no}}</text>
					</view>
					<!-- <view class="item">
						<u-icon name="email" size="28rpx" color="#333333"></u-icon>
						<text><EMAIL></text>
					</view> -->
				</view>
			</view>
			<image :src="details.member_info.image.thumbnail_path_url" mode=""></image>
		</view>

		<view class="wrap" @click="goJobExpection">
			<view class="title">
				<view class="name">
					<text>求职期望</text>
					<view class="star">*</view>
				</view>

			</view>
			<view class="content">
				<view class="cont">
					<view class="ui">
						<view class="expert">
							<view class="type">
                                {{details.public_resume.expect_job}}
							</view>
							<view class="money">
								{{details.public_resume.expect_salary_k}}
							</view>
						</view>
						<view class="pos">
							<view class="addr">
								{{details.public_resume.expect_address}}
							</view>
							<view class="type">
								{{details.public_resume.work_status_name}}
							</view>
						</view>
					</view>
					<u-icon name="arrow-right" size="28rpx"></u-icon>
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="title" @click="goSelf">
				<view class="name">
					<text>自我介绍</text>
					<view class="star">*</view>
				</view>
				<view class="plus">
					<u-icon name="arrow-right" size="28rpx"></u-icon>
				</view>
			</view>
			<view class="content">
				<view class="intro">
					{{details.member_info.introduce}}
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="title">
				<view class="name">
					<text>教育经历</text>
					<view class="star">*</view>
					<!-- <image src="../static/images/edit.png" mode="" @click="goEduExperience"></image> -->
				</view>
				<!-- <view class="plus">
					<image src="/static/images/index/plus.png" mode="" @click="goEduExperience"></image>
				</view> -->
			</view>

			<view class="content">
				<view class="jy-list">
					<view class="jy-item" v-for="(edu,eduInd) in details.member_info.education_log" :key="edu.id">
						<view class="name">
							 {{edu.school}}
							<!-- <u-icon name="arrow-right" size="28rpx"></u-icon> -->
						</view>
						<view class="time">
							{{edu.start}}-{{edu.end}}
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="title">
				<view class="name" >
					<text>工作经历</text>
				</view>
				<view class="plus">
					<image src="/static/images/index/plus.png" mode="" @click="goWorkExperience('add')"></image>
				</view>
			</view>
			<view class="content">
				<view class="sub-wrap" v-for="(item,index) in details.public_resume.job_log" :key="index">
					<view class="exper" @click="goWorkExperience('edit',index)">
						<view class="name">
							{{item.company}}
						</view>
						<view class="time">
							<text>{{item.start_date}}-{{item.end_date}}</text> <u-icon name="arrow-right"
								size="28rpx"></u-icon>
						</view>
					</view>
					<view class="user-info">
						<text class="user-name">{{item.contact_name}}</text>
						<text class="mobile">{{item.contract_cellphone}}</text>
					</view>

					<view class="types">
						<view class="type">
							{{item.industry}}
						</view>
						<view class="type">
							{{item.job_name}}
						</view>
					</view>

					<view class="list">
						<view class="item">
							<view class="dot">
							</view>
							<view class="js">
								业绩：{{item.achievement}}
							</view>
						</view>

						<view class="item">
							<view class="dot">
							</view>
							<view class="js">
								经历：{{item.content}}
							</view>
						</view>
					</view>
				</view>

			</view>
		</view>



		<view class="wrap">
			<view class="title">
				<view class="name">
					<text>资格证书</text>
				</view>
			</view>

			<view class="content">
				<view class="pic-list">
					<block>
						<view class="pic-item" v-for="(item,index) in certificates" :key="index">
							<image :src="item.path_url" mode=""></image>
							<view class="zz">
								<view class="del" @click="delCert(index)">
									删除
								</view>
							</view>
						</view>
					</block>

					<view class="pic-item add" @click="uploadCert">
						<image src="https://api-test.zhaopinbei.com/storage/uploads/images/Yrunj9s84WfBtqnGlkJNWCNucTQQivbhdGpacEWD.png" style="width: 40rpx;height: 40rpx;" mode=""></image>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="btns">
				<view class="btn zx">
					保存
				</view>
			</view>
		</view>
	</view>
</template>

<script>
    import {
    	authorizeShow
    } from "../../config/headhunterList_api.js"
	import {
		uploadImg,
		getResumeDetails,
		addResume
	} from "../../config/api.js"
	export default {
		data() {
			return {
                id:'',
				certificates:[],
				details: {},
                form: {
                    id: '',
                    name: '',
                    mobile: '',
                    age: '',
                    sex: '',
                    nation: '',
                    education_type: '', // 学历
                    education_log: [{
                        school: '',
                        start: '',
                        end: ''
                    }],
                    job_log: [{
                        company: '',
                        job_name: '',
                        start_date: '',
                        end_date: '',
                        industry: '',
                        contact_name: '',
                        contract_cellphone: '',
                        content: '',
                        achievement: ''
                    }],
                    certificates: [], // 证书
                    work_type_id: '', // 就业类型
                }

			}
		},
		computed: {
			userInfo() {
				return this.$store.state.userInfo || uni.getStorageSync('userInfo')
			}
		},
		onLoad(options) {
            this.id = options.id
			this.authorizeShow()
		},
		onShow() {
			// console.log("show方法：", options)
			// let pages = getCurrentPages();
			// // 数组中索引最大的页面--当前页面
			// let currentPage = pages[pages.length - 1];
			// let options = currentPage.options
			// this.getResumeDetails()
		},
		methods: {
			goResumePersonal() {
				uni.navigateTo({
					url: "/pagesA/add/resumePersonal"
				})
			},
			goWorkExperience(flag, index) {
				uni.navigateTo({
					url: "/pagesA/add/workExperience?id=" + this.id + '&flag=' + flag + '&index=' + index
				})
			},
			goEduExperience() {
				uni.navigateTo({
					url: "/pagesA/add/eduExperience"
				})
			},
			goJobExpection() {
				uni.navigateTo({
					url: "/pagesA/add/jobExpection?id=" + this.id
				})
			},

			goSelf() {
				uni.navigateTo({
					url: "/pagesA/add/selfIntroduction?id=" + this.id
				})
			},


			//删除
			async delCert(index){
				this.certificates.splice(index,1)
				let ids = this.certificates.map(item => item.id)

				let params = {
					id:this.id,
					certificates:ids
				}
				const {status_code,data} = await addResume(params)
				if(status_code==200){
					return uni.$u.toast('成功')
				}
			},

			//上传荣誉证书
			uploadCert() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						// $dialog.loading('上传中')
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								if (result.status_code == 200) {
									self.certificates.push({
										id: result.data.id,
										path_url: result.data.url
									})

									console.log("资格证书：",self.certificates)
									let ids = self.certificates.map(item => item.id)
									let params = {
										id:self.id,
										certificates:ids
									}
									const {status_code,data} = await addResume(params)
									if(status_code==200){
										// self.getResumeDetails()
										return uni.$u.toast('成功')
									}
								}
							}
						})
					}
				});
			},
			//简历详情
			async getResumeDetails() {
				let params = {
					id: this.id
				}
				const {
					status_code,
					data
				} = await getResumeDetails(params)
				if (status_code == 200) {
					this.details = data;
					this.certificates = data.certificates
					console.log(this.details, "简历详情")
				}
			},
            //简历详情
            async authorizeShow() {
            	let params = {
            		id: this.id
            	}
            	const res = await authorizeShow(params)
            	if (res.status_code == 200) {
            		console.log("res",res)
                    this.details = res.data;
                    this.certificates = res.data.public_resume.certificates;
                    const details = this.details;
                    this.form.name = details.member_info.nick_name;
                    // this.form.mobile=details.member_certification.mobile_no;
                    // this.form.age = details.member_info.age;
                    // this.form.nation = details.member_info.nation;
                    // this.form.education_type=details.member_info.education_type_name;
                    // this.form.education_log = details.member_info.education_log;
                    // this.form.job_log = details.public_resume.job_log;
                    // this.certificates = details.public_resume.certificates;

            	}
            },
		}
	}
</script>

<style lang="less" scoped>
	.home-index {
		padding: 0 32rpx 160rpx 32rpx;
	}

	.userInfo {
		display: flex;
		padding: 32rpx 0;
		border-bottom: 1px solid #F5F5F7;

		.info {
			display: flex;
			flex-direction: column;
			flex: 1;

			.userName {
				display: flex;
				align-items: center;

				.name {
                    width: 200rpx;
					font-weight: 600;
					font-size: 40rpx;
					color: #333333;
				}

				.dot {
					width: 12rpx;
					height: 12rpx;
					border-radius: 50%;
					background-color: #57D51C;
					margin-left: 16rpx;
				}

				.status {
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
					margin-left: 16rpx;
				}

				image {
					width: 32rpx;
					height: 32rpx;
					margin-left: 16rpx;
				}
			}

			.tags {
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				margin: 16rpx 0;
			}

			.items {
				display: flex;
				align-items: center;

				.item {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;

					// &:last-child {
					// 	margin-left: 32rpx;
					// }

					text {
						margin-left: 16rpx;
					}
				}
			}
		}

		&>image {
			width: 108rpx;
			height: 108rpx;

		}
	}

	.wrap {
		display: flex;
		flex-direction: column;
		border-bottom: 1px solid #F5F5F7;
		padding: 32rpx 0;

		&:last-child {
			border-bottom: none;
		}

		.title {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.star {
				font-weight: 600;
				font-size: 22rpx;
				color: #FE4D4F;
				margin-left: 16rpx;
			}

			.name {
				display: flex;
				align-items: center;
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;

				image {
					width: 32rpx;
					height: 32rpx;
					margin-left: 16rpx;
				}
			}

			.plus {
				image {
					width: 36rpx;
					height: 36rpx;
				}
			}

		}

		.content {
			display: flex;
			flex-direction: column;

			// align-items: center;
			.cont {
				display: flex;

				.ui {
					display: flex;
					flex-direction: column;
					flex: 1;

					.expert {
						display: flex;
						align-items: center;
						margin-top: 40rpx;

						.type {
							font-weight: 400;
							font-size: 28rpx;
							color: #333333;
						}

						.money {
							font-weight: 600;
							font-size: 28rpx;
							color: #4F8CF0;
							margin-left: 16rpx;
						}
					}

					.pos {
						display: flex;
						align-items: center;
						margin-top: 16rpx;
						font-weight: 400;
						font-size: 20rpx;
						color: #666666;

						.addr {}

						.type {
							margin-left: 32rpx;
						}
					}
				}

			}

			.sub-wrap {
				display: flex;
				flex-direction: column;
				margin-bottom: 32rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.exper {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 40rpx;

					.name {
						display: flex;
						flex: 1;
						font-weight: 500;
						font-size: 32rpx;
						color: #333333;
					}

					.time {
						display: flex;
						align-items: center;
						font-weight: 400;
						font-size: 20rpx;
						color: #666666;

						text {
							margin-right: 20rpx;
						}
					}
				}

				.user-info {
					display: flex;
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
					margin-top: 16rpx;

					.mobile {
						margin-left: 16rpx;
					}
				}

				.types {
					display: flex;
					margin-top: 16rpx;

					.type {
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
						margin-right: 24rpx;
					}
				}

				.list {
					display: flex;
					flex-direction: column;
					margin-top: 16rpx;

					.item {
						display: flex;
						margin-bottom: 16rpx;

						&:last-child {
							margin-bottom: 0;
						}

						.dot {
							width: 12rpx;
							height: 12rpx;
							border-radius: 50%;
							background-color: #666666;
							margin: 10rpx 24rpx;
						}

						.js {
							display: flex;
							flex: 1;
							font-weight: 400;
							font-size: 24rpx;
							color: #666666;
						}
					}
				}
			}

			.jy-list {
				display: flex;
				flex-direction: column;
				margin-top: 40rpx;

				.jy-item {
					display: flex;
					flex-direction: column;
					margin-bottom: 32rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.name {
						display: flex;
						justify-content: space-between;
						align-items: center;
						font-weight: 500;
						font-size: 32rpx;
						color: #333333;
					}

					.time {
						font-weight: 400;
						font-size: 24rpx;
						color: #666666;
						margin-top: 16rpx;
					}
				}
			}

			.pic-list {
				display: flex;
				flex-wrap: wrap;

				.pic-item {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 200rpx;
					width: calc(33.3% - 32rpx);
					margin-top: 32rpx;
					margin-right: 32rpx;
					position: relative;
					border-radius: 24rpx;

					&>image {
						width: 100%;
						height: 100%;
						border-radius: 24rpx;
					}

					.zz {
						display: flex;
						justify-content: center;
						align-items: center;
						background-color: rgba(0, 0, 0, 0.5);
						width: 100%;
						height: 100%;
						position: absolute;
						left: 0;
						top: 0;
						z-index: 10;
						border-radius: 24rpx;

						.del {
							display: flex;
							align-items: center;
							border-radius: 16rpx;
							padding: 0 16rpx;
							height: 56rpx;
							background: rgba(255, 255, 255, 0.5);
							font-weight: 500;
							font-size: 28rpx;
							color: #FFFFFF;
						}
					}
				}

				.add {
					background-color: #F5F5F7;

					image {}
				}

			}
		}

		.intro {
			font-weight: 400;
			font-size: 24rpx;
			color: #333333;
			margin-top: 40rpx;
		}
	}

	.footer {
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 140rpx;
		width: 100%;
		left: 0;
		bottom: 0;
		background: #FFFFFF;
		font-weight: 600;
		font-size: 28rpx;
		border-radius: 24rpx 24rpx 0 0;
		z-index: 999;

		.btns {
			display: flex;
			width: 90%;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 1;
				height: 80rpx;
				color: #333333;
				border-radius: 16rpx;
			}

			.cancel {
				background: rgba(254, 77, 79, 0.1);
				color: #FE4D4F;
			}

			.zx {
				color: #FFFFFF;
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			}
		}
	}
</style>
