<template>
	<view class="my">
		<top-bar-status :class="['topClass', isScroll ? 'isScroll' : '']">
			<template #default>
				<view class="top_content">
					<view class="logo" @click="open">
						<image src="/static/images/my/changeImg.png" mode=""></image>
						<image src="/static/images/my/scanImg.png" mode=""></image>
					</view>
					<view class="title">
						个人中心
					</view>
				</view>
			</template>
		</top-bar-status>

		<toggle-role-pop ref="rolePop" @sure="sure"></toggle-role-pop>

		<qlm-personal-center v-if="roleType=='member'||lroleType==''"></qlm-personal-center>
		<bl-personal-center v-if="roleType=='company'"></bl-personal-center>
		<jy-personal-center v-if="roleType=='headhunters'"></jy-personal-center>
		<tabbar></tabbar>
	</view>
</template>

<script>
	import {
		updateLoginInfo,
		switchLogin
	} from "../../config/api.js"
	import TopBarStatus from "../../components/topBarStatus.vue"
	import Tabbar from "../../components/tabbar.vue"
	import QlmPersonalCenter from "../../components/qlmPersonalCenter.vue"
	import BlPersonalCenter from "../../components/blPersonalCenter.vue"
	import JyPersonalCenter from "../../components/jyPersonalCenter.vue"
	import ToggleRolePop from "../../components/toggleRolePop.vue"
	export default {
		components: {
			TopBarStatus,
			Tabbar,
			QlmPersonalCenter,
			BlPersonalCenter,
			JyPersonalCenter,
			ToggleRolePop
		},
		data() {
			return {
				isScroll: false,
				isEye: true,
			}
		},
		computed: {
			roleType() {
				return this.$store.state.roleType || uni.getStorageSync('roleType')
			},

			userInfo() {
				return this.$store.state.userInfo || uni.getStorageSync('userInfo')
			}
		},
		methods: {

			goFavor() {
				uni.navigateTo({
					url: "/pagesA/list/qlm_collect_list"
				})
			},

			open() {
				this.$refs.rolePop.open()
			},

			async sure(type, companyId) {
				this.$refs.rolePop.close()
				let params = {
					type: type
				}
				if (companyId > 0) {
					params = {
						type: type,
						user_id: companyId,
					}
				}

				const {
					status_code,
					data
				} = await switchLogin(params)
				if (status_code == 200) {
					this.$store.commit('setToken', data.token)
					this.$store.commit('setRoleType', data.role_type)
					this.$store.commit('setUserInfo', data)
					let job_class_count = data.job_class_count < 1
					if (job_class_count) {
						uni.reLaunch({
							url: "/pagesA/tourist/selectJob?type=" + type
						})
					}
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}
				// if(status_code==200){
				// 	uni.$u.toast('成功')
				// 	let loginInfo = await updateLoginInfo()
				// 	if (loginInfo.status_code == 200) {
				// 		this.$store.commit('setUserInfo', loginInfo.data)
				// 		this.$store.commit('roleType', loginInfo.data['role_type'])
				// 		this.$store.commit('token', loginInfo.data['token'])
				// 	}
				// }else{
				// 	uni.$u.toast(message)
				// }
			}
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.topClass {
		position: inherit;
		width: 100%;
		z-index: 10;
		transition: all .5s;
		background: #FFFFFF;

		&.isScroll {
			position: fixed;
			top: 0;
			left: 0;
		}

		.top_content {
			display: flex;
			align-items: center;
			height: 100%;

			.logo {
				display: flex;
				position: absolute;
				align-items: center;
				color: #333333;
				font-size: 28rpx;
				padding-left: 48rpx;

				image {
					width: 40rpx;
					height: 40rpx;
					margin-right: 64rpx;
				}
			}

			.title {
				display: flex;
				flex: 1;
				justify-content: center;
				font-weight: 500;
				font-size: 34rpx;
				color: #000000;
			}
		}
	}
</style>
