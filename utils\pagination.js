// utils/pagination.js
export default {
    data() {
        return {
            page: 1,
            pageSize: 10,
            total: 0,
            list: [],
        };
    },
    methods: {
        // 请求分页数据的方法，需要根据具体API实现
        async fetchData(apiMethod,params = {}) {
            const res = await apiMethod(this.page, this.pageSize);
            if (res.success) {
                this.list = this.page === 1 ? res.data : [...this.list, ...res.data];
                this.total = res.total || this.list.length;
            } else {
                // 错误处理
            }
        },
        // 加载更多
        loadMore() {
            if (this.list.length < this.total) {
                this.page += 1;
                this.fetchData();
            }
        },
        // 重置分页数据
        resetPagination() {
            this.page = 1;
            this.list = [];
        }
    }
};