<template>
	<view class="home-index">
		<top-bar-status :class="['topClass', isScroll ? 'isScroll' : '']">
			<template #default>
				<view class="top_content">
					<view class="logo">
						<image src="../../static/images/index/logo.png" mode=""></image>
					</view>
					<view class="title">
						首页
					</view>
				</view>
			</template>
		</top-bar-status>
		<view class="search-wrap">
			<view class="pos">
				<text @click="goPage">{{userCity.name}}</text>
				<image src="/static/images/index/down.png" mode=""></image>
			</view>
			<u-search placeholder="请输入关键字" confirm-type="search" :animation="true" bgColor="#FFFFFF" :showAction="true"
				v-model="keyword" @custom="handleEnterPress" @clear="clear"></u-search>
		</view>

		<!-- 职位推荐 -->
		<index-nav @jobClassSearch="jobClassSearch" :list="navList" @update:list="handleUpdateList"
			:key="navList.length" v-if="roleType == 'member' || roleType == 'company' "></index-nav>

		<!-- 五个小标签 -->
		<block v-if="roleType=='member'">
			<qlm-nav></qlm-nav>
		</block>
		<block v-if="roleType=='company'||roleType=='headhunters'">
			<bl-nav></bl-nav>
		</block>

		<block v-if="roleType=='member'||roleType=='headhunters'">
			<qlmJyNavBottom></qlmJyNavBottom>
		</block>
		<block v-if="roleType=='company'">
			<qyNavBottom></qyNavBottom>
		</block>

		<view class="wrap">
			<view class="title">
				<view class="title-left">
					<image
						:src="roleType=='member'|| roleType==''?'/static/images/index/rm.png':'/static/images/index/ma.png'"
						mode="">
					</image>
					<text>{{roleType=='member'||roleType==''?'热门职位':'推荐千里马'}}</text>
				</view>

				<view class="title-right" @click="toJob">
					<text>{{roleType=='member'||roleType==''?'更多职位':'更多人才'}}</text>
					<u-icon name="arrow-right" size="28rpx"></u-icon>
				</view>
			</view>

			<view class="list">
				<block v-if="roleType=='member' || roleType==''">
					<job-item v-for="(item,index) in list" :key="index" :item="item"></job-item>
				</block>
				<block v-if="roleType=='company' || roleType=='headhunters'">
					<qlm-job-item v-for="(item,index) in list" :key="index" :item="item"></qlm-job-item>
				</block>
			</view>
		</view>
		<tabbar></tabbar>
		<ke-fu></ke-fu>
		<pub-job-btn></pub-job-btn>
		<Pages :status="status"></Pages>

		<tourist ref='touristRef' @childDestroyed="childDestroyed"></tourist>
	</view>
</template>

<script>
	// 引入部分
	import {
		getLocation
	} from '@/common/common.js'
	import {
		getCompanyMemberList,
		getRecomendList,
		getSysList,
		getCityList,
	} from "../../config/api.js"
    import {
    	updateMemberLocation,
        getCompanyLocation
    } from "../../config/common_api.js"

	import JobItem from "../../components/jobItem.vue"
	import QlmJobItem from "../../components/qlmJobItem.vue"
	import TopBarStatus from "../../components/topBarStatus.vue"
	import IndexNav from "../../components/indexNav.vue"
	import KeFu from "../../components/keFu.vue"
	import PubJobBtn from "../../components/pubJobBtn.vue"
	import Tabbar from "../../components/tabbar.vue"
	import QlmNav from "../../components/qlmNav.vue"
	import BlNav from "../../components/blNav.vue"
	import qlmJyNavBottom from "../../components/qlmJyNavBottom.vue"
	import qyNavBottom from "../../components/qyNavBottom.vue"
	import Tourist from "../../components/tourist.vue"
	import Pages from "../../components/pages.vue";
	import {
		webSoketInit
	} from "../../common/webSoket";
	export default {
		components: {
			Pages,
			TopBarStatus,
			IndexNav,
			KeFu,
			PubJobBtn,
			JobItem,
			QlmJobItem,
			Tabbar,
			QlmNav,
			BlNav,
			Tourist,
			qlmJyNavBottom,
			qyNavBottom
		},
		computed: {
			roleType() {
				let roleType = uni.getStorageSync('roleType') || this.$store.state.roleType
				return roleType
			},
		},

		data() {
			return {
				status: 'loadmore',
				more: false,
				keyword: '',
				page: 1,
				limit: 10,
				isScroll: false,
				list: [],
				navList: [],
				userCity: {
					name: '全国',
					id: 0
				},
				job_class_id: []
			}
		},
		onLoad() {
			// 用户选择的城市从本地获取name、id
			if (uni.getStorageSync('userCity')) {
				this.userCity = uni.getStorageSync('userCity');
			} else {
				uni.setStorageSync('userCity', this.userCity)
			}
			this.list = []
			if (!this.roleType) {
				this.$refs.touristRef.open()
			}
            let login_user_id = ''
            if (uni.getStorageSync('userInfo')) {
               login_user_id = uni.getStorageSync('userInfo').login_user.id
            }
            // 获取用户定位
            if(!uni.getStorageSync('userLocation')&&login_user_id&&this.roleType == 'member') {
                this.updateMemberLocation()
            }
            if(!uni.getStorageSync('userLocation')&&login_user_id&&this.roleType == 'company') {
                this.getCompanyLocation()
            }
		},
		onPageScroll(e) {
			this.isScroll = e.scrollTop > 0
		},
		onShow() {
			this.$store.commit('setSelected', 0)

			this.list = []
			this.getSysData()
			this.getCityList()
			this.init()
			this.updateNavList()

			if (this.userCity.name != uni.getStorageSync('userCity').name) {
				this.userCity = uni.getStorageSync('userCity'); // 更新当前城市
				this.page = 1;
				this.list = [];
				this.init()
			}
			// 千里马被就业管家注册首次登录触发
			if (uni.getStorageSync('roleType') == 'member') {
				if (uni.getStorageSync('userInfo').message && !uni.getStorageSync('loginMessage')) {
					uni.showModal({
						content: uni.getStorageSync('userInfo').message,
						showCancel: false,
						confirmText: '我知道了',
						success: function(res) {
							if (res.confirm) {
								uni.setStorageSync('loginMessage', 1)
								console.log('用户点击确定');
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}
			}

		},
		//触底加载更多
		onReachBottom() {
			if (this.more) {
				this.status = 'loading'
				this.page++
				if (this.roleType == 'member' || this.roleType == '') {
					this.getRecomendList()
				} else {
					this.getCompanyMemberList()
				}
			} else {
				this.status = 'nomore'
			}
		},
		methods: {
            // 获取定位的方法
			getLocation,
            // 修改用户定位信息
            async updateMemberLocation() {
                await this.getLocation()
                updateMemberLocation({
                    latitude: uni.getStorageSync('userLocation').latitude*1, // 纬度
                    longitude: uni.getStorageSync('userLocation').longitude*1, // 经度
                    login_user_id:uni.getStorageSync('userInfo').login_user.id, // 用户登录id
                    type:this.roleType
                }).then(res=>{
                    if(res.status_code != 200) {
                        uni.removeStorageSync('userLocation')
                        uni.showToast({
                        	title: res.message,
                        	icon: 'none'
                        })
                    } else {
                        this.init()
                    }
                })
            },
           async getCompanyLocation() {
                await this.getLocation()
                getCompanyLocation({
                    latitude: uni.getStorageSync('userLocation').latitude*1, // 纬度
                    longitude: uni.getStorageSync('userLocation').longitude*1, // 经度
                    user_id:uni.getStorageSync('userInfo').login_user.id, // 用户登录id
                    name:uni.getStorageSync('userInfo').company.name
                }).then(res=>{
                    if(res.status_code != 200) {
                        uni.removeStorageSync('userLocation')
                        uni.showToast({
                        	title: res.message,
                        	icon: 'none'
                        })
                    } else {
                        this.init()
                    }
                })
            },
			childDestroyed(e) {
				this.list = []
				this.getSysData()
				this.getCityList()
				this.init()
				this.updateNavList()

				if (this.userCity.name != uni.getStorageSync('userCity').name) {
					this.userCity = uni.getStorageSync('userCity'); // 更新当前城市
					this.page = 1;
					this.list = [];
					this.init()
				}
			},
			handleUpdateList(updatedList) {
				this.navList = [...updatedList];
				uni.setStorageSync('navList', updatedList);
				this.updateNavList();
				this.$forceUpdate();
			},
			updateNavList() {
				const res_userInfo = uni.getStorageSync('userInfo')
				if (!res_userInfo || !res_userInfo.login_user || !res_userInfo.login_user.job_class) {
					// console.error('userInfo 数据不完整');
					return;
				}

				this.navList = res_userInfo.login_user.job_class
				// console.log('更新前的 navList:', this.navList);
				this.navList.unshift({
					id: '0',
					name: '推荐',
					pid: '',
					tag: '为你推荐'
				})
			},

			goPage() {
				uni.navigateTo({
					url: '/pagesA/components/selectCitys'
				})
			},
			jobClassSearch(item) {
				this.list = []
				this.page = 1
				this.job_class_id = item.item.id == 0 ? [] : [item.item.id]

				if (this.roleType == 'member' || this.roleType == '') {
					this.getRecomendList()
				} else {
					this.getCompanyMemberList()
				}
			},
			handleEnterPress(event) {
				// 在这里处理回车事件
				this.keyword = event
				this.list = []
				this.page = 1
				if (this.roleType == 'member' || this.roleType == '') {
					this.getRecomendList()
				} else {
					this.getCompanyMemberList()
				}
			},
			// 打开城市选择页面
			// open() {
			// 	this.$refs.citySelect.open();
			// },

			toJob() {
				if (this.roleType == 'member' || this.roleType == '') {
					uni.navigateTo({
						url: "/pagesA/list/job_list?tabIndex=0"
					})
				} else {
					uni.navigateTo({
						url: "/pagesA/list/qlm_list?tabIndex=0"
					})
				}
			},
			//系统选项数据
			async getSysData() {
				const result = await getSysList()
				// this.sysData = result;
				this.$store.commit('setSysData', result);
			},
			//省市区数据
			async getCityList() {
				const result = await getCityList()
				if (result.status_code == 200) {
					this.$store.commit('setCityList', result.data)
				}
			},
			clear() {
				this.keyword = ''
				this.list = []
				this.page = 1
				if (this.roleType == 'member' || this.roleType == '') {
					this.getRecomendList()
				} else {
					this.getCompanyMemberList()
				}
			},

			init() {
				if (this.roleType == 'member' || this.roleType == '') {
					this.getRecomendList()
				} else {
					this.getCompanyMemberList()
				}
			},

			//member列表
			async getRecomendList() {
                let login_user_id = ''
                if(uni.getStorageSync('userInfo')&&uni.getStorageSync('userInfo').login_user.id) {
                    login_user_id =  uni.getStorageSync('userInfo').login_user.id;
                } else {
                    login_user_id = ''
                }
				let params = {
					limit: this.limit,
					page: this.page,
					title: this.keyword,
					tag_id: [],
                    login_user_id:login_user_id // 用户登录id
				}
				const {
					status_code,
					data
				} = await getRecomendList(params)
				if (status_code == 200) {
					this.list = this.list.concat(data.data);
					// 返回false代表没有下一页
					this.more = data.more;
					this.status = this.more ? "loadmore" : "nomore"
				}
			},

			//company列表
			async getCompanyMemberList() {
				let params = {
					limit: this.limit,
					page: this.page,
					name: this.keyword,
					job_class_id: this.job_class_id, //职位类型
					education: []
				}
				const {
					status_code,
					data
				} = await getCompanyMemberList(params)
				if (status_code == 200) {
					this.list = this.list.concat(data.data);

					this.more = data.more;
					this.status = this.more ? "loadmore" : "nomore"
				}
			},

		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding-bottom: 128rpx;
	}

	.search-wrap {
		display: flex;
		align-items: center;
		padding: 0 32rpx;
		margin: 32rpx 0;

		.pos {
			display: flex;
			align-items: center;
			margin-right: 16rpx;

			text {
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
			}

			image {
				width: 32rpx;
				height: 32rpx;
			}
		}
	}

	.bg {
		width: 100vw;
		height: 100vh;
		position: fixed;
		left: 0;
		top: 0;
		z-index: -100;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.topClass {
		position: inherit;
		width: 100%;
		z-index: 10;
		transition: all .5s;
		background: #FFFFFF;

		&.isScroll {
			position: fixed;
			top: 0;
			left: 0;
		}

		.top_content {
			display: flex;
			align-items: center;
			height: 100%;

			.logo {
				display: flex;
				position: absolute;

				image {
					width: 126rpx;
					height: 44rpx;
				}
			}

			.title {
				display: flex;
				flex: 1;
				justify-content: center;
				font-weight: 500;
				font-size: 34rpx;
				color: #000000;
			}
		}
	}

	.wrap {
		display: flex;
		flex-direction: column;
		padding: 0 32rpx;

		.title {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 34rpx 0;

			.title-left {
				display: flex;
				align-items: center;

				image {
					width: 32rpx;
					height: 32rpx;
					margin-right: 6rpx;
				}

				text {
					font-weight: 600;
					font-size: 32rpx;
					color: #333333;
				}
			}

			.title-right {
				display: flex;
				align-items: center;

				text {
					font-weight: 400;
					font-size: 28rpx;
					color: #999999;
				}
			}
		}

		.list {
			display: flex;
			flex-direction: column;

		}
	}
</style>

