<template>
	<view class="warp">
		<view class="top">
			<view class="ava-box">
				<img :src="info.ava" alt="" />
				<view class="">
					{{ info.name }}
				</view>
			</view>
			<view class="neirong">
				{{ detail.content }}
			</view>
			<view class="img-box" v-if="data.img">
				<image v-if="detail.form!=4" class="image" :src="detail.cover" alt="" />
				<video v-if="detail.form==4" :src="detail.cover" controls></video>

			</view>
			<view class="ip">ip归属地: {{ info.ip }}</view>
		</view>
		<view class="btm">
			<view class="title">{{infoList.length}}条评论</view>
			<view class="btm-box" v-for="(item,index) in infoList" @click="pinglunClick(item)">
				<view class="ava-box">
					<img :src="info.ava" alt="" />
					<view class="">
						{{ info.name }}
					</view>
				</view>
				<view class="btm-txt">{{item.content}}</view>
				<view class="btm-time">{{item.created_at}}</view>
				<view class="btm-box" v-for="(item,index) in infoList1" style="font-size: 24rpx;margin-left: 66rpx;">
					<view class="ava-box">
						<img :src="info.ava" alt="" />
						<view class="">
							{{ info.name }}
						</view>
					</view>
					<view class="btm-txt">{{item.reply}}</view>
					<view class="btm-time">{{item.created_at}}</view>
				</view>
			</view>
		</view>

		<view class="btm-bar">
			<view class="bar-left">
				<u--input ref="commentInput" type="text" :placeholder="places" border="surround" shape="circle" clearable v-model="inputValue" :focus="Focus" @confirm="handleEnter(places)"></u--input>
			</view>
			<view class="bar-right">
				<view class="">
					<img src="/static/images/publishCenter/msg_icon.png" alt="" />
					<text>{{infoList1.length}}</text>
				</view>
				<view class="">
					<!--<img src="/static/images/publishCenter/good_icon.png" alt="" @click="dianzanClick()"/>-->
					<u-icon size="20" :name="detail.namey"></u-icon>
					<text>{{detail.like_count}}</text>
				</view>
				<view class="">
					<!--<img src="/static/images/publishCenter/star_icon.png" alt=""  @click="shoucangClick()"/>-->
					<u-icon size="20" :name="detail.namet"></u-icon>
					<text>{{detail.love_count}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		storeAdd,
		indexList,
		replyEdit,
		storeFunt,
		destroyFunt,
	} from "../../config/api.js"
export default {
	data() {
		return {
			data: {
				title: '哈哈哈哈',
				time: '2025-04-19',
				view: 0,
				start: 0,
				msg: 2,
				good: 0,
				img: 'https://api-test.zhaopinbei.com/storage/uploads/images/jAfxcbrUljqX3kVonUX7C3eqePCloRBP9BewbEDp.png',
			},
			info: {
				name: '张三',
				ava: 'https://api-test.zhaopinbei.com/storage/uploads/images/7Md0Ku73yZXCUOENs3uRGMcZPkuGoUNdMIpFvY2b.png',
				ip: '河南',
			},
			infoList:[],
			infoList1:[],
			detail:'',
			inputValue:'',
			replyText: '', // 用于存储将要回复的文本内容
			Focus:false,
			places:'我来说两句...',
			flag:false,
			num:'',
			/*namey:'thumb-up',
			namet:'heart',
			num1:0,
			num2:0,*/
		};
	},
	onLoad(e){
		this.detail = JSON.parse(decodeURIComponent(e.key));
		//console.log(this.detail)
		//判断点过收藏，就让图标换实体图标
		if(this.detail.has_liked){
			this.detail.namey = 'thumb-up-fill';
		}else{
			this.detail.namey = 'thumb-up';
		}
		//判断点过赞，就让图标换实体图标
		if(this.detail.has_loved){
			this.detail.namet = 'heart-fill';
		}else{
			this.detail.namet = 'heart';
		}
		this.userType = uni.getStorageSync('roleType');
		this.company_id = uni.getStorageSync('userInfo')?.company.id;
		this.member_id = uni.getStorageSync('userInfo')?.member_info.member_id;
		this.indexLists();
	},
	methods: {
       //添加评论事件
		handleEnter(){
			//console.log(this.flag)
			if(!this.flag){
				const ls = {
					article_id:this.detail.id,
					content:this.inputValue,
				}
				if(this.userType=='company'){//伯乐
					ls.member_id = this.company_id;
				}else if(this.userType=='member'){//千里马
					ls.member_id = this.member_id;
				}else if(this.userType=='headhunters'){//就业管家

				}
				storeAdd(ls).then(({ status_code, data }) => {
					if (status_code == '200') {
						//console.log('111数据', data)
						this.inputValue = '';
						this.indexLists();
					}
				})
			}else {
				const ls1 = {
					id:this.num,
					//content:this.inputValue,
					reply:this.inputValue,
				}
				/*if(this.userType=='company'){//伯乐
					ls1.member_id = this.company_id;
				}else if(this.userType=='member'){//千里马
					ls1.member_id = this.member_id;
				}else if(this.userType=='headhunters'){//就业管家

				}*/
				replyEdit(ls1).then(({ status_code, data }) => {
					if (status_code == '200') {
						console.log('111数据', data)
						this.inputValue = '';
						this.indexLists();
						this.flag = false;
					}
				})
			}
		},
		//数组去重
		uniqueById(arr, idKey){
			const map = new Map();
			return arr.filter(item => !map.has(item[idKey]) && map.set(item[idKey], true));
		},
		//评论列表事件
		async indexLists(){
			const ls = {
				article_id:this.detail.id,
			}
			indexList(ls).then(({ status_code, data }) => {
				if (status_code == '200') {
					//console.log('111数据', data)
					data.forEach((item,i)=>{
						item.names = '张三';//先写假数据张三
						if(item.reply&&item.content){
							this.infoList1.push(item);
							this.infoList.push(item);

						}else if(item.content&&!item.reply){
							this.infoList.push(item);
						}
                       if(this.infoList.length>0){
                       	 this.infoList = this.uniqueById(this.infoList,'id')
					   }
					});


				}
			})
		},
		//点赞点击事件
		/*dianzanClick(){
			if(this.namey == 'thumb-up-fill'){//销毁点赞 先这样判断吧，没办法
				const ls = {
					article_id:this.detail.id,
				}
				destroyFunt(ls).then(({ status_code, data }) => {
					if (status_code == '200') {
						this.namey = 'thumb-up';
						this.num1 = 0;
					}
				})
			}else if(this.namey == 'thumb-up'){//添加点赞
				const ls = {
					article_id:this.detail.id,
					type:'1',
				}
				if(this.userType=='company'){//伯乐
					ls.member_id = this.company_id;
				}else if(this.userType=='member'){//千里马
					ls.member_id = this.member_id;
				}else if(this.userType=='headhunters'){//就业管家

				}
				storeFunt(ls).then(({ status_code, data }) => {
					if (status_code == '200') {
						this.namey = 'thumb-up-fill';
						this.num1 = 1;
					}
				})
			}

		},*/
		//收藏点击事件
		/*shoucangClick(){
			if(this.namet == 'heart-fill'){//销毁收藏 先这样判断吧，没办法
				const ls = {
					article_id:this.detail.id,
				}
				destroyFunt(ls).then(({ status_code, data }) => {
					if (status_code == '200') {
						this.namet = 'heart';
						this.num2 = 0;
					}
				})
			}else if(this.namet == 'heart'){//添加收藏
				const ls = {
					article_id:this.detail.id,
					type:'2',
				}
				if(this.userType=='company'){//伯乐
					ls.member_id = this.company_id;
				}else if(this.userType=='member'){//千里马
					ls.member_id = this.member_id;
				}else if(this.userType=='headhunters'){//就业管家

				}
				storeFunt(ls).then(({ status_code, data }) => {
					if (status_code == '200') {
						this.namet = 'heart-fill'
						this.num2 = 1;
					}
				})
			}

		},*/
		//评论点击事件
		async pinglunClick(item){
			const _this = this;
			_this.Focus = true;
			console.log(item)
			_this.num = item.id;
			// 获取软键盘的高度
			uni.onKeyboardHeightChange(res => {
				console.log(res.height);
				if(res.height === 0){
					_this.Focus = false;
				}
			})
			// 假设你想在输入框中显示被点击评论的内容，可以这样设置：
			const comment = _this.infoList.find(c => c.id === item.id);
			console.log(comment)
			if (comment) {
				_this.places = `回复 ${comment.names}:`; // 设置回复文本内容的前缀或其他信息
				_this.flag = true;
			} else {
				_this.places = '我来说两句...'; // 如果找不到对应的评论，清空输入框内容或设置其他默认值
			}
			//this.handleEnter(item)
		},

	},
	mounted() {
		this.$nextTick(() => {

		});
	},
};
</script>
<style>
page {
	background: #f5f5f7;
}
</style>
<style lang="less" scoped>
.warp {
	// width: 686rpx;
	// padding: 32rpx 32rpx;
	height: calc(100vh - 150rpx);
	overflow-y: auto;
	.top {
		background: #fff;
		width: 686rpx;
		padding: 32rpx 32rpx;
	}

	.ava-box {
		display: flex;
		align-items: center;

		img {
			width: 60rpx;
			height: 60rpx;
			border-radius: 30rpx;
			margin-right: 14rpx;
		}
	}

	.neirong {
		margin: 24rpx 0;
	}

	.img-box {
		margin-bottom: 24rpx;

		img {
			max-width: 686rpx;
			border-radius: 20rpx;
		}
	}

	.ip {
		font-size: 24rpx;
		color: #999;
	}

	.btm {
		background: #fff;
		width: 686rpx;
		padding: 32rpx 32rpx;
		margin-top: 20rpx;

		.title {
			font-size: 28rpx;
			color: #333;
			margin-bottom: 24rpx;
		}

		.btm-box {

			.btm-txt {
				width: 686rpx;
				margin: 24rpx 0;
				margin-left: 74rpx;
			}

			.btm-time {
				font-size: 22rpx;
				color: #999;
				margin-left: 74rpx;
			}
		}
	}

	.btm-bar {
		width: 100vw;
		padding-block-start: 32rpx;
		padding-block-end: calc(constant(safe-area-inset-bottom));
		padding-block-end: calc(env(safe-area-inset-bottom));
		position: fixed;
		bottom: 0;
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.bar-left {
			width: 50%;
		}

		.bar-right {
			width: 50%;
			display: flex;
			justify-content: space-around;
			align-items: center;

			& > view {
				display: flex;
				flex-direction: column;
				align-items: center;

				img {
					width: 32rpx;
					height: 32rpx;
					margin-bottom: 10rpx;
				}

				text {
					font-size: 28rpx;
					color: #999;
				}

			}
		}
	}
}
</style>
