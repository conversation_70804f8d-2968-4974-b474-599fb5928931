<template>
  <view>
    <view class="container">
      <view class="userInfo">
        <view class="avatar">
          <image
            :src="
              userInfo.member_info.image.path_url ||
              'https://api-test.zhaopinbei.com/storage/uploads/images/41R1LgOXldsXlyAjelHeNXsb0SuBPKE7ANcgobdG.png'
            "
            mode=""
          ></image>
        </view>
        <view class="user">
          <view class="user-up">
            <view class="name">
              {{ userInfo.member_certification.name }}
            </view>
            <view class="cert" v-if="userInfo.member.certification_status == 1"
              >已认证</view
            >
          </view>
          <view class="user-down">
            <view class="lab">就业管家</view>
            <view class="tags">
              {{ userInfo.company.name }}
            </view>
          </view>
        </view>

        <view class="personal" @click="goPersonalCenter">个人信息</view>
      </view>

      <view class="packageCard">
        <view class="package-wrap">
          <view class="package">
            <view class="title" @click="toggleEye">
              我的招聘币
              <image src="/static/images/my/eye.png" mode=""></image>
            </view>
            <view class="num">
              {{ isEye ? "88480.99" : "******" }}
            </view>

            <view class="lab" @click="goPackage">
              我的钱包
              <u-icon name="arrow-right" size="24rpx"></u-icon>
            </view>
          </view>
        </view>
        <view class="package-job">
          <view class="jobModule" @click="getjobViewed">
            <h3>36</h3>
            <p class="jobModule-title">浏览历史</p>
          </view>
          <view class="jobModule" @click="getjobList">
            <h3>22</h3>
            <p class="jobModule-title">千里马管理</p>
          </view>
          <view class="jobModule" @click="getjobHelper">
            <h3>3</h3>
            <p class="jobModule-title">伯乐管理</p>
          </view>
          <view class="jobModule" @click="getjobCollect">
            <h3>3</h3>
            <p class="jobModule-title">收藏</p>
          </view>
        </view>
      </view>
      <scroll-view scroll-y="true" class="scroll-Y">
        <view class="list tm">
          <view class="item" @click="goCto">
            <view class="item-left">
              <image src="/static/images/my/favor.png" mode=""></image>
              <text>企业信息管理</text>
            </view>
            <u-icon name="arrow-right" size="24rpx"></u-icon>
          </view>
          <view class="item" @click="goIntroduce">
            <view class="item-left">
              <image src="/static/images/my/favor.png" mode=""></image>
              <text>自我介绍</text>
            </view>
            <u-icon name="arrow-right" size="24rpx"></u-icon>
          </view>
          <view class="item" @click="gofllow">
            <view class="item-left">
              <image
                src="https://api-test.zhaopinbei.com/storage/uploads/images/V4Fcjjdp7kPJLNnPVc0gKi9JtUUXWVaNAFUhw6Fn.png"
                mode=""
              ></image>
              <text>我的关注</text>
            </view>
            <u-icon name="arrow-right" size="24rpx"></u-icon>
          </view>
          <view class="item" @click="goFavor">
            <view class="item-left">
              <image src="/static/images/my/favor.png" mode=""></image>
              <text>我的收藏</text>
            </view>
            <u-icon name="arrow-right" size="24rpx"></u-icon>
          </view>
          <view class="item" @click="gohome">
            <view class="item-left">
              <image src="/static/images/my/favor.png" mode=""></image>
              <text>管家信息</text>
            </view>
            <u-icon name="arrow-right" size="24rpx"></u-icon>
          </view>
          <view class="item" @click="goInvoiceCenter">
            <view class="item-left">
              <image src="/static/images/my/favor.png" mode=""></image>
              <text>开票中心</text>
            </view>
            <u-icon name="arrow-right" size="24rpx"></u-icon>
          </view>
          <!-- <view class="item" @click="goFeedback">
					<view class="item-left">
						<image src="/static/images/my/favor.png" mode=""></image>
						<text>用户反馈</text>
					</view>
					<u-icon name="arrow-right" size="24rpx"></u-icon>
				</view> -->

          <view class="item" @click="goSetting">
            <view class="item-left">
              <image src="/static/images/my/setting.png" mode=""></image>
              <text>设置</text>
            </view>
            <u-icon name="arrow-right" size="24rpx"></u-icon>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  name: "jyPersonalCenter",
  data() {
    return {
      isEye: false,
    };
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo || uni.getStorageSync("userInfo");
    },
  },
  methods: {
    goFavor() {
      uni.navigateTo({
        url: "/pagesA/headhunterList/list/collect_list",
      });
    },
    gohome() {
      //管家信息
      uni.navigateTo({
        url: "/pagesA/headhuntersInfo/index",
      });
    },
    goInvoiceCenter() {
      uni.navigateTo({
        url: "/pagesB/invoiceCenter/index",
      });
    },
    goIntroduce() {
      uni.navigateTo({
        url: "/pagesA/list/obtain_management_userDetails",
      });
    },
    goPersonalCenter() {
      uni.navigateTo({
        url: "/pagesA/personal/personalCenter",
      });
    },

    toggleEye() {
      console.log(1);
      this.isEye = !this.isEye;
    },
    goSetting() {
      uni.navigateTo({
        url: "/pages/my/setting",
      });
    },
    goPackage() {
      uni.navigateTo({
        url: "/pagesA/personal/package",
      });
    },
    goCto() {
      // uni.navigateTo({
      // 	url:"/pagesA/list/cto_info_list"
      // })
      uni.navigateTo({
        url: "/pagesA/details/ctoDetails",
      });
    },
    goFeedback() {
      uni.navigateTo({
        url: "/pagesA/personal/feedback",
      });
    },
    getjobList() {
      uni.navigateTo({
        url: "/pagesB/personal/find_job_record?id=1",
      });
    },
    getjobHelper() {
      uni.navigateTo({
        url: "/pagesA/member/details/authorizeDetail",
      });
    },
    getjobCollect() {
      uni.navigateTo({
        url: "/pagesB/personal/find_favor_record?id=1",
      });
    },
    getjobViewed() {
      uni.navigateTo({
        url: "/pagesB/personal/jyHistory?id=3",
      });
    },
	gofllow() {
      uni.navigateTo({
        url: "/pagesB/personal/myFllow?id=3",
      });
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  padding: 0 32rpx;

  .userInfo {
    // background-color: red;
    display: flex;
    position: relative;
    // padding: 0 32rpx;
    margin-top: 32rpx;

    .personal {
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      right: 0;
      top: 50%;
      margin-top: -30rpx;
      height: 60rpx;
      width: 136rpx;
      font-weight: 500;
      font-size: 20rpx;
      color: #333333;
      border-radius: 30rpx 0 0 30rpx;
      background: linear-gradient(112deg, #ffdb7b 0%, #f9ad14 100%);
    }

    .avatar {
      display: flex;
      justify-content: center;
      align-items: center;
      background: #ffffff;
      width: 108rpx;
      height: 108rpx;
      border-radius: 50%;

      image {
        width: 98rpx;
        height: 98rpx;
        border-radius: 50%;
      }
    }

    .user {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      flex: 1;
      padding-left: 16rpx;

      .user-up {
        display: flex;
        align-items: center;

        .name {
          font-weight: 500;
          font-size: 32rpx;
          color: #333333;
        }

        .cert {
          display: flex;
          align-items: center;
          height: 40rpx;
          background: #57d51c;
          font-weight: 600;
          font-size: 20rpx;
          color: #ffffff;
          padding: 0 12rpx;
          border-radius: 8rpx;
          margin-left: 12rpx;
        }
      }

      .user-down {
        display: flex;

        .lab {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #13c8f0;
          padding: 0 12rpx;
          height: 30rpx;
          border-radius: 15rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #ffffff;
          margin-right: 12rpx;
          font-style: italic;
        }

        .tags {
          display: flex;
          font-weight: 400;
          font-size: 22rpx;
          color: #999999;
        }
      }
    }
  }
  .packageCard {
    width: 100%;
    height: 187rpx;
    position: relative;
    margin-bottom: 32rpx;
  }

  .package-wrap {
    padding: 0 32rpx;
    margin-top: 30rpx;

    // background-color: red;
    .package {
      // background: linear-gradient( 90deg, #42B0EC 0%, #4F8CF0 100%);
      position: relative;
      border-radius: 24rpx;
      filter: blur(0.3px);
      background: linear-gradient(90deg, #42b0ec 0%, #4f8cf0 100%);
      border-radius: 12px 12px 12px 12px;
      padding: 24rpx 32rpx;

      .title {
        font-weight: 400;
        font-size: 24rpx;
        color: #ffffff;

        image {
          width: 24rpx;
          height: 24rpx;
          margin-left: 10rpx;
        }
      }

      .num {
        font-weight: bold;
        font-size: 64rpx;
        color: #ffffff;
        margin-top: 8rpx;
      }

      .lab {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        right: -32rpx;
        top: 50%;
        margin-top: -40rpx;
        height: 80rpx;
        width: 168rpx;
        font-weight: 500;
        font-size: 24rpx;
        color: #333333;
        border-radius: 16rpx;
        backdrop-filter: blur(25.6px);
        background: rgba(255, 255, 255, 0.7);
      }
    }
  }
  .package-job {
    width: 100%;
    height: 86%;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 16rpx;
    -webkit-backdrop-filter: blur(25.6rpx);
    backdrop-filter: blur(25.6rpx);
    font-size: 24rpx;
    position: absolute;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 28;
    margin-top: -19rpx;
    border-radius: 16rpx;

    .jobModule {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .jobModule-title {
        margin-top: 8rpx;
      }
    }
  }
  .tp {
    margin-top: 32rpx;
  }

  .tm {
    margin-top: -32rpx;
  }

  .list {
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(25.6px);
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    padding: 0 48rpx;
    border-radius: 24rpx;
    margin-top: 137rpx;

    z-index: 10;

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100rpx;

      .item-left {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;

        image {
          width: 32rpx;
          height: 32rpx;
        }

        text {
          margin-left: 24rpx;
        }
      }
    }
  }
}
.scroll-Y {
  height: 850rpx;
}
</style>
