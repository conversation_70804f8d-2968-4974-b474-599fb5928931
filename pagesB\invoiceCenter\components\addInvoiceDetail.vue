<template>
  <view class="warp">
    <view class="form-container">
      <view class="form-item">
        <view class="form-label">数电普票</view>
        <view class="form-content">
          <input class="form-input" type="text" :value="'开票中'" />
        </view>
      </view>
    </view>
    <view class="line">发票信息</view>
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 发票类型 -->
      <view class="form-item">
        <view class="form-label">发票类型</view>
        <view class="form-content">
          <view class="tab-group">
            <view
              class="tab-item"
              :class="{ active: formData.invoiceType === 'electronic' }"
              @click="formData.invoiceType = 'electronic'"
            >
              数电普票
            </view>
            <view
              class="tab-item"
              :class="{ active: formData.invoiceType === 'special' }"
              @click="formData.invoiceType = 'special'"
            >
              数电专票
            </view>
          </view>
        </view>
      </view>

      <!-- 抬头类型 -->
      <template>
        <view class="form-item">
          <view class="form-label">发票号码</view>
          <view class="form-content">
            <input class="form-input" type="text" :value="'12312321121'" />
          </view>
        </view>
        <view class="form-item">
          <view class="form-label required">抬头类型</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              :value="'企业单位'"
              placeholder="填写纳税人识别号（必填）"
              placeholder-class="placeholder"
            />
          </view>
        </view>
      </template>

      <view
        class="tips"
        v-if="
          formData.titleType === 'personal' && formData.invoiceType != 'special'
        "
        >如需报销建议选择企业单位</view
      >
      <!-- 发票抬头 -->
      <view
        class="form-item"
        v-if="
          formData.titleType === 'company' || formData.invoiceType === 'special'
        "
      >
        <view class="form-label required">发票抬头</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.title"
            placeholder="填写公司全称"
            placeholder-class="placeholder"
          />
        </view>
      </view>
      <!-- 发票抬头 -->
      <view
        class="form-item"
        v-if="
          formData.titleType === 'personal' && formData.invoiceType != 'special'
        "
      >
        <view class="form-label required">发票抬头</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.title"
            placeholder="填写个人真实姓名"
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <!-- 纳税人识别号 -->
      <view
        class="form-item"
        v-if="
          formData.titleType === 'company' || formData.invoiceType === 'special'
        "
      >
        <view class="form-label required">纳税人识别号</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.taxNumber"
            placeholder="填写纳税人识别号（必填）"
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <view
        class="form-item"
        v-if="
          formData.titleType === 'personal' && formData.invoiceType != 'special'
        "
      >
        <view class="form-label required">统一社会信用代码</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.title"
            placeholder="填写公司全称"
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <!-- 发票金额 -->
      <view class="form-item">
        <view class="form-label required">发票金额</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.amount"
            disabled
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <!-- 发票内容 -->
      <view class="form-item">
        <view class="form-label required">发票内容</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.content"
            placeholder="信息技术服务*网络招聘费"
            placeholder-class="placeholder"
          />
        </view>
      </view>
      <template
        v-if="
          formData.titleType === 'company' || formData.invoiceType === 'special'
        "
      >
        <!-- 公司地址 -->
        <view class="form-item">
          <view class="form-label">公司地址</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              v-model="formData.address"
              placeholder="填写公司注册地址"
              placeholder-class="placeholder"
            />
          </view>
        </view>

        <!-- 公司电话 -->
        <view class="form-item">
          <view class="form-label">公司电话</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              v-model="formData.phone"
              placeholder="填写公司注册电话"
              placeholder-class="placeholder"
            />
          </view>
        </view>

        <!-- 开户银行 -->
        <view class="form-item">
          <view class="form-label">开户银行</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              v-model="formData.bank"
              placeholder="填写公司开户银行"
              placeholder-class="placeholder"
            />
          </view>
        </view>

        <!-- 开户银行账号 -->
        <view class="form-item">
          <view class="form-label">开户银行账号</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              v-model="formData.bankAccount"
              placeholder="填写公司注册开户银行账号"
              placeholder-class="placeholder"
            />
          </view>
        </view>
      </template>
    </view>
    <view class="line">接受信息</view>
    <view class="form-container">
      <view class="form-item">
        <view class="form-label">电子邮箱</view>
        <view class="form-content">
          <input class="form-input" type="text" :value="' '" />
        </view>
      </view>
    </view>
    <view class="line1">
      <view class="inn">接受信息</view>
      <view class="txt">含1笔充值信息</view>
    </view>
    <view class="form-container">
      <view class="form-item">
        <view class="form-label">招聘币充值</view>
        <view class="form-content">
          <input class="form-input" type="text" v-model="formData.amount" />
        </view>
      </view>
    </view>
    <!-- 底部按钮 -->
    <view class="footer">
      <view class="top-btn" @click="topStep">上一步</view>
      <view class="next-btn" @click="show = true">确定</view>
    </view>
    <u-popup :show="show" mode="bottom" @close="show = false" @open="open">
      <view class="pop">
        <view class="pop-tit">
          <text>选择保存方式</text>
          <u-icon name="close" color="#666666" size="24"></u-icon>
        </view>
        <view class="pop-box">
          <view class="pop-inn" @click="show1 = true">
            <img
              src="https://api-test.zhaopinbei.com/storage/uploads/images/M3798IYRzkHPROZ93zPYfSDOWB6HDmjheTCvMfQ2.png"
              alt=""
            />
            <text>邮箱</text>
          </view>
          <view class="pop-inn" @click="show = false">
            <img
              src="https://api-test.zhaopinbei.com/storage/uploads/images/iJUR5GUceMZT7JTPQSzRKTchpD9LMnkZKXOM18uK.png"
              alt=""
            />
            <text>保存</text>
          </view>
        </view>
      </view>
    </u-popup>
    <u-popup :show="show1" mode="bottom" @close="show1 = false" @open="open">
      <view class="pop">
        <view class="pop-tit">
          <text>将发票发送至邮箱</text>
          <u-icon name="close" color="#666666" size="24"></u-icon>
        </view>
        <view class="pop-cont"
          >若您邮箱长时间未收到发票文件，建议您联系在线客服或选择保存方式，将发票下载到本地</view
        >
        <view class="pop-my">我的邮箱</view>
        <u--input
          placeholder="请输入内容"
          border="bottom"
          suffixIcon="edit-pen"
          clearable
        ></u--input>
        <view class="pop-btn" @click="nextStep">确认发送</view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  props: {
    // 从index页面传过来的金额
    amount: {
      type: [Number, String],
      default: 0,
    },
    // 从index页面传过来的选中的发票ID列表
    selectedIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      step: 0,
      show: false,
      show1: false,
      formData: {
        invoiceType: "electronic", // 默认数电普票
        titleType: "company", // 默认企业单位
        title: "", // 发票抬头
        taxNumber: "", // 纳税人识别号
        amount: "¥ 2657", // 发票金额
        content: "信息技术服务*网络招聘费", // 发票内容
        address: "", // 公司地址
        phone: "", // 公司电话
        bank: "", // 开户银行
        bankAccount: "", // 开户银行账号
      },
    };
  },
  created() {
    // 如果有传入金额，则设置到表单中
    if (this.amount) {
      this.formData.amount = `¥ ${parseFloat(this.amount).toFixed(2)}`;
    }
  },
  methods: {
    // 下一步
    nextStep() {
      uni.showToast({
        title: "发送成功",
        icon: "none",
      });
      this.show1 = false;
      this.show = false;
    },
    topStep() {
      uni.navigateBack();
    },

    // 验证表单
    validateForm() {
      // 验证发票抬头
      if (!this.formData.title) {
        uni.showToast({
          title: "请填写发票抬头",
          icon: "none",
        });
        return false;
      }

      // 验证纳税人识别号
      if (!this.formData.taxNumber) {
        uni.showToast({
          title: "请填写纳税人识别号",
          icon: "none",
        });
        return false;
      }

      // 验证发票内容
      if (!this.formData.content) {
        uni.showToast({
          title: "请填写发票内容",
          icon: "none",
        });
        return false;
      }

      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
.warp {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 196rpx;
}

.line {
  font-size: 28rpx;
  color: #333333;
  margin: 0 32rpx;
}
.line1 {
  font-size: 28rpx;
  color: #333333;
  margin: 0 32rpx;
  display: flex;
  justify-content: space-between;
  .txt {
    font-size: 24rpx;
    color: #333333;
  }
}

/* 步骤指示器样式 */
.steps-container {
  background-color: #ffffff;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f7;
}

.step-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.step-dot {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #cccccc;
  margin-bottom: 16rpx;
}

.step-dot.active .dot {
  background-color: #4f8cf0;
}

.step-text {
  font-size: 28rpx;
  color: #999999;
}

.step-text.active {
  color: #333333;
  font-weight: 500;
}

.step-line {
  height: 2rpx;
  background-color: #cccccc;
  flex: 1;
  margin: 0 16rpx;
  position: relative;
  top: -20rpx;
}

/* 表单容器样式 */
.form-container {
  background-color: #ffffff;
  padding: 0 32rpx;
  box-sizing: border-box;
  margin: 32rpx 32rpx 32rpx 32rpx;
  border-radius: 24rpx;
}
.tips {
  height: 44rpx;
  background: #e8f1ff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-size: 20rpx;
  color: #4f8cf0;
  line-height: 44rpx;
  // box-sizing: border-box;
  padding-left: 16rpx;
}

/* 表单项样式 */
.form-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f7;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  flex-shrink: 0;
  width: 200rpx;
}

.form-content {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  text-align: right;
}

.form-input {
  width: 100%;
  height: 60rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: right;
}

.placeholder {
  color: #cccccc;
  font-size: 28rpx;
  text-align: right;
}

/* 标签组样式 */
.tab-group {
  display: flex;
  gap: 32rpx;
  justify-content: flex-end;
}

.tab-item {
  padding: 12rpx 24rpx;
  border: 1rpx solid #e6e6e6;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

.tab-item.active {
  border-color: #4f8cf0;
  color: #4f8cf0;
  background-color: #f2f7ff;
}

/* 底部按钮样式 */
.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 32rpx;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.next-btn {
  width: 45%;
  height: 88rpx;
  background-color: #4f8cf0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
}
.top-btn {
  width: 45%;
  height: 88rpx;
  background: #e8f1ff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4f8cf0;
  font-size: 32rpx;
  font-weight: 500;
}
.pop {
  padding: 32rpx;
  .pop-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
    color: #999999;
    margin-bottom: 32rpx;
  }
  .pop-cont {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 32rpx;
  }
  .pop-my {
    font-size: 28rpx;
    color: #999999;
  }
  .pop-btn {
    width: 100%;
    height: 80rpx;
    background: #4f8cf0;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    line-height: 80rpx;
    margin-top: 40rpx;
  }
  .pop-box {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #999999;
    margin-bottom: 32rpx;
    gap: 230rpx;
    .pop-inn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-right: 64rpx;
      img {
        width: 76rpx;
        height: 76rpx;
      }
      text {
        font-size: 24rpx;
        color: #999;
        margin-top: 10rpx;
      }
    }
  }
}
</style>
