<template>
	<view class="item" @click="goDetails">
		<view class="item-up">
			<view class="name">
				{{ item.job.title }}
			</view>
			<view class="money">
				{{ item.job.salary_info_str }}
			</view>
		</view>

		<view class="desc"
			v-if="item.company.short_name || item.company_info.financing_type_name || item.company_info.size_type">
			<view class="desc-item" v-if="item.company.short_name">
				{{ item.company.short_name }}
			</view>
			<view class="desc-item" v-if="item.company_info.financing_type_name">
				{{ item.company_info.financing_type_name }}
			</view>
			<view class="desc-item" v-if="item.company_info.size_type">
				{{ item.company_info.size_type }}
			</view>
		</view>

		<view class="sub-name" v-if="item.company.intro">
			{{ item.company.intro }}
		</view>

		<block v-if="item && item.tags.length > 0">
			<view class="tags">
				<view class="tag" v-for="(sub, idx) in item.tags" :key="idx">
					{{ sub.title }}
				</view>
			</view>
		</block>
		<view class="item-down">
			<view class="addr">
				<image
					src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png"
					mode=""></image>
				<view class="addrText">{{ item.address[0].address_info }}</view>
			</view>
			<block v-if="type == 'active'">
				<view class="btn">立即报名</view>
			</block>
			<block v-else>
				<view class="btn">聊聊呗</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'collectJobItem',
		props: {
			item: {
				type: Object,
				default: () => {},
			},
			type: {
				type: String,
				default: '',
			},
		},
		data() {
			return {};
		},
		methods: {
			goDetails() {
				uni.navigateTo({
					url: '/pagesA/details/memberJobDetails?id=' + this.item.job_id,
				});
			},
		},
	};
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		padding: 32rpx;
		margin-bottom: 32rpx;
		background-color: #ffffff;
		border-radius: 24rpx;

		.item-up {
			display: flex;
			justify-content: space-between;

			.name {
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;
			}

			.money {
				font-weight: 600;
				font-size: 32rpx;
				color: #f98a14;
			}
		}

		.desc {
			display: flex;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			margin-top: 16rpx;

			.desc-item {
				border-right: 1px solid #999999;
				padding: 0 12rpx;

				&:first-child {
					padding-left: 0;
				}

				&:last-child {
					border-right: none;
				}
			}
		}

		.sub-name {
			font-weight: 400;
			font-size: 14px;
			color: #333333;
			margin-top: 16rpx;
		}

		.tags {
			display: flex;
			margin-top: 16rpx;
			margin-bottom: 16rpx;

			.tag {
				display: flex;
				align-items: center;
				background: #f6f6f6;
				border-radius: 8rpx;
				height: 46rpx;
				padding: 0 12rpx;
				font-weight: 400;
				font-size: 22rpx;
				color: #666666;
				margin-right: 16rpx;
			}
		}

		.item-down {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 28rpx 0 0 0;
			border-top: 1px solid #f5f5f5;

			.addr {
				display: flex;
				align-items: center;
				font-weight: 400;
				font-size: 12px;
				color: #333333;

				.addrText {
					width: 430rpx;
				}

				image {
					width: 32rpx;
					height: 32rpx;
					margin-right: 8rpx;
				}
			}

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #4f8cf0;
				border-radius: 8rpx;
				font-weight: 600;
				padding: 0 24rpx;
				height: 56rpx;
				font-size: 24rpx;
				color: #ffffff;
			}
		}
	}
</style>