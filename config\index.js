const AUTH_BASE = 'common-v2/auth'
// export const login = `${AUTH_BASE}/cellphone-login`
import {
	request
} from "./request"

export const getSchoolFocusList = (data) => {
	return request({
		url: `common-v2/college/attention-list`,
		data,
		method: 'get',
		 common: 'common'
	})
}

export const getJobList = (data) => {
	return request({
		url: `common-v2/job/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getCarouselList = (data) => {
	return request({
		url: `common-v2/system/carousel`,
		data,
		method: 'post',
		common: 'common'
	})
}
//新版轮播图接口
export const getCarouselList1 = (data) => {
	return request({
		url: `common-v2/system/carousel-banner`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getJobActiveList = (data) => {
	return request({
		url: `common-v2/job/active/index`,
		data,
		method: 'post',
		common: 'common'
	})
}


export const getCompanyList = (data) => {
	return request({
		url: `common-v2/company/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getHeadunterUserList = (data) => {
	return request({
		url: `common-v2/headhunter/user/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getInterviewHelloWordList = (data) => {
	return request({
		url: `common-v2/interview/helloWordList`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const interviewHelloWordAdd = (data) => {
	return request({
		url: `common-v2/interview/addHelloWord`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const interviewHelloWordUpdate = (data) => {
	return request({
		url: `common-v2/interview/updateHelloWord`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getArticleList = (data) => {
	return request({
		url: `common-v2/articles/getArticleList`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const commentLikeStore = (data) => {
	return request({
		url: `common-v2/commentlike/store`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const commentLikeDestroy = (data) => {
	return request({
		url: `common-v2/commentlike/destroy`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getArticleDetail = (data) => {
	return request({
		url: `common-v2/articles/articleDetail`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getCommentIndexList = (data) => {
	return request({
		url: `common-v2/comment/index`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getStrength = (data) => {
	return request({
		url: `common-v2/member/member-resume/getStrength`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const addStrength = (data) => {
	return request({
		url: `common-v2/member/member-resume/addStrength`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const updateStrength = (data) => {
	return request({
		url: `common-v2/member/member-resume/updateStrength`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const deleteStrength = (data) => {
	return request({
		url: `common-v2/member/member-resume/deleteStrength`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const showInfo = (data) => {
	return request({
		url: `common-v2/member/member-resume/showInfo`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const submitMember = (data) => {
	return request({
		url: `common-v2/member/member-resume/edit`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getJobClassList = (data) => {
	return request({
		url: `common-v2/job/class`,
		data,
		method: 'post',
		common: 'common'
	})
}

export const getJobPublishList = (data) => {
	return request({
		url: `common-v2/job/publish-job-list`,
		data,
		method: 'post',
		common: 'common'
	})
}
