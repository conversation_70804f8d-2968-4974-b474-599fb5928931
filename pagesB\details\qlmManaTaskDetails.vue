<template>
    <view class="home-index">
        <view class="task-info">
            <view class="task">
                <view class="name">
                    {{detailsList.task.title}}
                </view>
                <view class="per">
                    {{detailsList.task.unit_total}}/每位
                </view>
            </view>
            <view class="desc">
                <view class="time">
                    {{detailsList.task.start_at}}-{{detailsList.task.end_at}}
                </view>
                <view :class="['type',detailsList.task.type==1?'blue':detailsList.task.type==2?'yellow':'green']">
                    {{detailsList.task.type_name}}
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="info">
                <view class="username">
                    <image :src="detailsList.send_member_info.image.thumbnail_path_url" mode=""></image>
                    <view class="name">
                        {{detailsList.send_member_certification.name}}
                    </view>
                </view>
                <view class="phone">
                    {{detailsList.send_member_certification.mobile_no}}
                </view>
            </view>
        </view>

        <view class="item-wrap">
            <view class="item">
                <view class="name gl">
                    关联职位
                </view>

                <view class="number ms" v-for="(taskJob,index) in taskJobs" :key="index" :item="taskJob"
                    @click="goDetails(taskJob.id)">
                    {{ taskJob.title }}
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="item">
                <view class="name">
                    任务所需人数
                </view>

                <view class="number">
                    {{detailsList.task.count}}
                </view>
            </view>
        </view>


        <view class="wrap">
            <view class="title">
                任务简介
            </view>
            <view class="desc">
                {{detailsList.task.intro}}
            </view>

        </view>

        <!-- <view class="footer">
            <view :class="['favor',details.isFavor==1?'ysc':'']">
                <u-icon name="star-fill" :color="details.isFavor==1?'#F9AD14':'#999999'"></u-icon>
                收藏
            </view>
            <view class="btns">
                <view class="btn zx" @click="goDetailsPeople">
                    任务进度
                </view>
                <view class="btn agree" @click="getErCode">
                    任务二维码
                </view>
            </view>
        </view> -->

        <view class="bottomBox">
            <view class="btn" @click="goDetailsPeople">任务进度</view>
            <view class="btn1" @click="getErCode">任务二维码</view>
        </view>

        <u-overlay :show="show" @click="show=false">
            <view class="erCodeCla">
                <image :src="erImg" mode="" class="erCodeImg"></image>
                <view class="upDateBtn" @click.stop="downLoad()">点击下载二维码</view>
            </view>
        </u-overlay>

    </view>
</template>

<script>
    import {
        getTaskIndexDetail,
    } from '../../config/member_api.js'
    import {
        getErCode
    } from '../../config/common_api.js'
    import {
        getTaskJobs
    } from "../../config/common_api.js";

    export default {
        data() {
            return {
                detailsList: '',
                taskJobs: [],
                show: false,
                erImg: ''
            }
        },
        onLoad(option) {
            this.getTaskIndexDetail(option.id)
        },
        methods: {
            downLoad() {
                uni.downloadFile({
                    url: this.erImg, //仅为示例，并非真实的资源
                    success: (res) => {
                        if (res.statusCode === 200) {
                            // 需要将图片保存到相册
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath, // 图片文件路径，可以是临时文件路径也可以是永久文件路径，不支持网络图片路径
                                success(res) {
                                    uni.showToast({
                                        title: '保存成功',
                                        icon: 'none'
                                    })
                                    // 如果保存成功需要打开查看，请使用以下方法，同时也支持打开文件
                                    // uni.openDocument({
                                    //     filePath: res.savedFilePath,
                                    //     success(res) {},
                                    //     fail(err) {}
                                    // })
                                },
                                fail(err) {
                                    uni.showToast({
                                        title: '图片保存失败',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    }
                });

            },
            // 分享功能
            onShareAppMessage(res) {
                const pages = getCurrentPages(); //获取当前页面的参数
                // pages[0].$page.fullPath//当前页面路径及页面参数
                if (res.from === 'button') { // 来自页面内分享按钮
                    console.log(res.target)
                }
                return {
                    title: this.details.title,
                    path: pages[pages.length - 1].$page.fullPath,
                }
            },
            goDetailsPeople() {
                var _this = this;
                uni.navigateTo({
                    url: '/pagesA/details/qlmManaTaskDetailsPeople?task_user_id=' + _this.detailsList.id
                })
            },
            async getErCode() {
                // 获取屏幕信息
                const {
                    screenWidth
                } = uni.getSystemInfoSync();
                const id = this.taskJobs[0].id
                const model_user_id = uni.getStorageSync('userInfo').login_user.id
                const model_id = this.detailsList.task_id;
                const model_type = 'task'
                // 转换公式
                const px = Math.round(140 / 750 * screenWidth);
                let params = {
                    path: `pagesA/details/memberJobDetails`,
                    size: px,
                    scene: `${model_type},${model_id},${model_user_id},${id}`
                }

                const res = await getErCode(params)
                if (res.status_code == '200') {
                    this.erImg = res.data.code
                    this.show = true
                } else {
                    uni.showToast({
                        title: '获取失败',
                        icon: 'none'
                    })
                }

            },
            // 获取领取任务记录
            async getTaskIndexDetail(id) {
                // this.show = true
                let params = {
                    task_user_id: id
                }
                const res = await getTaskIndexDetail(params)
                this.detailsList = res.data;
                this.getJobList();
            },
            getJobList() {
                var _this = this;
                getTaskJobs({
                    task_id: _this.detailsList.task.id,
                }).then(response => {
                    _this.taskJobs = response.data || [];
                });
            },
            goDetails(id) {
                //    if(uni.getStorageSync('roleType')!='member') return
                // uni.navigateTo({
                // 	url:"/pagesA/details/memberJobDetails?id="+id
                // })

                const pages = getCurrentPages(); //获取当前页面的参数
                const model_user_id = uni.getStorageSync('userInfo').login_user.id;
                // if (uni.getStorageSync('roleType') != 'member') return
                uni.navigateTo({
                    url: `/pagesA/details/memberJobDetails?id=${id}&model_user_id=${model_user_id}&model_id=${this.detailsList.task.id}&model_type=task`
                })
            }
        }
    }
</script>
<style>
    page {
        background-color: #f5f5f7;
    }
</style>
<style lang="less" scoped>
    .home-index {
        padding: 32rpx 32rpx 170rpx 32rpx;
    }

    .task-info {
        display: flex;
        flex-direction: column;
        background-color: #FFFFFF;
        padding: 32rpx;
        border-radius: 24rpx;

        .task {
            display: flex;
            justify-content: space-between;

            .name {
                display: flex;
                flex: 1;
                font-weight: 500;
                font-size: 32rpx;
                color: #333333;
            }

            .per {
                font-weight: 600;
                font-size: 32rpx;
                color: #F98A14;
                margin-left: 16rpx
            }
        }

        .desc {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 16rpx 0;

            .time {
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
            }

            .type {
                font-weight: 600;
                font-size: 24rpx;
            }

            .yellow {
                color: #F9AD14;
            }

            .blue {
                color: #1690FF;
            }

            .green {
                color: #57D51C;
            }
        }

        .info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 32rpx;

            .username {
                display: flex;
                align-items: center;
                font-weight: 500;
                font-size: 28rpx;
                color: #333333;

                image {
                    width: 48rpx;
                    height: 48rpx;
                    margin-right: 32rpx;
					border-radius: 8rpx;
                }
            }

            .phone {
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
            }
        }
    }

    .item-wrap {
        display: flex;
        flex-direction: column;
        margin: 32rpx 0;
        border-radius: 24rpx;
        background-color: #FFFFFF;

        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 104rpx;
            padding: 0 32rpx;

            .name {
                font-weight: 400;
                font-size: 28rpx;
                color: #333333;
            }

            .number {
                font-weight: 600;
                font-size: 28rpx;
                color: #4F8CF0;
            }

            .gl {
                font-weight: 500;
                font-size: 28rpx;
                color: #999999;
            }

            .ms {
                font-weight: 500;
                font-size: 28rpx;
                color: #333333;
            }
        }

    }

    .wrap {
        display: flex;
        flex-direction: column;
        background-color: #FFFFFF;
        padding: 32rpx;
        border-radius: 24rpx;

        .title {
            font-weight: 600;
            font-size: 28rpx;
            color: #333333;
            padding-bottom: 32rpx;
        }

        .desc {
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
        }
    }

    .footer {
        position: fixed;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 140rpx;
        width: 100%;
        left: 0;
        bottom: 0;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 28rpx;

        .favor {
            display: flex;
            align-items: center;
            color: #999999;
            margin-left: 32rpx;
        }

        .ysc {
            color: #F9AD14;
        }

        .btns {
            display: flex;
            margin-right: 32rpx;

            .btn {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 0 72rpx;
                height: 80rpx;
                // background:#F5F5F7;
                color: #333333;
                border-radius: 16rpx;

                &:first-child {
                    margin-right: 20rpx;
                }

            }

            .zx {
                border: 1px solid #4F8CF0;
                color: #4F8CF0;
                background: #FFFFFF
            }

            .agree {
                background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
                color: #FFFFFF;
            }
        }
    }

    .bottomBox {
        box-sizing: border-box;
        width: 100%;
        height: 196rpx;
        background: #FFFFFF;
        box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
        padding: 24rpx 32rpx 92rpx 32rpx;
        position: fixed;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .btn {
            width: 332rpx;
            height: 80rpx;
            border-radius: 16rpx 16rpx 16rpx 16rpx;
            border: 2rpx solid #4F8CF0;
            text-align: center;
            line-height: 80rpx;
            font-weight: 600;
            font-size: 28rpx;
            color: #4F8CF0;
        }

        .btn1 {
            width: 332rpx;
            height: 80rpx;
            background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
            border-radius: 16rpx 16rpx 16rpx 16rpx;
            text-align: center;
            line-height: 80rpx;
            font-weight: 600;
            font-size: 28rpx;
            color: #FFFFFF;
        }
    }

    .erCodeCla {
        width: 612rpx;
        height: 758rpx;
        // background: red;
        background-image: url('https://api-test.zhaopinbei.com/images/erCodeBg.png');
        background-size: cover;
        /* 背景图片覆盖整个元素 */
        background-position: center;
        /* 背景图片居中 */
        background-repeat: no-repeat;
        /* 不重复背景图片 */
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
    }

    .erCodeImg {
        width: 280rpx;
        height: 280rpx;
        position: absolute;
        top: 218rpx;
        left: 166rpx;
    }

    .upDateBtn {
        position: absolute;
        bottom: 64rpx;
        left: 96rpx;
        width: 420rpx;
        height: 64rpx;
        background: #4F8CF0;
        border-radius: 82rpx 82rpx 82rpx 82rpx;
        text-align: center;
        line-height: 64rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #FFFFFF;
    }
</style>
