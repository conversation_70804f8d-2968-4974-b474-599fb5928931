<template>
  <view class="">
    <u-sticky bgColor="#F5F5F5">
      <view class="header">
        <view class="search-wrap">
          <u-search
            placeholder="请输入关键字"
            bgColor="#FFFFFF"
            :showAction="false"
            v-model="keyword"
          ></u-search>
        </view>
      </view>
    </u-sticky>
    <view class="wrap">
      <view class="list">
        <view class="item" @click="goNewFriend">
          <view class="item-left">
            <view class="name"> 新的朋友 </view>
          </view>
          <u-icon name="arrow-right" size="14"></u-icon>
        </view>
        <view class="item" @click="addFriend">
          <view class="item-left">
            <view class="name"> 添加朋友 </view>
          </view>
          <u-icon name="arrow-right" size="14"></u-icon>
        </view>
      </view>

      <view class="list">
        <view class="item" @click="goInner">
          <view class="item-left">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/zSTDzb0ZPfuGT3o2sjlRm5CmPSNM3Et9a2fc90v4.png"
              mode=""
            ></image>
            <view class="name"> 企业内部 </view>
          </view>
          <u-icon name="arrow-right" size="14"></u-icon>
        </view>
        <view class="item" @click="goOuter">
          <view class="item-left">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/8quBrnnuIPHngPpRJwzboXfwiRE7cQKaCprpwhfM.png"
              mode=""
            ></image>
            <view class="name"> 企业外部 </view>
          </view>
          <u-icon name="arrow-right" size="14"></u-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      keyword: "",
    };
  },
  methods: {
    addFriend() {
      uni.navigateTo({
        url: "/pagesA/connectivity/add_friend",
      });
    },
    goNewFriend() {
      uni.navigateTo({
        url: "/pagesA/connectivity/new_friend",
      });
    },
    goInner() {
      uni.navigateTo({
        url: "/pagesA/connectivity/inner",
      });
    },
    goOuter() {
      uni.navigateTo({
        url: "/pagesA/connectivity/friend_follow_fans",
      });
    },
  },
};
</script>
<style>
page {
  background-color: #f5f5f7;
}
</style>
<style lang="less" scoped>
.header {
  padding: 32rpx;
}

.wrap {
  .list {
    display: flex;
    flex-direction: column;
    background: #ffffff;

    &:first-child {
      margin-bottom: 40rpx;
    }

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 120rpx;
      padding: 0 20rpx;
      border-bottom: 1px solid #f5f5f7;

      &:last-child {
        border-bottom: none;
      }

      .item-left {
        display: flex;
        align-items: center;
        image {
          width: 72rpx;
          height: 72rpx;
          margin-right: 16rpx;
        }

        .name {
          font-weight: 400;
          font-size: 28rpx;
          color: #333333;
        }
      }
    }
  }
}
</style>
