<template>
    <view id="app">
        <u-sticky bgColor="#F5F5F7">
            <view class="tabsBox">
                <u-tabs :list="list1" :activeStyle="{
					color: '#4F8CF0',
					fontWeight: 'bold',
					transform: 'scale(1.05)'}" @click="tabClick()"></u-tabs>
            </view>

            <view class="search-wrap">
                <u-search placeholder="请输入姓名或手机号" bgColor="#FFFFFF" :showAction="true" actionText="搜索"
                          v-model="page.form.keyword"
                          @custom="custom" @clear="clear"></u-search>
            </view>
        </u-sticky>

        <view class="contentBox">
            <view class="cardBox" v-for="(item,index) in page.data" :key="index" @click="itemClick(item)" v-if="tab == 0">
                <!-- 三目判断右上角颜色class 待处理yellowIcon 已同意greenIcon 已拒绝redIcon 待审核blueIcon 已解除greyIcon-->
                <view class="topIcon yellowIcon">{{ item.company_authorizes_audit_user.status_name }}</view>
                <view class="contentText">
                    <image :src="item.member_info.image.path_url" mode="" class="headImg"></image>
                    <view class="contentText_right">
                        <view class="contentText_right_name">{{ item.member_certification.name }}</view>
                        <view class="contentText_right_company">{{ item.authorize_company.name }}</view>
                    </view>
                </view>
                <view class="labelBox" v-if="item.user_headhunter && item.user_headhunter.label">
                    <view class="labelChild" v-for="(labelItem,index) in item.user_headhunter.label" :key="index"
                          :item="labelItem">
                        {{ labelItem }}
                    </view>
                </view>
            </view>

            <view class="cardBox" v-for="(item,index) in page.data" :key="index" @click="itemClick(item)" v-if="tab == 1">
                <!-- 三目判断右上角颜色class 待处理yellowIcon 已同意greenIcon 已拒绝redIcon 待审核blueIcon 已解除greyIcon-->
                <view class="topIcon yellowIcon">{{ item.member_authorized_change.status_name }}</view>
                <view class="contentText">
                    <image :src="item.member.member_info.image.path_url" mode="" class="headImg"></image>
                    <view class="contentText_right">
                        <view class="contentText_right_name">{{ item.member.member_certification.name }}</view>
                        <view class="contentText_right_company">{{ item.authorized_company.company.name }}</view>
                    </view>
                </view>
                <view class="labelBox" v-if="item.old_headhunter.user_headhunter && item.old_headhunter.user_headhunter.label">
                    <view class="labelChild" v-for="(labelItem,index) in item.old_headhunter.user_headhunter.label" :key="index"
                          :item="labelItem">
                        {{ labelItem }}
                    </view>
                </view>
            </view>
        </view>
        <Pages :status="page.status"></Pages>
    </view>
</template>

<script>
    import Pages from "../../components/pages.vue";
    import {getAuditList, memberChangeAuditList} from '../../config/headhunterList_api.js'

    export default {
        components: {
            Pages
        },
        data() {
            return {
                list1: [{
                    name: '伯乐变更',
                }, {
                    name: '千里马变更',
                },
                // {
                //     name: '人员审核'
                // },
                ],
                tab: 0,
                page: {
                    form: {
                        page: 1,
                        limit: 10,
                        keyword: ''
                    },
                    data: [],
                    status: 'nomore',
                    more: false,
                },

            }
        },
        onLoad() {
            this.getList();
        },
        methods: {
            itemClick(item) {
                var _this = this;
                switch(_this.tab){
                    case 0:
                        uni.navigateTo({
                            url: '/pagesA/details/auditChangeDetail?id=' + item.company_authorizes_audit_user.id,
                        })
                        break;
                    case 1:
                        uni.navigateTo({
                            url: '/pagesA/details/auditMemberChangeDetail?id=' + item.member_authorized_change.id,
                        })
                        break;
                }

            },
            custom() {
                console.log("搜索")
            },
            clear() {
                console.log("清空搜索框")
                this.custom()
            },
            // tab切换
            tabClick(tab) {
                var _this = this;
                _this.tab = tab.index;
                _this.initPage();
            },
            initPage() {
                var _this = this;
                _this.page.data = [];
                _this.page.form['page'] = 1;
                this.page.status = 'loadmore';
                _this.getList();
            },
            setPageData(response) {
                var _this = this;
                _this.page.data = _this.page.data.concat(response.data.data);
                _this.page.more = response.data.more;
                _this.status = _this.page.more ? "loadmore" : "nomore"
            },
            getList() {
                var _this = this;
                switch (_this.tab) {
                    case 0:
                        _this.getAuditList();
                        break;
                    case 1:
                        _this.memberChangeList();
                        break;
                }
            },
            memberChangeList() {
                var _this = this;

                memberChangeAuditList(_this.page.form).then(response => {
                    _this.setPageData(response);
                });

            },
            async getAuditList() {
                var _this = this;

                getAuditList(_this.page.form).then(response => {
                    _this.setPageData(response);
                });
            }
        }
    }
</script>
<style>
    page {
        background-color: #F5F5F7;
    }
</style>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
    }

    .tabsBox {
        background: #fff;
        padding: 24rpx 32rpx;
        margin-bottom: 32rpx;
    }

    .search-wrap {
        padding: 0 32rpx;
    }

    .heightTop {
        height: 128rpx;
    }

    .contentBox {
        width: 100%;
        padding: 32rpx;
    }

    .cardBox {
        width: 100%;
        // height: 214rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        margin-bottom: 24rpx;
        padding: 32rpx;
        position: relative;
    }

    .topIcon {
        width: 132rpx;
        height: 62rpx;
        text-align: center;
        line-height: 62rpx;
        color: #FFFFFF;
        font-size: 28rpx;
        font-weight: 400;
        border-radius: 0 24rpx 0 24rpx;
        position: absolute;
        right: 0;
        top: 0;
    }

    .yellowIcon {
        background: #F9AD14;
    }

    .greenIcon {
        background: #57D51C;
    }

    .redIcon {
        background: #FE4D4F;
    }

    .blueIcon {
        background: #4F8CF0;
    }

    .greyIcon {
        background: #CCCCCC;
    }

    .contentText {
        display: flex;
        align-items: center;
    }

    .contentText_right {
        width: 418rpx;
    }

    .contentText_right_name {
        width: 100%;
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 16rpx;
    }

    .contentText_right_company {
        width: 100%;
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .labelBox {
        display: flex;
        align-items: center;
        margin-top: 16rpx;
    }

    .labelChild {
        min-width: 112rpx;
        max-width: 172rpx;
        // height: 46rpx;
        background: #F6F6F6;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 46rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 400;
        font-size: 22rpx;
        color: #666666;
        padding: 0 12rpx;
        margin-right: 16rpx;
    }

    .headImg {
        width: 88rpx;
        height: 88rpx;
        border-radius: 88rpx 88rpx 88rpx 88rpx;
        margin-right: 16rpx;
    }
</style>