<template>
	<!-- 期望岗位提交 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="form-container">
					<u-form labelWidth="140">
						<u-form-item label="期望行业" borderBottom @click="onIndustryPage">
							<u-input v-model="params.content.industry" inputAlign="right" placeholder="请选择期望行业" readonly
								border="none"></u-input>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
						<u-form-item label="期望职位" borderBottom>
							<u-input v-model="params.content.posts" inputAlign="right" placeholder="请输入期望职位"
								border="none"></u-input>
						</u-form-item>
						<u-form-item label="薪资要求" borderBottom @click="salaryShowPicker = true">
							<u-input v-model="salaryText" inputAlign="right" placeholder="请选择薪资要求" readonly
								border="none"></u-input>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
						<u-form-item label="工作城市" borderBottom @click="onWorkCityPage">
							<u-input v-model="params.content.city" inputAlign="right" placeholder="请选择工作城市" readonly
								border="none"></u-input>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
						<u-form-item label="其他感兴趣的城市" borderBottom>
							<u-input v-model="params.content.interestedCity" inputAlign="right"
								placeholder="请输入其他感兴趣的城市" border="none"></u-input>
						</u-form-item>
						<u-form-item label="期望职位详情">
							<u-input v-model="params.content.detail" inputAlign="right" placeholder="请输入期望职位详情"
								border="none"></u-input>
						</u-form-item>
					</u-form>
				</view>
			</view>


			<u-picker ref="salaryPicker" title="选择薪资范围" :show="salaryShowPicker" closeOnClickOverlay
				:columns="salaryColumns" @confirm="onSalaryPickerConfirm" @close="onSalaryPickerClose"
				@cancel="onSalaryPickerClose" @change="onSalaryPickerChange" immediateChange></u-picker>

			<u-toast ref="toast"></u-toast>
		</scroll-view>

		<view class="btn-container">
			<view class="btn" @click="onSubmit">确定</view>
		</view>
	</view>
</template>

<script>
	import {
		addStrength,
		updateStrength
	} from '@/config';

	export default {
		data() {
			return {
				salaryText: null,
				salaryShowPicker: false,
				salaryColumns: [
					['面议', '1k', '2k', '3k', '4k', '5k', '6k', '7k', '8k', '9k', '10k', '11k', '12k', '13k', '14k',
						'15k', '16k', '17k', '18k', '19k'
					],
					['面议'],
				],

				params: {
					strength_id: null,
					content: {
						industry: null,
						posts: null,
						salary: {
							salaryBegin: null,
							salaryEnd: null
						},
						city: null,
						interestedCity: null,
						detail: null,
					}
				}
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onLoad(options) {
			const res = JSON.parse(options.json);
			if (!res.id) return;
			this.params.strength_id = res.id;
			this.params.content = res.content;
			this.salaryText = `${res.content.salary.salaryBegin} - ${res.content.salary.salaryEnd}`
		},
		methods: {
			onSalaryPickerChange(event) {
				if (event.columnIndex === 0) {
					if (event.index === 0) {
						this.$refs.salaryPicker.setColumnValues(1, ['面议']);
						return;
					}
					if (event.index === 19) {
						this.$refs.salaryPicker.setColumnValues(1, ['20k']);
						return;
					}
					const columns = this.salaryColumns[0].filter((_, i) => i > event.index && i < event.index + 6);
					this.$refs.salaryPicker.setColumnValues(1, columns);
				}
			},
			onSalaryPickerConfirm(event) {
				this.params.content.salary.salaryBegin = event.value[0];
				this.params.content.salary.salaryEnd = event.value[1];
				const index = event.indexs.reduce((prev, el) => (prev + el), 0);
				this.salaryText = index ? event.value.join(' - ') : event.value[0];
				this.onSalaryPickerClose();
			},
			onSalaryPickerClose() {
				this.salaryShowPicker = false;
			},
			onIndustryPage() {
				uni.$u.route({
					url: '/pagesB/Jobsetting/newPages/expectedIndustry'
				})
			},
			onWorkCityPage() {
				uni.$u.route({
					url: '/pagesB/Jobsetting/newPages/workCity'
				})
			},
			async onSubmit() {
				const params = {
					type: '3',
					strength_id: this.params.strength_id,
					content: JSON.stringify(this.params.content),
					...this.userTypeParams,
				};
				const event = params.strength_id ? updateStrength : addStrength;
				const res = await event(params);
				if (res.status_code !== '200') return;
				this.$refs.toast.show({
					message: res.message,
					duration: 1000,
					complete: () => {
						uni.$u.route({
							type: 'back'
						});
					}
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;

		.btn-container {
			background-color: #FFFFFF;
			padding-block-start: 24rpx;
			padding-inline: 64rpx;
			padding-block-end: calc(24rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			border-start-start-radius: 16rpx;
			border-start-end-radius: 16rpx;
			box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);

			.btn {
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				border-radius: 16rpx;
				text-align: center;
				color: #FFFFFF;
				padding-block: 20rpx;
				font-size: 28rpx;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding: 32rpx;

				.form-container {
					background-color: #FFFFFF;
					padding-inline: 24rpx;
					padding-block: 16rpx;
					border-radius: 24rpx;
				}
			}
		}
	}
</style>