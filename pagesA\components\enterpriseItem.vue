<template>
	<view class="item" @click="goCompDetails">
		<view class="item-up">
			<image class="logo" :src="item.company_info.logo.path_url" mode=""></image>
			<view class="info">
				<view class="user">
					<view class="userInfo">
						<!-- {{item.name && item.name.length<10?item.name:item.name.substring(0,10)+'......'}} -->
						<view class="name">
							{{item.name || item.company.name}}
						</view>
					</view>
				</view>
				<view class="desc">
					<view class="desc-item" v-if="item.short_name">
						{{item.short_name}}
					</view>
					<view class="desc-item" v-if="item.company_info.financing_type_name">
						{{item.company_info.financing_type_name}}
					</view>
					<view class="desc-item" v-if="item.company_info.size_type">
						{{item.company_info.size_type}}
					</view>
				</view>

				<view class="tags" v-if="item.tags && item.tags.length>0">
					<view class="tag" v-for="(sub,idx) in item.tags" :key="idx">
						{{sub.title}}
					</view>
				</view>
				<view class="tags" v-else>
					<view class="tag">
						暂无标签
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "enterpriseItem",
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {

			};
		},
		methods: {
			goCompDetails() {
				uni.navigateTo({
					url: "/pagesA/details/companyDetail?id=" + this.item.id
				})
				// 原公司详情
				// uni.navigateTo({
				// 	url: "/pagesA/details/compDetails?id=" + this.item.id
				// })
			}
		}
	}
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		padding: 32rpx;
		margin-bottom: 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;

		.item-up {
			display: flex;

			.logo {
				width: 144rpx;
				height: 144rpx;
				border-radius: 16rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				flex: 1;
				padding-left: 24rpx;

				.user {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.userInfo {
						display: flex;
						align-items: center;

						.name {
							font-weight: 600;
							font-size: 32rpx;
							color: #333333;
						}
					}
				}

				.desc {
					display: flex;
					flex-wrap: wrap;
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;

					.desc-item {
						border-right: 1px solid #999999;
						padding: 0 12rpx;

						&:first-child {
							padding-left: 0;
						}

						&:last-child {
							border-right: none;
						}
					}
				}

				.tags {
					display: flex;
					flex-wrap: wrap;

					.tag {
						display: flex;
						align-items: center;
						background: #F6F6F6;
						border-radius: 8rpx;
						height: 46rpx;
						padding: 0 12rpx;
						font-weight: 400;
						font-size: 22rpx;
						color: #666666;
						margin-right: 10rpx;

						&:last-child {
							margin-right: 0;
						}
					}
				}
			}
		}
	}
</style>