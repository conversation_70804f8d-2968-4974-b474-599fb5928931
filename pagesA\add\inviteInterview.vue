<template>
	<view class="home-index">
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						面试联系人<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入联系人" border="none" fontSize="32rpx"
							v-model="form.contact_name"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						面试电话<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入联系电话" border="none" fontSize="32rpx"
							v-model="form.contact_cellphone"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						面试时间<text class="star">*</text>
					</view>
					<view class="in se">
						<!-- <picker mode="date" :value="form.interviewed_at" @change="bindStartDateChange">
							<view class="d-picker">{{form.interviewed_at?form.interviewed_at:"请选择开始时间"}}
							</view>
						</picker> -->
                        <uni-datetime-picker type="datetime" :show-clear="false" :value="form.interviewed_at"
                            @change="bindStartDateChange" :clear-icon="false">
                            <view class="d-picker">{{form.interviewed_at?form.interviewed_at:"请选择开始时间"}}</view>
                        </uni-datetime-picker>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						面试城市<text class="star">*</text>
					</view>
					<view class="in se">
						<uni-data-picker :map="map" placeholder="请选择工作地址" popup-title="请选择所在地区" :localdata="cityList"
							v-model="form.district_id" @change="onchange">
						</uni-data-picker>
					</view>
				</view>

				<view class="inp-item" @click="choosePosition">
					<view class="title">
						面试地址<text class="star">*</text>
					</view>
					<view class="in se">
						{{form.map_address?form.map_address:'请选择地址'}}
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						楼层/单元室/门牌号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入楼层/单元室/门牌号" border="none" fontSize="32rpx"
							v-model="form.address"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						备注
					</view>
					<view class="in">
						<u--input placeholder="请输入备注" border="none" fontSize="32rpx" v-model="form.remark"></u--input>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="next sure" @click="inviteInterview">
				邀请面试
			</view>
		</view>
	</view>
</template>

<script>
	import {
		inviteInterview
	} from "../../config/api.js"
	export default {
		data() {
			return {
				report_id: '',
				job_id: '',
				member_id: "",
				map: {
					text: 'label',
					value: 'value'
				},
				form: {
					contact_name: '',
					contact_cellphone: '',
					interviewed_at: '',
					remark: '',

					province_id: "",
					city_id: "",
					district_id: "",
					address: "",
					map_address: "",
					lat: "",
					lng: "",

				},
			}
		},
		computed: {
			roleType() {
				return this.$store.state.roleType
			},
			sysData() {
				return this.$store.state.sysData || uni.getStorageSync('sysData')
			},
			cityList() {
				return this.$store.state.cityList || uni.getStorageSync('cityList')
			}
		},
		onLoad(options) {
			this.report_id = options.report_id
			this.job_id = options.job_id
			this.member_id = options.member_id
		},
		methods: {
			onchange(e) {
				let data = e.detail.value
				this.form.province_id = data[0]['value']
				this.form.city_id = data[1]['value']
				this.form.district_id = data[2]['value']
			},

			bindStartDateChange(e) {
				this.form.interviewed_at = e;
			},

			//选择地图地址
			choosePosition() {
				console.log(1)
				let self = this;
				uni.chooseLocation({
					success: function(res) {
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
						self.form.lat = res.latitude
						self.form.lng = res.longitude
						self.form.map_address = res.address
					}
				});
			},
			async inviteInterview() {
                if(!uni.$u.test.mobile(this.form.contact_cellphone)) {
                    return uni.$u.toast('手机号格式不正确')
                }
                
				let params = {
					report_id: this.report_id,
					job_id: this.job_id,
					member_id: this.member_id,
					contact_name: this.form.contact_name,
					contact_cellphone: this.form.contact_cellphone,
					interviewed_at: this.form.interviewed_at,
					remark: this.form.remark,
					address: {
						province_id: this.form.province_id,
						city_id: this.form.city_id,
						district_id: this.form.district_id,
						map_address: this.form.map_address,
						address: this.form.address,
						lat: this.form.lat,
						lng: this.form.lng
					}
				}
				console.log(params)
				const {
					status_code,
					data,
					message
				} = await inviteInterview(params)
				if (status_code == 200) {
					uni.$u.toast('成功')
					uni.navigateBack()
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding: 32rpx;
	}

	.wrap {
		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;

			.default {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 32rpx;

				.name {
					font-weight: 500;
					font-size: 28rpx;
					color: #333333;
				}
			}

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					::v-deep uni-data-picker {
						width: 100%;
					}

					::v-deep .arrow-area {
						transform: rotate(-135deg);
					}

					::v-deep .input-arrow {
						width: 20rpx;
						height: 20rpx;
						border-left: 1px solid #606266;
						border-bottom: 1px solid #606266;
					}

					::v-deep .input-value-border {
						border: none;
					}

					::v-deep .input-value {
						padding: 0;
					}

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}

					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 16rpx 16rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			font-weight: 600;
			font-size: 28rpx;
			border-radius: 16rpx;
		}

		.sure {
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	}
</style>