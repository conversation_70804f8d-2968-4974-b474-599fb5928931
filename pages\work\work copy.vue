<template>
	<view class="home-index">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="search-wrap">
					<u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="true" @custom="handleEnterPress"
						@clear="clear" v-model="page.form.title"></u-search>
				</view>
				<view class="filters">
					<view class="filter">
						<picker mode="date" :value="page.form.created_at.start" @change="bindStartDateChange"
							@cancel="cancelStartDateChange">
							<view class="d-picker">{{page.form.created_at.start?page.form.created_at.start:'开始时间'}}
							</view>
						</picker>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>
					<view class="filter">
						<picker mode="date" :value="page.form.created_at.end" @change="bindEndDateChange"
							@cancel="cancelEndDateChange">
							<view class="d-picker">{{page.form.created_at.end?page.form.created_at.end:'结束时间'}}</view>
						</picker>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>
					<view class="filter">
						<picker @change="changeTaskType" :value="taskTypeIndex" :range="taskTypeList" range-key="name">
							<view class="d-picker">{{taskTypeList[taskTypeIndex]['name']}}</view>
						</picker>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>
					<view class="filter">
						<picker @change="changeUnitTotal" :value="taskCommissionIndex" :range="taskCommissionList"
							range-key="name">
							<view class="d-picker">{{taskCommissionList[taskCommissionIndex]['name']}}</view>
						</picker>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>
				</view>
			</view>
		</u-sticky>
		<view class="list">
			<task-item v-for="(item,index) in page.data" :item="item"></task-item>
		</view>
		<Pages :status="page.form.status"></Pages>
		<tabbar></tabbar>
	</view>
</template>

<script>
	import TopBarStatus from "../../components/topBarStatus.vue"
	import TaskItem from "../../components/taskItem.vue"
	import Tabbar from "../../components/tabbar.vue"
	import {
		taskSquare
	} from '../../config/common_api.js'
	import Pages from "../../components/pages.vue";

	export default {
		components: {
			TopBarStatus,
			TaskItem,
			Tabbar,
			Pages
		},
		computed: {},
		data() {
			return {
				page: {
					form: {
						created_at: {
							start: '',
							end: '',
						},
						page: 1,
						limit: 10,
						type: "",
						title: '',
						unit_total: 0,
					},
					status: 'loadmore',
					more: false,
					data: [],
				},
				isScroll: false,
				time: Number(new Date()),
				taskTypeIndex: 0,
				taskTypeList: [{
						value: 'job',
						name: '职位任务'
					},
					{
						value: 'custom',
						name: '自定义任务'
					},
				],
				taskCommissionIndex: 0,
				taskCommissionList: [{
						name: '任务佣金',
						value: null,
					},
					{
						name: '0',
						value: 0,
					},
					{
						name: '100',
						value: 100,
					},
					{
						name: '200',
						value: 200,
					},
					{
						name: '500',
						value: 500,
					},
					{
						name: '1000',
						value: 1000,
					},
					{
						name: '2000',
						value: 2000,
					},
					{
						name: '3000',
						value: 3000,
					},
					{
						name: '4000',
						value: 4000,
					}
				]
			}
		},
		onLoad() {
			this.taskSquare()
		},
		//触底加载更多
		onReachBottom() {
			if (this.page.form) {
				this.page.form.status = 'loading'
				this.page.form.page++
				this.taskSquare()
			} else {
				this.page.form.status = 'nomore'
			}
		},
		onPageScroll(e) {
			this.isScroll = e.scrollTop > 0
		},
		methods: {
			initPage() {
				this.page.form.page = 1;
				this.page.data = [];
				this.page.status = 'loadmore';
			},
			async taskSquare() {
				this.page.form.type = this.taskTypeList[this.taskTypeIndex]['value'] || null;
				this.page.form.unit_total = this.taskCommissionList[this.taskCommissionIndex]['value'];
				const res = await taskSquare(this.page.form)
				this.page.data = this.page.data.concat(res.data.data);
				// 返回false代表没有下一页
				this.page.form.more = res.data.more;
				this.page.form.status = this.page.form.more ? "loading" : "nomore"
			},
			changeTaskType(e) {
				this.taskTypeIndex = e.detail.value;
				this.initPage();
				this.taskSquare();
			},
			changeUnitTotal(e) {
				this.taskCommissionIndex = e.detail.value;
				this.initPage();
				this.taskSquare();
			},
			bindStartDateChange(e) {
				this.page.form.created_at.start = e.detail.value
				this.initPage();
				this.taskSquare();
			},
			cancelStartDateChange(e) {
				if (this.page.form.created_at.start != '') {
					this.page.form.created_at.start = ''
					this.initPage();
					this.taskSquare();
				}
			},
			bindEndDateChange(e) {
				if (this.page.form.created_at.end != '') {
					this.page.form.created_at.end = e.detail.value
					this.initPage();
					this.taskSquare();
				}
			},
			cancelEndDateChange() {
				this.page.form.created_at.end = ''
				this.initPage();
				this.taskSquare();
			},
			handleEnterPress() {
				this.initPage();
				this.taskSquare();
			},
			clear() {
				this.page.form.title = '';
				this.initPage();
				this.taskSquare();
			},

		}
	}
</script>
<style lang="scss" src="../../static/css/pages/work/work.scss"></style>