<template>
	<view class="home-index">
		<u-sticky>
			<view class="step">
				<u-steps :current="step" dot='true'>
					<u-steps-item title="实名认证"></u-steps-item>
					<u-steps-item title="完善信息"></u-steps-item>
				</u-steps>
			</view>
		</u-sticky>

		<block v-if="step==0">
			<view class="wrap">
				<view class="cert">
					<view class="title">
						身份证正面
					</view>
					<view class="desc">
						上传身份证自动识别信息
					</view>
					<view class="zm" @click="uploadIdcard">
						<image :src="idCard?idCard:'https://api-test.zhaopinbei.com/storage/uploads/images/YxJikUnFwZdcETzKImgUvlPdaETavNyW26nWmfdc.png'" mode="widthFix"></image>
					</view>
				</view>
			</view>


			<view class="wrap">
				<view class="inp">
					<view class="inp-item">
						<view class="title">
							姓名 <text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入姓名" placeholderClass="placeholderClass" clearable border="none"
								v-model="certForm.name"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							身份证号 <text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入身份证号" placeholderClass="placeholderClass" clearable border="none"
								v-model="certForm.id_no"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							手机号 <text class="star">*</text>
						</view>
						<view class="in">
							<u--input placeholder="请输入手机号" placeholderClass="placeholderClass" clearable border="none"
								v-model="certForm.mobile_no"></u--input>
						</view>
					</view>

					<view class="inp-item">
						<view class="title">
							验证码<text class="star">*</text>
						</view>
						<view class="in">
							<!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
							<!-- #ifndef APP-NVUE -->
							<u-input placeholder="请输入验证码" clearable placeholderClass="placeholderClass" border="none"
								v-model="certForm.code">
							<!-- #endif -->
								<!-- #ifdef APP-NVUE -->
								<u--input v-model="certForm.code" placeholder="请输入验证码" clearable
									placeholderClass="placeholderClass">
								<!-- #endif -->
									<template slot="suffix">
										<u-code ref="uCode" @change="codeChange" seconds="60"
											changeText="X秒重新获取"></u-code>
										<u-button @tap="getCode" :text="tips" type="success" size="mini"></u-button>
									</template>
							<!-- #ifndef APP-NVUE -->
							</u-input>
							<!-- #endif -->
							<!-- #ifdef APP-NVUE -->
							</u--input>
							<!-- #endif -->
						</view>
					</view>
				</view>
			</view>
		</block>

		<view class="footer">
			<view class="btns">
				<view class="btn jump" @click="jump">
					跳过
				</view>
				<block v-if="step==0">
					<view class="btn next" @click="saveBaseInfo">
						下一步
					</view>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		cert,
		uploadImg,
		distImg,
		updateLoginInfo,
	} from "../../config/api.js"
	export default {
		data() {
			return {
				tips:'',
				certForm: {
					id_no: '',
					mobile_no: '',
					name: '',
					code: '',
					idCard_img: []
				},
				step: 0,
			}
		},

		computed: {
			userInfo() {
				return this.$store.state.userInfo || uni.getStorageSync('userInfo')
			}
		},

		watch: {
			sysData: {
				handler(newValue, oldValue) {
					console.log("监听：：：", oldValue)
					if (!oldValue) {
						this.getSysData()
					}
				},
				immediate: true,
				deep: true
			},
			form: {
				handler(newValue, oldValue) {},
				immediate: true,
				deep: true
			}
		},

		onLoad() {
		},

		methods: {
			jump() {
				// uni.switchTab({
				// 	url: "/pages/index/index"
				// })
                uni.navigateBack()
			},
			hasEmptyValue() {
				for (let key in obj) {
					if (obj[key] === null || obj[key] === undefined || obj[key] === '' || obj[key].length == 0) {
						return true;
					}
				}
				return false;
			},
			async saveBaseInfo() {
				// let isEmpty = this.hasEmptyValue()
				// console.log("是否为空：", isEmpty)
				if (!this.certForm.id_no || !this.certForm.name || !this.certForm.mobile_no || !this.certForm.code)
					return uni.$u.toast('请填写完整信息')
				let params = {
					...this.certForm
				}
				let result = await cert(params)
				if (result.status_code == 200) {

					// this.step = 1
					let loginInfo = await updateLoginInfo()
					if (loginInfo.status_code == 200) {
						this.$store.commit('setUserInfo', loginInfo.data)
						if(uni.getStorageSync('userType')=='member') {
                            uni.navigateTo({
                            	url: '/pagesA/add/addResume'
                            })
                        } else {
                            uni.navigateBack()
                        }
						return uni.$u.toast('保存信息成功')

					}
				}
			},

			//上传身份证识别
			uploadIdcard() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						// $dialog.loading('上传中')
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								console.log("图片信息：", result.data)
								if (result.status_code == 200) {
									self.idCard = result.data.url
									self.certForm.idCard_img = [result.data.id]
									let distParams = {
										file_id: result.data.id,
										type: 'idcard'
									}
									// 识别
									const {
										status_code,
										data
									} = await distImg(distParams)

									self.certForm.name = data.data.name
									self.certForm.id_no = data.data.idcard
								}
							}
						})
					}
				});
			},



			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		// padding: 32rpx 32rpx 256rpx 32rpx;
		padding-bottom: 256rpx;
	}

	.step {
		background-color: #FFFFFF;
		border-radius: 0 0 24rpx 24rpx;
		margin-bottom: 28rpx;
		padding: 24rpx 0;
		// margin-top: 24rpx;
	}

	.cert {
		display: flex;
		flex-direction: column;
		padding: 24rpx 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;

		.title {
			font-weight: 400;
			font-size: 32rpx;
			color: #333333;
		}

		.desc {
			font-weight: 400;
			font-size: 22rpx;
			color: #999999;
			margin-top: 24rpx;
		}

		.zm {
			width: 100%;
			margin-top: 24rpx;

			image {
				width: 100%;
			}
		}
	}

	.wrap {
		margin-top: 32rpx;
		padding: 0 32rpx;

		.wrap-title {
			font-weight: 500;
			font-size: 32rpx;
			color: #333333;
			padding: 0 0 24rpx 0;

			.star {
				font-weight: 600;
				font-size: 22rpx;
				color: #FE4D4F;
				margin-left: 8rpx;
			}
		}

		.pic-list {
			display: flex;
			flex-wrap: wrap;

			.pic-item {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 200rpx;
				width: calc(33.3% - 32rpx);
				margin-bottom: 32rpx;
				margin-right: 32rpx;
				position: relative;
				border-radius: 16rpx;

				&>image {
					width: 100%;
					height: 100%;
					border-radius: 16rpx;
				}

				.zz {
					display: flex;
					justify-content: center;
					align-items: center;
					background-color: rgba(0, 0, 0, 0.5);
					width: 100%;
					height: 100%;
					position: absolute;
					left: 0;
					top: 0;
					z-index: 10;
					border-radius: 16rpx;

					.del {
						display: flex;
						align-items: center;
						border-radius: 16rpx;
						padding: 0 16rpx;
						height: 56rpx;
						background: rgba(255, 255, 255, 0.5);
						font-weight: 500;
						font-size: 28rpx;
						color: #FFFFFF;
					}
				}
			}

			.add {
				background-color: #FFFFFF;

				image {}
			}

		}

		.inp {
			background: #FFFFFF;
			border-radius: 24rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.supTitle {
					font-weight: 500;
					font-size: 32rpx;
					color: #333333;
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;


					::v-deep uni-data-picker {
						width: 100%;
					}

					::v-deep .arrow-area {
						transform: rotate(-135deg);
					}

					::v-deep .input-arrow {
						width: 20rpx;
						height: 20rpx;
						border-left: 1px solid #606266;
						border-bottom: 1px solid #606266;
					}

					::v-deep .input-value-border {
						border: none;
					}

					::v-deep .input-value {
						padding: 0;
					}

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}


					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}



				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #c0c4cc;
				}
			}
		}

		.mt {
			margin-top: 32rpx;
		}
	}

	.footer {
		width: 100%;
		height: 224rpx;
		left: 0;
		bottom: 0;
		z-index: 999;
		position: fixed;
		background-color: #F5F5F7;

		.btns {
			display: flex;
			flex-direction: column;
			justify-content: center;
			height: 100%;
			padding: 0 32rpx;
			flex: 1;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 72rpx;
				border-radius: 36rpx;
				font-weight: 600;
				font-size: 28rpx;

				&:first-child {
					margin-bottom: 24rpx;
				}
			}

			.jump {
				background: #FFFFFF;
				color: #4F8CF0;
			}

			.next {
				background: #4F8CF0;
				color: #FFFFFF;
			}
		}
	}
</style>
