<template>
    <view id="app">
        <view class="searchBox">
            <image src="/static/images/project/searchIcon.png" mode="" class="searchIcon"></image>
            <input type="text" class="searchIpt" placeholder="请输入项目名称"/>
        </view>

        <!-- <view class="projectState">
            <view>项目状态</view>
            <image src="/static/images/project/downIcon.png" mode="" class="downIcon"></image>
        </view> -->
        <!-- 项目列表 -->

        <view class="listBox" v-for="(item,index) in page.data" :key="index" :item="item" @click.stop="goDetails(item)">
            <view class="topRight">{{ item.active_status_name }}</view>
            <view class="listTil">{{ item.name }}</view>
            <view class="listType">
                <view>{{ item.type_name }}</view>
                <view class="listProgress">项目进度：<span class="listProgressNum">{{ item.push_member_count }}/{{ item.count }}</span></view>
            </view>
            <view class="listPeople">
                <view class="listPeople_top">
                    <image :src="item.user_member_info.image.path_url" mode="" class="peoHead"></image>
                    <view>{{ item.user_member_certification.name }}</view>
                </view>

                <!--<image v-if="page.more" src="/static/images/project/listMore.png" mode="" class="listMore"-->
                       <!--@click.stop="showActionSheet"></image>-->
            </view>
        </view>

        <Pages :status="page.status"></Pages>

        <view style="height: 196rpx;"></view>
        <view class="bottomBox">
            <view class="bottomBox_btn" @click="goPage">发布项目</view>
        </view>
    </view>
</template>

<script>
    import Pages from "../../components/pages.vue";
    import {projectList} from "../../config/headhunterList_api";

    export default {
        components: {
            Pages,
        },
        data() {
            return {
                page: {
                    form: {
                        limit: 10,
                        page: 1,
                    },
                    data: [],
                    more: false,
                    status: 'nomore',
                }
            }
        },
        onLoad() {
            var _this = this;
            _this.getList();
        },
        onReachBottom() {
            var _this = this;
            console.log('reload',_this.page.more);
            if (_this.page.more) {
                _this.page.form.page++;
                _this.getList()
            }
        },
        methods: {
            goDetails(item) {
                uni.navigateTo({
                    url: '/pagesA/project/projectDetail?id='+item.id,
                })
            },
            goPage() {
                uni.navigateTo({
                    url: '/pagesA/project/release'
                })
            },
            getList() {
                var _this = this;

                projectList(_this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.more ? 'loading' : 'nomore';
                    }
                })
            },
            showActionSheet() {
                uni.showActionSheet({
                    itemList: ['编辑', '结束'],
                    success: function (res) {
                        console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
                        if (res.tapIndex + 1 == 1) {
                            uni.navigateTo({
                                url: '/pagesA/project/release'
                            })
                        }
                    },
                    fail: function (res) {
                        console.log(res.errMsg);
                    }
                });
            }


        }
    }
</script>

<style scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
        min-height: 100vh;
        height: auto;
        background-color: #F5F5F7;
    }

    .searchBox {
        width: 100%;
        height: 64rpx;
        background: #ffffff;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
        display: flex;
        align-items: center;
        padding: 0 24rpx;
    }

    .searchIcon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
    }

    .searchIpt {
        width: 100%;
        height: 100%;
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        line-height: 64rpx;
    }

    .projectState {
        width: 160rpx;
        height: 50rpx;
        background: #FFFFFF;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 24rpx;
        margin-bottom: 32rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
    }

    .downIcon {
        width: 32rpx;
        height: 32rpx;
    }

    .listBox {
        width: 100%;
        height: 262rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 32rpx;
        position: relative;
        margin-bottom: 24rpx;
    }

    .topRight {
        position: absolute;
        top: 0;
        right: 0;
        width: 132rpx;
        height: 62rpx;
        background: #4F8CF0;
        text-align: center;
        line-height: 62rpx;
        border-radius: 0 24rpx 0 24rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
    }

    .listTil {
        font-weight: 600;
        font-size: 32rpx;
        color: #000000;
        margin-bottom: 16rpx;
    }

    .listType {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        border-bottom: 2rpx solid #F5F5F7;
        padding-bottom: 24rpx;
        margin-bottom: 24rpx;
    }

    .listProgress {
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
    }

    .listProgressNum {
        color: #4F8CF0;
    }

    .listMore {
        width: 6rpx;
        height: 40rpx;
    }

    .listPeople {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .listPeople_top {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }

    .peoHead {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        margin-right: 16rpx;
    }

    .bottomBox {
        width: 100%;
        height: 196rpx;
        padding: 24rpx 32rpx;
        background: #FFFFFF;
        box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
        position: fixed;
        bottom: 0;
        left: 0;
    }

    .bottomBox_btn {
        width: 100%;
        height: 80rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 80rpx;
        color: #FFFFFF;
    }

    .listMore {
        width: 48rpx;
        height: 48rpx;
    }

</style>