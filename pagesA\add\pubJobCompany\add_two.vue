<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="title">该公司下属品牌</view>
      <view class="subtitle">以下为该公司旗下全部品牌</view>
    </view>

    <!-- 公司列表 -->
    <view class="company-list">
      <view
        class="company-item"
        v-for="(item, index) in list"
        :key="index"
        @click="selectCompany(item)"
      >
        <image class="company-logo" :src="item.logo" mode="aspectFill"></image>
        <view class="company-info">
          <view class="company-name">
            {{ item.name }}
          </view>
          <text class="tag">{{ item.type }}</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next" @click="next"> 下一步 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      keyword: "",
      list: [
        { name: "云图", type: "互联网" },
        { name: "共享打车", type: "计算机软件" },
        { name: "地图", type: "互联网" },
      ],
    };
  },

  onLoad() {},

  methods: {
    next() {
      uni.navigateTo({
        url: "/pagesA/add/pubJobCompany/add_three",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
  padding: 32rpx;
}

.header {
  margin-bottom: 32rpx;

  .title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 24rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

.search-box {
  position: relative;
  margin-bottom: 32rpx;

  .count {
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24rpx;
    color: #999;
  }
}

.company-list {
  .company-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx 0rpx;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;

    .company-logo {
      width: 88rpx;
      height: 88rpx;
      background: #d9d9d9;
      border-radius: 8rpx;
      margin-right: 24rpx;
    }

    .company-info {
      flex: 1;

      .company-name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;
      }
      .tag {
        font-size: 20rpx;
        color: #4f8cf0;
        background-color: #ecf5ff;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
      }
      .company-type {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 12rpx;
      }
      .tags {
        width: 176rpx;
        height: 50rpx;
        background: #f4f4f4;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-size: 24rpx;
        color: #777777;
        text-align: center;
        line-height: 50rpx;
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  border-radius: 24rpx 24rpx 0 0;

  .next {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    width: 90%;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
    margin-top: 24rpx;
  }
}
</style>
