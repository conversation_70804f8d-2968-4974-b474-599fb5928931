<template>
	<view class="container">
		<u-cell @click="onListItemClick" v-for="_ in 30">
			<template #icon>
				<view class="item-image__box">
					<image class="avater" src="/static/new/客服 <EMAIL>"></image>
				</view>
			</template>
			<template #title>
				<view class="item-info__box">
					<view class="info-start">
						<text class="name">通知消息</text>
						<text class="time">刚刚</text>
					</view>
				</view>
			</template>
		</u-cell>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	},
	methods: {
		onListItemClick() {
			uni.$u.route({
				url: '/pagesC/message/message_customer',
			});
		},
	},
};
</script>
<style lang="scss" scoped>
.container {
	.item-image__box {
		margin-inline-end: 24rpx;
		border-radius: 999rpx;
		width: 88rpx;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #e8f1fe;

		.avater {
			width: 48rpx;
			height: 48rpx;
		}
	}

	.item-info__box {
		.info-start {
			display: flex;
			gap: 32rpx;
			align-items: center;

			.name {
				font-size: 28rpx;
				color: #041024;
			}

			.time {
				font-size: 24rpx;
				color: #666666;
				margin-inline-start: auto;
			}
		}
	}
}
</style>
