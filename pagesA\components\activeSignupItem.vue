<template>
	<view class="item">
		<view class="up">
			<image src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode=""></image>
			<view class="info">
				<view class="comp-name">
					<view class="name">
						腾讯视频有限公司
					</view>
					<view :class="['status',item.status==1?'wait':item.status==2?'pass':item.status==3?'back':'']">
						{{item.status==1?'待审核':item.status==2?'已通过':item.status==3?'驳回':''}}
					</view>
				</view>

				<view class="time">
					2024/01/01 19:00:01
				</view>
			</view>
		</view>

		<view class="down">
			<view class="pay">
				已支付
			</view>
			<view class="btn look" v-if="item.status==1">
				审核
			</view>
		</view>
	</view>
</template>


<script>
	export default {
		name:"activeSignupItem",
		props:{
			item:{
				type:Object,
				default:()=>{}
			}
		},

		data() {
			return {

			};
		},

		methods:{

		}
	}
</script>

<style lang="less" scoped>
	.item{
		display: flex;
		flex-direction: column;
		border-radius: 24rpx;
		padding: 32rpx;
		background-color: #FFFFFF;
		margin-bottom: 32rpx;
		.up{
			display: flex;
			image{
				width: 104rpx;
				height: 104rpx;
			}

			.info{
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				padding-left: 24rpx;
				.comp-name{
					display: flex;
					align-items: center;
					.name{
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
						margin-right: 20rpx;
					}

					.status{
						display: flex;
						align-items: center;
						height: 50rpx;
						padding: 0 16rpx;
						font-weight: 500;
						font-size: 24rpx;
						border-radius: 8rpx;
					}
					.wait{
						background: rgba(79,140,240,0.1);
						color: #4F8CF0;
					}
					.pass{
						background: rgba(87,213,28,0.1);
						color: #57D51C

					}
					.back{
						color: #FE4D4F;
						background: rgba(254,77,79,0.1);
					}
				}

				.time{
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
				}
			}
		}

		.down{
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-top: 32rpx;
			.pay{
				font-weight: 500;
				font-size: 24rpx;
				color: #57D51C;
			}

			.btn{
				display: flex;
				align-items: center;
				height: 64rpx;
				font-weight: 600;
				font-size: 28rpx;
				color: #FFFFFF;
				border-radius: 12rpx;
				padding: 0 24rpx;
				margin-left: 16rpx;
			}


			.look{
				background: #4F8CF0;
			}
		}
	}
</style>
