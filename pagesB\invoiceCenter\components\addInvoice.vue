<template>
  <view class="warp">
    <!-- 步骤指示器 -->
    <view class="steps-container">
      <u-steps :current="step" dot="true">
        <u-steps-item title="基础信息"></u-steps-item>
        <u-steps-item title="电子邮箱"></u-steps-item>
      </u-steps>
    </view>

    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 发票类型 -->
      <view class="form-item">
        <view class="form-label">发票类型</view>
        <view class="form-content">
          <view class="tab-group">
            <view
              class="tab-item"
              :class="{ active: formData.invoiceType === 'electronic' }"
              @click="formData.invoiceType = 'electronic'"
            >
              数电普票
            </view>
            <view
              class="tab-item"
              :class="{ active: formData.invoiceType === 'special' }"
              @click="formData.invoiceType = 'special'"
            >
              数电专票
            </view>
          </view>
        </view>
      </view>

      <!-- 抬头类型 -->
      <template v-if="formData.invoiceType != 'special'">
        <view class="form-item">
          <view class="form-label">抬头类型</view>
          <view class="form-content">
            <view class="tab-group">
              <view
                class="tab-item"
                :class="{ active: formData.titleType === 'company' }"
                @click="formData.titleType = 'company'"
              >
                企业单位
              </view>
              <view
                class="tab-item"
                :class="{ active: formData.titleType === 'personal' }"
                @click="formData.titleType = 'personal'"
              >
                个人/非企业
              </view>
            </view>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="form-item">
          <view class="form-label required">抬头类型</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              :value="'企业单位'"
              placeholder="填写纳税人识别号（必填）"
              placeholder-class="placeholder"
            />
          </view>
        </view>
      </template>

      <view
        class="tips"
        v-if="
          formData.titleType === 'personal' && formData.invoiceType != 'special'
        "
        >如需报销建议选择企业单位</view
      >
      <!-- 发票抬头 -->
      <view
        class="form-item"
        v-if="
          formData.titleType === 'company' || formData.invoiceType === 'special'
        "
      >
        <view class="form-label required">发票抬头</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.title"
            placeholder="填写公司全称"
            placeholder-class="placeholder"
          />
        </view>
      </view>
      <!-- 发票抬头 -->
      <view
        class="form-item"
        v-if="
          formData.titleType === 'personal' && formData.invoiceType != 'special'
        "
      >
        <view class="form-label required">发票抬头</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.title"
            placeholder="填写个人真实姓名"
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <!-- 纳税人识别号 -->
      <view
        class="form-item"
        v-if="
          formData.titleType === 'company' || formData.invoiceType === 'special'
        "
      >
        <view class="form-label required">纳税人识别号</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.taxNumber"
            placeholder="填写纳税人识别号（必填）"
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <view
        class="form-item"
        v-if="
          formData.titleType === 'personal' && formData.invoiceType != 'special'
        "
      >
        <view class="form-label required">统一社会信用代码</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.title"
            placeholder="填写公司全称"
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <!-- 发票金额 -->
      <view class="form-item">
        <view class="form-label required">发票金额</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.amount"
            disabled
            placeholder-class="placeholder"
          />
        </view>
      </view>

      <!-- 发票内容 -->
      <view class="form-item">
        <view class="form-label required">发票内容</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.content"
            placeholder="信息技术服务*网络招聘费"
            placeholder-class="placeholder"
          />
        </view>
      </view>
      <template
        v-if="
          formData.titleType === 'company' || formData.invoiceType === 'special'
        "
      >
        <!-- 公司地址 -->
        <view class="form-item">
          <view class="form-label">公司地址</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              v-model="formData.address"
              placeholder="填写公司注册地址"
              placeholder-class="placeholder"
            />
          </view>
        </view>

        <!-- 公司电话 -->
        <view class="form-item">
          <view class="form-label">公司电话</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              v-model="formData.phone"
              placeholder="填写公司注册电话"
              placeholder-class="placeholder"
            />
          </view>
        </view>

        <!-- 开户银行 -->
        <view class="form-item">
          <view class="form-label">开户银行</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              v-model="formData.bank"
              placeholder="填写公司开户银行"
              placeholder-class="placeholder"
            />
          </view>
        </view>

        <!-- 开户银行账号 -->
        <view class="form-item">
          <view class="form-label">开户银行账号</view>
          <view class="form-content">
            <input
              class="form-input"
              type="text"
              v-model="formData.bankAccount"
              placeholder="填写公司注册开户银行账号"
              placeholder-class="placeholder"
            />
          </view>
        </view>
      </template>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next-btn" @click="nextStep">下一步</view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 从index页面传过来的金额
    amount: {
      type: [Number, String],
      default: 0,
    },
    // 从index页面传过来的选中的发票ID列表
    selectedIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      step: 0,
      formData: {
        invoiceType: "electronic", // 默认数电普票
        titleType: "company", // 默认企业单位
        title: "", // 发票抬头
        taxNumber: "", // 纳税人识别号
        amount: "¥ 2657", // 发票金额
        content: "信息技术服务*网络招聘费", // 发票内容
        address: "", // 公司地址
        phone: "", // 公司电话
        bank: "", // 开户银行
        bankAccount: "", // 开户银行账号
      },
    };
  },
  created() {
    // 如果有传入金额，则设置到表单中
    if (this.amount) {
      this.formData.amount = `¥ ${parseFloat(this.amount).toFixed(2)}`;
    }
  },
  methods: {
    // 下一步
    nextStep() {
      // 验证必填字段
      if (!this.validateForm()) {
        return;
      }

      // 打印表单数据
      console.log("表单数据:", {
        ...this.formData,
        selectedIds: this.selectedIds,
      });

      uni.navigateTo({
        url: `/pagesB/invoiceCenter/components/addInvoice_emali`,
      });
    },

    // 验证表单
    validateForm() {
      // 验证发票抬头
      if (!this.formData.title) {
        uni.showToast({
          title: "请填写发票抬头",
          icon: "none",
        });
        return false;
      }

      // 验证纳税人识别号
      if (!this.formData.taxNumber) {
        uni.showToast({
          title: "请填写纳税人识别号",
          icon: "none",
        });
        return false;
      }

      // 验证发票内容
      if (!this.formData.content) {
        uni.showToast({
          title: "请填写发票内容",
          icon: "none",
        });
        return false;
      }

      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
.warp {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 120rpx;
}

/* 步骤指示器样式 */
.steps-container {
  background-color: #ffffff;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f7;
}

.step-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.step-dot {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #cccccc;
  margin-bottom: 16rpx;
}

.step-dot.active .dot {
  background-color: #4f8cf0;
}

.step-text {
  font-size: 28rpx;
  color: #999999;
}

.step-text.active {
  color: #333333;
  font-weight: 500;
}

.step-line {
  height: 2rpx;
  background-color: #cccccc;
  flex: 1;
  margin: 0 16rpx;
  position: relative;
  top: -20rpx;
}

/* 表单容器样式 */
.form-container {
  background-color: #ffffff;
  padding: 0 32rpx;
  box-sizing: border-box;
  margin: 32rpx 32rpx 118rpx 32rpx;
  border-radius: 24rpx;

}
.tips {
  height: 44rpx;
  background: #e8f1ff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-size: 20rpx;
  color: #4f8cf0;
  line-height: 44rpx;
  // box-sizing: border-box;
  padding-left: 16rpx;
}

/* 表单项样式 */
.form-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f7;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  flex-shrink: 0;
  width: 200rpx;
}

.form-label.required::after {
  content: " *";
  color: #ff0000;
}

.form-content {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  text-align: right;
}

.form-input {
  width: 100%;
  height: 60rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: right;
}

.placeholder {
  color: #cccccc;
  font-size: 28rpx;
  text-align: right;
}

/* 标签组样式 */
.tab-group {
  display: flex;
  gap: 32rpx;
  justify-content: flex-end;
}

.tab-item {
  padding: 12rpx 24rpx;
  border: 1rpx solid #e6e6e6;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

.tab-item.active {
  border-color: #4f8cf0;
  color: #4f8cf0;
  background-color: #f2f7ff;
}

/* 底部按钮样式 */
.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 32rpx;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  box-sizing: border-box;
}

.next-btn {
  width: 100%;
  height: 88rpx;
  background-color: #4f8cf0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
}
</style>
