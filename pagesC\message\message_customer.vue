<template>
	<view class="container">
		<u-navbar :autoBack="true" placeholder>
			<template #center>
				<view class="navbar-container">
					<text class="title">客服贝贝</text>
				</view>
			</template>
		</u-navbar>

		<view class="message-container">
			<scroll-view class="message-content__scroll" id="scroll-view" :scroll-with-animation="true" :scroll-y="true" :scroll-top="scrollTop">
				<view class="message-content">
					<view class="time">2022-12-12 12:00</view>
					<view class="message-custom">
						<view class="tabs-container">
							<u-tabs
								id="tabs"
								:list="tabsList"
								:inactiveStyle="{ fontSize: '28rpx', color: '#666666' }"
								:activeStyle="{ fontSize: '28rpx', color: '#041024' }"
								@click.stop="onTabsItemClick"></u-tabs>
						</view>

						<view class="custom-container">
							<view class="text-box">
								<text>人脸识别次数超出限制</text>
								<text>关于未成年就业保护</text>
								<text>关于面试备注</text>
								<text>代招是什么意思</text>
								<text>请问表情功能为什么不见了？</text>
							</view>
							<view class="actions" @click="onProblemPopupOpen">
								<text>查看更多</text>
								<image class="image" src="/static/new/右箭头@2x.png"></image>
							</view>
						</view>
					</view>
					<view class="message-item" v-for="_ in 5">
						<view class="customer">
							<image class="image" src="/static/new/客服 <EMAIL>"></image>
						</view>
						<view class="content-box">
							<view class="empty"></view>
							<view class="content party">
								工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。
							</view>
						</view>
					</view>
					<view class="message-item message-item__own">
						<view class="content-box">
							<view class="empty"></view>
							<view class="content own">
								工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。
							</view>
						</view>
						<view class="avater">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"></image>
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="message-total">
				<view class="evaluation" @click="onEvaluationPopupOpen">
					<image class="image" src="/static/new/表情icon@2x (1).png"></image>
					<text>评价</text>
				</view>
				<view class="input-box" :style="{ marginBlockEnd: `${keyboardHeight}px` }">
					<input class="input" type="text" :adjustPosition="false" placeholder="请尽量详细描述您的问题" />
					<image id="total-open" class="icon" src="/static/new/Group <EMAIL>" @click="onTotalBoxOpen"></image>
				</view>
				<view :class="{ 'total-box': true, 'total-box__open': totalBoxShow, 'total-box__close': !totalBoxShow }">
					<view class="total-content">
						<view class="total-item">
							<view class="image-box">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/5YtY23REVnHjSnyNctqoH99wCipQ5gNYAwLybr4l.png"></image>
							</view>
							<text class="text">表情</text>
						</view>
						<view class="total-item">
							<view class="image-box">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/yYvHpaaiNVuHe0bUtjfKizNpvIr166kozMidv2qo.png"></image>
							</view>
							<text class="text">图片</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<u-popup :show="problemPopup" :round="12" mode="bottom" closeable @close="onProblemPopupClose">
			<view class="popup-container__problem">
				<text class="title">猜你想问</text>

				<view class="problem-container">
					<u-cell v-for="_ in 30">
						<template #title>
							<text class="problem-title">人脸识别次数超出限制</text>
						</template>
					</u-cell>
				</view>
			</view>
		</u-popup>

		<u-popup :show="evaluationPopup" :round="12" mode="bottom" closeable @close="onEvaluationPopupClose">
			<view class="popup-container__evaluation">
				<text class="title">请对本次智能客服进行评价</text>
				<view class="evaluation-images">
					<view class="item">
						<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/h9SqZNqXhx4GJuyYh8AZXKCqFpGQd20UCGsfKjrO.png"></image>
						<text>非常不满意</text>
					</view>
					<view class="item">
						<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/h9SqZNqXhx4GJuyYh8AZXKCqFpGQd20UCGsfKjrO.png"></image>
						<text>非常不满意</text>
					</view>
					<view class="item">
						<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/h9SqZNqXhx4GJuyYh8AZXKCqFpGQd20UCGsfKjrO.png"></image>
						<text>非常不满意</text>
					</view>
				</view>

				<view class="evaluation-options">
					<view class="item is_active">理解不了我的话</view>
					<view class="item">方案看不懂</view>
					<view class="item">不想智能客服接待</view>
					<view class="item">方案不合理</view>
				</view>

				<view class="btn">提交</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	data() {
		return {
			scrollTop: 9999999,
			keyboardHeight: 0,
			totalBoxShow: false,
			problemPopup: false,
			evaluationPopup: false,

			tabsList: [
				{
					name: '猜你想问',
				},
				{
					name: '简历相关',
				},
				{
					name: '隐私与安全',
				},
				{
					name: '商业相关',
				},
			],
		};
	},
	onShow() {
		uni.onKeyboardHeightChange(res => {
			this.keyboardHeight = res.height > 0 ? res.height : 0;
			this.onTotalBoxClose();
		});
	},
	onHide() {
		uni.offKeyboardHeightChange(this.onKeyboardHeightChange);
	},
	methods: {
		onTabsItemClick(res) {},
		onTotalBoxOpen() {
			this.totalBoxShow = true;
			this.onScrollBottom();
		},
		onTotalBoxClose() {
			this.totalBoxShow = false;
			this.onScrollBottom();
		},
		onScrollBottom() {
			setTimeout(() => {
				this.scrollTop = this.scrollTop + 200;
			}, 300);
		},
		onProblemPopupOpen() {
			this.problemPopup = true;
		},
		onProblemPopupClose() {
			this.problemPopup = false;
		},
		onEvaluationPopupOpen() {
			this.evaluationPopup = true;
		},
		onEvaluationPopupClose() {
			this.evaluationPopup = false;
		},
	},
};
</script>
<style lang="scss" scoped>
.popup-container__evaluation {
	padding: 32rpx;
	border-start-start-radius: 24rpx;
	border-start-end-radius: 24rpx;
	display: flex;
	flex-direction: column;
	gap: 32rpx;

	.title {
		color: #333333;
		font-size: 32rpx;
	}

	.evaluation-images {
		color: #999999;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-evenly;

		.item {
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: 12rpx;

			.image {
				width: 76rpx;
				height: 76rpx;
			}
		}
	}

	.evaluation-options {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 24rpx;

		.item {
			background-color: #f5f5f5;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			padding-block: 12rpx;
			font-size: 24rpx;
			color: #333333;
			border: 1rpx #f5f5f5 solid;
		}

		.is_active {
			background-color: #e8f1fe;
			border: 1rpx #4f8cf0 solid;
			color: #4f8cf0;
		}
	}

	.btn {
		padding-block: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-image: linear-gradient(to right, #4f8cf0, #1e6dee);
		border-radius: 24rpx;
		color: #ffffff;
		font-size: 28rpx;
	}
}

.popup-container__problem {
	height: calc(100vh - 300rpx);
	padding: 32rpx;
	border-start-start-radius: 24rpx;
	border-start-end-radius: 24rpx;
	display: flex;
	flex-direction: column;
	gap: 32rpx;

	.title {
		color: #333333;
		font-size: 32rpx;
	}

	.problem-container {
		flex: 1;
		overflow-y: auto;

		.problem-title {
			color: #4f8cf0;
			font-size: 28rpx;
		}
	}
}

.container {
	height: 100vh;
	background-color: #f5f5f7;
	display: flex;
	flex-direction: column;

	.message-container {
		display: flex;
		flex-direction: column;
		overflow: hidden;

		.message-content__scroll {
			overflow-y: auto;
			flex: 1;

			.message-content {
				flex: 1;
				padding-block: 24rpx;
				padding-inline: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				box-sizing: border-box;

				.time {
					color: #999999;
					font-size: 24rpx;
					text-align: center;
				}

				.message-custom {
					height: 420rpx;
					background-color: #ffffff;
					box-sizing: border-box;
					border-radius: 24rpx;

					.custom-container {
						padding: 24rpx;
						font-size: 24rpx;
						color: #4f8cf0;
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.text-box {
							display: flex;
							flex-direction: column;
							gap: 16rpx;
						}

						.actions {
							display: flex;
							align-items: center;
							gap: 10rpx;
							justify-content: center;

							.image {
								width: 32rpx;
								height: 32rpx;
							}
						}
					}
				}

				.message-item {
					display: flex;
					gap: 24rpx;

					.customer {
						width: 72rpx;
						height: 72rpx;
						background-color: #e8f1fe;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 999rpx;

						.image {
							width: 40rpx;
							height: 40rpx;
						}
					}

					.avater {
						.image {
							width: 72rpx;
							height: 72rpx;
							border-radius: 999rpx;
						}
					}

					.content-box {
						display: flex;
						flex-direction: column;

						.empty {
							height: 28rpx;
						}

						.content {
							max-width: 400rpx;
							padding-inline: 24rpx;
							padding-block: 16rpx;
							font-size: 28rpx;
							color: #333333;
							border-radius: 0rpx 24rpx 24rpx 24rpx;
							background-color: #ffffff;
						}

						.own {
							border-radius: 24rpx 0rpx 24rpx 24rpx;
							background-color: rgba(160, 221, 255, 0.5);
						}
					}
				}

				.message-item__own {
					justify-content: flex-end;
				}
			}
		}

		.message-total {
			background-color: #ffffff;
			padding-block-start: 24rpx;
			border-start-start-radius: 32rpx;
			border-start-end-radius: 32rpx;
			position: relative;
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			box-sizing: border-box;

			.evaluation {
				position: absolute;
				top: 0;
				left: 0;
				width: 128rpx;
				height: 56rpx;
				background-color: #ffffff;
				border-radius: 999rpx;
				transform: translate(32rpx, -80rpx);
				color: #333333;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 8rpx;

				.image {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.input-box {
				padding-inline: 32rpx;
				display: flex;
				align-items: center;
				gap: 24rpx;

				.input {
					flex: 1;
					height: 64rpx;
					background-color: #f5f5f7;
					border-radius: 999rpx;
					padding-inline: 24rpx;
					box-sizing: border-box;
					padding-block-start: 4rpx;
				}

				.icon {
					width: 56rpx;
					height: 56rpx;
				}
			}

			.total-box {
				transition: all 0.2s linear;
				overflow: hidden;
				box-sizing: border-box;

				.express-content {
					height: 460rpx;
					padding-block-start: 32rpx;
					color: #333333;
					box-sizing: border-box;
					display: flex;
					flex-direction: column;

					.title-box {
						font-size: 28rpx;
						padding-block: 24rpx;
						padding-inline: 32rpx;
					}

					.express-list {
						flex: 1;
						overflow-y: auto;
						position: relative;

						.item {
							padding-inline: 32rpx;
							padding-block: 16rpx;
							display: flex;
							align-items: center;
							gap: 8rpx;
							font-size: 24rpx;
							border-block-start: 1rpx #e6e6e6 solid;

							.text {
								flex: 1;
								overflow: hidden;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 2;
								color: #333333;
							}

							.btn {
								width: 96rpx;
								height: 48rpx;
								border-radius: 999rpx;
								border: 1rpx #4f8cf0 solid;
								color: #4f8cf0;
								background-color: #ffffff;
								display: flex;
								align-items: center;
								justify-content: center;
							}
						}

						.express-total {
							position: sticky;
							bottom: 0;
							left: 0;
							width: 100%;
							height: 100rpx;
							display: flex;
							align-items: center;
							justify-content: space-around;
							color: #333333;
							font-size: 28rpx;
							background-color: #ffffff;
							box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
							padding-inline: 32rpx;
							box-sizing: border-box;

							.total-item {
								display: flex;
								align-items: center;
								gap: 12rpx;

								.image {
									width: 40rpx;
									height: 40rpx;
								}
							}
						}
					}
				}

				.total-content {
					padding: 32rpx;
					box-sizing: border-box;
					display: grid;
					grid-template-columns: repeat(4, 1fr);
					gap: 24rpx;

					.total-item {
						display: flex;
						flex-direction: column;
						align-items: center;
						gap: 24rpx;

						.image-box {
							width: 116rpx;
							height: 116rpx;
							background-color: #f5f5f7;
							border-radius: 24rpx;
							display: flex;
							align-items: center;
							justify-content: center;

							.image {
								width: 68rpx;
								height: 68rpx;
							}
						}

						.text {
							color: #666666;
							font-size: 28rpx;
						}
					}
				}
			}

			.total-box__close {
				height: 0rpx;
			}

			.total-box__open {
				height: 460rpx;
			}
		}
	}
}
</style>
