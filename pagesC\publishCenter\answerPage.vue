<template>
	<view class="warp">
		<view class="inner" v-for="item in 10">
			<view class="top">
				简历怎么写才能让你hr眼前一亮呢？
			</view>
			<view class="btm">
				<view class="ava-btm">
					<img :src="ava" alt="" />
					<text>期待你的解答</text>
				</view>
				<view class="btn" @click="goPublish">
					去解答
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				ava: '/static/images/login/avatar.png',
			}
		},
		methods:{
			goPublish(){
				uni.navigateTo({
					url: './publishAnswer'
				})
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 0 32rpx;

		.inner {
			width: 622rpx;
			padding: 32rpx;
			background: #fff;
			margin-top: 32rpx;
			border-radius: 16rpx;

			.top {
				font-size: 28rpx;
				color: #333333;
			}

			.btm {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 32rpx;

				.ava-btm {
					img {
						width: 32rpx;
						height: 32rpx;
						border-radius: 50%;
						margin-right: 16rpx;
					}
				}

				.btn {
					width: 126rpx;
					height: 50rpx;
					background: #4F8CF0;
					border-radius: 82rpx;
					font-size: 24rpx;
					color: #FFFFFF;
					text-align: center;
					line-height: 50rpx;
				}
			}
		}
	}
</style>