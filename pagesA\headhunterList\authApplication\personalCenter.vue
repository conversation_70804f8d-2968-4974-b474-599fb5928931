<template>
    <view class="home-index">
        <u-steps current="0">
            <u-steps-item title="第一步" desc="基本信息">
            </u-steps-item>
            <u-steps-item title="第二步" desc="完善简历"></u-steps-item>
            <!--    <u-steps-item title="第三步" desc="工作经验"></u-steps-item>-->
        </u-steps>
        <view class="wrap">
            <view class="inp">
                <view class="avatar">
                    <view class="inp-item">
                        <view class="title">
                            头像<text class="star">*</text>
                        </view>
                        <view class="in lab">
                            请选择系统默认头像
                        </view>
                    </view>
                    <view class="pic" @click="uploadAvatar">
                        <image :src="avatar && avatar.length>0?avatar[0]['path_url']:'https://api-test.zhaopinbei.com/storage/uploads/images/DjpYAiYx76keAZZls68bXyVPGHjYpURJJxA9YHcU.png'"
                            mode=""></image>
                    </view>
                </view>
                <u-line color="#F5F5F5" width="100%"></u-line>
                <view class="inp-item">
                    <view class="title">
                        昵称<text class="star">*</text>
                    </view>
                    <view class="in">
                        <u--input placeholder="请输入昵称" clearable placeholderClass="placeholderClass" border="none"
                            v-model="details.nick_name"></u--input>
                    </view>
                </view>
                <u-line color="#F5F5F5" width="100%"></u-line>
                <view class="inp-item">
                    <view class="title">
                        民族<text class="star">*</text>
                    </view>
                    <view class="in">
                        <u--input placeholder="请输入民族" clearable placeholderClass="placeholderClass" border="none"
                            v-model="details.nation"></u--input>
                    </view>
                </view>
                <u-line color="#F5F5F5" width="100%"></u-line>
                <view class="inp-item">
                    <view class="title">
                        性别<text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker @change="changeSex" :value="sexIndex" :range="sex" range-key="name">
                            <view class="d-picker" style="">{{sex[sexIndex]['name']}}
                            </view>
                        </picker> <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>
                <u-line color="#F5F5F5" width="100%"></u-line>
                <view class="inp-item">
                    <view class="title">
                        年龄<text class="star">*</text>
                    </view>
                    <view class="in">
                        <u--input placeholder="请输入年龄" clearable placeholderClass="placeholderClass" border="none"
                            v-model="details.age"></u--input>
                    </view>
                </view>
                <view class="inp-item">
                    <view class="title">
                        身份<text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker @change="changeflag" :value="flagIndex" :range="flag" range-key="name">
                            <view class="d-picker" style="">{{flag[flagIndex]['name']}}
                            </view>
                        </picker> <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>
                <u-line color="#F5F5F5" width="100%"></u-line>
                <view class="inp-item" @click="jobClass">
                    <view class="title">
                        行业<text class="star">*</text>
                    </view>
                    <view class="in fontCla" v-if="job_class.length<1">
                        请选择行业<u-icon name="arrow-right"></u-icon>
                    </view>
                   <view v-else class="jobClassStyle">
                       <view v-for="(item,index) in job_class" :key="item.id" style="margin-left: 10rpx;">
                           {{item.name}}
                       </view>
                   </view>
                </view>
                <u-line color="#F5F5F5" width="100%"></u-line>
                <view class="inp-item">
                    <view class="title">
                        最高学历<text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker @change="changeEdu" :value="eduIndex" :range="eduList" range-key="label">
                            <view class="d-picker" :style="{color:eduIndex==0?'#c0c4cc':'#303133'}">
                                {{eduList[eduIndex]['label']}}
                            </view>
                        </picker> <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>
                <u-line color="#F5F5F5" width="100%"></u-line>
                <view class="inp-item">
                    <view class="title">
                        工作状态<text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker @change="changeWorkStatus" :value="workStatusIndex" :range="workStatusList"
                            range-key="name">
                            <view class="d-picker">
                                {{workStatusList[workStatusIndex]['name']}}
                            </view>
                        </picker> <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>

                <!-- <view class="inp-item">
					<view class="title">
						求职类型<text class="star">*</text>
					</view>
					<view class="ins se" @click="show=true">
						<view v-for="(item,index) in selectedItems" :key="item.id">{{item.name}}</view>
					</view>
				</view> -->

            </view>
        </view>


        <view class="wrap" v-for="(item,index) in formList" :key="index">
            <view class="inp">
                <view class="inp-item">
                    <view class="title">
                        学校名称<text class="star">*</text>
                    </view>
                    <view class="in">
                        <u--input placeholder="请输入学校名称" fontSize='14px' clearable placeholderClass="placeholderClass"
                            border="none" v-model="item.school"></u--input>
                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        开始时间<text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker mode="date" :value="item.start" @change="bindStartDateChange(item,$event)">
                            <view class="d-picker">{{item.start?item.start:"请选择开始时间"}}</view>
                        </picker> <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        结束时间<text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker mode="date" :value="item.end" @change="bindEndDateChange(item,$event)">
                            <view class="d-picker">{{item.end?item.end:"请选择结束时间"}}</view>
                        </picker> <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>
            </view>
        </view>

        <view class="add" @click="add">
            新增一条教育经历
        </view>

        <view class="btn" @click="netStep">
            下一步
        </view>
        <u-modal :show="show" title="选择就业方式" cancelText="取消" showCancelButton @cancel='cancel' @confirm="confirm">
            <view class="slot-content">
                <view class="workList">
                    <view :class="['workItem', { 'active': isSelected(item) }]" v-for="(item,index) in workTypes"
                        :key="index" @click="handleTab(item)">
                        {{item.name||item.title}}
                    </view>
                </view>
            </view>
        </u-modal>
        <view class="jobStyle" v-if="showSelectJobs">
            <JYGJselectJobs ref="selectJobsRef" @childEvent="handleChildEvent"></JYGJselectJobs>
        </view>
    </view>
</template>

<script>
    import {
        getWorkType,
        uploadImg
    } from "../../../config/api.js"
    import {
        resumeShow
    } from "../../../config/headhunterList_api";
    import JYGJselectJobs from "../../../components/JYGJselectJobs.vue"
    export default {
        components: {
            JYGJselectJobs
        },
        data() {
            return {
                show: false,
                workTypes: [], // 工作类型数据
                selectedItems: [], // 存储选中的项
                formList: [{
                    id: 1,
                    school: '',
                    start: '',
                    end: ''
                }],
                details: {},
                avatar: [],
                sexIndex: 0,
                sex: [{
                        name: '男',
                        value: 1
                    },
                    {
                        name: '女',
                        value: 2
                    }, {
                        name: '保密',
                        value: 3
                    }
                ],
                flagIndex: 0,
                flag: [{
                        name: '学生',
                        value: 'student'
                    },
                    {
                        name: '职场人',
                        value: 'work'
                    }
                ],
                eduIndex: 0,
                workStatusList: [{
                        value: 1,
                        name: '离职'
                    },
                    {
                        value: 2,
                        name: '在职'
                    },
                    {
                        value: 3,
                        name: '考虑'
                    }
                ],
                workStatusIndex: 0,
                showSelectJobs: false,
                job_class:[],
                job_class_id:[]
            }
        },
        computed: {
            sysData() {
                return this.$store.state.sysData || uni.getStorageSync('sysData')
            },
            eduList() {
                return this.sysData.education
            },
        },
        onLoad(options) {
            if (options.id > 0) {
                //有id代表编辑，查询应聘人基本信息
                this.getResumeShow()
            }
            this.getWorkType()
        },
        methods: {
            jobClass() {
                this.showSelectJobs = true;
                this.$nextTick(() => {
                    const types = this.flag[this.flagIndex]['value'];
                    console.log("111111111", this.$refs.selectJobsRef);
                    this.$refs.selectJobsRef.open(types);
                });
                // this.$refs.selectJobsRef.open(types)
            },
            handleChildEvent(e) {
                this.showSelectJobs = false;
                this.job_class = e;
                this.job_class_id = e.map(item => item.id)

            },
            cancel() {
                this.show = false
            },
            confirm() {
                this.show = false
                this.details.work_type_id = this.selectedItems.map(item => item.id);
            },
            isSelected(item) {
                return this.selectedItems.includes(item);
            },
            changeEdu(e) {
                this.eduIndex = e.detail.value
                this.details.education_type = this.eduList[this.eduIndex]['value']
            },
            changeSex(e) {
                this.sexIndex = e.detail.value
                this.details.sex = this.sex[this.sexIndex]['value']
            },
            changeflag(e) {
                this.flagIndex = e.detail.value
                this.details.flag = this.flag[this.flagIndex]['value']
                console.log('e', this.details.flag)
            },
            changeWorkStatus(e) {
                this.workStatusIndex = e.detail.value
                this.details.job_status = this.workStatusList[this.workStatusIndex]['value']
            },
            handleTab(item) {
                const index = this.selectedItems.indexOf(item);
                if (index === -1) {
                    // 如果没有选中，添加到选中列表
                    this.selectedItems.push(item);
                } else {
                    // 如果已经选中，移除选中
                    this.selectedItems.splice(index, 1);
                }
            },
            bindStartDateChange(item, e) {
                let id = item.id
                let index = this.formList.findIndex(v => v.id == id)
                this.formList[index]['start'] = uni.$u.timeFormat(e.detail.value, 'yyyy-mm-dd');
            },
            bindEndDateChange(item, e) {
                let id = item.id
                let index = this.formList.findIndex(v => v.id == id)
                this.formList[index]['end'] = uni.$u.timeFormat(e.detail.value, 'yyyy-mm-dd');
            },

            add() {
                this.formList.push({
                    id: this.formList.length + 1,
                    school: '',
                    start: '',
                    end: ''
                })
            },
            async getResumeShow() {
                let params = {
                    limit: this.limit,
                    page: this.page,
                    title: this.keyword,
                    tag_id: [],
                }
                const {
                    status_code,
                    data
                } = await resumeShow(params)
                if (status_code == 200) {
                    this.list = this.list.concat(data.data);
                    this.more = data.more;
                    this.status = this.more ? "loadmore" : "nomore"
                }
            },
            async getWorkType() {
                const result = await getWorkType()
                if (result.status_code == 200) {
                    this.workTypes = result.data;
                }
            },
            async netStep() {

                if(this.avatar.length<1) return uni.$u.toast('请选择头像');
                if(!this.details.nick_name) return uni.$u.toast('请输入姓名');
                if(!this.details.nation) return uni.$u.toast('请输入民族');
                if(!this.details.age) return uni.$u.toast('请输入年龄');
                if(this.job_class_id.length <1) return uni.$u.toast('请选择行业');
                let formData = {
                    name: this.details.nick_name,
                    nation: this.details.nation,
                    sex: this.sex[this.sexIndex]['value'],
                    age: this.details.age,
                    education_type: this.eduList[this.eduIndex]['value'],
                    job_status: this.workStatusList[this.workStatusIndex]['value'],
                    member_image: [this.avatar[0].id],
                    education_log: this.formList,
                    flag: this.flag[this.flagIndex]['value'],
                    member_job_class_id:this.job_class_id
                }
                uni.setStorageSync('userFormData', formData)

                console.log(formData, "参数")
                uni.navigateTo({
                    url: '/pagesA/headhunterList/authApplication/addResume'
                })
            },

            //上传荣誉证书
            uploadAvatar() {
                let self = this

                uni.chooseMedia({
                    count: 1,
                    mediaType: ['image'],
                    sizeType: ['original', 'compressed'],
                    sourceType: ['album', 'camera'],
                    success: (tempFilePaths) => {
                        const path = tempFilePaths.tempFiles[0].tempFilePath;
                        // $dialog.loading('上传中')
                        uni.getFileSystemManager().readFile({
                            filePath: path,
                            encoding: 'base64',
                            success: async function(res) {

                                let imageParams = {
                                    ext: 'png',
                                    content: res.data,
                                    org_name: new Date().getTime() + '.png'
                                }
                                // 上传
                                const result = await uploadImg(imageParams)
                                console.log("图片信息：", result.data)
                                if (result.status_code == 200) {
                                    self.avatar = []
                                    self.avatar.push({
                                        id: result.data.id,
                                        path_url: result.data.url,
                                        pdf_url: "",
                                        thumbnail_path_url: result.data.thumbnail_url
                                    })
                                }
                            }
                        })
                    }
                });
            },
        }
    }
</script>
<style>
    page {
        background-color: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    .home-index {
        padding-bottom: 160rpx;
    }

    .wrap {
        padding: 30rpx;

        .inp {
            background: #FFFFFF;
            border-radius: 16rpx;
            padding: 0 30rpx;

            .avatar {
                display: flex;
                align-items: center;

                .pic {
                    // padding: 0 30rpx 0 0;
                    background-color: #F5F5F7;
                    border-radius: 50%;

                    image {
                        width: 108rpx;
                        height: 108rpx;
                        border-radius: 50%;
                    }
                }
            }

            .inp-item {
                display: flex;
                flex-direction: column;
                // padding: 0 30rpx;
                flex: 1;

                .title {
                    display: flex;
                    align-items: center;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #666666;
                    margin: 16rpx 0 0 0;

                    .star {
                        font-weight: 600;
                        font-size: 22rpx;
                        color: #FE4D4F;
                        margin-left: 8rpx;
                    }
                }

                .ins {
                    display: flex;
                    align-items: center;
                    // justify-content: space-between;
                    // border-bottom: 1px solid #F5F5F7;
                    height: 88rpx;
                    font-size: 32rpx;

                    view {
                        margin-right: 20rpx;
                    }
                }

                .in {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    // border-bottom: 1px solid #F5F5F7;
                    height: 88rpx;
                    font-size: 32rpx;

                    // ::v-deep picker {
                    // 	display: flex;
                    // 	flex-direction: column;
                    // 	flex: 1;
                    // 	height: 88rpx;

                    // 	.d-picker {
                    // 		display: flex;
                    // 		align-items: center;
                    // 		// width: 60vw;
                    // 		height: 88rpx;
                    // 	}
                    // }

                    ::v-deep .placeholderClass {
                        font-weight: 400;
                        font-size: 32rpx;
                    }

                    ::v-deep picker {
                        display: flex;
                        flex-direction: column;
                        flex: 1;
                        height: 88rpx;

                        .d-picker {
                            display: flex;
                            align-items: center;
                            // width: 60vw;
                            height: 88rpx;
                        }
                    }



                }

                .se {
                    color: #999;
                }

                .lab {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #999999;
                }
            }
        }
    }

    .add {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 24rpx;
        height: 64rpx;
        width: 260rpx;
        margin-left: 32rpx;
        background: #4F8CF0;
        font-weight: 600;
        font-size: 28rpx;
        color: #FFFFFF;
        border-radius: 8rpx;
    }

    .btn {
        position: fixed;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 90%;
        height: 100rpx;
        z-index: 100;
        bottom: 32rpx;
        left: 5%;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx;
        font-weight: 600;
        font-size: 34rpx;
        color: #FFFFFF;
    }

    .workList {
        display: flex;
        flex-wrap: wrap;

        .workItem {
            display: flex;
            align-items: center;
            justify-content: center;
            align-items: center;
            height: 64rpx;
            padding: 0 22rpx;
            border-radius: 8rpx;
            background-color: #F5F5F7;
            margin-bottom: 16rpx;
            margin-right: 20rpx;
            font-size: 28rpx;
            font-weight: 600;
        }

        .active {
            color: #4F8CF0;
            border: 1px solid #4F8CF0
        }
    }

    .jobStyle {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        background: grey;
        z-index: 999;
       padding: 32rpx;
    }

    .fontCla {
        color: #999 !important;
    }
    .jobClassStyle {
        display: flex;
        align-items: center;
        height: 88rpx;
        font-size: 32rpx;
    }
</style>
