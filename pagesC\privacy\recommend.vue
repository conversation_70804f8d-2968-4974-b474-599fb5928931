<template>
	<view class="warp">
		<view class="row" v-for="item in rowList" :key="item.id">
			<view class="top">
				<view class="tit-warp">
					<view class="tit">
						{{item.title}}
					</view>
					<u-icon name="error-circle" color="#999999" size="14"></u-icon>
				</view>
				<u-switch v-model="item.checked" active-color="#4F8CF0" @change="changeSwitch(item)"></u-switch>
			</view>
			<view class="btm">
				{{item.txt}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				rowList: [{
					title: '个性化职位推荐',
					txt: '基于您的简历信息、求职期望、操作记录、授权的位置信息等，推荐您可能感兴趣的职位。关闭后，我们仍可能基于您的简历信息、求职期望为您展示职位，但相关度会降低，可能影响您的求职体验。',
					checked: false,
					id: 1
				}, {
					title: '个性化内容添加',
					txt: '基于您的基本资料、期望信息、操作记录为您推荐【有了】社区内您可能感兴趣的内容，关闭后，仍向您推荐内容，但内容的相关度会降低。',
					checked: false,
					id: 2
				}],
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			changeSwitch(item) {
				console.log(item);
				switch (item.id) {
					case 1:
						// 切换为true
						if (item.checked) {
							uni.showModal({
								title: '关闭个性化职位推荐',
								content: '关闭后，无法获得精准职位推荐，将严重影响您的使用体验',
								success: function(res) {
									if (res.confirm) {

									} else {
										item.checked = false
									}
								}
							});
						} else {

						}
						break;
					case 2:
						// 切换为true
						if (item.checked) {
							uni.showModal({
								title: '关闭个性化内容添加',
								content: '关闭后，将无法获得精准的推荐，将严重影响您的使用体验',
								success: function(res) {
									if (res.confirm) {

									} else {
										item.checked = false
									}
								}
							});
						} else {

						}
						break;

				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 0 32rpx;
		height: 100vh;
		background: #fff;

		.row {
			padding: 32rpx 0;
			border-bottom: 1rpx solid #E6E6E6;

			.top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 14rpx;

				.tit-warp {
					display: flex;
					align-items: center;

					.tit {
						font-size: 28rpx;
						color: #333333;
						margin-right: 22rpx;
					}
				}

			}

			.btm {
				font-size: 24rpx;
				color: #999999;
			}
		}
	}
</style>