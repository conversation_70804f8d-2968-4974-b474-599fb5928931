<template>
  <view
    class="register-one"
    :style="{ paddingBottom: step == 0 ? '178rpx' : '266rpx' }"
  >
    <u-steps :current="step" dot="true">
      <u-steps-item title="第一步"></u-steps-item>
      <u-steps-item title="第二步"></u-steps-item>
    </u-steps>
    <view class="inp">
      <view class="inp-item">
        <view class="title">
          经验
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            v-if="pubJobOne.typeJob != '应届生'"
            @change="changeJobExperience"
            :value="jobExperienceIndex"
            :range="experience"
            range-key="label"
          >
            <view
              class="d-picker"
              :style="{
                color: jobExperienceIndex == 0 ? '#c0c4cc' : '#303133',
              }"
            >
              {{ experience[jobExperienceIndex]["label"] }}
            </view>
          </picker>
          <view v-else class="d-picker" :style="{ color: '#303133' }">
            在校应届
          </view>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item">
        <view class="title">
          学历
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            @change="changeEducation"
            :value="educationIndex"
            :range="educationList"
            range-key="label"
          >
            <view
              class="d-picker"
              :style="{ color: educationIndex == 0 ? '#c0c4cc' : '#303133' }"
            >
              {{ educationList[educationIndex]["label"] }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item">
        <view class="title">
          计薪规则
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            @change="changeFinance"
            :value="salary_type_index"
            :range="salary_type"
            range-key="label"
          >
            <view class="d-picker">
              {{ salary_type[salary_type_index]["label"] }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item" v-if="pubJobTwo.salary_type != 'discuss'">
        <view class="title">
          薪资范围
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            @change="changeSalary"
            :value="salaryIndex"
            :range="salary_range_search_name"
            range-key="label"
          >
            <view class="d-picker">
              {{ salary_range_search_name[salaryIndex]["label"] }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item" @click="goKeyWord">
        <view class="title">
          职位关键词
          <text class="star">*</text>
        </view>
        <view class="in se">
          <view class="d-picker">
            {{ "请选择职位关键词" }}
          </view>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item" v-if="pubJobOne.typeJob == '兼职'">
        <view class="title">
          工作地点
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            @change="changeAddress"
            :value="addressIndex"
            :range="addressList"
            range-key="map_address"
          >
            <view class="d-picker">{{
              addressList[addressIndex]["map_address"]
            }}</view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <template v-if="pubJobOne.typeJob == '兼职'">
        <view class="inp-item">
          <view class="title">
            性别
            <text class="star">*</text>
          </view>
          <view class="in se">
            <picker
              @change="changeSex"
              :value="sexIndex"
              :range="sexList"
              range-key="label"
            >
              <view
                class="d-picker"
                :style="{ color: sexIndex == 0 ? '#c0c4cc' : '#303133' }"
              >
                {{ sexList[sexIndex]["label"] }}
              </view>
            </picker>
            <u-icon name="arrow-right"></u-icon>
          </view>
        </view>
        <view class="inp-item">
          <view class="title">
            适用人群
            <text class="star">*</text>
          </view>
          <view class="in se">
            <picker
              @change="changeRq"
              :value="RqIndex"
              :range="peopleList"
              range-key="label"
            >
              <view
                class="d-picker"
                :style="{ color: RqIndex == 0 ? '#c0c4cc' : '#303133' }"
              >
                {{ peopleList[RqIndex]["label"] }}
              </view>
            </picker>
            <u-icon name="arrow-right"></u-icon>
          </view>
        </view>
        <view class="inp-item">
          <view class="title">
            年龄要求
            <text class="star">*</text>
          </view>
          <view class="in se">
            <picker
              @change="changeAge"
              :value="ageIndex"
              :range="ageList"
              range-key="label"
            >
              <view
                class="d-picker"
                :style="{ color: ageIndex == 0 ? '#c0c4cc' : '#303133' }"
              >
                {{ ageList[ageIndex]["label"] }}
              </view>
            </picker>
            <u-icon name="arrow-right"></u-icon>
          </view>
        </view>
      </template>
      <view
        class="inp-item"
        v-if="pubJobOne.typeJob == '应届生' || pubJobOne.typeJob == '实习生'"
      >
        <view class="title">
          毕业时间
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker mode="date" :value="pubJobTwo.by_time" @change="byDateChange">
            <view class="d-picker"
              >{{ pubJobTwo.by_time ? pubJobTwo.by_time : "请选择时间" }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item" v-if="pubJobOne.typeJob == '应届生'">
        <view class="title">
          招聘截至时间
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            mode="date"
            :value="pubJobTwo.jiezhi_time"
            @change="jzDateChange"
          >
            <view class="d-picker"
              >{{
                pubJobTwo.jiezhi_time ? pubJobTwo.jiezhi_time : "请选择时间"
              }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item" v-if="pubJobOne.typeJob == '实习生'">
        <view class="title">
          实习要求
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            @change="changeYaoqiu"
            :value="yaoqiuIndex"
            :range="yaoqiuList"
            range-key="label"
          >
            <view
              class="d-picker"
              :style="{ color: yaoqiuIndex == 0 ? '#c0c4cc' : '#303133' }"
            >
              {{ yaoqiuList[yaoqiuIndex]["label"] }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item">
        <view class="title">
          招聘截至时间
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            mode="date"
            :value="pubJobTwo.jiezhi_time"
            @change="jzDateChange"
          >
            <view class="d-picker"
              >{{
                pubJobTwo.jiezhi_time ? pubJobTwo.jiezhi_time : "请选择时间"
              }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item">
        <view class="title">
          面试时间
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            mode="date"
            :value="pubJobTwo.activity_start_at"
            @change="bindStartDateChange"
          >
            <view class="d-picker"
              >{{
                pubJobTwo.activity_start_at
                  ? pubJobTwo.activity_start_at
                  : "请选择时间"
              }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <!-- <view class="inp-item">
				<view class="title">
					面试结束时间
					<text class="star">*</text>
				</view>
				<view class="in se">
					<picker mode="date" :value="pubJobTwo.activity_end_at" @change="bindEndDateChange">
						<view class="d-picker">{{pubJobTwo.activity_end_at?pubJobTwo.activity_end_at:"请选择开始时间"}}</view>
					</picker>
					<u-icon name="arrow-right"></u-icon>
				</view>
			</view> -->
      <view class="inp-item">
        <view class="title">
          到岗时间
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            mode="date"
            :value="pubJobTwo.registration_end_at"
            @change="bindArriveDateChange"
          >
            <view class="d-picker"
              >{{
                pubJobTwo.registration_end_at
                  ? pubJobTwo.registration_end_at
                  : "请选择到岗时间"
              }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <!-- <view class="inp-item">
				<view class="title">
					需求人数
					<text class="star">*</text>
				</view>
				<view class="in">
					<u--input placeholder="请输入需求人数" placeholderClass="placeholderClass" clearable border="none"
						v-model="pubJobTwo.surplus_number"></u--input>
				</view>
			</view> -->
    </view>

    <view class="footers">
      <view class="btns">
        <view class="next prev" @click="prev"> 上一步 </view>
        <!-- <view class="next sure" @click="sure('draft')">
					草稿
				</view> -->
        <view class="next sure" @click="sure('submit')"> 提交 </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  addCompanyJob,
  getCompanyAddressList,
  getJobDetails,
} from "../../config/api.js";

export default {
  data() {
    return {
      id: "",
      step: 2,
      value: "",
      salaryIndex: 0,
      addressIndex: 0,
      addressList: [],
      sex: ["男", "女"],
      sexIndex: 0,
      salary_type_index: 0,
      jobExperienceIndex: 0,
      experienceIndex: 0,
      keywordindex: 0,
      key: 0,
      pubJobTwo: {
        salary_type: "",
        salary_info: {
          max: "",
          min: "",
          month: "",
        },
        experience: "",
        address_id: [],
        activity_start_at: uni.$u.timeFormat(new Date(), "yyyy-mmm-dd"),
        activity_end_at: uni.$u.timeFormat(new Date(), "yyyy-mmm-dd"),
        registration_end_at: uni.$u.timeFormat(new Date(), "yyyy-mmm-dd"),
        by_time: uni.$u.timeFormat(new Date(), "yyyy-mmm-dd"),
        jiezhi_time: uni.$u.timeFormat(new Date(), "yyyy-mmm-dd"),
        surplus_number: "",
        keyword: "",
        xueli: "",
        yaoqiu: "",
        sex: "",
        suitable_people: "",
        age_requirement: "",
      },
      educationIndex: 0,
      educationList: [
        {
          label: "请选择学历要求",
          value: "",
        },
        {
          label: "中专",
          value: "zhongzhuan",
        },
        {
          label: "专科",
          value: "zhuanke",
        },
        {
          label: "本科",
          value: "benke",
        },
        {
          label: "研究生",
          value: "yanjiusheng",
        },
        {
          label: "博士",
          value: "boshi",
        },
        {
          label: "其他",
          value: "other",
        },
      ],
      yaoqiuIndex: 0,
      yaoqiuList: [
        {
          label: "请选择实习要求",
          value: "",
        },
        {
          label: "3个月",
          value: "1",
        },
        {
          label: "6个月",
          value: "2",
        },
      ],
      sexIndex: 0,
      sexList: [
        {
          label: "请选择性别要求",
          value: "",
        },
        {
          label: "男",
          value: "male",
        },
        {
          label: "女",
          value: "female",
        },
        {
          label: "不限",
          value: "all",
        },
      ],
      RqIndex: 0,
      peopleList: [
        {
          label: "请选择适用人群",
          value: "",
        },
        {
          label: "在校学生",
          value: "student",
        },
        {
          label: "待业人员",
          value: "unemployed",
        },
        {
          label: "社会人士",
          value: "social",
        },
        {
          label: "不限",
          value: "all",
        },
      ],
      ageIndex: 0,
      ageList: [
        {
          label: "请选择年龄要求",
          value: "",
        },
        {
          label: "18-25岁",
          value: "18-25",
        },
        {
          label: "25-35岁",
          value: "25-35",
        },
        {
          label: "35-45岁",
          value: "35-45",
        },
        {
          label: "不限",
          value: "all",
        },
      ],
      copy: "",
    };
  },
  computed: {
    sysData() {
      return this.$store.state.sysData || uni.getStorageSync("sysData");
    },
    salary_range_search_name() {
      return this.sysData.salary_range_search_name;
    },

    salary_type() {
      return this.sysData.salary_type;
    },
    experience() {
      return this.sysData.experience;
    },
    pubJobOne() {
      return this.$store.state.pubJobOne;
    },
  },
  onLoad(options) {
    if (options.id) {
      let id = options.id;
      this.id = id;
    }
    console.log("one", this.$store.state.pubJobOne);
    this.getCompanyAddressList();

    if (this.id > 0) {
      this.addressIndex = 1;
      this.getJobDetails();
    }
    if (options.copy) {
      this.copy = options.copy;
    }
  },
  methods: {
    prev() {
      uni.navigateBack();
    },
    goKeyWord() {
      uni.navigateTo({
        url: "/pagesA/select/selectJobKeyWord",
      });
    },

    async sure(type) {
      if (this.copy) {
        this.id = "";
      }
      let arr = this.addressList.filter(
        (item) => item.id == this.addressList[this.addressIndex]["id"]
      );
      this.pubJobTwo.address_id = [arr[0]["id"]];
      this.pubJobTwo.salary_type =
        this.salary_type[this.salary_type_index]["value"];
      this.pubJobTwo.experience =
        this.experience[this.jobExperienceIndex]["value"];

      if (this.pubJobTwo.salary_type == "range") {
        let str = this.salary_range_search_name[this.salaryIndex]["value"];
        let arr = str.split("-");
        this.pubJobTwo.salary_info.max = parseInt(arr[1]) * 1000;
        this.pubJobTwo.salary_info.min = parseInt(arr[0]) * 1000;
        this.pubJobTwo.salary_info.month = 1;
      } else {
        this.pubJobTwo.salary_info.max = "";
        this.pubJobTwo.salary_info.min = "";
        this.pubJobTwo.salary_info.month = "";
      }

      let params = {};

      if (this.id) {
        params = {
          ...this.pubJobOne,
          ...this.pubJobTwo,
          submit_type: type,
          id: this.id,
        };
      } else {
        params = {
          ...this.pubJobOne,
          ...this.pubJobTwo,
          submit_type: type,
        };
      }

      const { status_code, message, data } = await addCompanyJob(params);

      uni.$u.toast(message || "失败");

      if (status_code == 200) {
        uni.navigateBack({
          delta: 2,
        });
        return true;
      }
    },

    changeSalary(e) {
      console.log(e);
      this.salaryIndex = e.detail.value;
      let str = this.salary_range_search_name[this.salaryIndex]["value"];
      let arr = str.split("-");
      this.pubJobTwo.salary_info.max = parseInt(arr[1]) * 1000;
      this.pubJobTwo.salary_info.min = parseInt(arr[0]) * 1000;
      this.pubJobTwo.salary_info.month = 1;
    },

    changeAddress(e) {
      this.addressIndex = e.detail.value;
      this.pubJobTwo.address_id.push(this.addressList[this.addressIndex]["id"]);
      // this.pubJobTwo.address_id[0] = this.addressList[this.addressIndex]['id']
    },

    changeFinance(e) {
      this.salary_type_index = e.detail.value;
      this.pubJobTwo.salary_type =
        this.salary_type[this.salary_type_index]["value"];
    },

    changeJobExperience(e) {
      this.jobExperienceIndex = e.detail.value;
      this.pubJobTwo.experience =
        this.experience[this.jobExperienceIndex]["value"];
    },
    changeExperience(e) {
      this.experienceIndex = e.detail.value;
      this.pubJobTwo.xueli = this.experience[this.experienceIndex]["value"];
    },
    changeKeyword(e) {
      this.keywordindex = e.detail.value;
      this.pubJobTwo.keyword = this.salary_type[this.keywordindex]["value"];
    },

    bindStartDateChange(e) {
      this.pubJobTwo.activity_start_at = uni.$u.timeFormat(
        e.detail.value,
        "yyyy-mmm-dd"
      );
    },

    bindEndDateChange(e) {
      this.pubJobTwo.activity_end_at = uni.$u.timeFormat(
        e.detail.value,
        "yyyy-mmm-dd"
      );
    },

    bindArriveDateChange(e) {
      this.pubJobTwo.registration_end_at = uni.$u.timeFormat(
        e.detail.value,
        "yyyy-mmm-dd"
      );
    },
    byDateChange(e) {
      this.pubJobTwo.by_time = uni.$u.timeFormat(e.detail.value, "yyyy-mmm-dd");
    },
    jzDateChange(e) {
      this.pubJobTwo.jiezhi_time = uni.$u.timeFormat(
        e.detail.value,
        "yyyy-mmm-dd"
      );
    },

    async getCompanyAddressList() {
      const { status_code, data } = await getCompanyAddressList();
      this.addressList = data;
    },
    changeEducation(e) {
      this.educationIndex = e.detail.value;
      this.pubJobTwo.xueli = this.educationList[this.educationIndex].value;
    },
    changeYaoqiu(e) {
      this.yaoqiuIndex = e.detail.value;
      this.pubJobTwo.yaoqiu = this.yaoqiuList[this.yaoqiuIndex].value;
    },
    changeSex(e) {
      this.sexIndex = e.detail.value;
      this.pubJobTwo.sex = this.sexList[this.sexIndex].value;
    },
    changeRq(e) {
      this.RqIndex = e.detail.value;
      this.pubJobTwo.suitable_people = this.peopleList[this.RqIndex].value;
    },
    changeAge(e) {
      this.ageIndex = e.detail.value;
      this.pubJobTwo.age_requirement = this.ageList[this.ageIndex].value;
    },
    async getJobDetails() {
      let params = {
        id: this.id,
      };
      const { status_code, data } = await getJobDetails(params);
      if (status_code == 200) {
        this.details = data;

        this.salary_type_index = this.salary_type.findIndex(
          (item) => item.value == this.details.salary_type
        );
        this.pubJobTwo.salary_type = this.details.salary_type;
        this.salaryIndex = this.salary_range_search_name.findIndex(
          (item) => item.label == this.details.salary_info_str
        );

        if (this.pubJobTwo.salary_type == "range") {
          this.pubJobTwo.salary_info = this.details.salary_info;
        }

        this.pubJobTwo.experience = this.details.experience;
        this.jobExperienceIndex = this.experience.findIndex(
          (item) => item.value == this.details.experience
        );

        this.pubJobTwo.address_id.push(this.details.addresses[0]["id"]);
        this.addressIndex = this.addressList.findIndex(
          (item) => item.id == this.details.addresses[0]["id"]
        );
        if (this.addressIndex == -1) {
          this.addressIndex = 0;
        }
        this.pubJobTwo.activity_start_at = this.details.activity_start_at;
        this.pubJobTwo.activity_end_at = this.details.activity_end_at;
        this.pubJobTwo.registration_end_at = this.details.registration_end_at;
        this.pubJobTwo.surplus_number = this.details.surplus_number;
      }
    },
  },
};
</script>
<style lang="scss">
@import "../../static/css/pagesA/add/pubJobTwo";
</style>
<style lang="less" scoped>
.footers {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 196rpx;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;

  .btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .next {
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 308rpx;
    height: 84rpx;
    background: #e8f1ff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-size: 32rpx;
    color: #4f8cf0;
  }

  .sure {
    background: linear-gradient(92deg, #4f8cf0 0%, #0061ff 100%);
    font-size: 32rpx;
    color: #ffffff;
    margin-left: 70rpx;
  }
}
</style>
