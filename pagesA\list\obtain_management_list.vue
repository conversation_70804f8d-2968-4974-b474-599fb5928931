<template>
    <view class="">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="search-wrap">
                    <u-search placeholder="请输入关键字" bgColor="#FFFFFF" v-model="page.form.keyword" :showAction="true"
                        @clear="clear" @custom="custom"></u-search>
                </view>
                <view class="filters">
                    <view class="filter" @click="selectCity">
                        {{userCity.name}}
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <!-- <view class="filter">
                        筛选
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view> -->
                </view>
            </view>
        </u-sticky>

        <view class="list">
            <obtain-item v-for="(item,index) in page.data" :key="index" :item="item"></obtain-item>
        </view>
        <Pages :status="page.status"></Pages>

    </view>
</template>

<script>
    import ObtainItem from "../components/obtainItem.vue"
    import {
        headhunterList
    } from "../../config/common_api";
    import Pages from "../../components/pages.vue";

    export default {
        components: {
            Pages,
            ObtainItem
        },
        data() {
            return {
                userCity: {
                    name: '全国',
                    id: 0
                },
                page: {
                    form: {
                        keyword: '',
                        page: 1,
                        limit: 10,
                        city_id: 0
                    },
                    status: 'loadmore',
                    more: false,
                    data: [],
                },
            }
        },
        onLoad() {
            // 用户选择的城市从本地获取name、id
            if (uni.getStorageSync('userCity')) {
                this.userCity = uni.getStorageSync('userCity');
                this.page.form.city_id = this.userCity.id;
            }
            this.headhunterList()
        },
        onShow() {
            const cityOld = this.userCity.id; // 当前城市ID
            const cityNew = uni.getStorageSync('userCity').id; // 从缓存中获取选中的城市ID

            if (uni.getStorageSync('userCity')&& cityNew != cityOld) {
                this.userCity = uni.getStorageSync('userCity'); // 更新当前城市
                this.page.form.city_id = this.userCity.id;
                this.initPage();
                this.headhunterList(); // 刷新列表数据
            }
        },
        //触底加载更多
        onReachBottom() {
            if (this.page.more) {
                this.page.status = 'loading';
                this.page.form.page++;
                this.headhunterList()
            }
        },
        methods: {
            selectCity() {
                uni.navigateTo({
                    url: '/pagesA/components/selectCitys'
                })
            },
            clear() {
                this.page.form.keyword = '';
                this.initPage();
                this.headhunterList();
            },
            custom() {
                this.initPage();
                this.headhunterList();
            },
            initPage() {
                this.page.data = [];
                this.page.form.page = 1;
                this.page.status = 'loadmore';
            },
            //member列表
            async headhunterList() {
                headhunterList(this.page.form).then(response => {
                    if (response.status_code == '200') {
                        this.page.more = response.data.more
                        this.page.data = this.page.data.concat(response.data.data);
                        this.page.status = this.page.more ? 'loadmore' : 'nomore';
                    }
                });
            },
        }
    }
</script>
<style lang="scss" src="../../static/css/pagesA/list/obtain_management_list.scss"></style>