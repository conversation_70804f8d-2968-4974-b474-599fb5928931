<template>
    <view class="wrap" @click="goPage(item)">
        <!-- 提交测试 -->
        <view class="item">
            <view class="item-up">
                <image :src="item.member_info.image.thumbnail_path_url" mode=""></image>
                <view class="info">
                    <view class="user">
                        <view class="userInfo">
                            <view class="name">
                                {{ item.member_certification.name}}
                            </view>
                        </view>
                    </view>
					<!-- 就业管家简介 -->
                    <!-- <view class="flag">
                        {{ item.user_headhunter.introduction ? item.user_headhunter.introduction : '' }}
                    </view> -->
					<!-- 公司名 -->
					<view class="flag">
					    {{ item.company.name }}
					</view>
                </view>
            </view>

            <view class="item-down">
                <view class="tags" v-if="item.user_headhunter != null && item.user_headhunter.label">
                    <view class="tag" v-for="(labelItem,index) in item.user_headhunter.label" :key="index"
                          :item="labelItem">
                        {{ labelItem }}
                    </view>
                </view>
            </view>
        </view>
        <u-line color="#F5F5F7" length="100%"></u-line>
        <company-tag :item="item"></company-tag>
		<view class="bottomBtn3">
			<view class="comm" @click.stop="communicate('user_headhunter',item.id)">聊聊呗</view>
		</view>
    </view>
</template>

<script>
    import CompanyTag from "../../public_label/companyTag.vue";
	import {communicate} from "../../common/common";
    export default {
        name: "obtainItem",
        components: {CompanyTag},
        props: {
            item: {
                type: Object,
                default: () => {
                }
            }
        },
        data() {
            return {};
        },
        methods: {
			communicate,
            goPage(item) {
                // uni.navigateTo({
                //     url: '/pagesA/details/obtainItemDetails?id=' + item.id
                // })
				uni.navigateTo({
				    url: '/pagesA/details/headhuntersDetail?id=' + item.id
				})
            }
        }
    }
</script>


<style lang="scss" src="../../static/css/pagesA/components/obtainItem.scss"></style>