<template>
	<view class="container">
		<!-- 教育经历 -->
		<view class="content">
			<scroll-view scroll-y="true" class="scroll-Y">
				<view class="form-item" @click="routerName">
					<label>学校名称</label>
					<u--input placeholder="请输入学校名称" :value="dateform.schoolname" suffixIcon="arrow-right"
						suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
				</view>

				<view class="form-item">
					<label>学历</label>
					<picker mode="selector" :range="dateform.education" v-model="dateform.educational">
						<view class="selected-con">
							<view class="picker-view" v-if="dateform.educational != ''">
								{{ dateform.educational }}
							</view>
							<view class="picker-view" v-else>
								请选择学历
							</view>
							<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
						</view>
					</picker>
				</view>

				<view class="form-item">
					<label>专业</label>
					<view class="selected-con">
						<view class="picker-view" v-if="dateform.study != ''">
							{{ dateform.study }}
						</view>
						<u--input type="text" placeholder="请选择专业" :value="dateform.study" suffixIcon="arrow-right"
							suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
					</view>
				</view>

				<view class="form-item">
					<label>时间段</label>
					<view class="time-picker">
						<view class="selected-con">
							<picker class="form-input" mode="date" :value="dateform.startTime" :start="startDate"
								:end="dateform.endTime" @change="bindDateChange">
								<view class="form-input-placeholder" v-if="dateform.startTime===''">请选择开始时间</view>
								<view class="form-input-placeholder" v-else>{{dateform.startTime}}</view>
							</picker>
							<text>-</text>
							<picker class="form-input" mode="date" :value="dateform.endTime" :start="dateform.startTime"
								@change="bindDateChange2">
								<view class="form-input-placeholder" v-if="dateform.endTime===''">请选择结束时间</view>
								<view class="form-input-placeholder" v-else>{{dateform.endTime}}</view>
							</picker>
							<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
						</view>
					</view>
				</view>

				<view class="form-item" @click="routerselection">
					<label>主修课程</label>
					<view class="selected-con">
						<!-- <view class="picker-view" v-if="dateform.educational != ''">
							{{ dateform.educational }}
						</view> -->
						<view class="picker-view">
							请选择主修课程
						</view>
						<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
					</view>
				</view>
				<view class="form-item">
					<label>专业排名（选填）</label>
					<picker mode="selector" :range="dateform.education" v-model="dateform.educational">
						<view class="selected-con">
							<!-- <view class="picker-view" v-if="dateform.educational != ''">
							{{ dateform.educational }}
						</view> -->
							<view class="picker-view">
								请选择专业排名
							</view>
							<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
						</view>
					</picker>
				</view>
				<view class="form-item" @click="routerexperience">
					<view style="display: flex; justify-content: space-between; align-items: center;">
						<label>在校经历（选填）</label>
						<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
					</view>
					<view class="selected-con">
						<!-- 	<view class="picker-view" v-if="dataform.workDescription != ''">
						{{ dataform.workDescription }}
					</view> -->
						<view class="picker-view" style="display: flex;flex-direction: column;">
							<text> 1、在校担任职务... </text>
							<text> 2、获得荣誉... </text>
							<text> 3.所学主要课程...</text>
						</view>

					</view>

				</view>
				<view class="form-item" @click="routerfinalproject">
					<label>毕业设计/论文题目（选填）</label>
					<view class="selected-con">
						<view class="picker-view" v-if="dateform.study != ''">
							{{ dateform.study }}
						</view>
						<u--input type="text" placeholder="请输入毕业设计/论文题目" :value="dateform.study"
							suffixIcon="arrow-right" suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
					</view>
				</view>
				<view class="form-item" @click="routerdescribe">
					<view style="display: flex; justify-content: space-between; align-items: center;">
						<label>毕业设计/论文描述（选填）</label>
						<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
					</view>
					<view class="selected-con">
						<!-- <view class="picker-view" v-if="dataform.workDescription != ''">
						{{ dataform.workDescription }}
					</view> -->
						<view class="picker-view" style="display: flex;flex-direction: column;">
							描述毕设/论文主要内容，像伯乐展示您的学术能力
							<text> 例如：</text>
							<text> 1、选题的目的及意义... </text>
							<text> 2、摘要及关键词...</text>
							<text> 3、论文结论或成果...</text>
						</view>

					</view>

				</view>
			</scroll-view>
		</view>
		<view class="footer">
			<button class="confirm-btn" @click="submitForm">完成</button>
		</view>

	</view>
</template>

<!-- -->
<script>
	export default {
		data() {
			return {
				dateform: {
					schoolname: '',
					schoolstyle: ['全日制', '非全日制'],
					style: '',
					education: ['初中', '高中', '大专', '本科'],
					educational: '',
					study: '',
					startTime: '',
					endTime: '',
				}
			};
		},

		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		methods: {
			submitForm() {
				// 这里可以添加表单提交逻辑，例如调用接口发送数据等
			},
			routerName() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/education/schoolname'
				})
			},
			routerexperience() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/education/schoolexperience'
				})
			},
			routerfinalproject() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/education/finalproject'
				})
			},
			routerdescribe() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/education/finalprojectdescribe'
				})
			},
			routerselection() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/Customize/selection'
				})
			},
			// 获取时间
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			// 选择开始时间
			bindDateChange(e) {
				this.dateform.startTime = e.detail.value
			},
			// 选择结束时间
			bindDateChange2(e) {
				this.dateform.endTime = e.target.value;
			},
			// 按照时间查找
			findDate() {
				if (this.dateform.startTime === '' || this.dateform.endTime === '') {
					uni.showModal({
						title: '提示',
						content: `请选择起始时间和结束时间后，再点击查询`,
						showCancel: false
					});
				} else {
					getTask(this.dateform).then(res => {
						this.tasklistArr = JSON.parse(JSON.stringify(res.data.data));
					})
				}

			},
			// 清除时间
			cleardateForm() {
				this.dateform.startTime = "";
				this.dateform.endTime = "";
				getTask(this.dateform).then(res => {
					this.tasklistArr = JSON.parse(JSON.stringify(res.data.data));
				})
			},

		}
	};
</script>

<style scoped>
	.container {
		padding: 0rpx 20rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		background-color: rgba(245, 245, 247, 1);
	}

	.content {
		flex: 1;
		width: 622rpx;
		padding: 32rpx;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.form-item {
		margin-bottom: 20rpx;
		width: 622rpx;
		border-bottom: 1rpx solid rgba(230, 230, 230, 1);
		padding-bottom: 32rpx;
	}

	.label {
		display: block;
		margin-bottom: 5rpx;
		height: 114rpx;
	}

	.scroll-Y {
		height: 85vh;
	}

	/* .input {
		width: 100%;
		padding: 10rpx;
		border: 1rpx solid #ccc;
		border-radius: 5rpx;
	} */

	::v-deep .u-input {
		border: none;
		padding: 0rpx !important;
		width: 622rpx;
	}

	.picker-view {
		width: 100%;
		/* padding: 10rpx; */
		/* border: 1rpx solid #ccc; */
		color: rgba(204, 204, 204, 1);
		margin-top: 16rpx;
		border-radius: 5rpx;
		font-size: 28rpx;
	}

	.selected-con {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 622rpx;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.time-picker {
		display: flex;
		align-items: center;
	}

	.time-picker text {
		margin: 0 10px;
	}

	.form-input-placeholder {
		color: #999;
	}
</style>