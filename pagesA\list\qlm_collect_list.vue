<template>
	<view class="">
		<u-sticky bgColor="#FFFFFF">
			<view class="tabs">
				<u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" @click="changeTab"></u-tabs>
			</view>

		</u-sticky>
		<block v-if="list.length>0">
			<view class="list">
				<u-swipe-action>
					<u-swipe-action-item @click="handleSwipe(item,$event)" :options="options" v-for="(item,index) in list"
						:name="index" :key="index">
						<block v-if="tabIndex==0">
							<collect-job-item :item="item"></collect-job-item>
						</block>
						<block v-if="tabIndex==1">
							<comp-item :item="item"></comp-item>
						</block>
						<block v-if="tabIndex==2">
							<obtain-item :item="item"></obtain-item>
						</block>
					</u-swipe-action-item>
				</u-swipe-action>
			</view>
		</block>
		<!-- <block v-else>
			<u-empty
			        mode="data"
			        icon="http://cdn.uviewui.com/uview/empty/data.png"
			>
			</u-empty>
		</block> -->
	</view>
</template>


<script>
	import {
		getCollectCompanyList,
		getCollectJobList,
		memberCancelCollectJob,
        getCollectHeadhunterList,
        cancelCollectEnterprise,
        cancelCollectCompanyHeadhunters
	} from "../../config/api.js"
	import CollectJobItem from "../components/collectJobItem.vue"
	import CompItem from "../components/compItem.vue"
	import ObtainItem from "../../components/obtainItem.vue"
	export default {
		components: {
			CollectJobItem,
			CompItem,
			ObtainItem
		},
		data() {
			return {
				page: 1,
				limit: 10,
				list: [],
				isScroll: false,
				options: [{
					text: '取消收藏',
					style: {
						backgroundColor: '#FE4D4F',
						borderRadius: '24rpx',
						bottom: '32rpx',
						height: '100%',
						width: '150rpx',
						marginLeft: '24rpx'
					}
				}],
				tabIndex: 0,
				tabs: [{
					name: '职位收藏',
				}, {
					name: '企业收藏',
				}, {
					name: '就业管家收藏'
				}]
			}
		},

		onLoad() {
			this.getCollectJobList()
		},

		methods: {
			changeTab(e) {
				this.page = 1
				this.list = []
				this.tabIndex = e.index

				if (this.tabIndex == 0) {
					this.getCollectJobList()
				} else if (this.tabIndex == 1) {
					this.getCollectCompanyList()
				} else if (this.tabIndex == 2) {
                    this.getCollectHeadhunterList()
                }
				console.log(e)
				console.log(this.tabIndex)
			},

			//删除
			async handleSwipe(item, e) {
				let self = this;
				//表示点击了删除按钮
				if (this.tabIndex == 0) {
					uni.showModal({
						title: '确定要删除吗？',
						success: async (res) => {
							if (res.confirm) {
								console.log('用户点击确定');
								let params = {
									id: item.job_id
								}
								const {
									status_code,
									data
								} = await memberCancelCollectJob(params)
								if (status_code == 200) {
                                    self.list.splice(e.name,1)
									return uni.$u.toast('成功')
								}
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					})
				}
                
                if (this.tabIndex == 1) {
                	uni.showModal({
                		title: '确定要删除吗？',
                		success: async (res) => {
                			if (res.confirm) {
                				console.log('用户点击确定');
                				let params = {
                					id: item.company_id
                				}
                				const {
                					status_code,
                					data
                				} = await cancelCollectEnterprise(params)
                				if (status_code == 200) {
                					self.list.splice(e.name,1)
                					return uni.$u.toast('成功')
                				}
                			} else if (res.cancel) {
                				console.log('用户点击取消');
                			}
                		}
                	})
                }
                
                if (this.tabIndex == 2) {
                	uni.showModal({
                		title: '确定要删除吗？',
                		success: async (res) => {
                			if (res.confirm) {
                				console.log('用户点击确定');
                				let params = {
                					id: item.id
                				}
                				const {
                					status_code,
                					data
                				} = await cancelCollectCompanyHeadhunters(params)
                				if (status_code == 200) {
                					self.list.splice(e.name,1)
                					return uni.$u.toast('成功')
                				}
                			} else if (res.cancel) {
                				console.log('用户点击取消');
                			}
                		}
                	})
                }
                
                
                
			},
            //就业管家收藏列表
            async getCollectHeadhunterList() {
            	let params = {
            		page: this.page,
            		limit: this.limit
            	}
            	const {
            		status_code,
            		message,
            		data
            	} = await getCollectHeadhunterList(params)
            	if (status_code == 200) {
            		this.list = data.data;
                    console.log(this.list)
            	}
            },
			//职位收藏列表
			async getCollectJobList() {
				let params = {
					page: this.page,
					limit: this.limit
				}
				const {
					status_code,
					message,
					data
				} = await getCollectJobList(params)
				if (status_code == 200) {
					this.list = data.data;
				}
			},
			

			//企业收藏列表
			async getCollectCompanyList() {
				let params = {
					page: this.page,
					limit: this.limit
				}
				const {
					status_code,
					message,
					data
				} = await getCollectCompanyList(params)
				if (status_code == 200) {
					this.list = data.data;
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f5;
	}
</style>
<style lang="less" scoped>
	.tabs {
		margin-bottom: 32rpx;
	}

	.list {
		padding: 0 32rpx;

		::v-deep .u-swipe-action-item__right {
			bottom: 32rpx;
			border-radius: 24rpx;
		}

		::v-deep .u-swipe-action-item__content {
			background: transparent;
		}
	}
</style>