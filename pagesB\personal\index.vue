<template>
	<!-- 身份验证 -->
	<view class="container">
		<u-navbar bgColor="transparent" title="账号与安全中心" placeholder :autoBack="true" :border="true" />
		<view class="title-container">
			<view class="title">2个安全优化建议</view>
			<view class="btn">立即优化</view>
		</view>
		<scroll-view class="scroll-view" :scroll-y="true">
			<view class="scroll-container">
				<u-cell @click="onRoute('account')">
					<template #title>
						<view class="cell-title">账号管理</view>
					</template>
				</u-cell>
				<u-cell @click="onRoute('permission')">
					<template #title>
						<view class="cell-title">权限管理</view>
					</template>
				</u-cell>
				<u-cell @click="onRoute('login_device')">
					<template #title>
						<view class="cell-title">登录设备管理</view>
					</template>
				</u-cell>
				<u-cell @click="onRoute('user_info')">
					<template #title>
						<view class="cell-title">个人信息管理</view>
					</template>
				</u-cell>
				<u-cell @click="onRoute('activitie')">
					<template #title>
						<view class="cell-title">活动记录管理</view>
					</template>
				</u-cell>
				<u-cell @click="onRoute('identity')" :border="false">
					<template #title>
						<view class="cell-title">身份验证</view>
					</template>
				</u-cell>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		methods: {
			onRoute(url) {
				uni.$u.route({
					url: `/pagesB/personal/${url}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-cell  {
		.u-cell__body {
			padding: 0 !important;
		}
	}
	.container {
		height: 100vh;
		background-color: #F5F5F7;
		background-image: url(https://api-test.zhaopinbei.com/storage/uploads/images/KMm3iv9bdTzAeTfbJPBEdYe6V5gZ2FWt9x5oCMXL.png);
		background-size: contain;
		background-repeat: no-repeat;
		display: flex;
		flex-direction: column;

		.title-container {
			padding-inline: 32rpx;
			padding-block-start: 82rpx;
			padding-block-end: 72rpx;
			display: flex;
			flex-direction: column;
			gap: 24rpx;

			.title {
				color: #4F8CF0;
				font-size: 32rpx;
			}

			.btn {
				width: 152rpx;
				height: 64rpx;
				background: rgba(255, 255, 255, 0.5);
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				color: #4F8CF0;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 28rpx;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;
			border-start-start-radius: 24rpx;
			border-start-end-radius: 24rpx;
			background-color: #FFFFFF;

			.scroll-container {
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
				padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

				.cell-title {
					color: #333333;
					font-size: 28rpx;
					padding-block: 48rpx;
				}
			}
		}
	}
</style>
