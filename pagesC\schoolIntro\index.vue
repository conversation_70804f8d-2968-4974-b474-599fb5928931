<template>
	<view class="container">
		<u-navbar bgColor="transparent" placeholder leftIconColor="#000" :autoBack="true" />
		<!-- 顶部图片 -->
		<view class="banner" style="background-color: #f5f5f5">
			<!-- 后续替换为实际图片 -->
		</view>

		<!-- 导航栏 -->
		<view class="nav-container">
			<!-- 顶部 Tabs -->
			<view class="tabs-wrap">
				<!-- 顶部 Tabs -->
				<view class="tabs-box">
					<u-tabs :list="tabs" :current="tabIndexs" @change="onTabChange"
						:activeStyle="{ color: '#4F8CF0', fontSize: '32rpx' }" scrollable :wait="true"></u-tabs>
				</view>
				<view class="tabs-box tabs_s2" v-if="subTabs.length > 0">
					<u-tabs :list="subTabs" :current="subtabIndexs" lineWidth="0"
						:activeStyle="{ color: '#4F8CF0', fontSize: '32rpx' }" @change="onSubTabChange"
						scrollable></u-tabs>
				</view>
			</view>
		</view>

		<!-- 学校简介内容 -->
		<template v-if="tabIndex == 0">
			<!-- 学校信息 -->
			<view class="school-info">
				<view class="school-logo">
					<image src="" style="background-color: #f5f5f5; width: 100%; height: 100%"></image>
				</view>
				<view class="school-detail">
					<view class="school-name">深美国际学校</view>
					<view class="school-tags">理工科 | 公办 | 本科</view>
				</view>
			</view>

			<!-- 基本信息列表 -->
			<view class="info-list">
				<view class="info-item">
					<text class="label">创办时间：</text>
					<text class="value">2005年</text>
				</view>
				<view class="info-item">
					<text class="label">学校类型：</text>
					<text class="value">综合</text>
				</view>
				<view class="info-item">
					<text class="label">普通高效学校属性：</text>
					<text class="value">普通高效</text>
				</view>
				<view class="info-item">
					<text class="label">学校地址：</text>
					<text class="value">广东省深圳市南山区蛇口公园路80号</text>
				</view>
				<view class="info-item">
					<text class="label">学校官网：</text>
					<text class="value link">A Cognia Accredited School</text>
				</view>
			</view>

			<!-- 学校简介 -->
			<view class="intro-section">
				<view class="section-title">学校简介</view>
				<view class="intro-content">
					郑州大学（Zhengzhou
					University）是国家"双一流"（世界一流大学和一流学科）建设高校、"211工程"重点建设高校。由河南省人民政府与教育部共建，是河南省规模最大、学科门类最全的综合性大学。
				</view>
				<view class="intro-item">
					<view class="sub-title">1. 学校概况</view>
					<view class="item-content">
						<view>-
							创办时间：1956年（原郑州大学），2000年由原郑州大学、郑州工业大学、河南医科大学合并建新郑州大学。</view>
						<view>- 校训：求是 担当</view>
						<view>- 校址：</view>
						<view>- 主校区：郑州市高新区科学大道100号</view>
						<view>- 南校区：郑州市二七区大学北路75号（原郑州工业大学）</view>
						<view>- 北校区：郑州市金水区文化路97号（原河南医科大学）</view>
						<view>- 东校区：郑州市郑东新区龙子湖校区（医学相关院系）</view>
					</view>
				</view>
			</view>
		</template>

		<view class="content"><!-- 工作信息内容 -->
			<!-- 全职 -->
			<template v-if="tabIndex == 1 && subTabIndex == 0">
				<view class="item_template1" v-for="v in jobList" :key="v.job.job_id" @click="onDetail(v)">
					<view class="item-start">
						<view class="title-box">
							<view class="title_1">{{ v.job.job_info_name }}</view>
							<view class="title_3">
								{{ currentSubTab === 0 ? "全职" : "兼职" }}
							</view>
							<view class="title_2">
								{{ v.salary.salary_min }}
							</view>
						</view>
						<view class="time">{{
               "25岁 | 男女不限 | 10人"
            }}</view>
						<view class="tags">
							<view class="tag" v-for="k in ['世界500强', '上市公司', '游戏大厂']" :key="k.id">{{ k }}</view>
						</view>
						<view class="times" v-if="currentSubTab === 1">
							<text>面试时间：2025-03-28</text>
							<text>到岗时间：2025-04-28</text>
						</view>
						<view class="inn">
							<view class="left">
								<view class="age"> 学创北京｜A轮｜0-20人 </view>
								<view class="add">
									<img src="/static/images/index/jyindex/address_icon.png" alt="" />
									<text>北京北京市昌平区1号院阳光小区</text>
									<view class="km"> 距12.1km </view>
								</view>
							</view>
						</view>
						<u-line></u-line>
						<view class="item-end">
							<view class="btn-box">
								<view class="btn_1" @click="openPopup">立即报名</view>
								<view class="btn_2" @click.stop @click="jumpLiao(v)">聊聊呗</view>
							</view>
							<view class="end">
								<view class="name">
									<image src="/static/images/index/jyindex/baozhang_icon.png" mode=""></image>
									<text>平台保障</text>
								</view>
								<view class="name">
									<image src="/static/images/index/jyindex/zipin_icon.png" mode=""></image>
									<text>平台自聘</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>

			<!-- 兼职 -->
			<template v-if="tabIndex == 1 && subTabIndex == 1">
				<view class="item_template1" @click="onDetail(v)">
					<view class="item-start">
						<view class="title-box">
							<view class="title_1">兼职静态</view>
							<view class="title_3">
								兼职
							</view>
							<view class="title_2">
								0
							</view>
						</view>
						<view class="time">{{
				   "25岁 | 男女不限 | 10人"
				}}</view>
						<view class="tags">
							<view class="tag" v-for="k in ['世界500强', '上市公司', '游戏大厂']" :key="k.id">{{ k }}</view>
						</view>
						<view class="times" v-if="currentSubTab === 1">
							<text>面试时间：2025-03-28</text>
							<text>到岗时间：2025-04-28</text>
						</view>
						<view class="inn">
							<view class="left">
								<view class="age"> 学创北京｜A轮｜0-20人 </view>
								<view class="add">
									<img src="/static/images/index/jyindex/address_icon.png" alt="" />
									<text>北京北京市昌平区1号院阳光小区</text>
									<view class="km"> 距12.1km </view>
								</view>
							</view>
						</view>
						<u-line></u-line>
						<view class="item-end">
							<view class="btn-box">
								<view class="btn_1">立即报名</view>
								<view class="btn_2">聊聊呗</view>
							</view>
							<view class="end">
								<view class="name">
									<image src="/static/images/index/jyindex/baozhang_icon.png" mode=""></image>
									<text>平台保障</text>
								</view>
								<view class="name">
									<image src="/static/images/index/jyindex/zipin_icon.png" mode=""></image>
									<text>平台自聘</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>
			<!-- 实习 -->
			<template v-if="tabIndex == 1 && subTabIndex == 2">
				<view class="item_template1" @click="onDetail(v)">
					<view class="item-start">
						<view class="title-box">
							<view class="title_1">实习静态</view>
							<view class="title_3">
								实习
							</view>
							<view class="title_2">
								0
							</view>
						</view>
						<view class="time">{{
				   "25岁 | 男女不限 | 10人"
				}}</view>
						<view class="tags">
							<view class="tag" v-for="k in ['世界500强', '上市公司', '游戏大厂']" :key="k.id">{{ k }}</view>
						</view>
						<view class="times" v-if="currentSubTab === 1">
							<text>面试时间：2025-03-28</text>
							<text>到岗时间：2025-04-28</text>
						</view>
						<view class="inn">
							<view class="left">
								<view class="age"> 学创北京｜A轮｜0-20人 </view>
								<view class="add">
									<img src="/static/images/index/jyindex/address_icon.png" alt="" />
									<text>北京北京市昌平区1号院阳光小区</text>
									<view class="km"> 距12.1km </view>
								</view>
							</view>
						</view>
						<u-line></u-line>
						<view class="item-end">
							<view class="btn-box">
								<view class="btn_1">立即报名</view>
								<view class="btn_2">聊聊呗</view>
							</view>
							<view class="end">
								<view class="name">
									<image src="/static/images/index/jyindex/baozhang_icon.png" mode=""></image>
									<text>平台保障</text>
								</view>
								<view class="name">
									<image src="/static/images/index/jyindex/zipin_icon.png" mode=""></image>
									<text>平台自聘</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>

			<!-- 校内管家内容 -->
			<template v-if="tabIndex == 2">
				<view class="item_template2" v-for="v in headunterList" :key="v.id" @click="onDetail(v.id)">
					<view class="item-start">
						<image class="image" :src="v.member_info.image.path_url"></image>
						<view class="info">
							<view class="info-start">
								<view class="name-box">
									<text class="name">{{ v.name }}</text>
									<view class="identity">
										<img src="https://api-test.zhaopinbei.com/storage/uploads/images/6HUdgbieZV8fIGjOkQFKOdeIXL9b5HKuUFihXlCF.png"
											alt="" />
										<text>{{ v.member.certification_status_name }}</text>
									</view>
								</view>
								<view class="tip-ide">主推: 电子/互联网/IT服务、大数据</view>
								<view class="detal-box" v-if="v.remark">{{ v.remark }}</view>
								<view class="tags">
									<view class="tag">5年经验</view>
								</view>
							</view>
							<view class="info-end" @click="jumpLiao(v)">聊聊呗</view>
						</view>
					</view>
					<u-line></u-line>
					<view class="item-end">
						<view class="evaluation">
							<text class="name">专业能力</text>
							<text class="number">4.4</text>
						</view>
						<view class="evaluation">
							<text class="name">专业能力</text>
							<text class="number">4.4</text>
						</view>
						<view class="evaluation">
							<text class="name">专业能力</text>
							<text class="number">4.4</text>
						</view>
					</view>
				</view>
			</template>

			<!-- 活动信息内容 -->
			<template v-if="tabIndex == 3 && subTabIndex == 0">
				<view class="item-type-1-warp" v-for="v in jobActiveList" :key="v.job_active.id">
					<view class="title">{{ v.job_active.title }}</view>
					<view class="item-type-1" @click="onDetail(v.job_active.id)">
						<view class="item-start">
							<view class="title">{{ v.job_active.title }}</view>
							<view class="sub-title">500/天</view>
						</view>
						<view class="item-center">
							{{ v.job_active.intro }}
						</view>
						<view class="item-end">
							<view>上岗时间：{{ v.job_active.start }}</view>
							<view>报名截止时间：{{ v.job_active.end }}</view>
						</view>
					</view>
					<view class="btm">
						<img src="/static/images/index/jyindex/job_icon.png" alt="" />
						<text>本次活动提供岗位：22个，参与的高校及乡镇街道共2个</text>
					</view>
				</view>
			</template>
			<template v-if="tabIndex == 3 && subTabIndex == 1">
				<view class="item-type-2">
					<view class="item-start">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"
							mode=""></image>
						<view class="start-info">
							<text class="title">xxx公司宣讲会</text>
							<text class="time">讲师：王哈哈</text>
							<text class="time">提供岗位：22个</text>
							<text class="time">河南大学礼堂</text>
							<view class="tag">进行中</view>
						</view>
					</view>
					<view class="item-end">
						<text class="time">·52367人参加</text>
						<text class="time">2025.09.02 08:00:00开始</text>
						<!-- <text class="detail">查看详情</text> -->
					</view>
				</view>
			</template>
			<template v-if="tabIndex == 3 && subTabIndex == 2">
				<view class="item-type-2" v-for="v in jobActiveList" :key="v.job_active.id">
					<view class="item-start" @click="onDetail(v.job_active.id)">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"
							mode=""></image>
						<view class="start-info">
							<text class="title">{{ v.job_active.title }}</text>
							<text class="time">2022年12月12日</text>
							<text class="time">已有200家企业入驻</text>
							<text class="time">地址：郑州市金水区万正商务大厦</text>
							<view class="tag">进行中</view>
						</view>
					</view>
					<view class="item-end">
						<text class="time">·52367人参加</text>
						<text class="time">9月15日 08:00:00开始</text>
					</view>
				</view>
			</template>
			<template v-if="tabIndex == 3 && subTabIndex == 3">
				<view class="item-type-2">
					<view class="item-start">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"
							mode=""></image>
						<view class="start-info">
							<text class="title">xxx公司宣讲会</text>
							<text class="time">2022年12月12日</text>
							<text class="time">已有200家企业入驻</text>
							<view class="tag">进行中</view>
						</view>
					</view>
					<view class="item-end">
						<text class="time">·52367人参加</text>
						<text class="time">9月15日 08:00:00开始</text>
					</view>
				</view>
			</template>
		</view>
		<uni-popup ref="bottomPopup" style=" position: absolute;" type="bottom">
			<view class="popup-content bg-gray rounded-xl flex flex-col justify-between" style="height: 760rpx;">
				<!-- <text>请输入您的简历链接：</text>
				<input v-model="resumeUrl" placeholder="https://example.com/resume.pdf" />
				<button @click="sendResume">发送简历</button> -->
				<text class="PM text-black pt-4 flex justify-center font-bold" style="font-size: 28rpx;">请选择发送简历</text>
				<view class="flex flex-col gap-3" style="padding: 48rpx 24rpx;">
					<view class="bg-whiter rounded-xl p-4 flex gap-4" v-for="i in 3" :key="i">
						<view class="bg-gray-500" style="width: 72rpx; height: 72rpx;">
							<img src="" alt="" />
						</view>
						<view class="flex flex-col gap-1">
							<text class="PM text-black" style="font-size: 28rpx;">简历名称.pdf</text>
							<text class="PR text-3" style="color: #999999;">更新于2025.03.14 14:06</text>
						</view>
					</view>
				</view>
				<view class="bg-blue py-3 flex justify-center text-white text-4 PR" @click="complete"
					style="width: 440rpx; margin: 0 auto; border-radius: 54rpx; margin-bottom: 40rpx;">
					投递
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import {
		getJobPublishList,
		getJobActiveList,
		getHeadunterUserList,
	} from "@/config";

	export default {
		data() {
			return {
				tabs: [{
						name: "学校简介"
					},
					{
						name: "工作信息"
					},
					{
						name: "校内管家"
					},
					{
						name: "活动信息"
					},
				],
				tabIndex: 0,
				tabIndexs: 0,
				subtabIndexs: 0,
				subTabs: [],
				subTabIndex: 0,
				jobList: [],
				jobActiveList: [],
				headunterList: [],
				params: {
					limit: 20,
					page: 1,
					work_type_id: "2",
					job_type: "2",
				},
				activeParams: {
					limit: 20,
					page: 1,
					type: "1",
				},
				headunterParams: {
					limit: 20,
					page: 1,
					member_id: 300,
				},
				isLoading: false,
			};
		},
		computed: {
			currentSubTab() {
				return this.subTabIndex;
			},
		},
		// 尝试使用watch在subTabIndex的值改变时再次执行getJobList，无用
		// watch: {
		// 	subTabIndex: {
		// 		immediate: true,
		// 		handler(newValue, oldValue) {
		// 			if (this.tabIndex === 1) {
		// 				console.log(1111);
		// 				this.getJobList();
		// 				console.log(this.tabIndex, this.jobList);
		// 			}
		// 		}
		// 	}
		// },
		methods: {
			onTabChange(index) {
				this.tabIndex = index.index;
				console.log("index", index);
				console.log("this.tabIndex", this.tabIndex);

				this.subTabIndex = 0;

				switch (index.index) {
					case 0:
						this.subTabs = [];
						break;
					case 1:
						this.subTabs = [{
							name: "全职"
						}, {
							name: "兼职"
						}, {
							name: "实习"
						}];
						this.getJobList();
						break;
					case 2:
						this.subTabs = [];
						this.getHeadunterList();
						break;
					case 3:
						this.subTabs = [{
								name: "特推招聘"
							},
							{
								name: "宣讲会"
							},
							{
								name: "招聘会"
							},
							{
								name: "岗位预定"
							},
						];
						this.getJobActiveList();
						break;
				}
			},
			onSubTabChange(index) {
				this.subTabIndex = index.index;
				if (this.tabIndex === 1) {
					this.getJobList();
					// console.log(this.jobList);
				}
			},
			async getJobList() {
				const params = {
					...this.params,
					job_type: this.currentSubTab === 0 ? "1" : "2",
				};
				const res = await getJobPublishList(params);
				if (res.status_code !== "200") return;
				this.jobList = res.data.jobs_list?.data || [];
			},
			async getJobActiveList() {
				const res = await getJobActiveList(this.activeParams);
				if (res.status_code !== "200") return;
				this.jobActiveList = res.data.data || [];
			},
			async getHeadunterList() {
				const res = await getHeadunterUserList(this.headunterParams);
				if (res.status_code !== "200") return;
				this.headunterList = res.data.data || [];
			},
			onDetail(v) {
				console.log(v);
				// 处理详情跳转
				if (this.tabIndex == 1) {
					uni.navigateTo({
						url: '/pagesC/schoolIntro/studentJob'
					})
				} else if (this.tabIndex == 2) {
					const id = v
					uni.navigateTo({
						url: `/pagesA/details/obtainItemDetails?id=${id}`
					})
				} else if (this.tabIndex == 3) {
					const id = v
					uni.navigateTo({
						url: `/pagesA/headhunterList/headhunters_jobfair_details?id=${id}`
					})
				}

			},
			// 聊聊呗跳转
			jumpLiao(v) {
				console.log(v, this.tabIndex);
				if (this.tabIndex == 1) {
					const type = "company"
					const id = v.job.job_id
					// console.log(type, id);
					uni.switchTab({
						url: `/pages/chat/message?type=${type}&id=${id}`
					})
				} else if (this.tabIndex == 2) {
					const type = v.type
					const id = v.id
					// console.log(type, id);
					uni.switchTab({
						url: `/pages/chat/message?type=${type}&id=${id}`
					})
				}
			},
			openPopup() {
				this.$refs.bottomPopup.open(); // 打开底部弹窗
				this.action = true
			},
			complete() {
				this.$refs.bottomPopup.close(); // 关闭弹窗
			}
		},
		mounted() {
			setTimeout(() => {
				this.tabs = [{
						name: "学校简介"
					},
					{
						name: "工作信息"
					},
					{
						name: "校内管家"
					},
					{
						name: "活动信息"
					},
				];
			}, 50);
		},
	};
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #fff;
	}

	.tabs-wrap {
		background: #fff;
		padding: 0 32rpx;

		.tabs-box {
			height: 106rpx;
		}
	}

	.banner {
		height: 260rpx;
		width: 100%;
	}

	.nav-container {
		background: #fff;
		position: sticky;
		top: 0;
		z-index: 99;
		border-radius: 16rpx 16rpx 0 0;
		padding-top: 20rpx;
		margin-top: -20rpx;
	}

	.school-info {
		padding: 32rpx;
		display: flex;
		align-items: center;
		gap: 24rpx;

		.school-logo {
			width: 120rpx;
			height: 120rpx;
			border-radius: 16rpx;
			overflow: hidden;
		}

		.school-detail {
			.school-name {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 12rpx;
			}

			.school-tags {
				font-size: 24rpx;
				color: #666;
			}
		}
	}

	.info-list {
		padding: 0 32rpx;

		.info-item {
			display: flex;
			margin-bottom: 24rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.label {
				color: #666;
				font-size: 28rpx;
				min-width: 200rpx;
			}

			.value {
				color: #333;
				font-size: 28rpx;
				flex: 1;

				&.link {
					color: #4f8cf0;
				}
			}
		}
	}

	.intro-section {
		padding: 32rpx;

		.section-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 24rpx;
		}

		.intro-content {
			font-size: 28rpx;
			color: #666;
			line-height: 1.6;
			margin-bottom: 32rpx;
		}

		.intro-item {
			.sub-title {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 16rpx;
			}

			.item-content {
				font-size: 28rpx;
				color: #666;
				line-height: 1.6;

				view {
					margin-bottom: 8rpx;
				}
			}
		}
	}

	.content {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		padding: 0 32rpx 32rpx 32rpx;
	}

	.item_template1 {
		background-color: #ffffff;
		border-radius: 24rpx;
		display: flex;
		justify-content: space-between;
		// flex-direction: column;
		gap: 24rpx;

		.avater {
			width: 48rpx;
			height: 48rpx;
		}

		.item-end {
			.btn-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 12rpx;

				view {
					width: 299rpx;
					height: 56rpx;
					background: #f1f6fe;
					border-radius: 16rpx 16rpx 16rpx 16rpx;
					font-size: 24rpx;
					color: #ffffff;
					text-align: center;
					line-height: 56rpx;
				}

				.btn_1 {
					color: #4f8cf0;
				}

				.btn_2 {
					background: linear-gradient(92deg, #4f8cf0 0%, #0061ff 100%);
				}
			}

			.end {
				display: flex;
				justify-content: space-between;
				align-items: center;
				gap: 28rpx;
			}

			.name {
				color: #666;
				font-size: 20rpx;
				display: flex;
				align-items: center;

				image {
					width: 48rpx;
					height: 48rpx;
					margin-right: 4rpx;
				}
			}
		}

		.item-start {
			width: 100%;
			display: flex;
			flex-direction: column;
			gap: 24rpx;

			.time {
				color: #666;
				font-size: 24rpx;
				overflow: hidden;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
			}

			.type-text {
				font-size: 28rpx;
				color: #041024;
			}

			.title-box {
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 12rpx;
				font-size: 32rpx;
				color: #333333;

				.title-inner {
					display: flex;
					align-items: center;
					gap: 12rpx;

					.tit-inn {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;
						gap: 12rpx;

						.age {
							font-size: 24rpx;
							color: #666666;
						}
					}
				}

				.title_1 {
					// flex: 1;
					width: 160rpx;

					overflow: hidden;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 1;
				}

				.title_2 {
					color: #f98a14;
					white-space: nowrap;
					margin-left: 100rpx;
				}

				.title_3 {
					color: #4f8cf0;
					white-space: nowrap;
					margin-left: 100rpx;
				}
			}

			.tags {
				flex: 1;
				display: flex;
				align-items: center;
				gap: 16rpx;
				overflow-x: auto;
				white-space: nowrap;

				.tag {
					flex-shrink: 0;
					padding: 12rpx;
					border-radius: 8rpx;
					color: #666666;
					font-size: 22rpx;
					background-color: #f6f6f6;
				}
			}

			.times {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 24rpx;
				color: #666666;
			}

			.inn {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 24rpx;
				color: #666666;

				.left {
					display: flex;
					flex-direction: column;

					.add {
						display: flex;
						align-items: center;
						margin-top: 14rpx;

						text {
							width: 250rpx;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						img {
							width: 32rpx;
							height: 32rpx;
							margin-right: 8rpx;
						}

						.km {
							height: 36rpx;
							padding: 0 8rpx;
							line-height: 36rpx;
							background: #f6f6f6;
							border-radius: 50rpx 50rpx 50rpx 50rpx;
						}
					}
				}

				.right {
					width: 144rpx;
					height: 56rpx;
					background: linear-gradient(132deg, #4f8cf0 0%, #0061ff 100%);
					border-radius: 8rpx 8rpx 8rpx 8rpx;
					font-size: 24rpx;
					color: #ffffff;
					text-align: center;
					line-height: 56rpx;
				}
			}
		}
	}

	.item_template2 {
		padding: 32rpx;
		background: linear-gradient(151deg, #4f8cf0 -50%, #ffffff 40%);
		border-radius: 24rpx;
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.item-end {
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 24rpx;

			.evaluation {
				display: flex;
				align-items: center;
				gap: 8rpx;
				font-size: 24rpx;

				.name {
					color: #666666;
				}

				.number {
					color: #dd4e41;
				}
			}
		}

		.item-start {
			display: flex;
			align-items: flex-start;
			gap: 20rpx;

			.image {
				width: 96rpx;
				height: 96rpx;
				border-radius: 999rpx;
			}

			.info {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.info-end {
					background-color: #4f8cf0;
					color: #ffffff;
					font-size: 24rpx;
					padding-block: 8rpx;
					padding-inline: 12rpx;
					border-radius: 8rpx;
				}

				.info-end-btn {
					border: 2rpx solid #4f8cf0;
					color: #4f8cf0;
					font-size: 24rpx;
					padding-block: 8rpx;
					padding-inline: 12rpx;
					border-radius: 8rpx;
				}

				.info-start {
					flex: 1;
					display: flex;
					flex-direction: column;
					gap: 16rpx;

					.tags {
						display: flex;
						align-items: center;
						gap: 16rpx;

						.tag {
							background: rgba(188, 213, 255, 0.2);
							border-radius: 8rpx;
							color: #7286a6;
							font-size: 24rpx;
							padding-block: 4rpx;
							padding-inline: 12rpx;
						}

						.tag2 {
							background: #f5f5f5;
							border-radius: 8rpx;
							color: #666666;
							font-size: 20rpx;
							padding-block: 4rpx;
							padding-inline: 12rpx;
						}
					}

					.tip-ide {
						font-size: 24rpx;
						color: #999999;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
					}

					.detal-box {
						flex: 1;
						color: #999999;
						font-size: 24rpx;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
					}

					.name-box {
						display: flex;
						align-items: center;
						gap: 32rpx;

						.name {
							color: #333333;
							font-size: 28rpx;
						}

						.identity {
							color: #4787f0;
							font-size: 20rpx;
							padding-block: 6rpx;
							padding-inline: 8rpx;
							background: rgba(79, 140, 240, 0.2);
							border-radius: 8rpx;
							display: flex;
							align-items: center;
							gap: 4rpx;

							img {
								width: 32rpx;
								height: 32rpx;
							}
						}
					}
				}
			}
		}
	}

	.item-type-2 {
		display: flex;
		flex-direction: column;
		align-items: center;

		.item-end {
			width: 80%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx;
			background: linear-gradient(90deg, #f5fcfc 0%, #fcfbfa 100%);
			border-end-start-radius: 16rpx;
			border-end-end-radius: 16rpx;
			font-size: 24rpx;

			.time {
				color: #333333;
			}

			.detail {
				color: #4f8cf0;
			}
		}

		.item-start {
			width: 100%;
			box-sizing: border-box;
			display: flex;
			align-items: center;
			gap: 24rpx;
			background-color: #ffffff;
			border-radius: 16rpx;
			padding: 32rpx;

			.start-info {
				display: flex;
				flex-direction: column;
				gap: 16rpx;
				align-items: flex-start;

				.title {
					color: #333333;
					font-size: 32rpx;
				}

				.time {
					color: #999999;
					font-size: 28rpx;
				}

				.tag {
					padding-block: 8rpx;
					padding-inline: 16rpx;
					background: rgba(87, 213, 28, 0.1);
					border-radius: 8rpx;
					color: #57d51c;
					font-size: 24rpx;
				}
			}

			.image {
				width: 196rpx;
				height: 196rpx;
			}
		}
	}

	.item-type-1-warp {
		.btm {
			display: flex;
			align-items: center;
			font-size: 24rpx;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 16rpx;
			}
		}
	}

	.item-type-1 {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 32rpx;
		margin: 24rpx 0;

		.item-end {
			display: flex;
			align-items: center;
			justify-content: space-between;
			color: #333333;
			font-size: 24rpx;
		}

		.item-center {
			color: #666666;
			font-size: 28rpx;
		}

		.item-start {
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 32rpx;

			.title {
				color: #333333;
			}

			.sub-title {
				color: #f98a14;
			}
		}
	}

	.item {
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 32rpx;
		display: flex;
		justify-content: space-between;
		// flex-direction: column;
		gap: 24rpx;

		.right {
			image {
				width: 176rpx;
				height: 176rpx;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
			}
		}

		.avater {
			width: 60rpx;
			height: 60rpx;
			border-radius: 50%;
		}

		.item-end {
			display: flex;
			align-items: center;
			gap: 28rpx;

			.name {
				color: #333333;
				font-size: 24rpx;
			}

			.address {
				margin-inline-start: auto;
				color: #999999;
				font-size: 24rpx;
			}
		}

		.item-start {
			width: 400rpx;
			display: flex;
			flex-direction: column;
			gap: 24rpx;

			.time {
				color: #999999;
				font-size: 24rpx;
				overflow: hidden;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
			}

			.type-text {
				font-size: 28rpx;
				color: #041024;
			}

			.title-box {
				display: flex;
				align-items: center;
				// justify-content: space-between;
				gap: 12rpx;
				font-size: 24rpx;
				color: #666666;

				.title_1 {
					// flex: 1;
					width: 160rpx;
					color: #777777;
					font-size: 24rpx;
					overflow: hidden;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 1;
				}

				.title_2 {
					color: #f98a14;
					font-size: 36rpx;
					white-space: nowrap;
				}
			}
		}
	}

	::v-deep .vue-ref {
		padding-bottom: 0rpx !important;
	}
</style>

<style lang="scss" scoped>
	@import url("../../static/css/pagesC/schoolIntro/commonCss.css");
</style>