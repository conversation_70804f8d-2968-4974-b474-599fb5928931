.item {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 0 32rpx;
    margin-bottom: 32rpx;
    background-color: #FFFFFF;
    border-radius: 24rpx;
    
    .item-up {
        display: flex;
        flex-direction: column;
        padding: 24rpx 0;
        border-bottom: 2rpx solid #E6E6E6FF;
		padding-bottom: 32rpx;
		margin-bottom: 32rpx;
        .info {
            display: flex;
            
            & > image {
                width: 96rpx;
                height: 96rpx;
            }
            
            .cont {
                display: flex;
                justify-content: space-between;
                flex-direction: column;
                // padding-left: 24rpx;
                flex: 1;
                
                .sup {
                    display: flex;
                    
                    .name {
                        font-weight: 600;
                        font-size: 32rpx;
                        color: #333333;
                    }
                    
                    .money {
                        font-weight: 600;
                        font-size: 32rpx;
                        color: #4F8CF0;
                        margin-left: 16rpx;
                    }
                }
                
                .sub {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #999999;
					margin-top: 24rpx;
                }
            }
            
        }
        
        .date {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 24rpx;
            
            .time {
                font-weight: 400;
                font-size: 24rpx;
                color: #666666FF;
            }
        }
			
		.jobActive{
			font-weight: 400;
			font-size: 24rpx;
			color: #666666FF;
			 margin-top: 24rpx;
		}
        
    }
    
    .item-down {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        padding: 0 0 24rpx 0;
        
        .btns {
            display: flex;
            
            .btn {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-left: 24rpx;
                border-radius: 8rpx;
                font-weight: 600;
                padding: 0 24rpx;
                height: 56rpx;
                font-size: 24rpx;
                color: #FFFFFF;
                
                &:first-child {
                    margin-left: 0;
                }
            }
            
            .agree {
                background-color: #4F8CF0;
                color: #FFFFFF;
            }
            
            .refuse {
                background-color: #FE4D4F;
                color: #FFFFFF;
            }
            
            .talk {
                background: linear-gradient(to right, #4F8CF0FF,#0061FFFF);
                color: #FFFFFFFF;
            }
				
			.Cancel{
				background-color: #CCCCCCFF;
				color: #777777FF;
			}
				
			.posts{
				background-color: #ECF5FFFF;
				color: #4F8CF0FF;
			}
        }
        
        .reason {
            font-weight: 600;
            font-size: 24rpx;
            color: #FE4D4F;
        }
    }
    
    .flexRow {
        display: flex;
        flex-direction: row-reverse;
    }
    
    .status {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 0;
        top: 0;
        font-size: 28rpx;
        width: 132rpx;
        height: 62rpx;
        border-radius: 0 24rpx 0 24rpx;
    }
    
    .wait {
        background: #4F8CF0;
        color: #FFFFFF;
    }
    
    .yqx {
        background: #cccccc;
        color: #FFFFFF;
    }
    
}

.greenBtn {
    background: #57D51C;
    color: #FFFFFF;
}

.redBtn {
    background: #FE4D4F;
    color: #FFFFFF;
}

.yellowBtn {
    background: #F9AD14;
    color: #FFFFFF;
}

.greyBtn {
    background: #cccccc;
    color: #FFFFFF;
}
	
.blue{
	background: #4F8CF0FF;
	color: #FFFFFF;
}
