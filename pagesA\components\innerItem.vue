<template>
  <view class="list">
    <view
      class="innerItem"
      v-for="(item, index) in list"
      :key="item.id"
      @click.stop="handleSelect(item)"
    >
      <view class="content">
        <view class="flag">
          <image src="../static/images/shugou.png" mode=""></image>
        </view>
        <view class="logo">
          <image
            :src="
              item.member_info.image['path_url'] ||
              'https://api-test.zhaopinbei.com/storage/uploads/images/41R1LgOXldsXlyAjelHeNXsb0SuBPKE7ANcgobdG.png'
            "
            mode=""
          ></image>
        </view>
        <view class="name">
          {{ item.name || item.member_certification.name }}
        </view>

        <!-- <view class="selected">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/1lNYcWzh5C62qWYX8IANZLzFD9mRq0iAxPO93muN.png" v-if="man.some(v=>v.id==item.id)" mode="">
					</image>
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/vEOx112PFSHNqvGbR2a4pIQcUkYARURwT0KgsvtD.png" v-else mode=""></image>
				</view> -->

        <view
          style="transition: transform 0.3s"
          :class="['down', isOpen(item.id) ? 'negative-rotate' : '']"
          v-if="item.children && item.children.length > 0"
          @click.stop="itemNodeClick(item)"
        >
          <image src="../static/images/down.png" mode=""></image>
        </view>
      </view>

      <inner-item
        @tree-node-click="$emit('tree-node-click', $event)"
        :list="item.children"
        v-show="item.children && item.children.length > 0 && isOpen(item.id)"
      ></inner-item>
    </view>
  </view>
</template>

<script>
import InnerItem from "./innerItem";
export default {
  name: "innerItem",
  components: {
    InnerItem,
  },
  props: {
    flag: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },

    list: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    //监听当前点击节点的id
    current: {
      handler(num) {
        this.curNameId = num;
      },
      deep: true,
      immediate: true,
    },
  },

  computed: {
    isOpen() {
      return function (id) {
        // 判断节点id在不在数组中，在则显示，不在则隐藏
        return this.expandedKeys.includes(id);
      };
    },
    // executor(){
    // 	return this.$store.state.executor
    // },
    // collaborator(){
    // 	return this.$store.state.collaborator
    // }
  },

  data() {
    return {
      man: [],
      // collaborator: [],
      expandedKeys: [], // 当前列表需要展开的节点id组成的数组
      curNameId: 0, //保存current的值
    };
  },
  methods: {
    handleSelect(item) {
      let list = [];
      if (this.type == "single") {
        //表示执行人只能单选
        this.man = [item];
      } else {
        console.log("协作人：", item);
        if (this.man.length == 0) {
          this.man.push(item);
          console.log("添加");
        } else {
          let isExsit = this.man.some((v) => v.id == item.id);
          if (isExsit) {
            let index = this.man.findIndex((v) => v.id == item.id);
            this.man.splice(index, 1);
          } else {
            this.man.push(item);
          }
        }
      }

      console.log("选中：", this.man);

      this.$emit("selected", this.man);
    },
    itemNodeClick(item) {
      console.log("1111执行");
      this.$emit("tree-node-click", item);
      if (item.children && item.children.length) {
      	let index = this.expandedKeys.indexOf(item.id)
      	if (index > -1) {
      		// 如果当前节点id存在数组中，则删除
      		this.expandedKeys.splice(index, 1)
      	} else {
      		// 如果当前节点id不存在数组中，则添加
      		this.expandedKeys.push(item.id)
      	}
      }
    },
  },
};
</script>

<style lang="less" scoped>
.list {
  background-color: #ffffff;
}

.negative-rotate {
  transform: rotate(-180deg);
}

.innerItem {
  padding-left: 32rpx;

  .content {
    position: relative;
    display: flex;
    align-items: center;
    // padding-left: 32rpx;
    height: 120rpx;
    border-bottom: 1px solid #f5f5f7;

    .flag {
      image {
        width: 32rpx;
        height: 32rpx;
      }
    }

    .logo {
      width: 72rpx;
      height: 72rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .name {
      flex: 1;
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      padding-left: 16rpx;
    }

    .selected {
      position: absolute;
      // margin-right: 20rpx;
      right: 60rpx;

      image {
        width: 40rpx;
        height: 40rpx;
      }
    }

    .down {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 60rpx;
      height: 60rpx;

      image {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}
</style>
