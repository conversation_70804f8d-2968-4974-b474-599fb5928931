<template>
	<view class="warp">
		<view class="top">
			<view class="arrow">
				<u-icon name="arrow-left" color="#fff" size="20" @click="back"></u-icon>
			</view>
			<view class="top-title">
				微信号
			</view>
			<view class="top-title1">
				检查你的微信号使用状态，用于向你展示在求职过程中你的微信号对伯乐的公开状态
			</view>
		</view>
		<view class="inner">
			<view class="box">
				<view class="title">
					{{cont.title}}
				</view>
				<view class="btm" v-for="item in cont.contents">
					<view class="box-top"></view>
					<view class="cont-inn">
						{{item}}
					</view>
				</view>
			</view>
		</view>
		<view class="detail">
			<view class="deta-txt">
				查看详情
			</view>
			<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAMZJREFUWEft1jEOAiEQBdCZregtLCz0KB7EZG04gdfYnhAo5CYWHsIrWNhvQYjZ3ur/SUgM9OQ/PoSMSuelnfNlAEYDUAMppX1r7eKcu8/z/GEeMgSIMd5EZBGR1zRNZ+/9G0VAgBDCQVWfqnpiERBgO23O+VhrfbAIGGCFoAAWCBrAIkwADMIMgCJMAQjivwDI32DWABK+XZkJAA03ATDhNIANpwAW4TDAKhwGdB9ISim7dV2v3UYydPz6tc/kH2BAAzAa+AIR6qkhBYG27AAAAABJRU5ErkJggg==" alt="" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				cont: {
					title: '伯乐如何获取你的微信号',
					contents: ['在聊天进行发起交换时，伯乐会获取你的微信号，可能会通过微信与你联系']
				}
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.warp {
		width: 100vh;
		background: #F5F5F7;
		position: relative;
		top: 0;

		.top {
			height: 464rpx;
			width: 686rpx;
			z-index: -1;
			background: linear-gradient(360deg, #F5F5F7 0%, #9DD3FF 18%, #4F75F0 100%);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			// filter: blur(3.5999999046325684px);
			position: absolute;
			top: 0;
			padding: 0 32rpx;

			.arrow {
				margin-top: 116rpx;
			}

			.top-title {
				font-size: 32rpx;
				margin-top: 52rpx;
				color: #FFFFFF;
			}

			.top-title1 {
				margin-top: 28rpx;
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}

		.inner {
			width: 686rpx;
			position: absolute;
			top: 380rpx;
			left: 32rpx;

			.box {
				width: 638rpx;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				padding: 32rpx 24rpx 10rpx 24rpx;
				margin-bottom: 40rpx;
				background: #FFFFFF;

				.btm {
					margin-bottom: 40rpx;
				}

				.title {
					font-size: 28rpx;
					color: #333333;
					margin-bottom: 16rpx;
				}

				.tip {
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 24rpx;
				}

				.box-top {
					width: 638rpx;
					height: 152rpx;
					background: #D9D9D9;
					border-radius: 24rpx 24rpx 0 0;
					margin-bottom: 24rpx;
				}

				.cont-inn {
					font-size: 24rpx;
					color: #666666;
				}

			}
		}

		.detail {
			width: 686rpx;
			padding: 32rpx 0;
			background: #FFFFFF;
			border-radius: 24rpx 24rpx 24rpx 24rpx;
			font-size: 28rpx;
			color: #333333;
			display: flex;
			align-items: center;
			position: absolute;
			top: 760rpx;
			left: 32rpx;

			.deta-txt {
				margin-left: 32rpx;
			}

			img {
				width: 32rpx;
				height: 32rpx;
			}
		}
	}
</style>
