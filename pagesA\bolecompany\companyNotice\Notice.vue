<template>
	<div class="popup-modal-container">
		<div class="mask" @click="closeModal"></div>
		<div class="popup-modal">
			<div class="header">
				<div class="title">公司信息填写须知</div>
				<div class="close" @click="closeModal">×</div>
			</div>
			<div class="content">
				<p>您在本页面编辑、上传的公司信息将作为招聘场景下的公司主页展示，供求职者浏览。为保障求职者合法权益并确保您的招聘效果，您承诺并确认：</p>
				<ul>
					<li>1、您的填写行为已获得所在公司的授权，相关内容经过公司确认。</li>
					<li>2、填写、上传真实、合法、有效的公司信息，据实填写并及时更新公司介绍、规模、福利等，以免对求职者造成误导。</li>
					<li>3、填写、上传的图文、视频等资料不存在违法违规或涉嫌侵犯第三方合法权益的情形，如由此产生相关法律风险或造成损失，由您及所在公司承担法律责任。</li>
					<li>4、您授权BOSS直聘以提供招聘服务为目的在其他场景下免费使用您在本页面填写的公司信息，以便为您提供更佳的服务体验。</li>
				</ul>
			</div>
			<div class="footer">
				<button class="confirm-btn" @click="confirmAndClose">我知道了</button>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		props: {
			visible: {
				type: Boolean,
				default: false
			}
		},
		methods: {
			closeModal() {
				this.$emit('close');
			},
			confirmAndClose() {
				this.$emit('close');
			}
		}
	}
</script>

<style scoped>
	.popup-modal-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 999;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
	}

	.popup-modal {
		background-color: white;
		/* padding: 16px; */
		border-radius: 5px;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
		width: 301px;
		max-width: 500px;
		position: relative;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
		width: 241px;
		margin: 16px auto;
	}

	.title {
		font-size: 14px;
		font-weight: bold;
		color: rgba(51, 51, 51, 1);
	}

	.close {
		font-size: 20px;
		cursor: pointer;
	}

	.content {
		margin-bottom: 10px;
		width: 241px;
		margin: 0 auto;
	}

	.content p {
		line-height: 1.6;
		color: rgba(119, 119, 119, 1);
		font-size: 12px;
	}

	/* 	.content ul {
		padding-left: 20px;
	} */

	.content li {
		line-height: 1.6;
		color: rgba(119, 119, 119, 1);
		font-size: 12px;
	}

	.footer {
		text-align: right;
		display: flex;
		justify-content: end;
		margin-bottom: 16px;
		margin-right: 16px;
		margin-top: 16px;
	}

	.confirm-btn {
		width: 67px;
		height: 26px;
		font-size: 12px;
		background-color: #1989fa;
		color: white;
		border: none;
		border-radius: 3px;
		cursor: pointer;
		margin-right: 16px;
		text-align: center;
		padding: 0px;
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>