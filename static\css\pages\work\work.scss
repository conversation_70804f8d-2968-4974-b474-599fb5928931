page {
    background: #F5F5F7;
}

.home-index {
    padding-bottom: 128rpx;
}

.topClass {
    position: inherit;
    width: 100%;
    z-index: 10;
    transition: all .5s;
    background: #FFFFFF;
    
    &.isScroll {
        position: fixed;
        top: 0;
        left: 0;
    }
    
    .top_content {
        display: flex;
        align-items: center;
        height: 100%;
        
        .logo {
            display: flex;
            position: absolute;
            
            image {
                width: 126rpx;
                height: 44rpx;
            }
        }
        
        .title {
            display: flex;
            flex: 1;
            justify-content: center;
            font-weight: 500;
            font-size: 34rpx;
            color: #000000;
        }
    }
}

.header {
    padding: 16rpx 32rpx 32rpx 32rpx;
	
    
    .search-wrap {
    }
    
    .filters {
        display: flex;
        margin-top: 32rpx;
        
        .filter {
            display: flex;
            align-items: center;
            height: 48rpx;
            background-color: #FFFFFF;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding: 0 12rpx;
            margin-right: 12rpx;
            border-radius: 8rpx;
            
            image {
                width: 24rpx;
                height: 24rpx;
            }
        }
    }
}

.list {
    padding: 0 32rpx;
}