<template>
	<view class="container">
		<scroll-view scroll-y="true" class="scroll-Y">
			<view class="skill-container">
				<!-- 参考技能词 -->
				<view class="selected-count">
					<view class="title">参考技能词</view>
					<text>{{ selectedSkills.length }}/10</text>
				</view>
				<!-- 技能分类模块 -->
				<view v-for="(category, index) in skillCategories" :key="category.id" class="category">
					<view style="display: flex; justify-content: space-between;">
						<view class="category-title">{{ category.title }}</view>
						<img :src="category.checked ? '/static/jobsetting/top.png' : '/static/jobsetting/down.png'"
							alt="" style="width: 32rpx; height: 32rpx;" @click="toggleCategory(category.id)" />
					</view>

					<view class="skill-items" v-if="category.checked">
						<view v-for="(skill, skillIndex) in category.skills" :key="skill.id" class="skill-item"
							@click="toggleSkill(skill)">
							<text :class="{'selected': isSkillSelected(skill)}">{{ skill.name }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<view>
			<view class="selected-courses">
				<view
					style="width: 96rpx; height: 42rpx; margin-left: 8rpx;font-size: 24rpx;text-align: center;display: flex; align-items: center;">
					已选：
				</view>
				<scroll-view class="scroll-view_H" scroll-x="true">
					<view style="display: flex; align-items: center;">
						<view v-for="(selectedCourse, index) in selectedSkills" :key="index" style="padding:6rpx 8rpx; font-size: 20rpx; background-color: rgba(232, 241, 255, 1); color: rgba(79, 140, 240, 1);
											 margin: 0rpx 8rpx; display: flex; align-items: center;">
							{{ selectedCourse.name }}
							<img src="/static/images/Apages/close.png" mode=""
								style="width: 24rpx; height: 24rpx; align-self: center; margin-left: 6rpx;"
								@click="removeSkill(selectedSkill)">
							</img>
						</view>
					</view>
				</scroll-view>
			</view>
			<!-- 确定按钮 -->
			<view class="footer">
				<button class="confirm-btn" @click="confirmSkills">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				skillCategories: [{
					id: 1,
					title: 'UI设计师',
					checked: true,
					skills: [{
							id: 1,
							name: 'AE',
							selected: false
						},
						{
							id: 2,
							name: 'PhotoShop',
							selected: false
						},
						{
							id: 3,
							name: 'Spine',
							selected: false
						},
						{
							id: 4,
							name: 'CAD',
							selected: false
						},
						{
							id: 5,
							name: 'MAYA',
							selected: false
						},
						{
							id: 6,
							name: 'Premiere',
							selected: false
						},
						{
							id: 7,
							name: 'UI设计',
							selected: false
						},
						{
							id: 8,
							name: '交互体验设计',
							selected: false
						},
						{
							id: 9,
							name: '动效设计',
							selected: false
						},
						{
							id: 10,
							name: 'UE设计',
							selected: false
						}
					]
				}, {
					id: 2,
					title: '大数据类',
					checked: false,
					skills: [{
							id: 1,
							name: 'AE',
							selected: false
						},
						{
							id: 2,
							name: 'PhotoShop',
							selected: false
						},
						{
							id: 3,
							name: 'Spine',
							selected: false
						},
						{
							id: 4,
							name: 'CAD',
							selected: false
						},
						{
							id: 5,
							name: 'MAYA',
							selected: false
						},
						{
							id: 6,
							name: 'Premiere',
							selected: false
						},
						{
							id: 7,
							name: 'UI设计',
							selected: false
						},
						{
							id: 8,
							name: '交互体验设计',
							selected: false
						},
						{
							id: 9,
							name: '动效设计',
							selected: false
						},
						{
							id: 10,
							name: 'UE设计',
							selected: false
						}
					]
				}, {
					id: 3,
					title: '开发编程类',
					checked: false,
					skills: [{
							id: 1,
							name: 'AE',
							selected: false
						},
						{
							id: 2,
							name: 'PhotoShop',
							selected: false
						},
						{
							id: 3,
							name: 'Spine',
							selected: false
						},
						{
							id: 4,
							name: 'CAD',
							selected: false
						},
						{
							id: 5,
							name: 'MAYA',
							selected: false
						},
						{
							id: 6,
							name: 'Premiere',
							selected: false
						},
						{
							id: 7,
							name: 'UI设计',
							selected: false
						},
						{
							id: 8,
							name: '交互体验设计',
							selected: false
						},
						{
							id: 9,
							name: '动效设计',
							selected: false
						},
						{
							id: 10,
							name: 'UE设计',
							selected: false
						}
					]
				}, {
					id: 4,
					title: '多媒体设计类',
					checked: false,
					skills: [{
							id: 1,
							name: 'AE',
							selected: false
						},
						{
							id: 2,
							name: 'PhotoShop',
							selected: false
						},
						{
							id: 3,
							name: 'Spine',
							selected: false
						},
						{
							id: 4,
							name: 'CAD',
							selected: false
						},
						{
							id: 5,
							name: 'MAYA',
							selected: false
						},
						{
							id: 6,
							name: 'Premiere',
							selected: false
						},
						{
							id: 7,
							name: 'UI设计',
							selected: false
						},
						{
							id: 8,
							name: '交互体验设计',
							selected: false
						},
						{
							id: 9,
							name: '动效设计',
							selected: false
						},
						{
							id: 10,
							name: 'UE设计',
							selected: false
						}
					]
				}, {
					id: 5,
					title: '工程制图类',
					checked: false,
					skills: [{
							id: 1,
							name: 'AE',
							selected: false
						},
						{
							id: 2,
							name: 'PhotoShop',
							selected: false
						},
						{
							id: 3,
							name: 'Spine',
							selected: false
						},
						{
							id: 4,
							name: 'CAD',
							selected: false
						},
						{
							id: 5,
							name: 'MAYA',
							selected: false
						},
						{
							id: 6,
							name: 'Premiere',
							selected: false
						},
						{
							id: 7,
							name: 'UI设计',
							selected: false
						},
						{
							id: 8,
							name: '交互体验设计',
							selected: false
						},
						{
							id: 9,
							name: '动效设计',
							selected: false
						},
						{
							id: 10,
							name: 'UE设计',
							selected: false
						}
					]
				}, ],
				selectedSkills: []
			};
		},
		methods: {
			// 切换技能选中状态
			toggleSkill(skill) {
				if (this.isSkillSelected(skill)) {
					this.selectedSkills = this.selectedSkills.filter(s => s.name !== skill.name);
				} else {
					if (this.selectedSkills.length < 10) {
						this.selectedSkills.push(skill);
					}
				}
				skill.selected = !skill.selected;
			},
			// 判断技能是否已被选中
			isSkillSelected(skill) {
				return this.selectedSkills.some(s => s.name === skill.name);
			},
			// 移除已选技能
			removeSkill(skill) {
				this.selectedSkills = this.selectedSkills.filter(s => s.name !== skill.name);
				const targetSkill = this.skillCategories.flatMap(c => c.skills).find(s => s.name === skill.name);
				if (targetSkill) {
					targetSkill.selected = false;
				}
			},
			// 确定选中技能的逻辑（这里可按需补充提交等操作）
			confirmSkills() {
				console.log('确定选中的技能:', this.selectedSkills);
			},
			toggleCategory(id) {
				this.skillCategories.forEach(category => {
					if (category.id === id) {
						category.checked = !category.checked;
					}
				});
			},
		}
	};
</script>

<style scoped>
	.container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.skill-container {
		padding: 0rpx 32rpx;
		display: flex;
		flex-flow: column;
	}

	.scroll-view_H {
		white-space: nowrap;
		/* flex: 1; */
		width: 88%;
	}

	.scroll-Y {
		height: 85vh;
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}

	.selected-count {
		font-size: 28rpx;
		display: flex;
		justify-content: space-between;
	}

	.category {
		margin-bottom: 20rpx;
	}

	.category-title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.skill-items {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
	}

	.skill-item {
		background-color: #f0f0f0;
		/* padding: 10rpx 20rpx; */
		border-radius: 5rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		cursor: pointer;
		width: 208rpx;
		height: 82rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.selected {
		/* background-color: #ccc; */
	}

	.selected-skills {
		margin-top: 20rpx;
		display: flex;
		/* flex-wrap: wrap; */
	}

	/* 	.selected-skill-item {
		background-color: #e0e0e0;
		padding: 10rpx 15rpx;
		border-radius: 5rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		display: flex;
		align-items: center;
	} */

	.remove-icon {
		margin-left: 10rpx;
		font-size: 28rpx;
		cursor: pointer;
	}

	.confirm-button {
		background-color: #007aff;
		color: white;
		text-align: center;
		padding: 15rpx 0;
		border-radius: 5rpx;
		margin-top: 20rpx;
		cursor: pointer;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-image: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.selected-courses {
		margin-top: 30rpx;
		display: flex;
	}
</style>