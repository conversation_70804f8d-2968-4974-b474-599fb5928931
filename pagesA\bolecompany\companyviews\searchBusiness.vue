<template>
	<view class="container">
		<!-- 可滚动的已选标签区域 -->
		<view class="header-scroll">
			<text class="header-text">
				已选
			</text>
			<scroll-view class="selected-tags-scroll" scroll-x="true">
				<view class="selected-tags">
					<view class="tag" v-for="(tag, index) in selectedTags" :key="index">
						<view class="tag-item" v-for="(item, i) in tag" :key="i">{{ item }}</view>
						<!-- <view class="remove-tag" @click="removeTag(index)">×</view> -->
						<image class="close-icon" :src="close" @click="removeTag(index)">
						</image>

					</view>
				</view>
			</scroll-view>
		</view>
		<!-- 搜索栏 -->
		<view class="search-bar">

			<u--input placeholder="请输入关键词搜索" prefixIcon="search" v-model="searchKeyword" border="none"
				prefixIconStyle="font-size: 28rpx; color:rgba(153, 153, 153, 1);line-height:44rpx"></u--input>
		</view>
	</view>
</template>

<script>
	/*import close from '../../static/images/close.png'*/
	export default {
		data() {
			return {
				selectedTags: [
					["五险一金"],
					["五险一金"],
					["五险一金"],
					["五险一金"],
					["五险一金"],
					["五险一金"],
					["五险一金"],
					["五险一金"]
				],
				searchKeyword: "",
				close:'https://api-test.zhaopinbei.com/storage/uploads/images/oTSzUI2bt0qYqMFDRcEudXVx3LrlynCxZAC2jWKD.png'
			};
		},
		methods: {
			removeTag(index) {
				this.selectedTags.splice(index, 1);
			}
		}
	};
</script>

<style>
	.container {
		padding: 10px;
	}

	.header-scroll {
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		margin-top: 16px;
	}

	.header-text {
		width: 48px;
		height: 20px;
		font-size: 15px;
		/* margin-bottom: 10px; */
		line-height: 14.06px;
	}

	.selected-tags-scroll {
		white-space: nowrap;
		overflow-x: auto;
		-webkit-overflow-scrolling: touch;
		margin-bottom: 10px;
	}

	.selected-tags {
		display: inline-flex;
	}

	.tag {
		display: inline-flex;
		align-items: center;
		background-color: #f0f0f0;
		border-radius: 5px;
		/* padding: 5px 10px; */
		margin-right: 10px;
		/* margin-bottom: 10px; */
		/* width: 63px;
		height: 20px; */
		padding: 6rpx 8rpx;
		font-size: 24rpx;
		justify-content: space-evenly;
		color: rgba(79, 140, 240, 1);
		background: rgba(232, 241, 255, 1);
	}

	.tag-item {
		margin-right: 5px;
		font-size: 24rpx;
	}

	.close-icon {
		width: 20rpx;
		height: 20rpx;
		/* position: absolute; */
		right: 5rpx;
		top: 5rpx;
	}

	.search-bar {
		display: flex;
		align-items: center;
		background-color: #f0f0f0;
		border-radius: 20px;
		padding: 5px 10px;
		margin-top: 22px;
	}

	.search-bar input {
		flex: 1;
		border: none;
		background-color: transparent;
	}
</style>
