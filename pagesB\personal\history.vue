<template>
  <!-- 查看求职记录 -->
  <view class="container">
    <scroll-view :scroll-y="true" class="scroll-view">
      <view class="scroll-container">
        <template v-if="numId == '1'">
          <view v-for="_ in 12">
            <view style="margin-bottom: 20rpx" class="time">4月8日</view>
            <view class="item">
              <view class="item-start">
                <image class="image" src="/static/new/火苗@2x.png"></image>
                <text class="text_1">Web 前端</text>
                <text class="text_2">实习/兼职</text>
                <text class="text_3">6-9K</text>
              </view>
              <view class="item-center">
                <view class="title">前端</view>
                <view class="tag-box">
                  <view class="tag">世界500强</view>
                  <view class="tag">上市公司</view>
                  <view class="tag">游戏大厂</view>
                  <view class="find-box">
                    <image
                      class="image"
                      src="/static/new/浏览量****************"
                    ></image>
                    <text class="number">1024</text>
                  </view>
                </view>
                <view class="info-box">
                  <view class="detail">学创北京｜A轮｜0-20人</view>
                  <view class="location-box">
                    <image class="image" src="/static/new/定位@2x.png"></image>
                    <text class="position">北京北京市昌平区1号</text>
                    <view class="distance">距12.1km</view>
                  </view>
                </view>
              </view>
              <u-line></u-line>
              <view class="item-end">
                <image
                  class="image"
                  src="https://api-test.zhaopinbei.com/storage/uploads/images/fAm3AgZSBHCsSb9A56QAAlxS9lcv24ezx9Lwzg51.png"
                ></image>
                <text class="text margin-text">平台保障</text>
                <image
                  class="image"
                  src="https://api-test.zhaopinbei.com/storage/uploads/images/OKHqUrYjgtO8nsbNaKp4h73DbHQHzIzZBczc5ScK.png"
                ></image>
                <text class="text">平台自聘</text>
              </view>
            </view>
          </view>
        </template>
        <template v-if="numId == '2'">
          <view class="content">
            <view style="margin-bottom: 20rpx" class="time">4月8日</view>
            <view class="jl-item" v-for="v in 12" :key="v" @click="onDetail(v)">
              <view class="item-start">
                <view class="title-box">
                  <view class="title-inner">
                    <image
                      class="avater"
                      :src="v.company.company_logo.thumb_url"
                    ></image>
                    <view class="">
                      <view class="tit-inn">
                        <view class="title_1">王先生</view>
                        <view class=""> ui设计师 </view>
                        <view class="title_2"> 10-20k </view>
                      </view>
                      <view class="tit-inn">
                        <view class="age"> 5年｜23岁 | 本科 </view>
                        <view class="title_3"> 实习/兼职 </view>
                      </view>
                    </view>
                  </view>
                </view>

                <view class="time">个人优势个人优势个人优势</view>
                <u-line></u-line>
                <view class="item-end">
                  <text class="name">求职状态: 在校-考虑机会</text>
                  <view class="address">
                    <view class="radi"> </view>
                    <view class=""> 在线 </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import interviewVue from "../jobSearch/interview.vue";
export default {
  components: {
    interviewVue,
  },
  data() {
    return {
      numId: "1",
      tabsList: [
        {
          key: "1",
          name: "浏览",
        },
        {
          key: "2",
          name: "仅沟通",
        },
        {
          key: "3",
          name: "仅交换",
        },
        {
          key: "4",
          name: "约面",
        },
        {
          key: "5",
          name: "面试",
        },
        {
          key: "6",
          name: "反馈",
        },
      ],
      tabsIndex: 0,
      tabsValue: "1",
      jobList: [
        {
          date: "4月9日",
          jobTitle: "产品ui设计师",
          jobSalary: "4-8K",
          status: "待评价",
          time: "10:10",
          tags: ["世界500强", "上市公司", "游戏大厂"],
          location: "北京北京市昌平区1号",
          continueBtnText: "继续沟通",
        },
        {
          date: "4月8日",
          jobTitle: "产品ui设计师",
          jobSalary: "4-8K",
          status: "已取消",
          time: "10:10",
          tags: ["世界500强", "上市公司", "游戏大厂"],
          location: "北京北京市昌平区1号",
          continueBtnText: "继续沟通",
        },
        {
          date: "4月8日",
          jobTitle: "产品ui设计师",
          jobSalary: "4-8K",
          status: "已超时",
          time: "10:10",
          tags: ["世界500强", "上市公司", "游戏大厂"],
          location: "北京北京市昌平区1号",
          continueBtnText: "继续沟通",
        },
      ],
      isTimePicker: false,
      viewedhide: false,
      interviewhide: false,
      time: Number(new Date()),
    };
  },
  onLoad(item) {
    this.numId = item.id;
    this.tabsValue = this.numId == 1 ? "2" : "1";
    this.tabsList =
      this.numId == 1 && this.tabsList.filter((el) => el.key !== "1");
  },
  created() {
    this.viewedhide = true;
  },
  methods: {
    onTabsItemClick(v) {
      this.tabsValue = v.key;
      this.tabsIndex = v.index;
      console.log(v);
      if (v.key === "5") {
        console.log(333);
        this.viewedhide = false;
        this.interviewhide = true;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f5f5f7;
  display: flex;
  flex-direction: column;

  .sticky-container {
    display: flex;
    flex-direction: column;
    padding-inline: 32rpx;
    gap: 32rpx;
    padding-block-start: 32rpx;
    background-color: #ffffff;

    .tabs-container {
      .tags-box {
        display: flex;
        align-items: center;
        gap: 16rpx;
        transition: all 0.2s ease-in;
        overflow: hidden;

        .tag {
          margin-block: 24rpx;
          padding-inline: 16rpx;
          padding-block: 6rpx;
          color: #777777;
          font-size: 24rpx;
          background-color: #ffffff;
          border: 1rpx #e6e6e6 solid;
          border-radius: 10rpx;
        }

        .is_active {
          background-color: #e8f1ff;
          color: #4f8cf0;
          border-color: #4f8cf0;
        }
      }
    }

    .title-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        color: #333333;
        font-size: 32rpx;
      }

      .sub-title {
        color: #666666;
        font-size: 28rpx;
      }
    }

    .desc {
      color: #666666;
      font-size: 24rpx;
    }
  }

  .scroll-view {
    flex: 1;
    overflow-y: auto;

    .scroll-container {
      display: flex;
      flex-direction: column;
      padding-inline: 32rpx;
      padding-block-start: 32rpx;
      gap: 32rpx;
      padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
      padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

      .time {
        color: #666666;
        font-size: 24rpx;
      }

      .item {
        background-color: #ffffff;
        border-radius: 24rpx;
        padding: 32rpx;
        display: flex;
        flex-direction: column;
        gap: 24rpx;

        .item-end {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 4rpx;

          .image {
            width: 48rpx;
            height: 48rpx;
          }

          .text {
            color: #666666;
            font-size: 20rpx;
          }

          .margin-text {
            margin-inline-end: auto;
          }
        }

        .item-center {
          display: flex;
          flex-direction: column;
          gap: 24rpx;

          .info-box {
            display: flex;
            flex-direction: column;
            gap: 12rpx;

            .detail {
              color: #666666;
              font-size: 24rpx;
            }

            .location-box {
              display: flex;
              align-items: center;
              gap: 12rpx;

              .image {
                width: 32rpx;
                height: 32rpx;
              }

              .position {
                color: #666666;
                font-size: 24rpx;
              }

              .distance {
                color: #666666;
                font-size: 20rpx;
                padding-inline: 8rpx;
                padding-block: 4rpx;
                border-radius: 999rpx;
                background-color: #f6f6f6;
              }
            }
          }

          .title {
            color: #333333;
            font-size: 28rpx;
          }

          .tag-box {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .tag {
              background-color: #f6f6f6;
              color: #666666;
              padding: 12rpx;
              border-radius: 8rpx;
              font-size: 22rpx;
            }

            .find-box {
              display: flex;
              align-items: center;
              margin-inline-start: auto;

              .image {
                width: 32rpx;
                height: 32rpx;
              }

              .number {
                color: #666666;
                font-size: 20rpx;
              }
            }
          }
        }

        .item-start {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 12rpx;

          .image {
            width: 48rpx;
            height: 48rpx;
          }

          .text_1 {
            color: #041024;
            font-size: 32rpx;
          }

          .text_2 {
            color: #4f8cf0;
            font-size: 32rpx;
            margin-inline: auto;
          }

          .text_3 {
            color: #f98a14;
            font-size: 40rpx;
          }
        }
      }
      .jl-item {
        background-color: #ffffff;
        border-radius: 24rpx;
        padding: 32rpx;
        display: flex;
        justify-content: space-between;
        gap: 24rpx;
        margin-bottom: 24rpx;
        .avater {
          width: 104rpx;
          height: 104rpx;
          border-radius: 50%;
        }

        .item-end {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 28rpx;

          .name {
            color: #666;
            font-size: 24rpx;
          }

          .address {
            margin-inline-start: auto;
            color: #4f8cf0;
            font-size: 24rpx;
            display: flex;
            align-items: center;

            .radi {
              width: 8rpx;
              height: 8rpx;
              border-radius: 4rpx;
              background: #4f8cf0;
              margin-right: 12rpx;
            }
          }
        }

        .item-start {
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 24rpx;

          .time {
            color: #666;
            font-size: 24rpx;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
          }

          .type-text {
            font-size: 28rpx;
            color: #041024;
          }

          .title-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12rpx;
            font-size: 32rpx;
            color: #333333;

            .title-inner {
              display: flex;
              align-items: center;
              gap: 12rpx;

              .tit-inn {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 12rpx;

                .age {
                  font-size: 24rpx;
                  color: #666666;
                }
              }
            }

            .title_1 {
              // flex: 1;
              width: 160rpx;

              overflow: hidden;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
            }

            .title_2 {
              color: #f98a14;
              white-space: nowrap;
              margin-left: 100rpx;
            }

            .title_3 {
              color: #4f8cf0;
              white-space: nowrap;
              margin-left: 100rpx;
            }
          }
        }
      }
    }
  }
}
</style>
