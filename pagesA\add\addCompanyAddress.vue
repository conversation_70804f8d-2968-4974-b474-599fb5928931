<template>
	<view class="">
		<view class="wrap" v-if="roleType=='headhunters'">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						关联公司<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="bindPickerChange" :value="sexIndex" :range="sex">
							<view class="d-picker">{{sex[sexIndex]}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</view>
		</view>
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						工作城市<text class="star">*</text>
					</view>
					<view class="in se">
						<uni-data-picker :map="map" placeholder="请选择工作地址" popup-title="请选择所在地区" :localdata="cityList"
							:value="form.district_id" @change="onchange">
						</uni-data-picker>
					</view>
				</view>

				<view class="inp-item" @click="choosePosition">
					<view class="title">
						工作地址<text class="star">*</text>
					</view>
					<view class="in se">
						{{form.map_address?form.map_address:'请选择地址'}}
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						楼层/单元室/门牌号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入楼层/单元室/门牌号" border="none" fontSize="32rpx"
							v-model="form.address"></u--input>
					</view>
				</view>
			</view>
		</view>

		<!-- <view class="wrap">
			<view class="inp">
				<view class="default">
					<view class="name">
						设为默认地址
					</view>
					<u-switch v-model="form.is_default" :activeValue='1' :inactiveValue='2' size="18"></u-switch>
				</view>
			</view>
		</view> -->

		<view class="footer">
			<view class="next sure" @click="add">
				新建地址
			</view>
		</view>
	</view>
</template>

<script>
	import {
		saveCompanyAddress,
		getCompanyAddressDetails
	} from "../../config/api.js"
	export default {
		data() {
			return {
				id: '',
				map: {
					text: 'label',
					value: 'value'
				},
				sex: ['男', '女'],
				sexIndex: 0,
				expect_address: '123',

				form: {
					province_id: "",
					city_id: "",
					district_id: "",
					address: "",
					map_address: "",
					lat: "",
					lng: "",
					remark: "",
					// is_default: 2
				}
			}
		},
		computed: {
			roleType() {
				return this.$store.state.roleType || uni.getStorageSync('roleType')
			},
			sysData() {
				return this.$store.state.sysData || uni.getStorageSync('sysData')
			},
			cityList() {
				return this.$store.state.cityList || uni.getStorageSync('cityList')
			}
		},

		onLoad(options) {
			let id = options.id
			this.id = id
			if (this.id) {
				this.getCompanyAddressDetails()
			}
		},

		methods: {
			onchange(e) {
				// console.log("工作地址：", e);
				let data = e.detail.value
				this.form.province_id = data[0]['value']
				this.form.city_id = data[1]['value']
				this.form.district_id = data[2]['value']
				this.expect_address = data.map(item => item.text).join('')
				// console.log("期望地址：", this.expect_address)
			},

			//选择地图地址
			choosePosition() {
				console.log(1)
				let self = this;
				uni.chooseLocation({
					success: function(res) {
						// console.log('位置名称：' + res.name);
						// console.log('详细地址：' + res.address);
						// console.log('纬度：' + res.latitude);
						// console.log('经度：' + res.longitude);
						self.form.lat = res.latitude
						self.form.lng = res.longitude
						self.form.map_address = res.address
					}
				});
			},



			//添加编辑地址
			async add() {
				let params = null;
				if (this.id) {
					params = {
						id: this.id,
						...this.form
					}
				} else {
					params = {
						...this.form
					}
				}

				const {
					status_code,
					data
				} = await saveCompanyAddress(params)
				if (status_code == 200) {
					return uni.$u.toast('成功')
				}
			},

			//获取地址详情
			async getCompanyAddressDetails() {
				let params = {
					id: this.id
				};
				const {
					status_code,
					data
				} = await getCompanyAddressDetails(params)
				this.expect_address = data.province_name + '/' + data.city_name + '/' + data.district_name

				const {
					province_id,
					city_id,
					district_id,
					address,
					map_address,
					lat,
					lng,
					remark,

				} = data

				this.form = {
					province_id,
					city_id,
					district_id,
					address,
					map_address,
					lat,
					lng,
					remark,
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.wrap {
		padding: 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;

			.default {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 32rpx;

				.name {
					font-weight: 500;
					font-size: 28rpx;
					color: #333333;
				}
			}

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					::v-deep uni-data-picker {
						width: 100%;
					}

					::v-deep .arrow-area {
						transform: rotate(-135deg);
					}

					::v-deep .input-arrow {
						width: 20rpx;
						height: 20rpx;
						border-left: 1px solid #606266;
						border-bottom: 1px solid #606266;
					}

					::v-deep .input-value-border {
						border: none;
					}

					::v-deep .input-value {
						padding: 0;
					}

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}

					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}



				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 16rpx 16rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			font-weight: 600;
			font-size: 28rpx;
			border-radius: 16rpx;
		}

		.sure {
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	}
</style>