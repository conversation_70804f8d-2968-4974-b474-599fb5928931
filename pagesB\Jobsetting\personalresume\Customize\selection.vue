<template>
	<view class="container">
		<view>

			<!-- 标题及提示信息 -->
			<view class="header">
				<text class="title">主修课程</text>
				<text class="count">{{ selectedCourses.length }}/5</text>
			</view>
			<text class="tips">请选择含金量高的课程，最多不超过5个</text>
			<!-- 搜索框 -->
			<view class="search-box">
				<!-- <input type="text" placeholder="搜索主修课程" v-model="searchKeyword" /> -->
				<u--input placeholder="搜索岗位名称" prefixIcon="search" prefixIconStyle="font-size: 22px;color: #909399"
					style="background-color: rgba(243, 243, 243, 1);	margin-top: 32rpx;" shape="circle"></u--input>
			</view>
		</view>
		<!-- 猜你想选区域 -->
		<view class="recommend-section">
			<text class="recommend-title">猜你想选</text>
			<view class="course-list">
				<view v-for="(course, index) in recommendCourses" :key="index" class="course-item"
					@click="selectCourse(course)" :class="{ selected: selectedCourses.includes(course) }">
					{{ course }}
				</view>
			</view>
		</view>
		<!-- 已选课程展示 -->
		<view>
			<view class="selected-courses">
				<view
					style="width: 96rpx; height: 42rpx; margin-left: 8rpx;font-size: 24rpx;text-align: center;display: flex; align-items: center;">
					已选：
				</view>
				<scroll-view class="scroll-view_H" scroll-x="true">
					<view style="display: flex; align-items: center;">
						<view v-for="(selectedCourse, index) in selectedCourses" :key="index" style="padding:6rpx 8rpx; font-size: 20rpx; background-color: rgba(232, 241, 255, 1); color: rgba(79, 140, 240, 1);
											 margin: 0rpx 8rpx; display: flex; align-items: center;">
							{{ selectedCourse }}
							<img src="/static/images/Apages/close.png" mode=""
								style="width: 24rpx; height: 24rpx; align-self: center; margin-left: 6rpx;"
								@click="removeCourse(selectedCourse)">
							</img>
						</view>
					</view>
				</scroll-view>
			</view>
			<!-- 保存按钮 -->
			<view class="footer">
				<button class="confirm-btn" @click="saveCourses">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				searchKeyword: '',
				recommendCourses: ['AE', 'PhotoShop', 'Spine', 'CAD', 'MAYA', '交互设计师体验'],
				selectedCourses: []
			};
		},
		methods: {
			selectCourse(course) {
				if (this.selectedCourses.length < 5 && !this.selectedCourses.includes(course)) {
					this.selectedCourses.push(course);
				}
			},
			removeCourse(course) {
				const index = this.selectedCourses.indexOf(course);
				if (index > -1) {
					this.selectedCourses.splice(index, 1);
				}
			},
			saveCourses() {
				// 这里可以添加保存逻辑，比如调用接口将已选课程数据发送到后端
				console.log('保存的课程:', this.selectedCourses);
			}
		}
	};
</script>

<style scoped>
	.container {
		padding: 0rpx 20rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.search-box {
		height: 64rpx;
	}

	.scroll-view_H {
		white-space: nowrap;
		/* flex: 1; */
		width: 88%;
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.count {
		font-size: 28rpx;
		color: #999;
	}

	.tips {
		font-size: 28rpx;
		color: #666;
		margin-top: 26rpx;
		margin-bottom: 32rpx;
	}

	.search-box input {
		width: 100%;
		border: none;
	}

	.recommend-section {
		margin-top: 32rpx;
		flex: 1;
	}

	.recommend-title {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 24rpx;
	}

	.course-list {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
		/* justify-content: space-between; */
	}

	.course-item {
		/* border: 1px solid #ccc; */
		border-radius: 12rpx;
		/* padding: 15rpx 20rpx;
		margin-bottom: 15rpx; */
		font-size: 28rpx;
		cursor: pointer;
		width: 208rpx;
		height: 82rpx;
		background-color: rgba(245, 245, 245, 1);
		align-items: center;
		display: flex;
		justify-content: center;
	}

	.selected {
		background-color: #f0f0f0;
	}

	.selected-courses {
		margin-top: 30rpx;
		display: flex;
	}

	.selected-course {
		background-color: #e0e0e0;
		border-radius: 10rpx;
		padding: 10rpx 15rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		font-size: 28rpx;
		position: relative;
	}

	.close {
		color: #999;
		margin-left: 5rpx;
		cursor: pointer;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}
</style>