<template>
  <view class="">
    <u-sticky bgColor="#F5F5F5">
      <view class="header">
        <view class="search-wrap">
          <u-search
            placeholder="请输入关键字"
            bgColor="#FFFFFF"
            :showAction="false"
            v-model="keyword"
          ></u-search>
        </view>
      </view>
    </u-sticky>

    <view class="wrap" v-if="topUser.id > 0">
      <view class="title"> 我的上游 </view>
      <view class="list">
        <view class="item" @click="selected">
          <view class="logo">
            <image :src="topUser.image" mode=""></image>
          </view>
          <view class="name">
            {{ topUser.name }}
          </view>
        </view>
      </view>
    </view>
    <view class="wrap">
      <view class="title"> 我的下游 </view>
      <block v-if="list && list.length > 0">
        <inner-item
          :list="list"
          :current="curNameId"
          :type="type"
          :flag="flag"
          @tree-node-click="nodeClick"
          @selected="selected"
        ></inner-item>
      </block>
    </view>

    <view class="btn" @click="ok" v-if="flag == 'edit'"> 我已选好执行人 </view>
  </view>
</template>

<script>
import { getUserTree } from "../../config/api.js";
import InnerItem from "../components/innerItem.vue";
export default {
  components: {
    InnerItem,
  },
  data() {
    return {
      flag: "",
      type: "",
      curNameId: 0,
      selectedList: [],
      list: [],
      topUser: {
        id: 0,
        name: "",
        image: "",
      },
    };
  },
  onLoad(options) {
    this.flag = options.flag;
    this.type = options.type;
    this.getUserTree();
  },
  computed: {
    roleType() {
      return this.$store.state.roleType || uni.getStorageSync("roleType");
    },

    userInfo() {
      return this.$store.state.userInfo || uni.getStorageSync("userInfo");
    },
  },
  methods: {
    selected(arr) {
      this.selectedList = arr;
      uni.navigateTo({
        url: `/pagesC/connectivity/detail`,
      });
    },
    ok() {
      if (this.type == "single") {
        this.$store.commit("setExecutor", this.selectedList);
      } else {
        this.$store.commit("setCollaborator", this.selectedList);
      }
      uni.navigateBack();
    },
    nodeClick(val) {
      this.curNameId = val.id;
      this.$set(val, "curNameId", this.curNameId);
    },
    async getUserTree() {
      let params = {
        type: "user", //获取类型：user-下级员工，all-全部
      };

      const { status_code, data, message } = await getUserTree(params);
      if (status_code == 200) {
        let loginUser = this.userInfo.login_user;
        console.log("userInfo", data[1]);
        if (loginUser.id == data[0].id) {
          this.topUser.id = data[0].id;
          this.topUser.name = data[0].member_certification.name;
          this.topUser.image = data[0].member_info.image.thumbnail_path_url;
        }
        this.list = data[0].children;
      }
    },
  },
};
</script>
<style>
page {
  background-color: #f5f5f7;
}
</style>
<style lang="less" scoped>
.header {
  padding: 32rpx;
}

.wrap {
  .title {
    padding: 16rpx 32rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
  }

  .list {
    display: flex;
    flex-direction: column;

    .item {
      display: flex;
      align-items: center;
      height: 120rpx;
      background-color: #ffffff;
      padding-left: 32rpx;

      image {
        width: 72rpx;
        height: 72rpx;
      }

      .name {
        flex: 1;
        padding-left: 16rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}

.btn {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  left: 50%;
  margin-left: -150rpx;
  bottom: 40rpx;
  background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
  font-weight: 600;
  font-size: 28rpx;
  color: #ffffff;
}
</style>
