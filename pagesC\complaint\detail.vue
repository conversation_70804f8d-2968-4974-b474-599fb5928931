<template>
  <view class="container">
    <view class="reporter-info">
      <image class="avatar" src="/static/images/default-avatar.png"></image>
      <view class="info">
        <view class="name">王哈哈</view>
        <view class="desc">学创联盟·招聘者</view>
      </view>
    </view>

    <view class="timeline">
      <!-- 举报评价 -->
      <view class="timeline-item">
        <view class="timeline-dot active"></view>
        <view class="timeline-content">
          <view class="timeline-title">举报得评价</view>
          <view class="feedback-box" v-if="!feedbackSubmitted">
            <view class="feedback-question">您对本次举报结果是否满意</view>
            <view class="feedback-options">
              <view class="option unsatisfied" @click="showUnsatisfiedPopup">
                <image
                  class="emoji"
                  src="https://api-test.zhaopinbei.com/storage/uploads/images/pWmmosMzhoFW2LnDqcHqO86lJ64k6d4VT8HyxC8V.png"
                ></image>
                <text>不满意</text>
              </view>
              <view class="option satisfied" @click="submitSatisfiedFeedback">
                <image
                  class="emoji"
                  src="https://api-test.zhaopinbei.com/storage/uploads/images/EkfdkRx3tR3OZ5yNvzRCSLXIuHIn3WkLlZskdkXq.png"
                ></image>
                <text>满意</text>
              </view>
            </view>
          </view>
          <view class="feedback-result" v-else>
            <image
              class="emoji-result"
              :src="
                feedbackType === 'satisfied'
                  ? 'https://api-test.zhaopinbei.com/storage/uploads/images/lVAatP7xBF45KzhDAjbPmpcgnzCLFHMW9q5ZvsD1.png'
                  : 'https://api-test.zhaopinbei.com/storage/uploads/images/pWmmosMzhoFW2LnDqcHqO86lJ64k6d4VT8HyxC8V.png'
              "
            ></image>
            <view>
              <view class="feedback-title">
                您对本次举报结果评价"{{
                  feedbackType === "satisfied" ? "满意" : "不满意"
                }}"
              </view>
              <view class="feedback-message">
                <text>{{ feedbackMessage }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 举报处理结果 -->
      <view class="timeline-item">
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="timeline-title">举报处理结果</view>
          <view class="timeline-time">2025-04-15 17:15:11</view>
          <view class="timeline-desc">
            您的举报已被核实并对涉事账号下发强制学习教育等限制处置，该举报结果将持续作用于该涉事账号的累计处罚和信用评价，感谢您的反馈。
          </view>
        </view>
      </view>

      <!-- 已处理 -->
      <view class="timeline-item">
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="timeline-title">已处理</view>
          <view class="timeline-time">2025-04-15 17:13:41</view>
          <view class="timeline-desc">
            您的举报已被核实并对涉事账号下发强制学习教育等限制处置，该举报结果将持续作用于该涉事账号的累计处罚和信用评价，感谢您的反馈。
          </view>
        </view>
      </view>

      <!-- 提交举报信息 -->
      <view class="timeline-item">
        <view class="timeline-dot"></view>
        <view class="timeline-content">
          <view class="timeline-title-with-icon">
            <text>提交举报信息</text>
            <image
              class="info-icon"
              src="/static/images/info-circle.png"
            ></image>
          </view>
          <view class="timeline-time">2025-04-15 17:13:23</view>
          <view class="report-reason"> 职位虚假-职位工作内容不符 </view>
        </view>
      </view>
    </view>

    <!-- 不满意原因弹框 -->
    <u-popup
      :show="showUnsatisfied"
      mode="bottom"
      @close="closeUnsatisfiedPopup"
      :round="16"
    >
      <view class="unsatisfied-popup">
        <view class="popup-header">
          <text class="popup-title">不满意的原因是?</text>
          <u-icon
            name="close"
            size="24"
            @click="closeUnsatisfiedPopup"
          ></u-icon>
        </view>

        <view class="reason-grid">
          <view
            class="reason-item"
            v-for="(item, index) in unsatisfiedReasons"
            :key="index"
            :class="{ active: selectedReasons.includes(item) }"
            @click="toggleReason(item)"
          >
            {{ item }}
          </view>
        </view>

        <view class="popup-buttons">
          <view class="cancel-btn" @click="closeUnsatisfiedPopup">返回</view>
          <view class="submit-btn" @click="submitUnsatisfiedReasons">提交</view>
        </view>
      </view>
    </u-popup>

    <!-- 感谢反馈弹框 -->
    <u-popup
      :show="showThankPopup"
      mode="bottom"
      @close="closeThankPopup"
      round="16"
    >
      <view class="thank-popup">
        <view class="thank-icon">
          <u-icon name="checkmark-circle" color="#4F8CF0" size="80"></u-icon>
        </view>
        <view class="thank-text">感谢您的反馈</view>
        <view class="close-icon" @click="closeThankPopup">
          <u-icon name="close" size="24"></u-icon>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      reportDetail: {
        reporter: {
          name: "王哈哈",
          desc: "学创联盟·招聘者",
          avatar: "/static/images/default-avatar.png",
        },
        timeline: [
          {
            title: "举报得评价",
            type: "feedback",
            active: true,
          },
          {
            title: "举报处理结果",
            time: "2025-04-15 17:15:11",
            desc: "您的举报已被核实并对涉事账号下发强制学习教育等限制处置，该举报结果将持续作用于该涉事账号的累计处罚和信用评价，感谢您的反馈。",
          },
          {
            title: "已处理",
            time: "2025-04-15 17:13:41",
            desc: "您的举报已被核实并对涉事账号下发强制学习教育等限制处置，该举报结果将持续作用于该涉事账号的累计处罚和信用评价，感谢您的反馈。",
          },
          {
            title: "提交举报信息",
            time: "2025-04-15 17:13:23",
            reason: "职位虚假-职位工作内容不符",
            hasInfoIcon: true,
          },
        ],
      },
      showUnsatisfied: false,
      unsatisfiedReasons: [
        "举报被驳回",
        "处理速度慢",
        "伯乐未被处罚",
        "损失无法挽回",
        "举报入口不易找",
        "举报原因分类不明确",
        "取证人员态度较差",
        "处理结果不明确",
        "伯乐处罚过轻",
      ],
      selectedReasons: [],
      showThankPopup: false,
      feedbackSubmitted: false,
      feedbackType: "", // 添加反馈类型：'satisfied' 或 'unsatisfied'
      feedbackMessage: "",
      satisfiedMessage: "感谢您的建议与反馈，祝您求职顺利！",
      unsatisfiedMessage:
        "感谢您的建议与反馈，BOSS直聘会持续提升服务能力和用户服务满意度，努力达到您的期盼！",
    };
  },
  onLoad(options) {
    // 可以根据options.id加载具体的举报详情
    console.log("加载举报详情，ID:", options.id);
  },
  methods: {
    showUnsatisfiedPopup() {
      this.showUnsatisfied = true;
    },
    closeUnsatisfiedPopup() {
      this.showUnsatisfied = false;
    },
    toggleReason(reason) {
      const index = this.selectedReasons.indexOf(reason);
      if (index === -1) {
        this.selectedReasons.push(reason);
      } else {
        this.selectedReasons.splice(index, 1);
      }
    },
    // 不满意按钮点击
    showUnsatisfiedPopup() {
      this.showUnsatisfied = true;
    },

    // 满意按钮点击
    submitSatisfiedFeedback() {
      this.feedbackType = "satisfied";
      this.feedbackMessage = this.satisfiedMessage;
      this.feedbackSubmitted = true;
      // 显示感谢弹框
      this.showThankPopup = true;
    },

    // 不满意原因提交
    submitUnsatisfiedReasons() {
      console.log("提交不满意原因:", this.selectedReasons);
      this.closeUnsatisfiedPopup();
      // 设置反馈类型和消息
      this.feedbackType = "unsatisfied";
      this.feedbackMessage = this.unsatisfiedMessage;
      this.feedbackSubmitted = true;
      // 显示感谢弹框
      this.showThankPopup = true;
    },
    // 关闭感谢弹框
    closeThankPopup() {
      this.showThankPopup = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  background-color: #ffffff;
  min-height: 100vh;
  padding: 32rpx;
}

.reporter-info {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;

  .avatar {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background-color: #f0f0f0;
    margin-right: 20rpx;
  }

  .info {
    display: flex;
    flex-direction: column;

    .name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
      margin-bottom: 8rpx;
    }

    .desc {
      font-size: 26rpx;
      color: #999999;
    }
  }
}

.timeline {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: 8rpx;
    top: 0;
    height: 100%;
    width: 2rpx;
    background-color: #e0e0e0;
  }

  .timeline-item {
    position: relative;
    padding-left: 40rpx;
    margin-bottom: 40rpx;

    .timeline-dot {
      position: absolute;
      left: 0;
      top: 10rpx;
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      background-color: #4f8cf0;
      border: 2rpx solid #ffffff;
      z-index: 1;
    }

    .timeline-content {
      .timeline-title {
        font-size: 30rpx;
        font-weight: 500;
        color: #333333;
        margin-bottom: 16rpx;
      }

      .timeline-title-with-icon {
        display: flex;
        align-items: center;
        font-size: 30rpx;
        font-weight: 500;
        color: #333333;
        margin-bottom: 16rpx;

        .info-icon {
          width: 28rpx;
          height: 28rpx;
          margin-left: 8rpx;
        }
      }

      .timeline-time {
        font-size: 26rpx;
        color: #999999;
        margin-bottom: 16rpx;
      }

      .timeline-desc {
        font-size: 28rpx;
        color: #666666;
        line-height: 1.5;
      }

      .report-reason {
        font-size: 28rpx;
        color: #666666;
        background-color: #f5f5f7;
        padding: 24rpx;
        border-radius: 8rpx;
      }
    }
  }
}

.feedback-box {
  background: #ffffff;
  box-shadow: 0rpx 2rpx 32rpx 0rpx rgba(0, 0, 0, 0.12);
  padding: 24rpx;
  border-radius: 24rpx;

  .feedback-question {
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 24rpx;
  }

  .feedback-options {
    display: flex;
    justify-content: space-between;

    .option {
      display: flex;
      align-items: center;
      background-color: #f0f0f0;
      padding: 12rpx 40rpx;
      border-radius: 999rpx;

      .emoji {
        width: 64rpx;
        height: 64rpx;
        margin-right: 24rpx;
      }

      text {
        font-size: 28rpx;
        color: #666666;
      }

      &.unsatisfied {
        .emoji {
          content: url("https://api-test.zhaopinbei.com/storage/uploads/images/pWmmosMzhoFW2LnDqcHqO86lJ64k6d4VT8HyxC8V.png");
        }
      }

      &.satisfied {
        .emoji {
          content: url("https://api-test.zhaopinbei.com/storage/uploads/images/EkfdkRx3tR3OZ5yNvzRCSLXIuHIn3WkLlZskdkXq.png");
        }
      }
    }
  }
}
// 提交反馈后的样式
.feedback-result {
  background: #ffffff;
  box-shadow: 0rpx 2rpx 32rpx 0rpx rgba(0, 0, 0, 0.12);
  border-radius: 24rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .feedback-title {
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 16rpx;
  }
  .emoji-result {
    width: 64rpx;
    height: 64rpx;
    margin-right: 32rpx;
    flex-shrink: 0;
  }

  .feedback-message {
    display: flex;
    align-items: flex-start;

    text {
      font-size: 28rpx;
      color: #666666;
      line-height: 1.5;
    }
  }
}
/* 不满意原因弹框样式 */
.unsatisfied-popup {
  background-color: #ffffff;
  padding: 32rpx;
  border-radius: 24rpx;
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }
  }

  .reason-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 40rpx;

    .reason-item {
      width: 48%;
      height: 80rpx;
      background-color: #f5f5f7;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #333333;
      margin-bottom: 16rpx;

      &.active {
        background-color: #ecf2fe;
        color: #4f8cf0;
        border: 1rpx solid #4f8cf0;
      }
    }
  }

  .popup-buttons {
    display: flex;
    justify-content: space-between;

    .cancel-btn,
    .submit-btn {
      height: 88rpx;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
    }

    .cancel-btn {
      width: 24%;
      border: 2rpx solid #4f8cf0;
      color: #4f8cf0;
    }

    .submit-btn {
      width: 74%;
      background-color: #4f8cf0;
      color: #ffffff;
    }
  }
}
.thank-popup {
  width: 100vw;
  height: 480rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;

  .thank-icon {
    margin-bottom: 20rpx;
  }

  .thank-text {
    font-size: 28rpx;
    color: #333333;
  }

  .close-icon {
    position: absolute;
    top: 16rpx;
    right: 16rpx;
  }
}
</style>
