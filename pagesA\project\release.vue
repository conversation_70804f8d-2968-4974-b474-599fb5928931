<template>
    <view id="app">
        <view class="iptBox">
            <view class="release_title">项目名称
                <text class="xing">*</text>
            </view>
            <input type="text" placeholder="请输入项目名称" v-model="form.name" class="ipt"/>
        </view>
        <view class="iptBox">
            <view class="release_title">项目类型
                <text class="xing">*</text>
            </view>
            <view class="iptCenter">
                <!--<input type="text" v-model="form.type" placeholder="请选择项目类型" disabled class="ipt"/>-->
                <!--<image src="/static/images/project/rightIcon.png" mode="" class="rightIcon"></image>-->
                <picker @change="changeType" :value="form.type" :range="typeOptions" range-key="label">
                    <view class="d-picker" :style="{color : form.type == 0 ? '#c0c4cc' : '#303133'}">
                        {{ typeOptions[typeIndex]['label'] }}
                    </view>
                </picker>
                <u-icon name="arrow-right"></u-icon>
            </view>
        </view>
        <view class="iptBox" @click="changeUsers()">
            <view class="release_title">选择项目协助人
                <text class="xing">*</text>
            </view>
            <view class="iptCenter">
                <view class="ipt">
                    <view v-if="collaborator.length>0">
                        <text v-for="(name,index) in collaborator" :key="index" :item="index" class="userBox">
                            {{ name.name }}
                        </text>
                    </view>
                    <input v-else type="text" placeholder="请选择项目协作人" disabled/>
                </view>
                <image src="/static/images/project/rightIcon.png" mode="" class="rightIcon"></image>
            </view>
        </view>
        <view class="iptBox">
            <view class="release_title">选择职位
                <text class="xing">*</text>
            </view>
            <view class="iptCenter" @click="selectJob">
                <!--<input type="text" placeholder="选择职位" disabled class="ipt" v-model="form.job_ids"/>-->
                <!--<image src="/static/images/project/rightIcon.png" mode="" class="rightIcon"></image>-->
                {{changeJob?changeJob.title:'请选择关联职位'}}
            </view>
        </view>
        <view class="iptBox">
            <view class="release_title">需要人数
                <text class="xing">*</text>
            </view>
            <input type="text" placeholder="请输入项目完成所需人数" v-model="form.count" class="ipt"/>
        </view>
        <view class="iptBox">
            <view class="release_title">项目描述
                <text class="xing">*</text>
            </view>
            <input type="text" placeholder="请输入项目描述" v-model="form.description" class="ipt"/>
        </view>
        <view class="bottomBox">
            <view class="bottomBox_btn" @click="submit">发布项目</view>
        </view>
        <AssistingPeople ref="AssistingPeopleRef" @custom-event="userChangeData"></AssistingPeople>
    </view>

</template>

<script>
    import AssistingPeople from "../../pagesA/components/dialog/AssistingPeople.vue";
    import {projectStore} from "../../config/headhunterList_api";

    export default {
        components: {
            AssistingPeople,
        },
        data() {
            return {
                typeOptions: [
                    {
                        label: "请选择项目类型",
                        value: "",

                    }, {
                        label: "职位类型",
                        value: "job",
                    }
                ],
                typeIndex: 0,
                changeUserName: [],
                changeJob: '',
                form: {
                    name: "",
                    count: 0,
                    user_ids: [],
                    job_ids: [],
                    type: 0,
                    description: "",
                }
            }
        },
        computed: {
            collaborator() {
                console.log(this.$store.state.collaborator)
                return this.$store.state.collaborator
            }
        },
        onShow() {
            if (uni.getStorageSync('selectedItems')) {
                this.changeJob = uni.getStorageSync('selectedItems')
            }
        },
        methods: {
            selectJob() {
                uni.navigateTo({
                    url: '/pagesA/headhunterList/selectJobList'
                })
            },
            changeType(e) {
                var _this = this;
                _this.typeIndex = e.detail.value || 0;
                _this.form.type = _this.typeOptions[_this.typeIndex]['value'];
            },
            userChangeData(data) {
                var _this = this;
                if (data.length > 0) {
                    data.forEach(function (item, index) {
                        _this.form.user_ids.push(item.id);
                        _this.changeUserName.push(item.member_certification.name);
                    })
                }
            },
            submit() {
                var _this = this;
                var _selectJobs = uni.getStorageSync('selectedItems');
                if (this.collaborator.length > 0) {
                    this.collaborator.forEach(function (item, index) {
                        _this.form.user_ids.push(item.id);
                        _this.changeUserName.push(item.name);
                    })
                }
                if(_selectJobs){
                    _this.form.job_ids = [_selectJobs.id];
                }

                projectStore(_this.form).then(response => {
                    if(response.status_code != 200){
                        uni.$u.toast(response.message);
                        return false;
                    }
                    uni.$u.toast('发布成功');

                    uni.navigateBack();
                });
            },
            changeUsers() {
                uni.navigateTo({
                    url:'/pagesA/select/selectPeople?type=2'
                })
                // var _this = this;
                // _this.$refs.AssistingPeopleRef.open();
            }
        }
    }
</script>

<style scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
        min-height: 100vh;
        height: auto;
        background: #FFFFFF;
    }

    .iptBox {
        width: 100%;
        height: 138rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 24rpx 0;
        border-bottom: 2rpx solid #F5F5F7;
    }

    .release_title {
        font-weight: 400;
        font-size: 22rpx;
        color: #666666;
    }

    .xing {
        font-weight: 600;
        font-size: 22rpx;
        color: #FE4D4F;
        padding-left: 8rpx;
    }

    .iptCenter {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .userBox {
        padding-right: 8rpx;
    }

    .rightIcon {
        width: 32rpx;
        height: 32rpx;
    }

    .ipt {
        width: 100%;
    }

    .bottomBox {
        width: 100%;
        height: 196rpx;
        padding: 24rpx 32rpx;
        background: #FFFFFF;
        box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
        position: fixed;
        bottom: 0;
        left: 0;
    }

    .bottomBox_btn {
        width: 100%;
        height: 80rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 80rpx;
        color: #FFFFFF;
    }

</style>
