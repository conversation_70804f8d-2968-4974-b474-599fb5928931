import {ENV} from './env'

export let BASE_URL_ORIGIN = 'https://api-test.zhaopinbei.com/'
export let REQUEST_URL = `${BASE_URL_ORIGIN}`;

switch (ENV){
	case 'production':
		BASE_URL_ORIGIN = 'https://api.zhaopinbei.com/';
		REQUEST_URL = `${BASE_URL_ORIGIN}`;
		break;
	case 'test':
		BASE_URL_ORIGIN = 'https://api-test.zhaopinbei.com/';
		// BASE_URL_ORIGIN = 'http://192.168.10.18/';
		REQUEST_URL = `${BASE_URL_ORIGIN}`;
		break;
	default:
		BASE_URL_ORIGIN = 'https://api.zhaopinbei.com/';
		REQUEST_URL = `${BASE_URL_ORIGIN}`;
		break;
}
