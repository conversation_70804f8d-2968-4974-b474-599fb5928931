<template>
  <view class="warp">
    <u-navbar bgColor="#fff" placeholder :autoBack="true" />

    <!-- 顶部标题栏 -->
    <view class="header">
      <view class="title-left">开票中心</view>
      <view class="title-right" @click="goToInvoiceRecord">开票记录</view>
    </view>

    <!-- 日期筛选 -->
    <view class="date-filter">
      <view
        class="date-item"
        :class="{ active: currentDateFilter === 'all' }"
        @click="changeDateFilter('all')"
        >全部</view
      >
      <view
        class="date-item"
        :class="{ active: currentDateFilter === 'threeMonths' }"
        @click="changeDateFilter('threeMonths')"
        >近三月</view
      >
      <view
        class="date-item"
        :class="{ active: currentDateFilter === 'oneYear' }"
        @click="changeDateFilter('oneYear')"
        >近一年</view
      >
      <view
        class="date-item custom"
        :class="{ active: currentDateFilter === 'custom' }"
        @click="showCustomDatePicker"
        >自定义时间</view
      >
    </view>

    <!-- 发票列表 -->
    <scroll-view scroll-y class="invoice-list" v-if="invoiceList.length > 0">
      <view
        class="invoice-item"
        v-for="(item, index) in invoiceList"
        :key="index"
      >
        <view class="checkbox-wrapper">
          <u-checkbox
            :name="item.id"
            :checked="selectedInvoices.includes(item.id)"
            @change="(e) => selectInvoice(item.id, e)"
            shape="circle"
            activeColor="#4F8CF0"
          ></u-checkbox>
        </view>
        <view class="invoice-content" @click="toggleInvoice(item.id)">
          <view class="invoice-title">{{ item.title }}</view>
          <view class="invoice-time">{{ item.apply_time }}</view>
        </view>
        <view class="invoice-amount">+{{ item.amount }}</view>
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <u-empty mode="data"> </u-empty>
    </view>
    <view style="height: 220rpx"> </view>
    <!-- 底部操作栏 -->
    <view class="footer">
      <view class="footer-left">
        <u-checkbox
          name="all"
          :checked="isAllSelected"
          @change="toggleSelectAll"
          shape="circle"
          activeColor="#4F8CF0"
          label="全选"
        ></u-checkbox>
      </view>
      <view class="footer-center">
        <text class="total-text">合计：¥{{ totalAmount }}</text>
        <text class="count-text">{{ selectedCount }}张发票记录</text>
      </view>
      <view class="next-btn" @click="goToNextStep">下一步</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 日期筛选
      currentDateFilter: "all",
      customStartDate: "",
      customEndDate: "",

      // 发票列表数据
      invoiceList: [
        {
          id: 1,
          title: "合同套餐",
          amount: "8848",
          apply_time: "2024-09-24 17:25:42",
          type: "招聘币",
        },
        {
          id: 2,
          title: "职位套餐",
          amount: "8848",
          apply_time: "2024-09-24 17:25:42",
          type: "招聘币",
        },
        {
          id: 3,
          title: "合同套餐",
          amount: "8848",
          apply_time: "2024-09-24 17:25:42",
          type: "招聘币",
        },
        {
          id: 4,
          title: "合同套餐",
          amount: "8848",
          apply_time: "2024-09-24 17:25:42",
          type: "招聘币",
        },
        {
          id: 5,
          title: "合同套餐",
          amount: "8848",
          apply_time: "2024-09-24 17:25:42",
          type: "招聘币",
        },
        {
          id: 6,
          title: "合同套餐",
          amount: "8848",
          apply_time: "2024-09-24 17:25:42",
          type: "招聘币",
        },
        {
          id: 7,
          title: "合同套餐",
          amount: "8848",
          apply_time: "2024-09-24 17:25:42",
          type: "招聘币",
        },
        {
          id: 8,
          title: "合同套餐",
          amount: "8848",
          apply_time: "2024-09-24 17:25:42",
          type: "招聘币",
        },
      ],

      // 选中的发票ID列表
      selectedInvoices: [],
    };
  },
  computed: {
    // 是否全选
    isAllSelected() {
      return (
        this.selectedInvoices.length === this.invoiceList.length &&
        this.invoiceList.length > 0
      );
    },

    // 选中的发票总金额
    totalAmount() {
      if (this.selectedInvoices.length === 0) return 0;

      return this.selectedInvoices.reduce((total, id) => {
        const invoice = this.invoiceList.find((item) => item.id === id);
        return total + (invoice ? parseFloat(invoice.amount) : 0);
      }, 0);
    },

    // 选中的发票数量
    selectedCount() {
      return this.selectedInvoices.length;
    },
  },
  onLoad() {
    // 加载发票列表数据
    this.loadInvoiceList();
  },
  methods: {
    // 加载发票列表
    loadInvoiceList() {
      // 实际项目中应该调用API获取数据
      // 这里使用模拟数据
      console.log("加载发票列表");
    },

    // 前往开票记录页面
    goToInvoiceRecord() {
      uni.navigateTo({
        url: "/pagesB/invoiceCenter/components/addInvoiceList",
      });
    },

    // 切换日期筛选
    changeDateFilter(filter) {
      this.currentDateFilter = filter;

      // 根据筛选条件获取数据
      let startDate = "";
      let endDate = "";

      const now = new Date();

      switch (filter) {
        case "threeMonths":
          // 近三个月
          startDate = new Date(
            now.getFullYear(),
            now.getMonth() - 3,
            now.getDate()
          );
          endDate = now;
          break;
        case "oneYear":
          // 近一年
          startDate = new Date(
            now.getFullYear() - 1,
            now.getMonth(),
            now.getDate()
          );
          endDate = now;
          break;
        case "custom":
          // 自定义时间范围，不做处理，等待用户选择
          return;
        default:
          // 全部，不设置时间范围
          break;
      }

      // 根据时间范围筛选数据
      this.filterInvoicesByDate(startDate, endDate);
    },

    // 显示自定义日期选择器
    showCustomDatePicker() {
      this.currentDateFilter = "custom";
      // 这里可以调用日期选择器组件
      uni.showToast({
        title: "请选择自定义时间范围",
        icon: "none",
      });
    },

    // 根据日期筛选发票
    filterInvoicesByDate(startDate, endDate) {
      // 实际项目中应该调用API获取对应日期范围的数据
      console.log("按日期筛选:", startDate, endDate);
    },

    // 选择/取消选择发票
    selectInvoice(id, checked) {
      if (checked) {
        // 添加到选中列表
        if (!this.selectedInvoices.includes(id)) {
          this.selectedInvoices.push(id);
        }
      } else {
        // 从选中列表移除
        const index = this.selectedInvoices.indexOf(id);
        if (index !== -1) {
          this.selectedInvoices.splice(index, 1);
        }
      }
    },

    // 点击内容区域切换选择状态
    toggleInvoice(id) {
      const isSelected = this.selectedInvoices.includes(id);
      this.selectInvoice(id, !isSelected);
    },

    // 全选/取消全选
    toggleSelectAll(checked) {
      if (checked) {
        // 全选
        this.selectedInvoices = this.invoiceList.map((item) => item.id);
      } else {
        // 取消全选
        this.selectedInvoices = [];
      }
    },

    // 下一步
    goToNextStep() {
      if (this.selectedInvoices.length === 0) {
        uni.showToast({
          title: "请至少选择一张发票",
          icon: "none",
        });
        return;
      }

      // 跳转到下一步
      uni.navigateTo({
        url: `/pagesB/invoiceCenter/components/addInvoice?amount=${this.totalAmount}&selectedIds=${JSON.stringify(this.selectedInvoices)}`
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.warp {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

/* 顶部标题栏 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background: #fff;
}

.title-left {
  font-size: 32rpx;
  color: #333333;
}

.title-right {
  font-size: 28rpx;
  color: #666666;
}

/* 日期筛选 */
.date-filter {
  display: flex;
  padding: 32rpx;
  background-color: #ffffff;
  margin-bottom: 24rpx;
  width: 100%;
  box-sizing: border-box;
  overflow-x: auto;
}

.date-item {
  height: 50rpx;
  padding: 0 16rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  background-color: #f5f5f7;
  color: #666666;
}

.date-item.active {
  background-color: #f2f7ff;
  color: #4f8cf0;
  border: 2rpx solid #4f8cf0;
}

.date-item.custom {
  display: flex;
  align-items: center;
}

/* 发票列表 */
.invoice-list {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  padding: 0 32rpx;
}

.invoice-item {
  display: flex;
  align-items: center;
  padding: 48rpx 0;
  margin: 0;
  border-bottom: 1rpx solid #eeeeee;
  background: #fff;
  box-sizing: border-box;
  width: 100%;
}

.invoice-item:first-child {
  border-radius: 24rpx 24rpx 0 0;
}

.invoice-item:last-child {
  border-radius: 0 0 24rpx 24rpx;
  border-bottom: none;
}

.checkbox-wrapper {
  margin-left: 32rpx;
  margin-right: 24rpx;
}

.invoice-content {
  flex: 1;
}

.invoice-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.invoice-time {
  font-size: 24rpx;
  color: #999999;
}

.invoice-amount {
  font-size: 32rpx;
  color: #4f8cf0;
  font-weight: bold;
  margin-right: 32rpx;
}

/* 空状态 */
.empty-state {
  padding: 120rpx 32rpx;
}

/* 底部操作栏 */
.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 196rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  border-top: 1rpx solid #eeeeee;
  box-sizing: border-box;
}

.footer-left {
  display: flex;
  align-items: center;
}

.footer-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: 32rpx;
}

.total-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
}

.count-text {
  font-size: 24rpx;
  color: #999999;
  margin-top: 4rpx;
}

.next-btn {
  width: 164rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 28rpx;
}
</style>
