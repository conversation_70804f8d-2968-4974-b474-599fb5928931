<template>
	<view class="city-select-container">
		<!-- 标题和步骤信息 -->
		<view class="header">
			<text class="title">选择城市</text>
			<text class="step">{{ step }}/10</text>
		</view>
		<!-- 提示信息 -->
		<view class="tip">选择多个城市，可以获得更多工作机会</view>
		<!-- 搜索框 -->
		<view class="search-box">
			<u--input border="none" placeholder="搜索城市名称" prefixIcon="search"
				prefixIconStyle="font-size: 22px;color: #909399" v-model="searchCity" shape="circle"></u--input>
		</view>
		<scroll-view scroll-y="true" class="scroll-Y">
			<!-- 当前/历史访问城市 -->
			<view class="section">
				<text class="section-title">当前/历史访问城市</text>
				<view class="city-list">
					<view class="city-item" v-for="(city, index) in currentCities" :key="index"
						:class="{ active: selectedCurrentCities.includes(city) }" @click="toggleCurrentCity(city)">
						{{ city }}
					</view>
				</view>
			</view>
			<!-- 推荐城市 - 计算机类专业热招 -->
			<view class="section">
				<text class="section-title" style="font-size: 28rpx;">推荐城市</text>
				<text class="sub-title">计算机类专业热招</text>
				<view class="city-list">
					<view class="city-item" v-for="(city, index) in computerCities" :key="index"
						:class="{ active: selectedComputerCities.includes(city) }" @click="toggleComputerCity(city)">
						{{ city }}
					</view>
				</view>
			</view>
			<!-- 推荐城市 - 服务业专业热招 -->
			<view class="section">
				<text class="sub-title">服务业专业热招</text>
				<view class="city-list">
					<view class="city-item" v-for="(city, index) in serviceCities" :key="index"
						:class="{ active: selectedServiceCities.includes(city) }" @click="toggleServiceCity(city)">
						{{ city }}
					</view>
				</view>
			</view>
			<!-- 相近城市圈 -->
			<view class="section">
				<text class="section-title">相近城市圈</text>
				<view class="city-list">
					<view class="city-item" v-for="(city, index) in similarCities" :key="index"
						:class="{ active: selectedSimilarCities.includes(city) }" @click="toggleSimilarCity(city)">
						{{ city }}
					</view>
				</view>
			</view>
			<!-- 热门城市 -->
			<view class="section">
				<text class="section-title">热门城市</text>
				<view class="city-list">
					<view class="city-item" v-for="(city, index) in hotCities" :key="index"
						:class="{ active: selectedHotCities.includes(city) }" @click="toggleHotCity(city)">
						{{ city }}
					</view>
				</view>
			</view>
			<!-- A开头城市 -->
			<view class="section">
				<text class="section-title">A</text>
				<view class="city-list">
					<view class="city-item" v-for="(city, index) in aCities" :key="index"
						:class="{ active: selectedACities.includes(city) }" @click="toggleACity(city)">
						{{ city }}
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="footer">
			<button class="confirm-btn" @click="submitInfo">提交</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				step: 3,
				searchCity: '',
				// 当前/历史访问城市
				currentCities: ['郑州', '广西', '厦门'],
				selectedCurrentCities: [],
				// 计算机类专业热招城市
				computerCities: ['广州', '成都', '武汉', '北京', '上海', '深圳'],
				selectedComputerCities: [],
				// 服务业专业热招城市
				serviceCities: ['杭州', '重庆', '西安'],
				selectedServiceCities: [],
				// 相近城市圈
				similarCities: ['三门峡', '开封', '阜阳', '新乡', '商丘', '长治'],
				selectedSimilarCities: [],
				// 热门城市
				hotCities: ['兰州', '沈阳', '青岛', '南京', '宁波', '天津'],
				selectedHotCities: [],
				// A开头城市
				aCities: ['安达市', '鞍山', '阿尔山市', '阿拉善盟', '安国市', '安康'],
				selectedACities: []
			};
		},
		methods: {
			// 切换当前/历史访问城市选中状态
			toggleCurrentCity(city) {
				if (this.selectedCurrentCities.includes(city)) {
					this.selectedCurrentCities = this.selectedCurrentCities.filter(c => c !== city);
				} else {
					this.selectedCurrentCities.push(city);
				}
			},
			// 切换计算机类专业热招城市选中状态
			toggleComputerCity(city) {
				if (this.selectedComputerCities.includes(city)) {
					this.selectedComputerCities = this.selectedComputerCities.filter(c => c !== city);
				} else {
					this.selectedComputerCities.push(city);
				}
			},
			// 切换服务业专业热招城市选中状态
			toggleServiceCity(city) {
				if (this.selectedServiceCities.includes(city)) {
					this.selectedServiceCities = this.selectedServiceCities.filter(c => c !== city);
				} else {
					this.selectedServiceCities.push(city);
				}
			},
			// 切换相近城市圈选中状态
			toggleSimilarCity(city) {
				if (this.selectedSimilarCities.includes(city)) {
					this.selectedSimilarCities = this.selectedSimilarCities.filter(c => c !== city);
				} else {
					this.selectedSimilarCities.push(city);
				}
			},
			// 切换热门城市选中状态
			toggleHotCity(city) {
				if (this.selectedHotCities.includes(city)) {
					this.selectedHotCities = this.selectedHotCities.filter(c => c !== city);
				} else {
					this.selectedHotCities.push(city);
				}
			},
			// 切换A开头城市选中状态
			toggleACity(city) {
				if (this.selectedACities.includes(city)) {
					this.selectedACities = this.selectedACities.filter(c => c !== city);
				} else {
					this.selectedACities.push(city);
				}
			}
		}
	};
</script>

<style scoped>
	.city-select-container {
		padding: 15px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
	}

	.scroll-Y {
		height: 1056rpx;
	}

	.title {
		font-size: 20px;
		font-weight: bold;
	}

	.step {
		font-size: 16px;
		color: #666;
	}

	.tip {
		font-size: 14px;
		color: #999;
		margin-bottom: 15px;
	}

	.search-box {
		margin-bottom: 15px;
		background-color: #f2f2f2;
		/* padding: 8px 10px; */
		border-radius: 126rpx;
	}

	.search-box input {
		width: 100%;
		border: none;
		background: transparent;
	}

	.section {
		margin-bottom: 15px;
		display: flex;
		flex-direction: column;
	}

	.section-title {
		font-size: 24rpx;
		font-weight: bold;
		margin-bottom: 5px;
	}

	.sub-title {
		font-size: 14px;
		color: #666;
		margin-bottom: 10px;
	}

	.city-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		/* justify-content: space-evenly; */
	}

	.city-item {
		background-color: #f2f2f2;
		padding: 8px 15px;
		border-radius: 12rpx;
		margin-bottom: 10px;
		margin-right: 10px;
		cursor: pointer;
		width: 120rpx;
		height: 46rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 12rpx;
		font-size: 24rpx;
	}


	::v-deep .u-input {
		padding-left: 24rpx !important;
		height: 64rpx;
	}

	.active {
		background-color: rgba(238, 244, 255, 1);
		color: rgba(79, 140, 240, 1);
		border: 1rpx solid rgba(79, 140, 240, 1);
	}


	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}
</style>