<template>
	<!-- 工作/实习经历提交 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="form-container">
					<u-form labelWidth="120">
						<u-form-item label="公司名称" borderBottom>
							<u-input v-model="params.content.firmName" inputAlign="right" placeholder="请输入公司名称"
								border="none"></u-input>
						</u-form-item>
						<u-form-item label="所属行业" borderBottom>
							<u-input v-model="params.content.industry" inputAlign="right" placeholder="请输入所属行业"
								border="none"></u-input>
						</u-form-item>
						<u-form-item label="所属部门" borderBottom>
							<u-input v-model="params.content.department" inputAlign="right" placeholder="请输入所属部门"
								border="none"></u-input>
						</u-form-item>
						<u-form-item label="职位名称" borderBottom>
							<u-input v-model="params.content.position" inputAlign="right" placeholder="请输入职位名称"
								border="none"></u-input>
						</u-form-item>
						<u-form-item label="在职开始时间" borderBottom @click="startTimeShowPicker = true">
							<u-input v-model="params.content.Time.startTime" inputAlign="right" placeholder="请选择在职开始时间"
								readonly border="none"></u-input>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
						<u-form-item label="在职结束时间" borderBottom @click="endTimeShowPicker = true">
							<u-input v-model="params.content.Time.endTime" inputAlign="right" placeholder="请选择在职结束时间"
								readonly border="none"></u-input>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
						<u-form-item label="工作内容" borderBottom>
							<u-textarea v-model="params.content.jobContent" autoHeight border="none"></u-textarea>
						</u-form-item>
						<u-form-item label="工作业绩">
							<u-textarea v-model="params.content.jobAchievements" autoHeight border="none"></u-textarea>
						</u-form-item>
					</u-form>
				</view>
			</view>

			<u-datetime-picker ref="startTimePicker" v-model="startTimeTextModel" closeOnClickOverlay
				:show="startTimeShowPicker" mode="year-month" @confirm="onStartTimePickerConfirm"
				@cancel="onStartTimePickerClose" @close="onStartTimePickerClose"></u-datetime-picker>

			<u-datetime-picker ref="endTimePicker" v-model="endTimeTextModel" closeOnClickOverlay
				:show="endTimeShowPicker" mode="year-month" @confirm="onEndTimePickerConfirm"
				@cancel="onEndTimePickerClose" @close="onEndTimePickerClose"></u-datetime-picker>

			<u-toast ref="toast"></u-toast>
		</scroll-view>

		<view class="btn-container">
			<view class="btn" @click="onSubmit">确定</view>
		</view>
	</view>
</template>

<script>
	import {
		addStrength,
		updateStrength
	} from '@/config';

	export default {
		data() {
			return {
				startTimeTextModel: Number(new Date()),
				startTimeShowPicker: false,

				endTimeTextModel: Number(new Date()),
				endTimeShowPicker: false,

				params: {
					strength_id: null,
					content: {
						firmName: null,
						industry: null,
						department: null,
						position: null,
						Time: {
							startTime: null,
							endTime: null,
						},
						jobContent: null,
						jobAchievements: null,
						state: null
					}
				}
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onReady() {
			this.$refs.startTimePicker.setFormatter(this.formatter);
			this.$refs.endTimePicker.setFormatter(this.formatter);
		},
		onLoad(options) {
			const res = JSON.parse(options.json);
			if (!res.id) return;
			this.params.strength_id = res.id;
			this.params.content = res.content;
			this.startTimeTextModel = Number(new Date(res.content.Time.startTime));
			this.endTimeTextModel = Number(new Date(res.content.Time.endTime));
		},
		methods: {
			formatTimestampToYearMonth(timestamp) {
				const date = new Date(timestamp);
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				return `${year}-${month}`;
			},
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			onStartTimePickerConfirm(event) {
				this.params.content.Time.startTime = this.formatTimestampToYearMonth(event.value);
				this.onStartTimePickerClose();
			},
			onStartTimePickerClose() {
				this.startTimeShowPicker = false;
			},
			onEndTimePickerConfirm(event) {
				this.params.content.Time.endTime = this.formatTimestampToYearMonth(event.value);
				this.onEndTimePickerClose();
			},
			onEndTimePickerClose() {
				this.endTimeShowPicker = false;
			},
			async onSubmit() {
				const params = {
					type: '2',
					strength_id: this.params.strength_id,
					content: JSON.stringify(this.params.content),
					...this.userTypeParams,
				};
				const event = params.strength_id ? updateStrength : addStrength;
				const res = await event(params);
				if (res.status_code !== '200') return;
				this.$refs.toast.show({
					message: res.message,
					duration: 1000,
					complete: () => {
						uni.$u.route({
							type: 'back'
						});
					}
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;

		.btn-container {
			background-color: #FFFFFF;
			padding-block-start: 24rpx;
			padding-inline: 64rpx;
			padding-block-end: calc(24rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			border-start-start-radius: 16rpx;
			border-start-end-radius: 16rpx;
			box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);

			.btn {
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				border-radius: 16rpx;
				text-align: center;
				color: #FFFFFF;
				padding-block: 20rpx;
				font-size: 28rpx;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding: 32rpx;

				.form-container {
					background-color: #FFFFFF;
					padding-inline: 24rpx;
					padding-block: 16rpx;
					border-radius: 24rpx;

					::v-deep .u-textarea {
						padding: 0 !important;
					}
				}
			}
		}
	}
</style>