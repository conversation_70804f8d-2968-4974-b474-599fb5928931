<template>
	<view class="container">
		<scroll-view :scroll-x="true" class="scroll-view">
			<view class="scroll-container">
				<view class="article">
					<view class="title-box">
						<image class="huomiao"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/YHbvGsl0Pn0nsTw6lgPo2zEn4c994fg8dSpx6EPv.png"
							mode=""></image>
						<text>红榜</text>
					</view>
					<view class="item">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/XZlvLicLN0X8V782rFuJdwzio9jshtFO3UZwYkEE.png">
						</image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
					<view class="item">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/IrO6l3QDPABXEeHogiCs2g1NlERO1Rx8hjXqLpeI.png">
						</image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
					<view class="item">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/FTG2fPOpIHpHzMjBHVFUGT8tYWNmYYPSaL01kcDa.png">
						</image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
				</view>
				<view class="article">
					<view class="title-box">
						<image class="huomiao"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/2AcIdJex1qICma91u3hJQ6Ud8tiJ81kdBu1Nqdqb.png"
							mode=""></image>
						<text>黑榜</text>
					</view>
					<view class="item">
						<image class="image" src="/static/new/jinpai.png"></image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
					<view class="item">
						<image class="image" src="/static/new/yinpai.png"></image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
					<view class="item">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/xfEwCPjT4oiDPnE9kmiTNkwAkjhER1eFPZAmp1jy.png">
						</image>
						<view class="text-box">
							<view class="text">2025年的春天，你在干嘛？</view>
							<view class="text-bot">
								<view class="company">学创联盟网络科技（北京）有限公司</view>
								<image class="image" src="/static/new/浏览量****************" mode=""></image>
								<text class="desc">124</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<view class="column-container">
			<text class="title">精彩问答</text>
			<text>查看更多</text>
			<image class="image" src="/static/new/右箭头@2x1.png"></image>
		</view>

		<view class="content">
			<view class="question-item">
				<view class="item-start">
					<view class="start-left">
						<view class="user-box">
							<image class="avatar"
								src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png">
							</image>
							<text>匿名</text>
							<u-line direction="col" length="40rpx"></u-line>
							<text>2025-3-14</text>
						</view>
						<view class="content-title">字节跳动Fink大规模云原生化实践</view>
						<view class="content-sub-title">
							本文整理自字节跳动基瑞架构工程师刘畅，在Flink Forrard Asia 生产实践专场的分享，字节跳动拥有业界领先的 Flink
							流式计算任务规模。随着云原生时代的到来，我
						</view>
					</view>
				</view>
				<view class="item-end">
					<!-- 回答需跳转到问答详情页 -->
					<view class="btn" @click="jumpArticle">回答</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		methods: {

		},
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.scroll-view {
			.scroll-container {
				display: inline-flex;
				padding-inline: 32rpx;
				gap: 24rpx;

				.article {
					display: flex;
					padding: 32rpx;
					background: linear-gradient(180deg, #ffe3d6 0%, #ffffff 53%);
					border-radius: 24rpx;
					flex-direction: column;
					gap: 20rpx;

					.title-box {
						.huomiao {
							width: 40rpx;
							height: 40rpx;
						}
					}

					.item {
						display: flex;
						align-items: flex-start;
						gap: 16rpx;

						.image {
							width: 40rpx;
							height: 40rpx;
						}

						.text-box {
							display: flex;
							flex-direction: column;

							.text {
								width: 70%;
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
							}

							.text-bot {
								display: flex;
								align-items: center;

								.company {
									color: #666666;
									font-size: 22rpx;
									width: 70%;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.image {
									width: 32rpx;
									margin-inline-start: auto;
									height: 32rpx;
								}

								.desc {
									color: #666666;
									font-size: 20rpx;
								}
							}
						}
					}
				}
			}
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding-inline: 32rpx;

			.question-item {
				background: linear-gradient(to bottom, #dbe8ff, #ffffff);
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-end {
					display: flex;
					justify-content: flex-end;

					.btn {
						display: inline;
						color: #4f8cf0;
						font-size: 24rpx;
						padding-block: 8rpx;
						padding-inline: 26rpx;
						background: rgba(79, 140, 240, 0.3);
						border: 1rpx #4f8cf0 solid;
						border-radius: 4rpx;
					}
				}

				.item-start {
					display: flex;
					align-items: center;
					gap: 32rpx;

					.start-right {
						.image {
							width: 176rpx;
							height: 176rpx;
							border-radius: 24rpx;
						}
					}

					.start-left {
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.content-title {
							flex: 1;
							color: #041024;
							font-size: 28rpx;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.content-sub-title {
							color: #666666;
							font-size: 24rpx;
							flex: 1;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.user-box {
							display: flex;
							align-items: center;
							gap: 24rpx;
							color: #777777;
							font-size: 24rpx;

							.avatar {
								width: 48rpx;
								height: 48rpx;
								border-radius: 999rpx;
							}
						}
					}
				}
			}
		}

		.column-container {
			display: flex;
			align-items: center;
			color: #999999;
			font-size: 24rpx;
			padding-inline: 32rpx;

			.title {
				font-size: 32rpx;
				color: #041024;
				margin-inline-end: auto;
			}

			.image {
				width: 32rpx;
				height: 32rpx;
			}
		}

		.scroll-x-view {
			.scroll-x-container {
				display: inline-flex;
				padding-inline: 32rpx;
				gap: 24rpx;

				.article {
					display: flex;
					padding: 32rpx;
					background: linear-gradient(180deg, #ffe3d6 0%, #ffffff 53%);
					border-radius: 24rpx;
					flex-direction: column;
					gap: 20rpx;

					.title-box {
						.wenzheng {
							width: 148rpx;
							height: 52rpx;
						}
					}

					.item {
						display: flex;
						align-items: flex-start;
						gap: 16rpx;

						.image {
							width: 40rpx;
							height: 40rpx;
						}

						.text-box {
							display: flex;
							flex-direction: column;

							.text {
								width: 70%;
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
							}

							.text-bot {
								display: flex;
								align-items: center;

								.company {
									color: #666666;
									font-size: 22rpx;
									width: 70%;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.image {
									width: 32rpx;
									margin-inline-start: auto;
									height: 32rpx;
								}

								.desc {
									color: #666666;
									font-size: 20rpx;
								}
							}
						}
					}
				}
			}
		}
	}
</style>