<template>
	<!-- 安全问题检测 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="title">安全问题</view>
				<view class="sub-title">安全问题用于验证您的身份以保护您的账号安全，防止被他人盗用</view>
				<u-form class="form-container" labelPosition="top" labelWidth="500rpx"
					:labelStyle="{color: '#333333', fontSize: '28rpx'}">
					<u-form-item label="请选择要设置的安全问题" borderBottom>
						<view class="input-container">
							<u-input placeholder="1.请选择要设置的问题" disabled disabledColor="transparent">
								<template #suffix>
									<u-icon name="arrow-right"></u-icon>
								</template>
							</u-input>
							<u-input border="none" disabled disabledColor="transparent"></u-input>
						</view>
					</u-form-item>
					<u-form-item label="请选择要设置的安全问题" borderBottom>
						<view class="input-container">
							<u-input placeholder="2.请选择要设置的问题" disabled disabledColor="transparent">
								<template #suffix>
									<u-icon name="arrow-right"></u-icon>
								</template>
							</u-input>
							<u-input border="none" disabled disabledColor="transparent"></u-input>
						</view>
					</u-form-item>
					<u-form-item label="请选择要设置的安全问题" borderBottom>
						<view class="input-container">
							<u-input placeholder="3.请选择要设置的问题" disabled disabledColor="transparent">
								<template #suffix>
									<u-icon name="arrow-right"></u-icon>
								</template>
							</u-input>
							<u-input border="none" disabled disabledColor="transparent"></u-input>
						</view>
					</u-form-item>
				</u-form>
			</view>
		</scroll-view>
		<view class="btn-container">
			<view class="btn">下一步</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #FFFFFF;
		display: flex;
		flex-direction: column;

		.btn-container {
			padding-inline: 32rpx;
			padding-block-start: 32rpx;
			padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

			.btn {
				background-color: #4F8CF0;
				color: #FFFFFF;
				font-size: 28rpx;
				border-radius: 12rpx;
				padding-block: 20rpx;
				text-align: center;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				display: flex;
				flex-direction: column;
				padding-inline: 32rpx;
				padding-block-start: 32rpx;
				gap: 32rpx;

				.input-container {
					display: flex;
					flex-direction: column;
					gap: 24rpx;
				}

				.title {
					color: #333333;
					font-size: 32rpx;
				}

				.sub-title {
					color: #666666;
					font-size: 24rpx;
				}
			}
		}
	}
</style>