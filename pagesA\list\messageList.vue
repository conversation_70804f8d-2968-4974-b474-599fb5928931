<template>
	<view class="page">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="search-wrap">
					<u-search placeholder="请输入职位名称或发布人" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
				</view>
			</view>
		</u-sticky>
		<view class="list-item" v-for="(item,index) in users" :key="index" @click="connect(item)">
			<view class="avatar">
				<view style="width: 100%;height: 100%;border-radius: 45rpx;
			overflow: hidden;">
					<!-- <text class="round" v-if="item.read"></text> -->
					<image :src="item.avatar" mode="widthFix"></image>
				</view>
				<view class="person_type">
          <!-- <img src="../../static/images/icon_jygj.png" alt="" /> -->
		    <image src="/static/images/message/talent.png" style="width: 62rpx;height: 30rpx;" mode="">
		    </image>
        </view>
			</view>
			<view class="text_box">
				<view class="title5">
					<view class="title">
						<view class="name">{{ item.name }}</view>
						<view class="company">{{ item.company }}</view>
					</view>
					<view class="time">{{ item.time }}</view>
				</view>

				<view class="txt">{{ item.msg }}</view>
			</view>
		</view>
		<view class="height_bottom"></view>
	</view>
</template>

<script>
    import {
        chatList,
        sendMessage
    } from "../../config/common_api";
	export default {
		data() {
			return {
				options: [{
					text: '取消',
					style: {
						backgroundColor: '#007aff'
					}
				}, {
					text: '确认',
					style: {
						backgroundColor: '#dd524d'
					}
				}],
				users: [{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '杨涛',
						read: 1,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '雨中漫步',
						read: 1,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '糖果梦境',
						read: 1,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '海上日落',
						read: 1,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '男朋友',
						read: 1,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '女朋友',
						read: 1,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '静谧之夜',
						read: 1,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '风吹麦浪',
						read: 0,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '路过岁月',
						read: 0,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '繁星点点',
						read: 0,
						time: '23:59',
						msg: '没有消息就是最好的消息',
						company: '学创联盟（北京）网络科技公司'
					}
				]
			};
		},
        onLoad() {
            this.chatList()
        },
		methods: {
			onClick(e) {
				console.log('点击了' + (e.position === 'left' ? '左侧' : '右侧') + e.content.text + '按钮')
			},
			swipeChange(e, index) {
				console.log('当前状态：' + e + '，下标：' + index)
			},
			connect(item) {
				console.log("send",item)
				uni.showModal({
					title: '确定发送？',
					success: function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
                            this.sendMessage({
                                text: '简历',
                                id: 'selectedResume.member_resume.id'
                            }, 'resume', 'resume', 'selectedResume.member_resume.id')
							// 返回简历页面
							uni.navigateBack()
						} else if (res.cancel) {
							console.log('用户点击取消');
							uni.navigateBack()
						}
					}
				})
			},
            async chatList() {
                let params = {
                    type: 'job',
                    id: this.id
                }
                const {
                    status_code,
                    message,
                    data
                } = await chatList(params)
                if (status_code == 200) {
                    this.users = data
                    console.log('聊天人列表：', data)
                }
            },
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		padding: 0 32rpx;
		color: #333;
		background-color: #F5F5F7;

		.height_bottom {
			height: 110rpx
		}
	}

	.header {
		padding: 15rpx 0;
	}

	.list-item {
		display: flex;
		padding: 30rpx 0;
		border-bottom: 1px solid #ccced3;

		.avatar {
			width: 90rpx;
			height: 90rpx;
			border-radius: 10rpx;
			margin-right: 20rpx;
			position: relative;

			.round {
				position: absolute;
				width: 14rpx;
				height: 14rpx;
				border-radius: 50%;
				background: #ef5656;
				top: -4rpx;
				right: -4rpx;
				z-index: 1;
			}

			image {
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
			}
		}

		.person_type {
			width: 100%;
			position: absolute;
			bottom: -8rpx;
            left: 0;
			right: 0;
			margin: auto;
			text-align: center;
			image {
				height: 36rpx;
				border-radius: 0;
			}
		}

		.text_box {
			flex: 1;
			.title5{
				display: flex;
				justify-content: space-between;
			}
			.title {
				display: flex;

				// justify-content: space-between;
				.name {
					font-weight: bold;
				}

				.company {
					font-size: 20rpx;
					margin: 9rpx 20rpx;
					font-weight: bold;
					color: #666666;
				}

				.time {
					color: #999999;
					font-size: 24rpx;
					margin-left: 5rpx
				}

			}

			.txt {
				margin-top: 10rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				text-align: left;
				color: #999;
				font-size: 26rpx;
			}
		}
	}
</style>
