.title {
	font-size: 34rpx;
	color: white;
	margin-bottom: 30rpx;
}

.topCon {
	padding: 24rpx;
	.onetent {
		align-items: center;
		display: flex;
		justify-content: space-between;
		.name {
			// text-align: center;
			color: white;
			font-size: 38rpx;
			width: 70%;
			display: flex;
			gap: 20rpx;
			align-items: center;
			image {
				width: 126rpx;
				height: 100rpx;
				border-radius: 50%;
				// margin-right: 20rpx;
			}
		}
		.like {
			display: flex;
			text-align: center;
			color: white;
			border: 1px solid white;
			padding: 14rpx 16rpx;
			min-width: 130rpx;
			border-radius: 16rpx;
			background-color: #414751;
			align-items: center;
			justify-content: center;
			margin-left: 14rpx;
			image {
				width: 32rpx;
				height: 32rpx;
				margin-top: 6rpx;
				margin-right: 6rpx;
			}
		}
	}
	.twotent {
		color: #CCD7E9;
		margin: 20rpx 0;
		font-size: 20rpx;
	}
	.threetent {
		display: flex;
		color: #EFF5FF;
		font-size: 20rpx;
		.tent {
			display: flex;
			margin-right: 20rpx;
			align-items: center;
			image {
				width: 26rpx;
				height: 26rpx;
			}
		}
	}
}

// 企业福利
.compMoney {
	padding: 24rpx;
	
	.tags {
		display: flex;
		
		.tag {
			display: flex;
			align-items: center;
			background-color: #FFFFFF;
			border-radius: 8rpx;
			height: 46rpx;
			padding: 0 12rpx;
			font-weight: 400;
			font-size: 22rpx;
			color: #666666;
			margin-right: 16rpx;
		}
	}
}

// 公司评论
.comment {
	padding: 24rpx;
	
	.leftCom {
		width: 280rpx;
		text-align: center;
		justify-content: center;
		border-right: 1rpx solid #ccc;
		margin-right: 30rpx;
		padding: 12rpx 0;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		
		.grade {
			color: white;
		}
		.star {
			display: flex;
			justify-content: center;
		}
		.goComment {
			text-align: center;
			justify-content: center;
			color: #CCD7E9;
			display: flex;
			align-items: center;
			image {
				width: 32rpx;
				margin-top: 6rpx;
				height: 32rpx;
			}
		}
	}
	.rightCom {
		margin-bottom: 26rpx;
		.Allcom {
			display: flex;
			align-items: center;
			text-align: center;
			color: #777777;
			margin-bottom: 20rpx;
			image {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				margin-right: 30rpx;
			}
		}
	}
}

// 公司介绍
.intro {
	position: relative;
	padding: 24rpx;
	/* 文字内容样式 */
	.text-content {
	  font-size: 14px;
	  line-height: 20px;
	  color: white;
	  overflow: hidden;
	  text-overflow: ellipsis;
	  display: -webkit-box;
	  -webkit-line-clamp: 3; /* 限制显示三行 */
	  -webkit-box-orient: vertical;
	}
	
	/* 展开状态下的文字样式 */
	.text-content.expanded {
	  -webkit-line-clamp: unset; /* 取消行数限制 */
	}
	
	/* 展开按钮样式 */
	.expand-button {
	  position: absolute;
	  right: 24rpx;
	  bottom: 24rpx;
	  display: flex;
	  align-items: center;
	  justify-content: flex-end;
	  margin-top: 10px;
	  color: white;
	  font-size: 14px;
	}
	
	/* 展开按钮图标样式 */
	.expand-button .icon {
	  width: 16px;
	  height: 16px;
	  margin-left: 5px;
	}
}

// 人事列表
.hrList {
	padding: 24rpx;
	.title {
		font-size: 34rpx;
		color: white;
	}
	.list {
		display: flex;
		white-space: nowrap;
		overflow-x: auto; /* 允许横向滚动 */
		// padding-bottom: 20rpx;
	}
	.oneHr {
		display: inline-flex;
		align-items: center;
		margin-right: 40rpx;
		image {
			width: 90rpx;
			height: 90rpx;
			border-radius: 50%;
			margin-right: 20rpx;
		}
		.hrCon {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			background-color: #343d4f;
			.name {
				font-size: 28rpx;
				color: white;
				margin-bottom: 10rpx;
			}
			.job {
				font-size: 24rpx;
				color: #CCD7E9;
			}
		}
	}
}

// 公司职位
.jobList {
	color: white;
	display: flex;
	justify-content: space-between;
	align-items: center;
	text-align: center;
	.title-right {
		display: flex;
		align-items: center;
	
		text {
			font-weight: 400;
			font-size: 28rpx;
		}
	}
}
.threeList {
	margin-bottom: 26rpx;
	.oneItem {
		background-color: #EFF5FF;
		padding: 16rpx 30rpx;
		border-radius: 16rpx;
		.top {
			display: flex;
			justify-content: space-between;
		}
		.tags {
			display: flex;
			margin: 10rpx 0;
			
			.tag {
				display: flex;
				align-items: center;
				background-color: #fff;
				border-radius: 8rpx;
				height: 46rpx;
				padding: 0 12rpx;
				font-weight: 400;
				font-size: 22rpx;
				color: #666666;
				margin-right: 16rpx;
			}
		}
		.jobIntro {
			font-size: 30rpx;
			display: -webkit-box; /* 将元素设置为弹性盒子 */
			-webkit-box-orient: vertical; /* 设置文本垂直排列 */
			-webkit-line-clamp: 2; /* 限制文本显示两行 */
			overflow: hidden; /* 超出部分隐藏 */
			text-overflow: ellipsis; /* 超出部分显示省略号 */
			color: #666666;
		}
	}
}

// 公司地址
.address {
	padding: 24rpx;
	color: white;
	.Add {
		display: flex;
		justify-content: space-between;
		image {
			width: 36rpx;
			height: 36rpx;
			margin-right: 10rpx;
		}
		.leftAdd {
			display: flex;
			align-items: center;
			color: white;
			font-size: 28rpx;
		}
		.rightAdd {
			display: flex;
			align-items: center;
			font-size: 28rpx;
			color: white;
			background-color: #343d4f;
			padding: 16rpx;
			border-radius: 30rpx;
			text-align: center;
		}
	}
}

// 我来说两句
.myCom {
	padding: 24rpx;
	.coming {
		display: flex;
		justify-content: space-between;
		image {
			width: 36rpx;
			height: 36rpx;
			margin-right: 10rpx;
		}
		.leftCom {
			color: white;
			display: flex;
			align-items: center;
			font-size: 28rpx;
		}
		.rightCom {
			display: flex;
			align-items: center;
			color: white;
			background-color: #343d4f;
			font-size: 28rpx;
			padding: 12rpx 16rpx;
			border-radius: 30rpx;
			text-align: center;
		}
	}
}


.info {
	padding: 24rpx;
	.info-item {
	  display: flex;
	  align-items: center;
	  margin-bottom: 12px;
	  color: #ccc;
	}
	
	/* 左侧标签样式 */
	.label {
	  width: 120px;
	  font-size: 14px;
	}
	
	/* 右侧内容样式 */
	.content {
	  flex: 1;
	  font-size: 14px;
	}
}
