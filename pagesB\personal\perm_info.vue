<template>
	<!-- 详细权限管理 -->
	<view class="container">
		<u-cell v-for="v in list" :key="v.key">
			<template #title>
				<view class="cell-container">
					<view class="cell-start">
						<text class="text">{{v.title}}</text>
						<u-switch v-model="v.isActive" size="22"></u-switch>
					</view>
					<view class="cell-end">{{v.subTitle}}</view>
				</view>
			</template>
		</u-cell>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [
					{
						key: '1',
						title: '允许在地图中使用您的位置信息',
						subTitle: '用于为你快速选中当前所在地点',
						isActive: true
					},
					{
						key: '2',
						title: '允许在路线规划时使用您的位置信息',
						subTitle: '用于为你提供从当前位置前往目标地点的路线和时间',
						isActive: true
					},
					{
						key: '3',
						title: '允许线下面试签到时使用您的位置信息',
						subTitle: '用于在前往面试地点后，进行定位签到',
						isActive: true
					},
					{
						key: '4',
						title: '允许在【附件】中使用您的位置信息',
						subTitle: '用于为你快速推荐定位附件的职位',
						isActive: false
					},
					{
						key: '5',
						title: '允许在伯乐卡片中使用您的位置信息',
						subTitle: '用于为你推荐给距离较近的招聘者（不展示具体位置信息，仅以商圈、粗略距离的方式展示）',
						isActive: true
					}
				]
			}
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-cell  {
		.u-cell__body {
			padding: 0 !important;
		}
	}
	.container {
		height: 100vh;
		background-color: #FFFFFF;
		
		.cell-container {
			display: flex;
			flex-direction: column;
			gap: 20rpx;
			padding: 32rpx;
		
			.cell-end {
				color: #999999;
				font-size: 22rpx;
				width: 80%;
			}
		
			.cell-start {
				display: flex;
				align-items: center;
		
				.text {
					color: #333333;
					font-size: 28rpx;
					margin-inline-end: auto;
				}
			}
		}
	}
</style>