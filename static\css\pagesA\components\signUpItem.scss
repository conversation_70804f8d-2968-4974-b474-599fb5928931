.btnBox {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.item {
    display: flex;
    flex-direction: column;
    background: #FFFFFF;
    border-radius: 24rpx;
    padding: 0 32rpx;
    position: relative;
    margin-bottom: 32rpx;
    
    .up {
        display: flex;
        flex-direction: column;
        padding: 24rpx 0;
        
        .info {
            display: flex;
            
            .name {
                font-weight: 600;
                font-size: 32rpx;
                color: #333333;
            }
            
            .money {
                font-weight: 600;
                font-size: 32rpx;
                color: #F98A14;
                margin-left: 32rpx;
            }
            
        }
		.active{
			display: flex;
			.name{
					font-weight: 600;
					font-size: 32rpx;
					color: #333333;
					padding-bottom: 24rpx;
				}
			.time{
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
			}
		}
        .hd {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 28rpx;
            .time {
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
                
            }
            
            .name {
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
            }
            
        }
        .comp {
            display: flex;
            margin-top: 28rpx;
            
            image {
                width: 40rpx;
                height: 40rpx;
				border-radius: 8rpx;
            }
            
            .name {
                margin-left: 16rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #333333;
            }
            
        }
        
        .sign-name {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 28rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
      
        }
        
    }
    
    .down {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0;
        
        position: relative;
        
        .reason {
            width: 400rpx;
            white-space: nowrap;
            overflow: hidden;
            text-align: left;
            text-overflow: ellipsis;
            font-weight: 600;
            font-size: 24rpx;
            color: #FE4D4F;
        }
        
        .btn {
            display: flex;
            align-items: center;
			justify-content: center;
			width: 152rpx;
            height: 58rpx;
            font-weight: 600;
            font-size: 24rpx;
            border-radius: 15rpx;
            margin-left: 14rpx;
        }
        
    }
    
    .talk {
        background: linear-gradient(to right, #4F8CF0FF,#0061FFFF);
        color: #fff;
    }
    
    .bd {
        border-top: 1px solid #F5F5F7;
    }
    
    .flexEnd {
    // align-items:flex-end;
        display: flex;
    // flex-direction:row-reverse;
    }
    
    .grey {
       background-color: #CCCCCCFF;
       color: #777777FF;
    }
    
    .status {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 0;
        top: 0;
        font-size: 28rpx;
        width: 132rpx;
        height: 62rpx;
        border-radius: 0 24rpx 0 24rpx;
    }
    
    .ybm {
        background: #4F8CF0;
        color: #FFFFFF;
    }
    
    .yqx {
        background: #cccccc;
        color: #FFFFFF;
    }
    
    .byq {
        background: #57D51C;
        color: #FFFFFF;
    }
	.Yellow{
		background: #FDBB2CFF;
		color: #FFFFFF;
	}
	.red{
		background: #FE4D4FFF;
		color: #FFFFFF;
	}
    
}