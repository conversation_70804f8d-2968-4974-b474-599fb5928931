<template>
  <view class="container">
    <u-sticky>
      <view class="sticky-container">
        <view class="tabs-container">
          <u-tabs
            :current="tabsIndex"
            :list="tabsList"
            @click="changeTab"
            :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.05)' }"
            :inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }"
          ></u-tabs>
        </view>
      </view>
    </u-sticky>
    <scroll-view :scroll-y="true" class="scroll-view">
      <view class="scroll-container">
        <template v-if="tabsValue == 1">
          <view v-for="_ in 12">
            <view style="margin-bottom: 20rpx" class="time">4月8日</view>
            <view class="item">
              <view class="item-right">
                <img class="ava" src="" alt="" />
                <view class="item-right-inn">
                  <view class="item-start">
                    <text class="text_1">Web 前端</text>
                  </view>
                  <view class="tag-box">
                    <view class="tag">世界500强</view>
                    <view class="tag">上市公司</view>
                    <view class="tag">游戏大厂</view>
                  </view>
                </view>
              </view>
              <view class="item-center">
                <view class="info-box">
                  <view class="location-box">
                    <image class="image" src="/static/new/定位@2x.png"></image>
                    <text class="position">北京北京市昌平区1号</text>
                  </view>
                </view>
                <!-- <view class="btn-box">
                  <view class="btn"> 聊聊呗 </view>
                </view> -->
              </view>
            </view>
          </view>
        </template>
        <template v-if="tabsValue == 2">
          <view v-for="_ in 12">
            <view style="margin-bottom: 20rpx" class="time">4月8日</view>
            <view class="item">
              <view class="item-right">
                <img class="jy-ava" src="" alt="" />
                <view class="item-right-inn">
                  <view class="item-start" style="justify-content: flex-start">
                    <text class="text_1">就业管家姓名</text>
                    <view class="rz"> 已认证 </view>
                  </view>
                  <view class="info-box">
                    <view class="location-box">
                      <text
                        class="position"
                        style="font-size: 24rpx; color: #666666"
                        >学创联盟（北京）网络科技有限公司</text
                      >
                    </view>
                  </view>
                </view>
              </view>
              <view class="item-center">
                <view class="jy-tag-box">
                  <view class="tag">行业大牛</view>
                  <view class="tag">目标管理</view>
                  <view class="tag">完美主页</view>
                </view>

                <view class="btn-box">
                  <view class="btn"> 聊聊呗 </view>
                </view>
              </view>
            </view>
          </view>
        </template>
        <template v-if="tabsValue == 3">
          <view class="content">
            <view style="margin-bottom: 20rpx" class="time">4月8日</view>
            <view class="jl-item" v-for="v in 12" :key="v" @click="onDetail(v)">
              <view class="item-start">
                <view class="title-box">
                  <view class="title-inner">
                    <image
                      class="avater"
                      :src="v.company.company_logo.thumb_url"
                    ></image>
                    <view class="">
                      <view class="tit-inn">
                        <view class="title_1">王先生</view>
                        <view class=""> ui设计师 </view>
                        <view class="title_2"> 10-20k </view>
                      </view>
                      <view class="tit-inn">
                        <view class="age"> 5年｜23岁 | 本科 </view>
                        <view class="title_3"> 实习/兼职 </view>
                      </view>
                    </view>
                  </view>
                </view>

                <view class="time">个人优势个人优势个人优势</view>
                <u-line></u-line>
                <view class="item-end">
                  <text class="name">求职状态: 在校-考虑机会</text>
                  <view class="address">
                    <view class="radi"> </view>
                    <view class=""> 在线 </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
      </view>
      <u-datetime-picker
        v-model="time"
        title="请选择重新开始统计时间"
        :show="isTimePicker"
        mode="date"
      ></u-datetime-picker>
    </scroll-view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      numId: "",
      tabsList: [],
      tabsIndex: 0,
      tabsValue: 0,
    };
  },
  methods: {
    changeTab(v) {
      this.tabsIndex = v.index;
      this.tabsValue = v.key;
    },
    onLoad(item) {
      this.numId = item.id;
      if (item.id == "1") {
        this.tabsList = [
          {
            key: "1",
            name: "伯乐",
          },
          {
            key: "2",
            name: "就业管家",
          },
        ];
        this.tabsValue = 1;
      }
      if (item.id == "2") {
        this.tabsList = [
          {
            key: "3",
            name: "千里马",
          },
          {
            key: "2",
            name: "就业管家",
          },
        ];
        this.tabsValue = 3;
      }
      if (item.id == "3") {
        this.tabsList = [
          {
            key: "3",
            name: "千里马",
          },
          {
            key: "1",
            name: "伯乐",
          },
        ];
        this.tabsValue = 3;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f5f5f7;
  display: flex;
  flex-direction: column;

  .sticky-container {
    display: flex;
    flex-direction: column;
    padding-inline: 32rpx;
    gap: 32rpx;
    padding-block-start: 32rpx;
    background-color: #ffffff;

    .tabs-container {
      .tags-box {
        display: flex;
        align-items: center;
        gap: 16rpx;
        transition: all 0.2s ease-in;
        overflow: hidden;

        .tag {
          margin-block: 24rpx;
          padding-inline: 16rpx;
          padding-block: 6rpx;
          color: #777777;
          font-size: 24rpx;
          background-color: #ffffff;
          border: 1rpx #e6e6e6 solid;
          border-radius: 10rpx;
        }

        .is_active {
          background-color: #e8f1ff;
          color: #4f8cf0;
          border-color: #4f8cf0;
        }
      }
    }

    .title-box {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title {
        color: #333333;
        font-size: 32rpx;
      }

      .sub-title {
        color: #666666;
        font-size: 28rpx;
      }
    }

    .desc {
      color: #666666;
      font-size: 24rpx;
    }
  }

  .scroll-view {
    flex: 1;
    overflow-y: auto;

    .scroll-container {
      display: flex;
      flex-direction: column;
      padding-inline: 32rpx;
      padding-block-start: 32rpx;
      gap: 32rpx;
      padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
      padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

      .time {
        color: #666666;
        font-size: 24rpx;
      }

      .item {
        background-color: #ffffff;
        border-radius: 24rpx;
        padding: 32rpx;
        display: flex;
        flex-direction: column;
        gap: 24rpx;
        .ava {
          width: 90rpx;
          height: 90rpx;
          background: #d9d9d9;
          border-radius: 12rpx;
        }
        .jy-ava {
          width: 88rpx;
          height: 88rpx;
          background: #d9d9d9;
          border-radius: 44rpx;
        }
        .item-right {
          display: flex;
          align-items: center;
          gap: 24rpx;
        }
        .item-right-inn {
          display: flex;
          flex-direction: column;
          gap: 12rpx;
        }
        .jy-tag-box {
          display: flex;
          align-items: center;
          gap: 16rpx;
          margin-top: 12rpx;
          .tag {
            background: rgba(188, 213, 255, 0.2);
            color: #7286a6;
            padding: 12rpx;
            border-radius: 8rpx;
            font-size: 22rpx;
          }

          .find-box {
            display: flex;
            align-items: center;
            margin-inline-start: auto;

            .image {
              width: 32rpx;
              height: 32rpx;
            }

            .number {
              color: #666666;
              font-size: 20rpx;
            }
          }
        }
        .tag-box {
          display: flex;
          align-items: center;
          gap: 16rpx;
          margin-top: 12rpx;
          .tag {
            background-color: #f6f6f6;
            color: #666666;
            padding: 12rpx;
            border-radius: 8rpx;
            font-size: 22rpx;
          }

          .find-box {
            display: flex;
            align-items: center;
            margin-inline-start: auto;

            .image {
              width: 32rpx;
              height: 32rpx;
            }

            .number {
              color: #666666;
              font-size: 20rpx;
            }
          }
        }
        .item-end {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 4rpx;

          .image {
            width: 48rpx;
            height: 48rpx;
          }

          .text {
            color: #666666;
            font-size: 20rpx;
          }

          .margin-text {
            margin-inline-end: auto;
          }
        }

        .item-center {
          display: flex;
          flex-direction: column;
          gap: 24rpx;

          .info-box {
            display: flex;
            flex-direction: column;
            gap: 12rpx;

            .detail {
              color: #666666;
              font-size: 24rpx;
            }

            .location-box {
              display: flex;
              align-items: center;
              gap: 12rpx;

              .image {
                width: 32rpx;
                height: 32rpx;
              }

              .position {
                color: #666666;
                font-size: 24rpx;
              }

              .distance {
                color: #666666;
                font-size: 20rpx;
                padding-inline: 8rpx;
                padding-block: 4rpx;
                border-radius: 999rpx;
                background-color: #f6f6f6;
              }
            }
          }

          .title {
            color: #333333;
            font-size: 28rpx;
          }
        }
        .btn-box {
          margin-top: 24rpx;
          .btn {
            width: 136rpx;
            height: 58rpx;
            background: linear-gradient(92deg, #4f8cf0 0%, #0061ff 100%);
            border-radius: 8rpx;
            font-size: 24rpx;
            color: #ffffff;
            float: right;
            text-align: center;
            line-height: 58rpx;
          }
        }

        .item-start {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 12rpx;

          .image {
            width: 48rpx;
            height: 48rpx;
          }

          .text_1 {
            color: #333;
            font-size: 28rpx;
          }
          .rz {
            width: 92rpx;
            height: 44rpx;
            background: #ecf5ff;
            border-radius: 8rpx 8rpx 8rpx 8rpx;
            border: 2rpx solid #dbedff;
            font-size: 20rpx;
            color: #4f8cf0;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .text_2 {
            color: #4f8cf0;
            font-size: 32rpx;
            margin-inline: auto;
          }

          .text_3 {
            color: #f98a14;
            font-size: 40rpx;
          }
        }
      }
      .jl-item {
        background-color: #ffffff;
        border-radius: 24rpx;
        padding: 32rpx;
        display: flex;
        justify-content: space-between;
        gap: 24rpx;
        margin-bottom: 24rpx;
        .avater {
          width: 104rpx;
          height: 104rpx;
          border-radius: 50%;
        }

        .item-end {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 28rpx;

          .name {
            color: #666;
            font-size: 24rpx;
          }

          .address {
            margin-inline-start: auto;
            color: #4f8cf0;
            font-size: 24rpx;
            display: flex;
            align-items: center;

            .radi {
              width: 8rpx;
              height: 8rpx;
              border-radius: 4rpx;
              background: #4f8cf0;
              margin-right: 12rpx;
            }
          }
        }

        .item-start {
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 24rpx;

          .time {
            color: #666;
            font-size: 24rpx;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
          }

          .type-text {
            font-size: 28rpx;
            color: #041024;
          }

          .title-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 12rpx;
            font-size: 32rpx;
            color: #333333;

            .title-inner {
              display: flex;
              align-items: center;
              gap: 12rpx;

              .tit-inn {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 12rpx;

                .age {
                  font-size: 24rpx;
                  color: #666666;
                }
              }
            }

            .title_1 {
              // flex: 1;
              width: 160rpx;

              overflow: hidden;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
            }

            .title_2 {
              color: #f98a14;
              white-space: nowrap;
              margin-left: 100rpx;
            }

            .title_3 {
              color: #4f8cf0;
              white-space: nowrap;
              margin-left: 100rpx;
            }
          }
        }
      }
    }
  }
}
</style>
