<template>
  <view class="warp">
    <!-- 步骤指示器 -->
    <view class="steps-container">
      <u-steps :current="step" dot="true">
        <u-steps-item title="基础信息"></u-steps-item>
        <u-steps-item title="电子邮箱"></u-steps-item>
      </u-steps>
    </view>

    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 发票内容 -->
      <view class="form-item">
        <view class="form-label required">电子邮箱</view>
        <view class="form-content">
          <input
            class="form-input"
            type="text"
            v-model="formData.email"
            placeholder="填写电子邮箱"
            placeholder-class="placeholder"
          />
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="top-btn" @click="topStep">上一步</view>
      <view class="next-btn" @click="nextStep">下一步</view>
    </view>
  </view>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      step: 1,
      formData: {
        email: "",
      },
    };
  },
  created() {},
  methods: {
    // 下一步
    nextStep() {
      // 验证必填字段
      if (!this.validateForm()) {
        return;
      }

      uni.navigateTo({
        url: `/pagesB/invoiceCenter/components/addInvoiceList`,
      });
    },
    topStep() {
      uni.navigateBack();
    },

    // 验证表单
    validateForm() {
      // 验证发票抬头
      if (!this.formData.email) {
        uni.showToast({
          title: "请输入电子邮箱",
          icon: "none",
        });
        return false;
      }
      return true;
    },
  },
};
</script>

<style lang="scss" scoped>
.warp {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f7;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 120rpx;
}

/* 步骤指示器样式 */
.steps-container {
  background-color: #ffffff;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f7;
}

.step-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.step-dot {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
}

.dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #cccccc;
  margin-bottom: 16rpx;
}

.step-dot.active .dot {
  background-color: #4f8cf0;
}

.step-text {
  font-size: 28rpx;
  color: #999999;
}

.step-text.active {
  color: #333333;
  font-weight: 500;
}

.step-line {
  height: 2rpx;
  background-color: #cccccc;
  flex: 1;
  margin: 0 16rpx;
  position: relative;
  top: -20rpx;
}

/* 表单容器样式 */
.form-container {
  background-color: #ffffff;
  padding: 0 32rpx;
  box-sizing: border-box;
  margin: 32rpx 32rpx 118rpx 32rpx;
  border-radius: 24rpx;
}
.tips {
  height: 44rpx;
  background: #e8f1ff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-size: 20rpx;
  color: #4f8cf0;
  line-height: 44rpx;
  // box-sizing: border-box;
  padding-left: 16rpx;
}

/* 表单项样式 */
.form-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f5f5f7;
}

.form-label {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
  flex-shrink: 0;
  width: 200rpx;
}

.form-label.required::after {
  content: " *";
  color: #ff0000;
}

.form-content {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  text-align: right;
}

.form-input {
  width: 100%;
  height: 60rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: right;
}

.placeholder {
  color: #cccccc;
  font-size: 28rpx;
  text-align: right;
}

/* 标签组样式 */
.tab-group {
  display: flex;
  gap: 32rpx;
  justify-content: flex-end;
}

.tab-item {
  padding: 12rpx 24rpx;
  border: 1rpx solid #e6e6e6;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666666;
  text-align: center;
}

.tab-item.active {
  border-color: #4f8cf0;
  color: #4f8cf0;
  background-color: #f2f7ff;
}

/* 底部按钮样式 */
.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 32rpx;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.next-btn {
  width: 45%;
  height: 88rpx;
  background-color: #4f8cf0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
}
.top-btn {
  width: 45%;
  height: 88rpx;
  background: #e8f1ff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4f8cf0;
  font-size: 32rpx;
  font-weight: 500;
}
</style>
