<template>
	<view class="container">
		<view style="height: 194rpx; margin-bottom: 38rpx;">
			<view style="display: flex; justify-content: space-between; align-items: center;">
				<view class="title">资格证书</view>
				<view class="total-selected-count">{{totalSelectedCount}}/30</view>
			</view>
			<view class="search-box">
				<u--input placeholder="搜索证书名称" prefixIcon="search" prefixIconStyle="font-size: 22px;color: #909399"
					style=""></u--input>
			</view>
			<!-- 新增显示选中模块区域 -->
			<view class="selected-items-view">
				<view
					style="width: 96rpx; height: 42rpx; margin-left: 8rpx;font-size: 24rpx;text-align: center;display: flex; align-items: center;">
					已选：
				</view>
				<scroll-view class="scroll-view_H" scroll-x="true">
					<view style="display: flex; align-items: center;">
						<view v-for="(selectedItem, index) in selectedItemsView" :key="index" style="padding:6rpx 8rpx; font-size: 20rpx; background-color: rgba(232, 241, 255, 1); color: rgba(79, 140, 240, 1);
							 margin: 0rpx 8rpx; display: flex; align-items: center;">
							{{selectedItem.context}}
							<img src="/static/images/Apages/close.png" mode=""
								style="width: 24rpx; height: 24rpx; align-self: center; margin-left: 6rpx;"
								@click="reducetags(index)">
							</img>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<view class="job-list-container">
			<view class="joblist-left">
				<scroll-view scroll-y="true" class="scroll-Y">
					<view
						style="display: flex; flex-direction: column; justify-content: space-evenly; align-items: center; width: 260rpx;">
						<view v-for="(category, index) in jobList" :key="index" class="job-category"
							@click="toggleChecked(index)" :class="category.checked? 'active':'job-category' ">
							<text class="category-name"
								:class="category.checked? 'active-item':'category-name' ">{{category.name}}</text>
							<!-- 新增显示该类别选中数量 -->
							<text class="selected-count" v-if="category.selectedCount > 0">
								({{category.selectedCount}})</text>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="joblist-right">
				<scroll-view scroll-y="true" class="scroll-Y">
					<view
						style="display: flex; flex-direction: column; justify-content: space-evenly; align-items: center;">
						<view v-for="(category, index) in jobList" :key="index">
							<radio-group v-model="category.list">
								<view class="job-item" v-for="(job, jobIndex) in category.list" :key="jobIndex"
									v-if="category.checked">
									<view class="context-box" @click="toggle(job)">
										<text class="job-context">{{job.context}}</text>
									</view>
								</view>
							</radio-group>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<view class="footer">
			<button class="confirm-btn" @click="submitInfo">提交</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				jobList: [{
						name: '英语类',
						checked: false,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: true
							},
							{
								name: '4',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					},
					{
						name: '外语类',
						checked: true,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: true
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: false
							},
							{
								name: '4',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					},
					{
						name: 'IT类',
						checked: false,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: true
							},
							{
								name: '4',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					},
					{
						name: '项目管理类',
						checked: false,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: true
							},
							{
								name: '4',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					},
					{
						name: '会计类',
						checked: false,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: true
							},
							{
								name: '4',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					},
					{
						name: '审计类',
						checked: false,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: true
							},
							{
								name: '4',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					},
					{
						name: '统计类',
						checked: false,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: true
							},
							{
								name: '4',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					},
					{
						name: '金融类',
						checked: false,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: true
							},
							{
								name: '4',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					},
					{
						name: '法律类',
						checked: false,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: true
							},
							{
								name: '4',
								context: '大学英语四级',
								checked: true
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					},
					{
						name: '教育类',
						checked: false,
						selectedCount: 0,
						list: [{
								name: '1',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '2',
								context: '大学英语四级',
								checked: true
							},
							{
								name: '3',
								context: '大学英语四级、',
								checked: false

							},
							{
								name: '4',
								context: '大学英语四级',
								checked: false
							},
							{
								name: '5',
								context: '大学英语四级、',
								checked: false
							}
						]
					}
				],
				job: [],
				selectedItemsView: [],
				totalSelectedCount: 0,
			}
		},
		methods: {
			toggleChecked(index) {
				this.jobList.forEach((item) => {
					item.checked = false;
					item.selectedCount--; // 取消选中时，该类别选中数量清零
				});
				this.jobList[index].checked = true;
				this.jobList[index].selectedCount = this.jobList[index].list.filter(job => job.checked).length; // 重新计算选中数量
				this.totalSelectedCount = this.jobList.reduce((sum, item) => sum + item.selectedCount, 0); // 重新计算总选中数量
			},
			toggle(item) {
				console.log(item);
				this.job.push(item)
				console.log(this.job);
				// 找到对应的类别并更新选中数量
				const categoryIndex = this.jobList.findIndex(cat => cat.list.includes(item));
				if (categoryIndex !== -1) {
					if (item.checked) {
						this.jobList[categoryIndex].selectedCount++;
					} else {
						this.jobList[categoryIndex].selectedCount--;
					}
					this.totalSelectedCount = this.jobList.reduce((sum, cat) => sum + cat.selectedCount, 0);
					// 这里添加将选中模块在搜索栏下显示的逻辑，假设添加到一个新视图selectedItemsView中
					this.selectedItemsView.push(item);
				}
			},
			submitInfo() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/hopePosition'
				})
			},
			reducetags(index) {
				const selectedItem = this.selectedItemsView[index];
				// 找到对应的类别并更新选中数量
				const categoryIndex = this.jobList.findIndex(cat => cat.list.includes(selectedItem));
				if (categoryIndex !== -1) {
					// 假设 item 有 checked 属性来判断是否选中
					selectedItem.checked = false;
					this.jobList[categoryIndex].selectedCount--;
					// 从显示区域移除未选中项
					this.selectedItemsView.splice(index, 1);
					this.totalSelectedCount = this.jobList.reduce((sum, cat) => sum + cat.selectedCount, 0);
				}
			}
		}
	}
</script>

<style scoped>
	.container {
		height: 100vh;
		/* padding: 32rpx; */
		/* background-color: rgba(245, 245, 247, 1); */
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.title {
		font-size: 32rpx;
		margin-left: 32rpx;
		margin-top: 32rpx;
	}

	.scroll-view_H {
		white-space: nowrap;
		/* flex: 1; */
		width: 89%;
	}

	.scroll-Y {
		height: 100%;
	}

	.search-box {
		height: 64rpx;
	}

	.job-list-container {
		padding: 10px;
		display: flex;
		flex: 1;
		justify-content: space-around;
	}

	.joblist-left {
		display: flex;
		flex-direction: column;
		background: rgba(245, 245, 245, 1);
		/* justify-content: space-evenly; */
	}

	.joblist-right {
		width: 426rpx;
		padding: 0rpx 32rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.job-category {
		height: 88rpx;
		/* margin-bottom: 15px; */
		display: flex;
		justify-content: start;
		align-items: center;
		width: 234rpx;
		padding-left: 24rpx;
	}

	.category-name {
		font-size: 28rpx;
		color: rgba(119, 119, 119, 1);
		margin-bottom: 5px;
		overflow: hidden;
		/* 隐藏溢出的文本 */
		white-space: nowrap;
		/* 保持文本在一行内显示 */
		text-overflow: ellipsis;

	}

	.active {
		color: rgba(79, 140, 240, 1);
		;
	}

	.job-item {
		width: 362rpx;
		margin-bottom: 10px;
		background-color: #f5f5f5;
		border-radius: 8px;
		padding: 32rpx;
	}

	.job-label {
		/* width: 108rpx; */
		height: 40rpx;
		/* justify-content: space-between; */
		display: flex;
		align-items: center;
	}

	.job-label text {
		font-size: 28rpx;
		color: rgba(51, 51, 51, 1);
	}


	.job-context {
		margin-left: 10px;
		font-size: 14px;
		line-height: 1.4;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.context-box {
		width: 362rpx;
		height: 68rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
		color: rgba(51, 51, 51, 1);
	}

	.context-box text {
		width: 230rpx;
		font-size: 28rpx;
		display: -webkit-box;
		/*弹性伸缩盒子模型显示*/
		-webkit-box-orient: vertical;
		/*排列方式*/
		-webkit-line-clamp: 2;
		/*显示文本行数(这里控制多少行隐藏)*/
		overflow: hidden;
		/*溢出隐藏*/
	}

	.context-box img {
		width: 32rpx;
		height: 32rpx;
		align-self: self-end;
	}

	::v-deep .u-input {
		width: 686rpx;
		height: 64rpx;
		background-color: rgba(243, 243, 243, 1);
		border-radius: 126rpx;
		padding: 0rpx !important;
		margin: 32rpx auto;
	}

	.active {
		background-color: rgba(255, 255, 255, 1);
		position: relative;
		/* 点击后改变的背景颜色 */
	}

	.active-item {
		color: rgba(79, 140, 240, 1);
	}

	.active:before {
		content: '';
		width: 6rpx;
		height: 88rpx;
		background-color: rgba(79, 140, 240, 1);
		position: absolute;
		left: 0rpx;
	}

	.popup-content {
		background-color: #fff;
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
		box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
		padding: 15px;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}

	.popup-title {
		font-size: 18px;
		font-weight: 500;
	}

	.popup-close {
		width: 20px;
		height: 20px;
	}

	.popup-body {
		line-height: 1.5;
		font-size: 14px;
	}

	.header {
		width: 686rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.selected-count {
		color: #909399;
		font-size: 12px;
	}

	.selected-items-view {
		display: flex;
		/* flex-wrap: nowrap; */
		padding: 10rpx;
		/* height: 40rpx; */
		/* margin-top: 24rpx; */
	}

	.total-selected-count {
		text-align: center;
		font-size: 14px;
		color: #333;
	}
</style>