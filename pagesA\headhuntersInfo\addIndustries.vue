<template>
  <view class="warp">
    <view class="inner">
      <view class="list">
        <view class="yixuan">已选</view>
        <scroll-view class="scroll-view_H" scroll-x="true" scroll-left="120">
          <view class="item" v-for="item in list" :key="item">
            <text>{{ item }}</text>
            <u-icon
              name="close"
              color="#4F8CF0"
              size="14"
              @click="delItem(item)"
            ></u-icon>
          </view>
        </scroll-view>
      </view>
      <view class="sear-box">
        <u-search actionText="添加"></u-search>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      list: [],
      type: "1",
    };
  },
  onLoad: function (options) {
    this.type = options.type;
    if (this.type == "2") {
      uni.setNavigationBarTitle({
        title: "添加擅长职业",
      });
    } else if (this.type == "3") {
      uni.setNavigationBarTitle({
        title: "添加服务人群",
      });
    } else if (this.type == "4") {
      uni.setNavigationBarTitle({
        title: "添加我的标签",
      });
    }
    const myList = uni.getStorageSync("myList");
    console.log(myList);
    this.list = myList;
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.inner {
  padding: 32rpx;

  .list {
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .yixuan {
      flex: 1;
      height: 52rpx;
      background: #ffffff;
      font-size: 24rpx;
      color: #333333;
    }

    .scroll-view_H {
      white-space: nowrap;
      width: 90%;
    }

    .item {
      display: inline-flex;
      align-items: center;
      gap: 14rpx;
      padding: 10rpx 24rpx;
      background: #eef4ff;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-size: 24rpx;
      color: #4f8cf0;
      margin-right: 24rpx;
    }
  }
}
</style>
