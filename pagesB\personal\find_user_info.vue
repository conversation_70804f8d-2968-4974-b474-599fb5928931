<template>
	<!-- 个人信息查看 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<u-cell-group title="基本信息" :border="false">
					<u-cell title="头像" :titleStyle="{fontSize: '28rpx', color: '#333333'}">
						<template #value>
							<image class="avater" src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"></image>
						</template>
					</u-cell>
					<u-cell title="姓名" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
					<u-cell title="手机号" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
					<u-cell title="参加工作时间" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
					<u-cell title="微信号" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
					<u-cell title="出生年月" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
					<u-cell title="出生地" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
				</u-cell-group>

				<u-cell-group title="在线简历" :border="false">
					<u-cell title="在线简历" :titleStyle="{fontSize: '28rpx', color: '#333333'}"></u-cell>
					<u-cell title="自我介绍" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
					<u-cell title="行业" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
					<u-cell title="工作地点" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
					<u-cell title="工作经历" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
					<u-cell title="教育经历" :titleStyle="{fontSize: '22rpx', color: '#666666'}">
						<template #label>
							<text class="label">娃哈哈</text>
						</template>
					</u-cell>
				</u-cell-group>
			</view>
		</scroll-view>

		<view class="btn-container">
			<view class="btn" @click="onRoute('export_user_info')">导出个人信息</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			onRoute(url) {
				uni.$u.route({
					url: `/pagesB/personal/${url}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	::v-deep .u-cell {
		.u-cell__body {
			padding-inline: 0 !important;
		}
	}

	::v-deep .u-cell-group {
		.u-cell-group__title {
			padding-inline: 0 !important;
			font-size: 32rpx;
			color: #333333;
		}
	}

	.container {
		height: 100vh;
		background-color: #FFFFFF;
		display: flex;
		flex-direction: column;

		.btn-container {
			padding-inline: 32rpx;
			padding-block-start: 32rpx;
			padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

			.btn {
				background-color: #4F8CF0;
				color: #FFFFFF;
				font-size: 28rpx;
				border-radius: 12rpx;
				padding-block: 20rpx;
				text-align: center;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				display: flex;
				flex-direction: column;
				padding: 32rpx;
				gap: 32rpx;

				.avater {
					width: 88rpx;
					height: 88rpx;
					border-radius: 999rpx;
				}

				.label {
					font-size: 28rpx;
					color: #333333;
				}
			}
		}
	}
</style>
