<template xmlns="">
  <u-index-list :indexList="indexList">
    <view slot="header" class="list">
      <view class="header-list">
        <view class="item" @click="newFriend()">
          <view class="item-left">
            <view class="name"> 新的朋友 </view>
          </view>
          <u-icon name="arrow-right" size="14"></u-icon>
        </view>
        <view class="item" @click="addFriend">
          <view class="item-left">
            <view class="name"> 添加朋友 </view>
          </view>
          <u-icon name="arrow-right" size="14"></u-icon>
        </view>
      </view>
      <!-- <view class="list__item" @click="newFriend()">
        <u-avatar shape="square" size="35" icon="man-add-fill" fontSize="26" randomBgColor></u-avatar>
        <text class="list__item__user-name">新的朋友</text>
      </view>
      <u-line></u-line>
      <view class="list__item" @click="addFriend">
        <u-avatar shape="square" size="35" icon="tags-fill" fontSize="26" randomBgColor></u-avatar>
        <text class="list__item__user-name">添加好友</text>
      </view>
      <u-line></u-line> -->
      <!--      <view class="list__item">-->
      <!--        <u-avatar shape="square" size="35" icon="chrome-circle-fill" fontSize="26" randomBgColor></u-avatar>-->
      <!--        <text class="list__item__user-name">朋友圈</text>-->
      <!--      </view>-->
      <!--      <u-line></u-line>-->
      <!--      <view class="list__item">-->
      <!--        <u-avatar shape="square" size="35" icon="qq-fill" fontSize="26" randomBgColor></u-avatar>-->
      <!--        <text class="list__item__user-name">QQ</text>-->
      <!--      </view>-->
      <!--      <u-line></u-line>-->
    </view>
    <template v-for="(item, index) in list">
      <!-- #ifdef APP-NVUE -->
      <u-index-anchor :text="indexList[index]" :key="index"></u-index-anchor>
      <!-- #endif -->
      <u-index-item :key="index">
        <!-- #ifndef APP-NVUE -->
        <u-index-anchor :text="indexList[index]"></u-index-anchor>
        <!-- #endif -->
        <view
          class="list"
          v-for="(item1, index1) in item"
          :key="index1"
          @click.stop="communicate('user_headhunter', item1.to_user_id)"
        >
          <view class="list__item">
            <image
              class="list__item__avatar"
              :src="item1.member_info.image.thumbnail_path_url"
            ></image>
            <text class="list__item__user-name">{{
              item1.member_certification.name
            }}</text>
          </view>
          <u-line></u-line>
        </view>
      </u-index-item>
    </template>
    <!--    <view slot="footer" class="u-safe-area-inset&#45;&#45;bottom">-->
    <!--      <text class="list__footer">共305位好友</text>-->
    <!--    </view>-->
  </u-index-list>
</template>

<script>
import { friendAgree, friendList } from "../../config/common_api";
export default {
  data() {
    return {
      page: 1,
      limit: 10,
      list: [],
      indexList: "",
    };
  },
  computed: {
  },
  methods: {
    communicate(){
      uni.navigateTo({
        url: `/pagesC/connectivity/detail`,
      });
    },
    // 添加好友
    addFriend() {
      uni.navigateTo({
        url: `/pagesA/connectivity/add_friend`,
      });
    },
    //新好友
    newFriend() {
      uni.navigateTo({
        url: `/pagesA/connectivity/new_friend`,
      });
    },
    async friendList() {
      const { status_code, data } = await friendList();
      if (status_code == "200") {
        this.list = data.value;
        this.indexList = data.index;
        console.log("`````````````", data.value);
      }
    },
    onLoad() {
      this.friendList();
    },
  },
};
</script>
<style>
page {
  background-color: #f5f5f7;
}
/* 全局样式，不使用scoped */
.u-index-anchor {
  background-color: #f5f5f7 !important;
}
</style>
<style lang="scss" scoped>
/* 使用多种深度选择器写法，增加兼容性 */
:deep(.u-index-anchor) {
  background: #f5f5f7 !important;
}
.list {
  &__item {
    @include flex;
    padding: 6px 12px;
    background: #fff !important;
    align-items: center;

    &__avatar {
      height: 35px;
      width: 35px;
      border-radius: 3px;
    }

    &__user-name {
      font-size: 16px;
      margin-left: 10px;
      color: $u-main-color;
    }
  }

  &__footer {
    color: $u-tips-color;
    font-size: 14px;
    text-align: center;
    margin: 15px 0;
  }
  .header-list {
    display: flex;
    flex-direction: column;
    background: #ffffff;

    &:first-child {
      // margin-bottom: 40rpx;
    }

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 120rpx;
      padding: 0 20rpx;
      border-bottom: 1px solid #f5f5f7;

      &:last-child {
        border-bottom: none;
      }

      .item-left {
        display: flex;
        align-items: center;
        image {
          width: 72rpx;
          height: 72rpx;
          margin-right: 16rpx;
        }

        .name {
          font-weight: 400;
          font-size: 28rpx;
          color: #333333;
        }
      }
    }
  }
}
</style>
