<template>
  <view class="warp">
    <view
      v-for="(invoice, index) in invoiceList"
      :key="index"
      class="invoice-detail"
    >
      <!-- 发票信息头部 -->
      <view class="invoice-header">
        <view class="invoice-type-box">
          <text class="invoice-type">{{ invoice.type }}</text>
          <text
            :class="
              invoice.status == '已开出' ? 'invoice-status' : 'invoice-status1'
            "
            >{{ invoice.status }}</text
          >
        </view>
        <text class="invoice-amount">{{ invoice.amount }}元</text>
      </view>

      <!-- 公司信息 -->
      <view class="company-info">
        <text class="company-name">{{ invoice.company }}</text>
      </view>
      <u-line></u-line>
      <!-- 发票详细信息 -->
      <view class="invoice-info">
        <view class="info-item">
          <text class="info-label">发票号码：</text>
          <text class="info-value">{{ invoice.invoiceNumber }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">申请时间：</text>
          <text class="info-value">{{ invoice.applyTime }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">开出时间：</text>
          <text class="info-value">{{ invoice.issueTime }}</text>
        </view>
      </view>
      <u-line></u-line>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view
          class="action-btn apply-btn"
          v-if="invoice.status == '已开出'"
          @click="applyExchange(invoice)"
          >申请换开</view
        >
        <view class="action-btn send-btn" @click="sendInvoiceDetail(invoice)">{{
          invoice.status == "已开出" ? "发送订单明细" : "查看详情"
        }}</view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next-btn">咨询在线客服</view>
    </view>

    <!--@open="open" -->
    <u-popup :show="show1" mode="bottom" @close="show1 = false">
      <view class="pop">
        <view class="pop-tit">
          <text>发送订单明细</text>
          <u-icon name="close" color="#666666" size="24"></u-icon>
        </view>
        <view class="pop-cont">订单明细接收邮箱</view>
        <u--input
          placeholder="填写后，可接收订单明细，地址以填写的邮箱为准"
          border="bottom"
          clearable
        ></u--input>
        <view class="pop-btn" @click="send">确定</view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      show1: false,
      invoiceList: [
        {
          id: 1,
          type: "数电普票",
          status: "已开出",
          amount: "2657",
          company: "学创联盟网络科技（北京）有限公司",
          invoiceNumber: "25117000000659783704",
          applyTime: "2025-05-09 17:06:27",
          issueTime: "2025-05-09 17:06:27",
        },
        {
          id: 2,
          type: "数电普票",
          status: "已开出",
          amount: "1980",
          company: "学创联盟网络科技（北京）有限公司",
          invoiceNumber: "25117000000659783705",
          applyTime: "2025-05-08 14:30:15",
          issueTime: "2025-05-08 14:30:15",
        },
        {
          id: 3,
          type: "数电普票",
          status: "开票中",
          amount: "3500",
          company: "学创联盟网络科技（北京）有限公司",
          invoiceNumber: "25117000000659783706",
          applyTime: "2025-05-07 09:15:42",
          issueTime: "2025-05-07 09:15:42",
        },
      ],
    };
  },
  methods: {
    // 申请换开发票
    applyExchange(item) {
      uni.navigateTo({
        url: `/pagesB/invoiceCenter/components/addInvoice?amount=${
          item.amount
        }&selectedIds=${JSON.stringify(item.company)}`,
      });
    },

    // 发送订单明细
    sendInvoiceDetail(item) {
      if (item.status == "已开出") {
        this.show1 = true;
      } else {
        uni.navigateTo({
          url: `/pagesB/invoiceCenter/components/addInvoiceDetail`,
        });
      }
    },
    send() {
      uni.showToast({
        title: "订单明细已发送，请前往邮箱查看",
        icon: "none",
      });
      this.show1 = false;
    },

    // 联系客服
    contactCustomerService() {
      uni.showToast({
        title: "正在连接客服",
        icon: "none",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.warp {
  padding: 32rpx 32rpx 220rpx;
  background-color: #f5f5f7;
}

.invoice-detail {
  margin-bottom: 32rpx;
  position: relative;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  background: #fff;
}

/* 头部样式 */
.invoice-header {
  background-color: #fff;
  padding: 32rpx 32rpx 14rpx 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.invoice-type-box {
  display: flex;
  align-items: center;
}

.invoice-type {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.invoice-status {
  font-size: 20rpx;
  color: #4f8cf0;
  background-color: #fff;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
  border: 1rpx solid #4f8cf0;
}
.invoice-status1 {
  font-size: 20rpx;
  color: #f9ad14;
  background-color: #fff;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
  border: 1rpx solid #f9ad14;
}

.invoice-amount {
  font-size: 32rpx;
  color: #4f8cf0;
}

/* 公司信息样式 */
.company-info {
  background-color: #fff;
  padding: 0 32rpx 16rpx;
}

.company-name {
  font-size: 24rpx;
  color: #333;
}

/* 发票详情样式 */
.invoice-info {
  background-color: #fff;
  padding: 24rpx 32rpx 32rpx 32rpx;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: #333;
}

.info-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 32rpx;
  padding: 0 32rpx;
  margin-top: 24rpx;
  margin-bottom: 32rpx;
}

.action-btn {
  height: 50rpx;
  padding: 8rpx 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50rpx;
  font-size: 24rpx;
}

.apply-btn {
  border: 2rpx solid #999;
  color: #666;
  background-color: #fff;
}

.send-btn {
  border: 2rpx solid #999;
  color: #666;
  background-color: #fff;
}

.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 32rpx;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  box-sizing: border-box;
}

.next-btn {
  width: 100%;
  height: 88rpx;
  background-color: #4f8cf0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
}
.pop {
  padding: 32rpx;
  .pop-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
    color: #999999;
    margin-bottom: 32rpx;
  }
  .pop-cont {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 32rpx;
  }
  .pop-my {
    font-size: 28rpx;
    color: #999999;
  }
  .pop-btn {
    width: 100%;
    height: 80rpx;
    background: #4f8cf0;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    line-height: 80rpx;
    margin-top: 40rpx;
  }
}
</style>
