<template>
    <view id="app">
       <!-- <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="search-wrap">
                    <u-search placeholder="请输入姓名或手机号" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
                </view>
            </view>
        </u-sticky>
        <view style="height: 32rpx;"></view> -->
        <view class="content" v-for="(item, index) in list" :key="item.id" @click="toggleSelect(item)" :class="{ selected: isSelected(item) }">
            <view >{{ item.member_certification.name?item.member_certification.name : item.name}}</view>
        </view>
        <Pages :status="status"></Pages>
        <view style="height: 196rpx;"></view>
        <view @click="selected" class="btn">
            <view class="btnCla">选择完成</view>
        </view>
    </view>
</template>

<script>
    import Pages from "../../components/pages.vue";
    import { selectPeopleQY } from "../../config/company_api.js"
    import { selectPeopleJYGJ } from "../../config/headhunterList_api.js"
    export default {
        data() {
            return {
                list: [],
                selectedItems: [], // 用于存储选中的项
                type: '',
                more: false,
                status: 'loadmore',
                page: 1,
                limit: 10,
            }
        },
        
       //触底加载更多
       onReachBottom() {
           if (this.more) {
               this.status = 'loading'
               this.page++
               if (this.roleType == 'headhunters') {
                   this.selectPeopleJYGJ()
               } else {
                   this.selectPeopleQY()
               }
           } else {
               this.status = 'nomore'
           }
       },
        computed: {
            roleType() {
                return this.$store.state.roleType || uni.getStorageSync('roleType')
            },
        },
        onLoad(options) {
            if(this.roleType == 'headhunters') {
                this.selectPeopleJYGJ()
            } else {
                this.selectPeopleQY()
            }
            // 单选是1多选是2
            this.type = options.type
        },
        methods: {
            // 在这里拿到所需要的值
            selected() {
                const newArray = this.selectedItems.map(item => {
                  return { id: item.id, name: item.member_certification.name };
                });
                if(this.type == 1) {
                    this.$store.commit('setExecutor',newArray)
                } else {
                    this.$store.commit('setCollaborator',newArray)
                }
                uni.navigateBack()
            },
            async selectPeopleQY() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    search:this.search
                }
                
                const { status_code, data } = await selectPeopleJYGJ(params)
                if (status_code == 200) {
                   this.list = this.list.concat(data.data);
                   this.more = data.more;
                   this.status = this.more ? "loadmore" : "nomore"
                }
            },
            async selectPeopleJYGJ() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    search:this.search
                }

                const { status_code, data } = await selectPeopleJYGJ(params)
                if (status_code == 200) {
                    this.list = this.list.concat(data.data);
                    this.more = data.more;
                    this.status = this.more ? "loadmore" : "nomore"
                }
            },
            toggleSelect(item) {
                if (this.type == 2) { // 根据进入页面的type判断是多选还是单选
                    const index = this.selectedItems.findIndex(selected => selected.id === item.id);
                    if (index > -1) {
                        this.selectedItems.splice(index, 1); // 取消选择
                    } else {
                        this.selectedItems.push(item); // 添加选择
                    }
                } else {
                    this.selectedItems = [item]; // 单选情况下只保留当前选中项
                }
            },
            isSelected(item) {
                return this.selectedItems.some(selected => selected.id === item.id);
            }
        }
    }
</script>

<style lang="scss">
    @import "../../static/css/pagesA/select/selectCompany";
</style>