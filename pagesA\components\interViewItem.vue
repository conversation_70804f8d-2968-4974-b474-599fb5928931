<template>
	<view class="item" @click.stop="openDetails(item)">
		<!-- <block v-if="item.status==2 || item.status==3 || item.status==4">
            <view :class="['status',item.status==2||item.status==3?'wait':item.status==4?'yqx':'']">
                {{item.status==2?'等待面试':item.status==3?'等待结果':item.status==4?'已取消':''}}
            </view>
        </block> -->
		<!-- <block v-if="item.sign_status=='singed' && item.interview_result.length <= 0">
            <view class="status wait">
                等待面试
            </view>
        </block> -->
		<!-- v-else -->
		<block>
			<view class="status greenBtn" v-if="item.interview_result.result_status=='pass'">
				通过
			</view>
			<view class="status redBtn" v-if="item.interview_result.result_status=='eliminate'">
				淘汰
			</view>
			<view class="status yellowBtn" v-if="item.interview_result.result_status=='alternate'">
				审核中
			</view>
			<view class="status blue" v-if="item.interview_result.result_status=='none'">
				等待面试
			</view>
			<view class="status greyBtn" v-if="item.interview_result.result_status=='cancel'">
				已取消
			</view>
			<view class="status greenBtn" v-if="item.interview_result.result_status=='ongoing'">
				进行中
			</view>
		</block>

		<view class=" item-up" v-if="roleType=='headhunters'">
			<view class="info">
				<image :src="item.member_info.image.thumbnail_path_url" mode=""></image>
				<view class="cont">
					<view class="sup">
						<view class="name">
							{{item.member_certification.name?item.member_certification.name:item.member_info.nick_name}}
						</view>
					</view>
					<view class="sub">
						{{item.job.title}}
					</view>
				</view>
			</view>
			<view class="date">
				<view class="time">
					面试时间：{{item.interviewed_at}}
				</view>
				<view class="more">
					<u-icon name="arrow-right" size="24rpx"></u-icon>
				</view>
			</view>
		</view>

		<view class="item-up" v-else>
			<view class="info">
				<image style="padding-right: 24rpx;width: 204rpx;height: 100rpx;"
					:src="item.company_info.logo.thumbnail_path_url" mode="" v-show="item.model_type=='job_fairs'">
				</image>
				<view class="cont">
					<view class="sup">
						<view class="name">
							{{item.job.title}}
						</view>
					</view>
					<view class="sub">
						{{item.job.company_name}}
					</view>
				</view>
			</view>
			<view class="date" v-if="item.model_type!=='job_fairs'">
				<view class="time">
					面试时间：{{item.interviewed_at}}
				</view>
				<view class="more" v-show="item.model_type!=='job_active'">
					<u-icon name="arrow-right" size="24rpx"></u-icon>
				</view>
			</view>
			<view v-show="item.model_type=='job_active'">
				<view class="jobActive">
					就业管家：{{item.contact_name}}
				</view>
				<view class='date'>
					<view class="time">
						联系电话：{{item.contact_cellphone}}
					</view>
					<view class="more" v-show="item.model_type!=='job_active'">
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<view :class="['item-down',item.status==1||item.status==2||item.status==3?'flexRow':'']"
			v-if="roleType=='headhunters'">

			<view class="btns">
				<view class="btn talk" @click.stop="communicate('interview',item.id)">
					聊聊呗
				</view>
				<view class="btn agree" v-if="item.sign_status=='singed'" @click.stop="approve(1)">
					审核
				</view>
			</view>
			<view class="reason" v-if="item.cancel_status==1">
				取消原因：{{item.cancel_reason}}
			</view>
		</view>
		<view :class="['item-down',item.status==1||item.status==2||item.status==3?'flexRow':'']" v-else>
			<view class="reason" v-if="item.cancel_status==1">
				取消原因：{{item.cancel_reason}}
			</view>
			<view class="btns">
				<!-- <view class="btn talk" @click.stop="openApprove">
					结果反馈
				</view> -->
				<view class="btn agree"
					v-if="item.interview_result.result_status=='none'&&item.model_type!=='job_fairs'"
					@click.stop="punch">
					打卡
				</view>

				<view class="btn talk" v-if="item.model_type!=='job_fairs'&&item.model_type!=='job_fairs'"
					@click.stop="communicate('interview',item.id)">
					聊聊呗
				</view>

				<view class="btn agree"
					v-if="item.interview_result.result_status=='pass'&&item.model_type!=='job_fairs'"
					@click.stop="approve(1)">
					同意
				</view>

				<view class="btn Cancel"
					v-if="item.interview_result.result_status=='pass'&&item.model_type!=='job_fairs'" @click.stop="
					approve(3)">
					取消
				</view>

				<view class="btn Cancel" v-if="item.model_type=='job_fairs'">
					取消报名
				</view>
				
				<view class="btn posts" v-if="item.model_type=='job_fairs'">
					职位列表
				</view>
			</view>
		</view>
		<!-- 展示面试结果 -->
		<view v-if="showDialog" class="custom-dialog" @click.stop="showDialog = false">
			<view class="dialog-content" @click.stop>
				<view class="dialog-title">结果反馈</view>
				<view style="margin-bottom: 40rpx;">
					结果：{{item.interview_result.examine_data}}
				</view>
				<u-input style="margin-bottom: 40rpx;" v-model="inputText" placeholder="请输入反馈" type="text"
					:border="true" />
				<view class="dialog-buttons">
					<button @tap.stop="handleAgree">同意</button>
					<button @tap.stop="handleRefuse">拒绝</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		communicate
	} from "../../common/common.js";
	export default {
		name: "interViewItem",
		props: {
			item: {
				type: Object,
				default: () => {}
			},
			itemIndex: {
				type: Number,
			},
		},
		data() {
			return {
				roleType: '',
				showDialog: false, // 面试结果布尔值
				inputText: ''
			};
		},
		mounted() {
			this.roleType = uni.getStorageSync('roleType')
		},
		methods: {
			communicate,
			openDetails() {
				this.$emit('open', this.item)
			},

			approve(status) {
				this.$emit('approve', {
					item: this.item,
					status: status,
					itemIndex: this.itemIndex,
				})
			},

			punch() {
				this.$emit('punch', {
					item: this.item,
					itemIndex: this.itemIndex,
				})
			},

			// 打开弹窗
			openApprove() {
				this.showDialog = true;
			},

			handleAgree() {
				// 处理同意的逻辑
				this.showDialog = false; // 关闭弹窗
				// this.approve(1); // 调用同意方法
				this.$emit('approve', {
					item: this.item,
					status: "pass",
					examine_data: this.inputText
				});
				this.inputText = '';
			},

			handleRefuse() {
				// 处理拒绝的逻辑
				this.showDialog = false; // 关闭弹窗
				// this.approve(3); // 调用拒绝方法
				this.$emit('approve', {
					item: this.item,
					status: "eliminate",
					examine_data: this.inputText
				});
				this.inputText = '';
			}
		}
	}
</script>

<style lang="scss" src="../../static/css/pagesA/components/interViewItem.scss">
</style>