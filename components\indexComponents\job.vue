<template>
	<view class="container">
		<u-sticky>
			<view class="tabs-container">
				<u-tabs :current="tabsIndex" :list="tabsList"
					:activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
					:inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }" @click="onTabsItemClick"></u-tabs>
			</view>
		</u-sticky>
		<view class="content">
			<view class="item" v-for="v in jobList" :key="v.id" @click="onDetail(v.id)">
				<view class="item-start">
					<image v-if="v.hot_status === 1" class="image" src="/static/new/火苗@2x.png"></image>
					<text class="text_1">Web 前端</text>
					<text class="text_2">全职</text>
					<text class="text_3">{{v.salary_info_str}}</text>
				</view>
				<view class="item-center">
					<view class="title">{{v.company_name}}</view>
					<view class="tag-box">
						<scroll-view :scroll-x="true" class="tags-scroll">
							<view class="tag" v-for="k in v.tags" :key="k.id">{{k.title}}</view>
						</scroll-view>
						<view class="find-box">
							<image class="image" src="/static/new/浏览量****************"></image>
							<text class="number">1024</text>
						</view>
					</view>
					<view class="info-box">
						<view class="text-box">
							<view class="detail">
								{{v.company_info.financing_type_name}}｜{{v.company_info.size_type_name}}
							</view>
							<view class="location-box">
								<image class="image" src="/static/new/定位@2x.png"></image>
								<text class="position">北京北京市昌平区1号</text>
								<view class="distance">距12.1km</view>
							</view>
						</view>
						<view class="btn" @click.stop @click="jumpLiao(v)">聊聊呗</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getJobList
	} from '@/config';
	export default {
		data() {
			return {
				tabsIndex: 0,
				tabsValue: '1',
				tabsList: [{
						key: '1',
						name: '推荐',
					},
					{
						key: '2',
						name: '全职',
					},
					{
						key: '3',
						name: '兼职',
					}
				],
				jobList: [],
				params: {
					limit: 20,
					page: 1
				},
				isLoading: false,
				more: true
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetJobList();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetJobList();
		},
		methods: {
			onDetail(id) {
				uni.$u.route({
					url: '/pagesA/details/memberJobDetails',
					params: {
						id
					}
				});
			},
			onInitTaskList() {
				this.params.page = 1;
				this.jobList = [];
			},
			onTabsItemClick(res) {
				this.tabsIndex = res.index;
				this.tabsValue = res.key;
			},
			onScrollGetList() {
				if (!this.more) return;
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetJobList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getJobList(params);
				if (res.status_code !== '200') return;
				this.jobList = [...this.jobList, ...res.data.data];
				this.more = res.data.more;
				this.isLoading = false;
			},
			jumpLiao(v) {
				console.log(v);
				const type = v.type
				const id = v.id
				// console.log(type, id);
				uni.switchTab({
					url: `/pages/chat/message?type=${type}&id=${id}`
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.tabs-container {
			padding-inline: 16rpx;
			background-color: #f5f5f7;
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding-inline: 32rpx;

			.item {
				background-color: #ffffff;
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-end {
					display: flex;
					align-items: center;
					justify-content: space-between;
					gap: 4rpx;

					.image {
						width: 48rpx;
						height: 48rpx;
					}

					.text {
						color: #666666;
						font-size: 20rpx;
					}

					.margin-text {
						margin-inline-end: auto;
					}
				}

				.item-center {
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.info-box {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.btn {
							background: linear-gradient(132deg, #4F8CF0 0%, #0061FF 100%);
							border-radius: 8rpx;
							font-size: 24rpx;
							color: #ffffff;
							padding-block: 12rpx;
							padding-inline: 24rpx;
						}

						.text-box {
							flex: 1;
							display: flex;
							flex-direction: column;
							gap: 12rpx;

							.detail {
								flex: 1;
								color: #666666;
								font-size: 24rpx;
								overflow: hidden;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 1;
							}

							.location-box {
								display: flex;
								align-items: center;
								gap: 12rpx;

								.image {
									width: 32rpx;
									height: 32rpx;
								}

								.position {
									color: #666666;
									font-size: 24rpx;
								}

								.distance {
									color: #666666;
									font-size: 20rpx;
									padding-inline: 8rpx;
									padding-block: 4rpx;
									border-radius: 999rpx;
									background-color: #f6f6f6;
								}
							}
						}
					}

					.title {
						color: #333333;
						font-size: 28rpx;
					}

					.tag-box {
						display: flex;
						align-items: center;
						gap: 16rpx;

						.tags-scroll {
							width: 80%;
							white-space: nowrap;
						}

						.tag {
							display: inline-block;
							background-color: #f6f6f6;
							color: #666666;
							padding: 12rpx;
							border-radius: 8rpx;
							font-size: 22rpx;
							margin-inline: 8rpx;
						}

						.find-box {
							display: flex;
							align-items: center;
							margin-inline-start: auto;

							.image {
								width: 32rpx;
								height: 32rpx;
							}

							.number {
								color: #666666;
								font-size: 20rpx;
							}
						}
					}
				}

				.item-start {
					display: flex;
					align-items: center;
					justify-content: space-between;
					gap: 12rpx;

					.image {
						width: 48rpx;
						height: 48rpx;
					}

					.text_1 {
						color: #041024;
						font-size: 32rpx;
					}

					.text_2 {
						color: #4f8cf0;
						font-size: 32rpx;
						margin-inline: auto;
					}

					.text_3 {
						color: #f98a14;
						font-size: 40rpx;
					}
				}
			}
		}
	}
</style>