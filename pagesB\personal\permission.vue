<template>
	<!-- 权限管理 -->
	<view class="container">
		<u-navbar bgColor="transparent" placeholder leftIconColor="#FFFFFF" :autoBack="true" />
		<scroll-view class="scroll-view" :scroll-y="true">
			<view class="scroll-container">
				<view class="title">权限设置与信息收集</view>
				<view class="sub-title">查看管理您的已提供的权限，了解招聘呗如何收集、使用您的信息，以及如何保证您的信息安全</view>
				<view class="item" v-for="v in list" :key="v.key" @click="onRoute(v.key)">
					<image class="item-image" :src="v.image" mode=""></image>
					<view class="item-text">
						<view class="text-start">
							<text class="text">{{ v.title }}</text>
							<text class="sub-text">{{ v.stateText }}</text>
							<image class="text-image" src="/static/new/右箭头@2x1.png" alt="" />
						</view>
						<view class="text-end">{{ v.subTitle }}</view>
					</view>
				</view>
				<view class="bottom-text" @click="onRoute('perm_info')">
					<text>详细权限设置</text>
					<image class="image" src="/static/new/右箭头@2x (1).png" alt="" />
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			list: [
				{
					key: 'location',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/zXZrApL3Fge6xi3cQBz9sQvfHKVK2UyVys8oCLw1.png',
					stateText: '已开启',
					title: '位置权限',
					subTitle: '推荐附件职位、选择目标求职地点、添加/修改住址、设置我的住址等申请此权限',
				},
				{
					key: 'calendar',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/HAcFyMYZpBDdeDOJiZPqrd4uGSVkqT1s8STlThuo.png',
					stateText: '已开启',
					title: '日历权限',
					subTitle: '预约/接受面试邀请等申请此权限',
				},
				{
					key: 'camera',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/4MMQzVis2RKuUy91X6ICxtXXOcAny5aDsEWh9gjl.png',
					stateText: '已开启',
					title: '相机权限',
					subTitle: '使用二维码扫描、拍摄图片等申请此权限',
				},
				{
					key: 'micro',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/DEO0lLsFLX44XG3tZHPzuFWvs2iAhcYVCk0U1RPo.png',
					stateText: '已开启',
					title: '麦克风权限',
					subTitle: '聊天时，点击发送语音/语音通话、视频招呼等申请此权限',
				},
				{
					key: 'bluetooth',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/uP1Z5AC7Qta8T1GKGY6wjSB0BMukHCFNS3VCDBUN.png',
					stateText: '已开启',
					title: '蓝牙权限',
					subTitle: '视频面试、语音通话、视频通话、招聘者实名认证及招聘者环境认证中使用蓝牙设备申请此权限',
				},
				{
					key: 'device',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/Dp4yMzM8PRuG5LTbWFloOtOlnQTdWUlQnuojn267.png',
					stateText: '已开启',
					title: '设备权限',
					subTitle: '当蓝牙耳机传输音频被手机电话中断时，用于自动恢复音频传输申请此权限',
				},
				{
					key: 'album',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/4MK1X9fdDlaR0t1j1fNBSWOsIV4O3blO9eA6e2yF.png',
					stateText: '已开启',
					title: '相册权限',
					subTitle: '聊天时，点击图片-相册、头像上传等申请此权限',
				},
			],
		};
	},
	methods: {
		onRoute(url) {
			uni.$u.route({
				url: `/pagesB/personal/${url}`,
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f7;
	background-image: url(https://api-test.zhaopinbei.com/storage/uploads/images/hX35z9FRZhkJHVqOeiDVRp4wkWGTxH2fUR7gvGhC.png);
	background-size: contain;
	background-repeat: no-repeat;
	display: flex;
	flex-direction: column;

	.bottom-text {
		color: #4f8cf0;
		font-size: 24rpx;
		display: flex;
		align-items: center;

		.image {
			width: 32rpx;
			height: 32rpx;
		}
	}

	.scroll-view {
		flex: 1;
		overflow-y: auto;

		.scroll-container {
			padding: 32rpx;
			display: flex;
			flex-direction: column;
			gap: 32rpx;
			padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

			.title {
				color: #ffffff;
				font-size: 32rpx;
			}

			.sub-title {
				color: #ffffff;
				font-size: 24rpx;
			}

			.item {
				display: flex;
				gap: 24rpx;
				padding: 32rpx;
				border-radius: 24rpx;
				background-color: #ffffff;

				.item-image {
					width: 136rpx;
					height: 136rpx;
				}

				.item-text {
					flex: 1;
					display: flex;
					flex-direction: column;
					gap: 10rpx;

					.text-start {
						display: flex;
						align-items: center;

						.text {
							color: #333333;
							font-size: 28rpx;
						}

						.sub-text {
							color: #999999;
							font-size: 24rpx;
							margin-inline-start: auto;
						}

						.text-image {
							width: 32rpx;
							height: 32rpx;
						}
					}

					.text-end {
						color: #666666;
						font-size: 24rpx;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
					}
				}
			}
		}
	}
}
</style>
