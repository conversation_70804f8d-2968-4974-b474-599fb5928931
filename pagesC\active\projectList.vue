<template>
  <view
    class="home-index"
    :style="{ paddingBottom: tabIndex == 0 ? '120rpx' : '0' }"
  >
    <u-sticky bgColor="#F5F5F5">
      <view class="header">
        <view class="tabs">
          <view
            :class="['tab pg_1', tabIndex == 0 ? 'active' : '']"
            @click="changeTab(0)"
          >
            我的活动项目
          </view>
          <view
            :class="['tab pg_2', tabIndex == 1 ? 'active' : '']"
            @click="changeTab(1)"
          >
            我协助的
          </view>
        </view>
        <view class="search-wrap">
          <u-search
            placeholder="请输入名称或发布人"
            bgColor="#FFFFFF"
            :showAction="false"
            v-model="title"
            @search="getCompanyJobList"
          ></u-search>
        </view>
        <view class="filters">
          <view class="filter">
            <picker
              @change="changeStatus"
              :value="statusIndex"
              :range="statusList"
              range-key="name"
            >
              <view class="d-picker">{{
                statusList[statusIndex]["name"]
              }}</view>
            </picker>
            <image src="/static/images/index/down.png" mode=""></image>
          </view>
        </view>
      </view>
    </u-sticky>

    <view class="list">
      <block>
        <u-swipe-action>
          <u-swipe-action-item
            :options="options"
            v-for="(item, index) in 5"
            :name="index"
            :key="index"
            @click="handleSwipe(item, $event)"
          >
            <ActiveItem
              :item="item"
              :index="index"
              @expose="expose"
              @detail="goDetail"
            ></ActiveItem>
          </u-swipe-action-item>
        </u-swipe-action>
      </block>
    </view>

    <view class="footer" v-if="tabIndex == 0">
      <view class="next sure" @click="add"> 发布活动 </view>
    </view>
  </view>
</template>

<script>
import {
  topCompanyJob,
  exposeCompanyJob,
  delCompanyJob,
  getCompanyJobList,
  jobCopyIndex,
} from "../../config/api.js";
import ActiveItem from "./components/activeItem.vue";

export default {
  components: {
    ActiveItem,
  },
  data() {
    return {
      title: "",
      page: 1,
      limit: 10,
      show: false,
      date: "",
      options: [
        {
          text: "删除",
          style: {
            backgroundColor: "#FE4D4F",
            borderRadius: "24rpx",
            bottom: "32rpx",
            height: "100%",
            width: "150rpx",
            marginLeft: "24rpx",
          },
        },
      ],
      tabIndex: 0,
      list: [],
      list_1: [],
      statusList: [
        {
          value: "0",
          name: "全部",
        },
        {
          value: "1",
          name: "特推招聘",
        },
        {
          value: "2",
          name: "宣讲会",
        },
        {
          value: "3",
          name: "招聘会",
        },
        {
          value: "4",
          name: "岗位预定",
        },
      ],
      statusIndex: 0,
    };
  },

  onLoad() {
    this.date = uni.$u.timeFormat(this.start, "yyyy-mm-dd");
  },
  onShow() {
    this.getCompanyJobList();
  },

  methods: {
    // 抄送列表
    async jobCopyIndex() {
      let params = {
        limit: this.limit,
        page: this.page,
      };
      const res = await jobCopyIndex(params);
      this.list_1 = res.data.data;
      console.log("抄送列表", res);
    },
    open() {
      this.show = true;
    },
    close() {
      this.show = false;
    },
    openDetails(item) {
      this.show = true;
    },
    changeTab(index) {
      this.tabIndex = index;
      if (this.tabIndex == 0) {
        this.getCompanyJobList();
      } else {
        this.getCompanyJobList();
      }
    },
    changeEdu(e) {
      console.log(e);
      this.eduIndex = e.detail.value;
    },
    changeJobExperience(e) {
      this.jobExperienceIndex = e.detail.value;
    },

    changeStatus(e) {
      this.statusIndex = e.detail.value;
    },

    bindDateChange(e) {
      console.log(e);
      this.date = e.detail.value;
    },

    add() {
      uni.navigateTo({
        url: "/pagesC/active/addItem",
      });
    },

    //获取列表
    async getCompanyJobList() {
      let params = {
        page: this.page,
        limit: this.limit,
        title: this.title, //职位名称
        status: "", //审核状态：1-通过，2-待审核，3-驳回
        hot_status: "", //热门状态：1-开启，2-关闭，可不填，不填查全部职位
        list_status: this.statusList[this.statusIndex]["value"], //列表状态：默认：alll，全部： all	当前登录账号， 所有已发布的职位	招聘中 active	当前账号发布职位中， 状态为 已上架的职位	下架 off_shelf	当前账号发布职位中， 状态为 已下架的职位	草稿 draft	当前账号发布职位中， 提交状态为草稿的职位
      };
      const { status_code, data } = await getCompanyJobList(params);
      if (status_code == 200) {
        this.list = data.data;
      }
    },

    handleSwipe(item, e) {
      console.log(item, e);
      let self = this;
      //表示点击了删除按钮
      if (e.index == 0) {
        uni.showModal({
          title: "确定要删除该职位吗？",
          success: async (res) => {
            if (res.confirm) {
              console.log("用户点击确定");
              let params = {
                id: item.id,
              };
              const { status_code, data, message } = await delCompanyJob(
                params
              );
              if (status_code == 200) {
                self.page = 1;
                self.list = [];
                self.getCompanyJobList();
                return uni.$u.toast("成功");
              } else {
                return uni.$u.toast(message || "失败");
              }
            } else if (res.cancel) {
              console.log("用户点击取消");
            }
          },
        });
      }
    },

    //曝光
    async expose(item) {
      let self = this;
      uni.showModal({
        title: "确定要曝光该职位吗？",
        success: async (res) => {
          if (res.confirm) {
            console.log("用户点击确定");
            let params = {
              id: item.id,
            };
            const { status_code, data, message } = await exposeCompanyJob(
              params
            );
            if (status_code == 200) {
              self.page = 1;
              self.list = [];
              self.getCompanyJobList();
              return uni.$u.toast("成功");
            } else {
              return uni.$u.toast("失败" || message);
            }
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },

    //置顶
    async backTop(item) {
      let self = this;
      uni.showModal({
        title: "确定要置顶该职位吗？",
        success: async (res) => {
          if (res.confirm) {
            console.log("用户点击确定");
            let params = {
              id: item.id,
              day: 10,
            };
            const { status_code, data, message } = await topCompanyJob(params);
            if (status_code == 200) {
              self.page = 1;
              self.list = [];
              self.getCompanyJobList();
              return uni.$u.toast("成功");
            } else {
              return uni.$u.toast("失败" || message);
            }
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
    goDetail(index) {
      console.log(index);
      uni.navigateTo({
        url: "/pagesC/active/components/activeDetail?id="+index,
      });
    },
  },
};
</script>
<style>
page {
  background: #f5f5f7;
}
</style>
<style lang="less" scoped>
// .home-index{
// 	padding: 32rpx;
// }
.header {
  padding: 32rpx;

  .tabs {
    display: flex;
    justify-content: space-between;

    .tab {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 88rpx;
      display: flex;
      flex: 1;
      background: #ffffff;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;

      &:first-child {
        border-radius: 44rpx 0 0 44rpx;
      }

      &:last-child {
        margin-left: -40rpx;
        border-radius: 0 44rpx 44rpx 0;
      }
    }

    .pg_1 {
      clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
    }

    .pg_2 {
      clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
    }

    .active {
      color: #4f8cf0;
      font-weight: 600;
    }
  }

  .search-wrap {
    margin-top: 32rpx;
  }

  .filters {
    display: flex;
    align-items: center;
    margin-top: 32rpx;

    .filter {
      display: flex;
      align-items: center;
      height: 48rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
      padding: 0 16rpx;
      margin-right: 12rpx;
      border-radius: 8rpx;
      background: #ffffff;

      image {
        width: 24rpx;
        height: 24rpx;
      }
    }

    .search-wrap {
      flex: 1;
    }
  }
}

.list {
  padding: 0 32rpx 108rpx 32rpx;

  ::v-deep .u-swipe-action-item__right {
    bottom: 32rpx;
    border-radius: 24rpx;
  }

  ::v-deep .u-swipe-action-item__content {
    background: transparent;
  }
}

.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 196rpx;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;

  .next {
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #ffffff;
    height: 88rpx;
    width: 90%;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    border-radius: 16rpx;
  }

  .sure {
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
  }
}
</style>
