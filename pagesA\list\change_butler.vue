<template>
	<view id="app">
		<view class="changeTitle">
			现任人员
		</view>

		<view class="butlerBox">
			<image :src="head" mode="" class="butlerHeadImg"></image>
			<view class="butlerText">
				<view class="butlerNameCla">{{butlerName}}</view>
				<view class="butlerCompanyCla">{{companyName}}</view>
			</view>
		</view>

		<!-- <textarea name="" id="" cols="30" rows="10" class="reasonBox" placeholder="请输入变更原因" v-model="reasonContent"></textarea> -->

		<view class="changeTitle">
			变更人员
		</view>

		
		<view class="butlerBox" @click="selectButler">
			<view class="selectPeoCla" v-if="!butlerItem">
				<view>选择人员</view>
				<image src="/static/images/project/rightIcon.png" mode="" class="rightIcon"></image>
			</view>
			<image :src="head" mode="" class="butlerHeadImg" v-if="butlerItem"></image>
			<view class="butlerText" v-if="butlerItem">
				<view class="butlerNameCla">{{butlerItem.name}}</view>
				<view class="butlerCompanyCla">{{butlerItem.phone}}</view>
			</view>
		</view>

		<view class="btnBox">
			<view :class="!butlerItem?'btnCla btnGrey': 'btnCla btnBlue'" @click="setAuthEnterpriseStaff">确认变更</view>
		</view>

	</view>
</template>

<script>
	import {updateButler,setAuthEnterpriseStaff} from "../../config/api.js"
	export default {
		data() {
			return {
				head:'',
				butlerName:'',
				companyName:'',
				reasonContent:'',
				butlerItem:''
			}
		},
		onLoad(option) {
			this.head = option.head;
			this.butlerName = option.name;
			this.companyName = option.company;
			this.companyId = option.companyId;
		},
		onShow() {
			this.butlerItem = uni.getStorageSync('selectedInd');
		},
		methods: {
			goBack() {
				uni.navigateBack()
			},
			selectButler() {
				uni.navigateTo({
					url: '/pagesA/list/butler_list'
				})
			},
			async setAuthEnterpriseStaff() {
				if(!this.butlerItem) {
					this.selectButler()
					return
				}
				let params = {
					authorize_company_id: this.companyId,
					user_id: this.butlerItem.id
				}
				const {
					status_code,
					data,
					message
				} = await setAuthEnterpriseStaff(params)
				
				if (status_code == 200) {
					uni.removeStorageSync('selectedInd')
					uni.navigateBack()
					// this.list = data.data;
					console.log("1111111",data)
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style scoped lang="less">
	view {
		box-sizing: border-box;
	}

	#app {
		width: 100%;
		padding: 32rpx;
	}

	.changeTitle {
		font-weight: bold;
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 16rpx;
	}

	.butlerBox {
		display: flex;
		align-items: center;
		padding: 32rpx;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		background: #FFFFFF;
		margin-bottom: 24rpx;
		.butlerHeadImg {
			width: 88rpx;
			height: 88rpx;
			background: #D9D9D9;
			border-radius: 88rpx 88rpx 88rpx 88rpx;
			margin-right: 16rpx;
		}

		.butlerText {
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.butlerNameCla {
				font-weight: 600;
				font-size: 28rpx;
				color: #333333;
				margin-bottom: 16rpx;
			}

			.butlerCompanyCla {
				font-weight: 400;
				font-size: 24rpx;
				color: #666666;
			}
		}
	}

	.reasonBox {
		width: 100%;
		min-height: 166rpx;
		height: auto;
		padding: 32rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		margin: 24rpx 0;
		font-weight: 400;
		font-size: 24rpx;
		color: #333333;
		line-height: 32rpx;
		text-align: justify;
		box-sizing: border-box;
	}

	.rightIcon {
		width: 32rpx;
		height: 32rpx;
	}

	.selectPeoCla {
		width: 100%;
		// padding: 32rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 32rpx;
	}

	.btnBox {
		width: 100%;
		height: 196rpx;
		padding: 28rpx 42rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-radius: 24rpx 24rpx 0 0;
		background: #FFFFFF;
		font-weight: 600;
		font-size: 34rpx;
		color: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		box-sizing: border-box;
	}

	.btnCla {
		width: 330rpx;
		height: 100rpx;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		text-align: center;
		line-height: 100rpx;
	}

	.btnRed {
		background: #FE4D4F;
		margin-right: 26rpx;
	}

	.btnGrey {
		background: #CCCCCC;
	}
	
	.btnBlue {
		background: #4F8CF0;
	}
</style>