<template>
	<view class="">
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						签署关键字 <text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入签署关键字" placeholderClass="placeholderClass" clearable  border="none" v-model="value"
							></u--input>
					</view>
				</view>
				
				<view class="inp-item">
					<view class="title">
						摘要 
					</view>
					<view class="in">
						<u--input placeholder="请输入摘要" placeholderClass="placeholderClass" clearable  border="none" v-model="value"
							></u--input>
					</view>
				</view>
			</view>
		</view>
		
		<view class="footer">
			<view class="next save" @click="next">
				保存
			</view>
		</view>
		
		
	</view>
</template>

<script>
	export default{
		data(){
			return{
				
			}
		}
	}
</script>
<style>
	page{
		background-color: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.wrap {
		padding: 30rpx;
		
		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			.avatar{
				display: flex;
				align-items: center;
				.pic{
					padding: 0 30rpx 0 0;
					image{
						width: 108rpx;
						height: 108rpx;
					}
				}
			}
			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;
				.title {
					display: flex;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;
					.star{
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}
				
				
	
				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;
					
					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}
					::v-deep picker{
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;
						.d-picker{
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}
				
				.fb{
					font-size: 32rpx;
				}
				
				.se{
					color: #999;
				}
				
				.lab{
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}
	
	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;
	
		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 80rpx;
			font-weight: 600;
			font-size: 28rpx;
			width: 90%;
			border-radius: 16rpx;
		}
		
		.save{
			background: linear-gradient( 135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	
		.gray {
			background-color: #cccccc;
			color: #FFFFFF;
		}
	}
</style>