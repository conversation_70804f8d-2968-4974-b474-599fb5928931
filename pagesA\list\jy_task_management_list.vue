<template>
    <view class="home-index">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="tabs">
                    <u-tabs lineWidth="20" :current="tabIndex" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" @click="changeTab"></u-tabs>
                </view>
                <view class="search-wrap">
                    <u-search placeholder="请输入任务名称" bgColor="#FFFFFF" :showAction="true" @clear="clear"
                              v-model="keyword" @custom="custom"></u-search>
                </view>
                <view class="filters">
                    <view class="filter">
                        <picker mode="date" :value="filterTime1" @change="bindDateChange1">
                            <view class="d-picker">{{filterTime1 ?filterTime1 :'选择时间'}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker mode="date" :value="filterTime2" @change="bindDateChange2">
                            <view class="d-picker">{{filterTime2?filterTime2:'选择时间'}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <!-- <view class="filter">
                        <picker @change="changeStatus" :value="statusIndex" :range="statusList" range-key="name">
                            <view class="d-picker">{{statusList[statusIndex]['name']}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view> -->
                    <view class="filter" @click="clearAllFilter">
                        清除条件
                    </view>
                </view>
            </view>

        </u-sticky>

        <view class="list">
            <jy-task-item v-for="(item,index) in list" :key="index" :item="item"></jy-task-item>
        </view>
        <Pages :status="status"></Pages>
        <view style="height:140rpx;"></view>
        <view class="footer">
            <view class="btns">
                <view class="btn zx" @click="pub">
                    发布任务
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import {getTaskList} from '../../config/headhunterList_api.js'
    import JyTaskItem from '../components/jyTaskItem.vue'
    import Pages from "../../components/pages.vue";

    export default {
        components: {
            JyTaskItem,
            Pages
        },
        data() {
            return {
                status: 'loadmore',
                more: false,
                date: '',
                start: '2024-11-04',
                filterTime1: '',
                filterTime2: '',
                tabIndex: 0,
                tabs: [
                    {
                        name: '职位任务',
                    }, {
                        name: '自定义任务',
                    },
                    // {
                    //     name: '招聘会'
                    // }
                ],
                list: [],
                typeList: [
                    'job', 'custom', 'job_active'
                ],
                statusList: [
                    {
                        value: 0,
                        name: '任务状态',
                    }, {
                        value: 1,
                        name: '进行中',
                    }, {
                        value: 3,
                        name: '已结束',
                    }
                ],
                statusIndex: 0,
                page: 1,
                limit: 10,
                submit_type: '',
                type: '',
                keyword: '',
            }
        },
        onLoad() {
            this.date = uni.$u.timeFormat(this.start, 'yyyy-mm-dd');
            // this.getTaskList()
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getTaskList()
            } else {
                this.status = 'nomore'
            }
        },
        onShow() {
            this.page = 1;
            this.list = [];
            this.getTaskList();
        },
        methods: {
            async getTaskList() {
                const created_at = {start: this.filterTime1, end: this.filterTime2}
                let params = {
                    page: this.page,
                    limit: this.limit,
                    type: this.typeList[this.tabIndex],
                    title: this.keyword,
                    created_at: created_at,
                    // created_at[end]:this.filterTime2
                }
                const res = await getTaskList(params)
                this.list = this.list.concat(res.data.data);
                // 返回false代表没有下一页
                this.more = res.data.more;
                this.status = this.more ? "loadmore" : "nomore"
            },
            // 搜索功能
            custom() {
                this.page = 1;
                this.list = [];
                this.getTaskList();
            },
            // 清除搜索框
            clear() {
                this.keyword = '';
                this.custom()
            },
            // 清除所有筛选条件
            clearAllFilter() {
                this.keyword = '';
                this.filterTime1 = '';
                this.filterTime2 = '';
                this.getTaskList()
            },
            // tab切换
            changeTab(e) {
                console.log(e)
                this.tabIndex = e.index;
                this.page = 1;
                this.list = [];
                this.keyword = '';
                this.getTaskList()
            },
            pub() {
                uni.navigateTo({
                    url: "/pagesA/add/pubTask"
                })
            },
            // 选择时间1
            bindDateChange1(e) {
                this.filterTime1 = e.detail.value
                this.page = 1;
                this.list = [];
                this.getTaskList()
            },
            // 选择时间2
            bindDateChange2(e) {
                this.filterTime2 = e.detail.value
                this.page = 1;
                this.list = [];
                this.getTaskList()
            },
            changeStatus(e) {
                this.statusIndex = e.detail.value
            }
        }
    }
</script>
<style>
    page {
        background-color: #f5f5f7;
    }
</style>
<style lang="less" scoped>
    .home-index {
        padding: 0 0 170rpx 0;
    }

    .header {
        padding-bottom: 32rpx;
        background-color: #F5F5F7;
        .tabs {
            background-color: #FFFFFF;
            border-radius: 0 0 24rpx 24rpx;
        }

        .search-wrap {
            margin: 32rpx;
        }

        .filters {
            display: flex;
            margin: 0 32rpx;
            .filter {
                display: flex;
                align-items: center;
                height: 48rpx;
                background-color: #FFFFFF;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 0 12rpx;
                margin-right: 12rpx;
                border-radius: 8rpx;

                image {
                    width: 24rpx;
                    height: 24rpx;
                }
            }
        }
    }

    .list {
        padding: 0 32rpx;
    }

    .footer {
        position: fixed;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 140rpx;
        width: 100%;
        left: 0;
        bottom: 0;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 28rpx;
        border-radius: 24rpx 24rpx 0 0;

        .btns {
            display: flex;
            width: 90%;

            .btn {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 80rpx;
                color: #333333;
                border-radius: 16rpx;
            }

            .zx {
                color: #FFFFFF;
                background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
            }
        }
    }
</style>