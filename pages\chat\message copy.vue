<template>
	<view class="page">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="search-wrap">
					<u-search placeholder="请输入职位名称或发布人" bgColor="#FFFFFF" :showAction="false"
						v-model="keyword"></u-search>
				</view>
			</view>
		</u-sticky>
		<view class="list-item" v-for="(item,index) in users" :key="index" @click="connect(item)">
			<view class="avatar">
				<view style="width: 100%;height: 100%;border-radius: 45rpx;
			overflow: hidden;">
					<text class="round" v-if="item.read"></text>
					<image :src="item.avatar" mode="widthFix"></image>
				</view>
				<view class="person_type">
					<!-- <img src="/static/images/message/talent.png" style="width: 62rpx;height: 30rpx;" mode=""
                        v-if="item.personType == 'member' "> -->
					<view class="personIcon" v-if="item.personType == 'member' ">千里马</view>
					<img src="/static/images/message/talent.png" style="width: 62rpx;height: 30rpx;" mode=""
						v-if="item.personType == 'company' ">
					<img src="/static/images/message/icon_jygj.png" style="width: 62rpx;height: 30rpx;" mode=""
						v-if="item.personType == 'headhunters' ">
					</img>
				</view>
			</view>
			<view class="text_box">
				<view class="title5">
					<view class="title">
						<view class="name">{{ item.name }}</view>
						<!-- {{ item.companyName.length>10 ? item.companyName.slice(0, 12) + "..." : item.companyName }} -->
						<view class="company" v-if="item.personType !='member'" style="font-size: 24rpx;">
							{{ item.companyName && item.companyName.length > 10 ? item.companyName.slice(0, 12) + "..." : item.companyName }}
						</view>
					</view>
					<view class="time" style="font-size: 24rpx; margin-top: 8rpx;">{{ item.newTime }}</view>
				</view>

				<view class="txt">{{ item.newMsg }}</view>
			</view>
		</view>
		<view class="height_bottom"></view>
		<tabbar></tabbar>
	</view>
</template>

<script>
	import Tabbar from "../../components/tabbar.vue";
	import {
		authChat,
		chatList
	} from "../../config/common_api";
	export default {
		components: {
			Tabbar,
		},
		data() {
			return {
				options: [{
					text: '取消',
					style: {
						backgroundColor: '#007aff'
					}
				}, {
					text: '确认',
					style: {
						backgroundColor: '#dd524d'
					}
				}],
				users: []
			};
		},
		onLoad(options) {
			// this.chatList()
		},
		onShow() {
			this.chatList()
		},
		methods: {
			onClick(e) {
				console.log('点击了' + (e.position === 'left' ? '左侧' : '右侧') + e.content.text + '按钮')
			},
			swipeChange(e, index) {
				console.log('当前状态：' + e + '，下标：' + index)
			},
			async chatList() {
				let params = {
					type: 'job',
					id: this.id
				}
				const {
					status_code,
					message,
					data
				} = await chatList(params)
				if (status_code == 200) {
					this.users = data
					console.log('聊天人列表：', data)
				}
			},
			connect(item) {
				uni.navigateTo({
					url: `/pages/message/message?personType=${item.personType}&selfType=${item.selfType}&company_id=${item.company_id}&name=${item.name}&avatar=${item.avatar}&chatId=${item.chat_id}&member_id=${item.member_id}&user_id=${item.user_id}&personTypeName=${item.personTypeName}&typeClass=${item.typeClass}&job_status_name=${item.job_status_name}`,
				})
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="scss" scoped>
	.page {
		padding: 0 32rpx;
		color: #333;

		.height_bottom {
			height: 110rpx
		}
	}

	.header {
		padding: 15rpx 0;
	}

	.list-item {
		display: flex;
		padding: 30rpx 0;
		border-bottom: 0.5px solid #e6e6e6;

		.avatar {
			width: 90rpx;
			height: 90rpx;
			border-radius: 10rpx;
			margin-right: 20rpx;
			position: relative;

			.round {
				position: absolute;
				width: 14rpx;
				height: 14rpx;
				border-radius: 50%;
				background: #ef5656;
				top: -4rpx;
				right: -4rpx;
				z-index: 1;
			}

			image {
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
			}
		}

		.person_type {
			width: 100%;
			position: absolute;
			bottom: -8rpx;
			left: 0;
			right: 0;
			margin: auto;
			text-align: center;

			image {
				height: 36rpx;
				border-radius: 0;
			}

			.personIcon {
				padding: 2rpx 12rpx;
				background: linear-gradient(180deg, #81F21E 0%, #22C332 100%);
				border-radius: 56rpx 56rpx 56rpx 56rpx;
				border: 2rpx solid;
				border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.6)) 2 2;
				text-align: center;
				color: #FFFFFF;
				font-size: 20rpx;
			}
		}

		.text_box {
			flex: 1;

			.title5 {
				display: flex;
				justify-content: space-between;
			}

			.title {
				display: flex;

				// justify-content: space-between;
				.name {
					font-weight: bold;
				}

				.company {
					font-size: 20rpx;
					margin: 9rpx 20rpx;
					font-weight: bold;
					color: #666666;
				}

				.time {
					color: #999999;
					font-size: 24rpx;
					margin-left: 5rpx
				}

			}

			.txt {
				margin-top: 10rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				text-align: left;
				color: #999;
				font-size: 26rpx;
			}
		}
	}
</style>