<template>
	<view class="home-index">
		<view class="userInfo">
			<view class="info">
				<view class="userName">
					<view class="name">
						{{details.member_info.nick_name}}
					</view>
					<view class="dot">

					</view>
					<view class="status">
						{{details.member_info.job_status_name}}
					</view>
				</view>

				<view class="tags">
					{{details.member_info.sex_str}}·{{details.member_info.age}}岁·{{details.member_info.nation}}·{{details.member_info.education_type_name}}
				</view>

				<view class="items">
					<view class="item">
						<u-icon name="phone" size="28rpx" color="#333333"></u-icon>
						<text>{{details.member_certification.mobile_no}}</text>
					</view>
					<!-- <view class="item">
						<u-icon name="email" size="28rpx" color="#333333"></u-icon>
						<text><EMAIL></text>
					</view> -->
				</view>
			</view>
			<image :src="details.member_info.image.thumbnail_path_url" mode=""></image>
		</view>
    <view class="wrap">
      <view class="title">
        <view class="name">
          <text>教育经历</text>
        </view>
      </view>

      <view class="content">
        <view class="jy-list">
          <view class="jy-item" v-for="(edu,eduInd) in details.member_info.education_log" :key="edu.id">
            <view class="name">
              {{edu.school}}
            </view>
            <view class="time">
              {{edu.start}}-{{edu.end}}
            </view>
          </view>
        </view>
      </view>
    </view>
		<view class="wrap">
			<view class="title">
				<view class="name" @click="goJobExpection">
					<text>求职期望</text>
				</view>

			</view>
			<view class="content">
				<view class="cont" >
					<view class="ui">
						<view class="expert">
							<view class="type">
								{{details.public_resume.expect_job}}
							</view>
							<view class="money">
								{{details.public_resume.expect_salary_k}}
							</view>
						</view>
						<view class="pos">
							<view class="addr">
								{{details.public_resume.expect_address}}
							</view>
							<view class="type">
								{{details.public_resume.work_status_name}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="title">
				<view class="name">
					<text>自我介绍</text>
				</view>
			</view>
			<view class="content">
				<view class="intro">
					{{details.member_info.introduce}}
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="title">
				<view class="name">
					<text>工作经历</text>
				</view>
			</view>
			<view class="content">
				<view class="sub-wrap" v-for="(job,jobInd) in details.public_resume.job_log" :key="index">
					<view class="exper" >
						<view class="name">
							{{job.company}}
						</view>
						<view class="time">
							<text>{{job.start_date}}-{{job.end_date}}</text>
						</view>
					</view>
					<view class="user-info">
						<text class="user-name">{{job.contact_name}}</text>
						<text class="mobile">{{job.contract_cellphone}}</text>
					</view>

					<view class="types">
						<view class="type">
							{{job.industry}}
						</view>
						<view class="type">
							{{item.job_name}}
						</view>
					</view>

					<view class="list">
						<view class="item">
							<view class="dot">
							</view>
							<view class="js">
								业绩：{{job.achievement}}
							</view>
						</view>

						<view class="item">
							<view class="dot">
							</view>
							<view class="js">
								业绩：{{job.content}}
							</view>
						</view>
					</view>
				</view>

			</view>
		</view>

		

		<view class="wrap">
			<view class="title">
				<view class="name">
					<text>资格证书</text>
				</view>
			</view>

			<view class="content">
				<view class="pic-list">
					<view class="pic-item" v-for="imgItem in details.public_resume.certificates">
						<image :src="imgItem.thumbnail_path_url" mode=""></image>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="btns">
				<view class="btn zx" style="margin-left: 32rpx;">
					立即联系
				</view>
			</view>
		</view>
	</view>
</template>

<script>
    import {memberShareShow} from '../../config/headhunterList_api.js'
	import {
		authorizeShow,
        auditMemberAuthorizeHandel
	} from "../../config/headhunterList_api.js"
	export default {
		data() {
			return {
				id:'',
                details:''
			}
		},
		onLoad(options) {
			this.memberShareShow(options.id)
		},
		methods: {
            async memberShareShow(id) {
                let params = {
                    member_share_id:id
                }
                const res = await memberShareShow(params)
                this.details = res.data
                console.log("res",res)
            },
			//简历详情
			async authorizeShow() {
				let params = {
					id: this.id
				}
				const res = await authorizeShow(params)
				if (res.status_code == 200) {
					console.log("res",res)
                    this.details = res.data
				}
			},
			
			edit(){
				uni.navigateTo({
					url:"/pagesA/details/setResumeDetails?id="+this.id
				})
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding: 32rpx 32rpx 120rpx 32rpx;
	}

	.userInfo {
		display: flex;
		padding: 32rpx 24rpx;
		border-bottom: 1px solid #F5F5F7;
		background-color: #FFFFFF;
		margin-bottom: 24rpx;
		border-radius: 24rpx;

		.info {
			display: flex;
			flex-direction: column;
			flex: 1;

			.userName {
				display: flex;
				align-items: center;

				.name {
					font-weight: 600;
					font-size: 40rpx;
					color: #333333;
				}

				.dot {
					width: 12rpx;
					height: 12rpx;
					border-radius: 50%;
					background-color: #57D51C;
					margin-left: 16rpx;
				}

				.status {
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
					margin-left: 16rpx;
				}

				image {
					width: 32rpx;
					height: 32rpx;
					margin-left: 16rpx;
				}
			}

			.tags {
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				margin: 16rpx 0;
			}

			.items {
				display: flex;
				align-items: center;

				.item {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;

					&:last-child {
						margin-left: 32rpx;
					}

					text {
						margin-left: 16rpx;
					}
				}
			}
		}

		&>image {
			width: 108rpx;
			height: 108rpx;

		}
	}

	.wrap {
		display: flex;
		flex-direction: column;
		border-bottom: 1px solid #F5F5F7;
		padding: 32rpx 24rpx;
		background-color: #FFFFFF;
		margin-bottom: 32rpx;
		border-radius: 24rpx;

		.title {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.name {
				display: flex;
				align-items: center;
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;

				image {
					width: 32rpx;
					height: 32rpx;
					margin-left: 16rpx;
				}
			}

			.plus {
				image {
					width: 36rpx;
					height: 36rpx;
				}
			}

		}

		.content {
			display: flex;
			flex-direction: column;

			// align-items: center;
			.cont {
				display: flex;

				.ui {
					display: flex;
					flex-direction: column;
					flex: 1;

					.expert {
						display: flex;
						align-items: center;
						margin-top: 40rpx;

						.type {
							font-weight: 400;
							font-size: 28rpx;
							color: #333333;
						}

						.money {
							font-weight: 600;
							font-size: 28rpx;
							color: #4F8CF0;
							margin-left: 16rpx;
						}
					}

					.pos {
						display: flex;
						align-items: center;
						margin-top: 16rpx;
						font-weight: 400;
						font-size: 20rpx;
						color: #666666;

						.addr {}

						.type {
							margin-left: 32rpx;
						}
					}
				}

			}

			.sub-wrap {
				display: flex;
				flex-direction: column;
				margin-bottom: 32rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.exper {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 40rpx;

					.name {
						display: flex;
						flex: 1;
						font-weight: 500;
						font-size: 32rpx;
						color: #333333;
					}

					.time {
						display: flex;
						align-items: center;
						font-weight: 400;
						font-size: 20rpx;
						color: #666666;

						text {
							margin-right: 20rpx;
						}
					}
				}

				.user-info {
					display: flex;
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
					margin-top: 16rpx;

					.mobile {
						margin-left: 16rpx;
					}
				}

				.types {
					display: flex;
					margin-top: 16rpx;

					.type {
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
						margin-right: 24rpx;
					}
				}

				.list {
					display: flex;
					flex-direction: column;
					margin-top: 16rpx;

					.item {
						display: flex;
						margin-bottom: 16rpx;

						&:last-child {
							margin-bottom: 0;
						}

						.dot {
							width: 12rpx;
							height: 12rpx;
							border-radius: 50%;
							background-color: #666666;
							margin: 10rpx 24rpx;
						}

						.js {
							display: flex;
							flex: 1;
							font-weight: 400;
							font-size: 24rpx;
							color: #666666;
						}
					}
				}
			}

			.jy-list {
				display: flex;
				flex-direction: column;
				margin-top: 40rpx;

				.jy-item {
					display: flex;
					flex-direction: column;
					margin-bottom: 32rpx;

					&:last-child {
						margin-bottom: 0;
					}

					.name {
						display: flex;
						justify-content: space-between;
						align-items: center;
						font-weight: 500;
						font-size: 32rpx;
						color: #333333;
					}

					.time {
						font-weight: 400;
						font-size: 24rpx;
						color: #666666;
						margin-top: 16rpx;
					}
				}
			}

			.pic-list {
				display: flex;
				flex-wrap: wrap;

				.pic-item {
					display: flex;
					justify-content: center;
					align-items: center;
					height: 200rpx;
					width: calc(33.3% - 32rpx);
					margin-top: 32rpx;
					margin-right: 32rpx;
					position: relative;

					&>image {
						width: 100%;
						height: 100%;
					}

					.zz {
						display: flex;
						justify-content: center;
						align-items: center;
						background-color: rgba(0, 0, 0, 0.5);
						width: 100%;
						height: 100%;
						position: absolute;
						left: 0;
						top: 0;
						z-index: 10;

						.del {
							display: flex;
							align-items: center;
							border-radius: 16rpx;
							padding: 0 16rpx;
							height: 56rpx;
							background: rgba(255, 255, 255, 0.5);
							font-weight: 500;
							font-size: 28rpx;
							color: #FFFFFF;
						}
					}
				}

				.add {
					background-color: #F5F5F7;

					image {}
				}

			}
		}

		.intro {
			font-weight: 400;
			font-size: 24rpx;
			color: #333333;
			margin-top: 40rpx;
		}
	}

	.edit{
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		bottom: 160rpx;
		left: 50%;
		margin-left: -108rpx;
		width: 216rpx;
		height: 64rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #FFFFFF;
		border-radius: 32rpx;
		background: #4F8CF0;
		box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.1);
		z-index: 999;
		image{
			width: 28rpx;
			height: 28rpx;
		}
	}

	.footer {
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 140rpx;
		width: 100%;
		left: 0;
		bottom: 0;
		background: #FFFFFF;
		font-weight: 600;
		font-size: 28rpx;
		border-radius: 24rpx 24rpx 0 0;
		z-index: 999;

		.btns {
			display: flex;
			width: 90%;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 1;
				height: 80rpx;
				color: #333333;
				border-radius: 16rpx;
			}

			.cancel {
				background: rgba(254, 77, 79, 0.1);
				color: #FE4D4F;
			}

			.zx {
				color: #FFFFFF;
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			}
		}
	}
</style>