<template>
	<view>
		<view class="day">4月9日</view>
		<view class="job-item">
			<view class="header">
				<text class="date">{{ date }}</text>
			</view>
			<view class="content">
				<view class="job-info">
					<view class="job-info-leftbox">
						<view class="job-icon"></view>
						<view class="job-text">
							<text class="job-title">{{ jobTitle }}</text>
							<text class="job-salary">{{ jobSalary }}</text>
						</view>
					</view>
					<view class="job-info-rightbox">
						<text class="status">{{ status }}</text>
						<text class="time">{{ time }}</text>
					</view>
				</view>
				<view class="tags">
					<view v-for="(tag, index) in tags" :key="index" class="tag">{{ tag }}</view>
				</view>
			</view>
			<view class="bottom-location-box">
				<view class="location">
					<img
						src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png"
						alt=""
						class="location-icon" />
					<text>{{ location }}</text>
				</view>
				<button class="continue-btn" @click="continueChat">{{ continueBtnText }}</button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			date: '',
			jobTitle: '产品ui设计师',
			jobSalary: '4-8K',
			status: '待评价',
			time: '10:10',
			tags: ['世界500强', '上市公司', '游戏大厂'],
			location: '北京北京市昌平区1号',
			continueBtnText: '继续沟通',
		};
	},
	methods: {
		continueChat() {
			// 这里添加继续沟通的逻辑，比如跳转页面或打开聊天窗口等
			console.log('点击了继续沟通按钮');
			uni.navigateTo({
				url: '/pagesB/interviewDetails/Wait',
			});
		},
	},
};
</script>

<style scoped>
.day {
	margin: 32rpx 640rpx 24rpx 32rpx;
	font-size: 24rpx;
	color: rgba(102, 102, 102, 1);
}

.job-item {
	background-color: #fff;
	border-radius: 24rpx;
	margin-bottom: 32rpx;
	margin-top: 24rpx;
	width: 654rpx;
	height: 290rpx;
	margin: 24rpx auto;
	padding: 32rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.header {
	margin-bottom: 16rpx;
}

.date {
	color: #999;
	font-size: 28rpx;
}

.content {
	display: flex;
	flex-direction: column;
}

.job-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.job-info-leftbox {
	width: 334rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 90rpx;
}

.job-icon {
	width: 90rpx;
	height: 90rpx;
	background-color: #ddd;
	border-radius: 8rpx;
}

.job-text {
	display: flex;
	flex-direction: column;
	width: 228rpx;
	height: 32rpx;
	justify-content: space-evenly;
}

.job-info-rightbox {
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	align-items: center;
	width: 72rpx;
	height: 76rpx;
}

.job-title {
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 8rpx;
	color: rgba(4, 16, 36, 1);
	line-height: 32rpx;
}

.job-salary {
	color: #666;
	font-size: 24rpx;
}

.status {
	color: #999;
	font-size: 24rpx;
}

.time {
	color: #999;
	font-size: 20rpx;
}

.tags {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 16rpx;
}

.tag {
	background-color: #f2f2f2;
	color: #333;
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 8rpx;
	margin-right: 16rpx;
	margin-bottom: 16rpx;
}

.bottom-location-box {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.location {
	display: flex;
	align-items: center;
	color: rgba(102, 102, 102, 1);
	font-size: 24rpx;
}

.location-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 8rpx;
}

.continue-btn {
	background: linear-gradient(to right, rgba(79, 140, 240, 1), rgba(0, 97, 255, 1));
	color: #fff;
	border: none;
	border-radius: 8rpx;
	align-self: flex-end;
	margin: 0;
	width: 145rpx;
	height: 58rpx;
	font-size: 24rpx;
}
</style>
