<template>
  <view>
    <!-- 搜索 -->
    <view class="u-search">
      <view class="u-flex u-p-t-20 u-p-b-20">
        <u-search
          class="u-m-l-30 u-m-r-30 part-item"
          placeholder="输入手机号/兼职号"
          shape="square"
          :clearabled="true"
          :show-action="false"
          @search="getUser"
        ></u-search>
        <view @click="scan"
          ><u-icon name="scan" color="#2979ff" size="60"></u-icon
        ></view>
      </view>
      <text
        v-if="isUser === 4"
        class="u-flex u-text-center"
        style="justify-content: center"
        >该用户还没有注册</text
      >
    </view>
    <view v-if="isUser === 2">
      <view class="u-flex u-p-30 u-border-bottom">
        <view class="u-p-t-30 left">
          <image class="img" :src="user.user_avatar" mode=""></image>
        </view>
        <view class="u-p-l-30 right">
          <view class="uni-title">{{ user.alias_name }}</view>
          <view class="uni-ellipsis uni-color-6">{{ user.description }}</view>
        </view>
      </view>
      <view class="u-p-l-30 u-p-r-30">
        <u-form :model="form" label-position="top">
          <u-form-item label="签名" prop="name"
            ><u-input v-model="form.name" type="textarea"
          /></u-form-item>
          <u-form-item label="申请信息" prop="intro"
            ><u-input v-model="form.intro" type="textarea"
          /></u-form-item>
        </u-form>
      </view>
    </view>

    <!-- 添加 -->
    <view class="u-p-l-30 u-p-r-30" v-if="isUser === 3"> </view>

    <view class="part-btn" v-if="isUser === 3">
      <view class="btn-item">
        <u-button
          @click="submit"
          :custom-style="customStyle"
          size="medium"
          type="primary"
          >发送添加邀请</u-button
        >
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isUser: 1,
      type: "",
      img: "https://img2.baidu.com/it/u=3006358845,2976569821&fm=26&fmt=auto",
      form: {
        nick_name: "",
        sex: "",
        remarks: "",
        reason: "",
        image: null,
        friend_id: "",
        friend_type: "",
      },
      user: {
        alias_name: "",
        description: "",
        user_avatar: "",
        user_app_id: "",
      },
      show: false,
      customStyle: {
        width: "80%",
      },
      actionSheetList: [
        {
          text: "男",
        },
        {
          text: "女",
        },
        {
          text: "保密",
        },
      ],
      rules: {
        nick_name: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "blur,change",
          },
        ],
        sex: [
          {
            required: true,
            message: "请选择性别",
            trigger: "change",
          },
        ],
      },
    };
  },
  methods: {
    actionSheetCallback(index) {
      this.form.sex = this.actionSheetList[index].text;
    },
    // 提交
    submit() {
      addFriend({
        friend_id: this.form.friend_id,
        friend_type: this.form.friend_type,
        reason: this.form.reason,
        notes: this.form.notes,
      }).then((res) => {
        if (res.status_code == "200") {
          uni.showToast({
            title: res.message,
            icon: "none",
          });
          uni.navigateBack({
            delta: 1,
          });
        } else {
          uni.showToast({
            title: res.message,
            icon: "none",
          });
        }
      });
    },
    getUser(e) {
      getuserInfo({
        phone: e,
        type: this.type,
      }).then((res) => {
        if (res.data === null) {
          this.isUser = 4; //用户不存在
        } else {
          this.isUser = 3;
          console.log("查询信息==", res.data.member_info);
          let obj = {
            nick_name: res.data.member_info.nick_name,
            sex: res.data.member_info.sex_str,
            remarks: res.data.member_info.remark,
            friend_id: res.data.member_info.id,
            friend_type: res.data.member_info.member_type,
          };
          this.form = obj;
        }
      });
    },
  },
  onReady() {
    //this.$refs.uForm.setRules(this.rules);
  },
  onLoad(option) {
    this.type = option.type;
  },
};
</script>

<style lang="scss" scoped>
.img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
}

.left {
  width: 15%;
}

.right {
  width: 85%;
}

.part-btn {
  .btn-item {
    text-align: center;
    position: fixed;
    width: 100%;
    bottom: 120rpx;
  }
}

.u-search {
  background-color: #ffffff;

  .part-item {
    width: 80%;
  }
}

.image {
  width: 80rpx;
  height: 80rpx;
}
</style>
