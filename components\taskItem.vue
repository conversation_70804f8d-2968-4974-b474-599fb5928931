<template>
	<view class="item" @click="go(item.task.id)">
		<view class="up">
			<view class="">
				<view class="name">{{item.task.title}}</view>
				<view :class="['type',item.task.type=='job'?'blue':item.task.type=='custom'?'yellow':item.task.type=='job_active'?'green':'']">
					{{item.task.type_name}}
				</view>
			</view>
			<view class="num">
				{{item.task.unit_total}}/人
			</view>
		</view>
		<view class="down">
			<view class="name">
				发布人：{{item.member_certification.name}}
			</view>
			<view class="name">
				发布时间：{{item.task.created_at}}
			</view>
			<view class="comp">
				<view class="info">
					<image :src="item.company_info.logo.thumbnail_path_url" mode=""></image>
					<text>{{item.company.name}}</text>
				</view>
				<view class="status" v-if="item.task.active_status_name=='进行中'">
					{{item.task.active_status_name}}
				</view>
				<view class="statuss" v-if="item.task.active_status_name=='已结束'">
					{{item.task.active_status_name}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			item:{
				type:Object,
				default:()=>{}
			}
		},
		name:"taskItem",
		data() {
			return {
				
			};
		},
		methods:{
			go(id){
				uni.navigateTo({
					url:"/pagesA/details/taskDetails?id="+id
				})
			}
		}
	}
</script>

<style lang="less" scoped>
.item{
	display: flex;
	flex-direction: column;
	padding: 0 32rpx;
	background: #FFFFFF;
	border-radius: 24rpx;
	margin-bottom: 24rpx;
	// &:last-child{
	// 	margin-bottom: 0;
	// }
	.up{
		display: flex;
		padding: 24rpx 0;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid #F5F5F7;
		.name{
			font-weight: 600;
			font-size: 32rpx;
			color: #333333;
		}
		.num{
			font-weight: 600;
			font-size: 32rpx;
			color: #F98A14;
		}
		.type{
			font-weight: 600;
			font-size: 24rpx;
		}
		
		.yellow{
			color: #F9AD14;
		}
		.blue{
			color: #1690FF;
		}	
		.green{
			color: #57D51C;
		}
	}
	
	.down{
		display: flex;
		flex-direction: column;
		padding: 24rpx 0;
		.name{
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			
		}
		
		.comp{
			display: flex;
			align-items: center;
			justify-content: space-between;
			.info{
				display: flex;
				align-items: center;
				margin-top: 16rpx;
				image{
					width: 40rpx;
					height: 40rpx;
					margin-right: 16rpx;
					border-radius: 6rpx;
				}
				text{
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
				}
			}
			
			.status{
				display: flex;
				align-items: center;
				border-radius: 8rpx;
				height: 50rpx;
				padding: 0 12rpx;
				font-weight: 600;
				font-size: 24rpx;
				background: rgba(79,140,240,0.1);
				color: #4F8CF0;
			}
			.statuss{
				display: flex;
				align-items: center;
				border-radius: 8rpx;
				height: 50rpx;
				padding: 0 12rpx;
				font-weight: 600;
				font-size: 24rpx;
				background-color: #cccccc;
				color: white;
			}
			
		}
		
	}
}
</style>