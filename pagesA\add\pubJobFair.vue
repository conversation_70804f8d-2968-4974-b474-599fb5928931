<template>
	<view class="home-index">
		<!--		<u-sticky>-->
		<!--			<view class="tabs">-->
		<!--				<view :class="['tab pg_1',tabIndex==0?'active':'']" @click="changeTab(0)">-->
		<!--					多主题活动-->
		<!--				</view>-->
		<!--				<view :class="['tab pg_2',tabIndex==1?'active':'']" @click="changeTab(1)">-->
		<!--					单主题活动-->
		<!--				</view>-->
		<!--			</view>-->
		<!--		</u-sticky>-->
		<view class="wrap">
			<view class="inp">
				<view class="avatar">
					<view class="inp-item">
						<view class="title">
							活动封面<text class="star">*</text>
						</view>
						<view class="in lab">
							请选择上传活动封面，最多支持上传一张图片，每个图片不超过12MB
						</view>
					</view>
					<view class="pic" @click="uploadLogo">
						<image :src="logo?logo:'https://api-test.zhaopinbei.com/storage/uploads/images/DjpYAiYx76keAZZls68bXyVPGHjYpURJJxA9YHcU.png'" mode=""></image>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						活动主题<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入活动标题" placeholderClass="placeholderClass" border="none"
							v-model="form.title"></u--input>
					</view>
				</view>
                 <!-- @click="handleSelect('edit','single')" -->
				<view class="inp-item" @click="goSelectPeople(1)">
					<view class="title">
						主委会（活动负责人）<text class="star">*</text>
					</view>
					<view class="in">
						<!-- <u--input placeholder="请输入活动主委会" placeholderClass="placeholderClass" border="none"
							v-model="value"></u--input> -->
						{{executorValue?executorValue:'请选择活动负责人'}}
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
                 <!-- @click="handleSelect('edit','multi')" -->
				<view class="inp-item"  @click="goSelectPeople(2)">
					<view class="title">
						活动执行人（协作人）<text class="star">*</text>
					</view>
					<view class="in">
						<!-- <u--input placeholder="请输入活动执行人" placeholderClass="placeholderClass" border="none"
							v-model="value"></u--input> -->
						{{collaboratorValue?collaboratorValue:'请选择活动协作人'}}
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						活动城市<text class="star">*</text>
					</view>
					<view class="in se">
						<uni-data-picker :map="map" placeholder="请选择活动城市" popup-title="请选择活动城市" :localdata="cityList"
							v-model="form.address.district_id" @change="onchange">
						</uni-data-picker>
					</view>
				</view>
				<view class="inp-item" @click="choosePosition">
					<view class="title">
						活动地址<text class="star">*</text>
					</view>
					<view class="in se">
						{{form.address.map_address?form.address.map_address:'请选择地址'}}
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						详细地址<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入详细地址" border="none" fontSize="32rpx"
							v-model="form.address.remark"></u--input>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						报名费用（最多两个小数点，最少数值0.01）<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入报名费用" placeholderClass="placeholderClass" border="none"
							v-model="form.price"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						委托费用（最多两个小数点，最少数值0.01）<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入报名费用" placeholderClass="placeholderClass" border="none"
							v-model="form.authorize_price"></u--input>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						展台数量<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入展台数量" placeholderClass="placeholderClass" border="none"
							v-model="form.place_count"></u--input>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						简介<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入活动简介" placeholderClass="placeholderClass" border="none"
							v-model="form.intro"></u--input>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						活动开始时间<text class="star">*</text>
					</view>
					<view class="in se">
						<picker mode="date" :value="form.start" @change="bindStartDateChange">
							<view class="d-picker">{{form.start?form.start:"请选择开始时间"}}
							</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						活动结束时间<text class="star">*</text>
					</view>
					<view class="in se">
						<picker mode="date" :value="form.end" @change="bindEndDateChange">
							<view class="d-picker">{{form.end?form.end:"请选择开始时间"}}
							</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				<!-- <view class="inp-item">
					<view class="title">
						企业报名时间<text class="star">*</text>
					</view>
					<view class="in se">
						<picker mode="date" :value="pubJobTwo.activity_start_at" @change="bindSignDateChange">
							<view class="d-picker">{{pubJobTwo.activity_start_at?pubJobTwo.activity_start_at:"请选择开始时间"}}
							</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view> -->
			</view>
		</view>

		<view class="footer">
			<!-- <view class="next save" @click="next">
				保存草稿
			</view> -->
			<view class="next pub" @click="saveHeadhuntersJobfair">
				发布活动
			</view>
		</view>
	</view>
</template>

<script>
	import {
		uploadImg,
		getHeadhuntersJobfairDetails,
		saveHeadhuntersJobfair
	} from "../../config/api.js"
	export default {
		data() {
			return {
				id: '',
				map: {
					text: 'label',
					value: 'value'
				},
				tabIndex: 0,
				logo: "",
				form: {
					title: "", //标题啊
					image: [], //封面图片
					intro: "", //简介
					price: "", //价格
					authorize_price: "", //委托费用
					start: "", //开始日期
					end: "", //结束日期
					manage_user_id: [], //负责人
					coordination_user_id: [], //协作人
					place_count: "", //展台数量
					address: {
						province_id: "",
						city_id: "",
						district_id: "",
						address: "",
						map_address: "",
						lat: "",
						lng: "",
						remark: ""
					}
				}
			}
		},

		computed: {
			roleType() {
				return this.$store.state.roleType || uni.getStorageSync("roleType")
			},
			sysData() {
				return this.$store.state.sysData || uni.getStorageSync('sysData')
			},
			cityList() {
				return this.$store.state.cityList || uni.getStorageSync('cityList')
			},

			executor() {
				return this.$store.state.executor || []
			},
			//负责人显示值
			executorValue() {
				if (this.executor.length > 0) {
					return this.executor.map(item => item.name).join(',')
				}
			},
			collaborator() {
				return this.$store.state.collaborator || []
			},
			//协作人显示值
			collaboratorValue() {
				return this.collaborator.map(item => item.name).join(',')
			}

		},

		onLoad(options) {
			this.id = options.id
			if (this.id) {
				this.getHeadhuntersJobfairDetails() //招聘会详情
			}
		},

		methods: {
			//去选择执行人，协作人
			// handleSelect(flag, type) {
			// 	uni.navigateTo({
			// 		url: "/pagesA/connectivity/inner?flag=" + flag + "&type=" + type
			// 	})
			// },
			goSelectPeople(num) {
                uni.navigateTo({
                    url:'/pagesA/select/selectPeople?type='+num
                })
            },
			changeTab(index) {
				this.tabIndex = index
			},

			//开始时间
			bindStartDateChange(e) {
				this.form.start = uni.$u.timeFormat(e.detail.value, 'yyyy-mmm-dd')
			},

			//结束时间
			bindEndDateChange(e) {
				this.form.end = uni.$u.timeFormat(e.detail.value, 'yyyy-mmm-dd')
			},

			//选择省市区
			onchange(e) {
				console.log("工作地址：", e);
				let data = e.detail.value
				this.form.address.province_id = data[0]['value']
				this.form.address.city_id = data[1]['value']
				this.form.address.district_id = data[2]['value']
				this.form.address.address = data.map(item => item.text).join('')
				// console.log("期望地址：", this.expect_address)
			},

			//选择地图地址
			choosePosition() {
				console.log(1)
				let self = this;
				uni.chooseLocation({
					success: function(res) {
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
						self.form.address.lat = res.latitude
						self.form.address.lng = res.longitude
						self.form.address.map_address = res.address
					}
				});
			},

			//上传招聘会图片
			uploadLogo() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								if (result.status_code == 200) {
									self.logo = result.data.url
									self.form.image = [result.data.id]
								}
							}
						})
					}
				});
			},

			//清空执行人，协作人
			clear() {
				this.$store.commit('setExecutor', [])
				this.$store.commit('setCollaborator', [])
			},


			//获取详情编辑时候回显
			async getHeadhuntersJobfairDetails() {
				let params = {
					id: this.id
				}
				const {
					status_code,
					data,
					message
				} = await getHeadhuntersJobfairDetails(params)
				if (status_code == 200) {
					this.logo = data.image.path_url
					this.form.image = [data.image.id]
					this.form.title = data.title
					this.form.intro = data.intro
					this.form.price = data.price
					this.form.authorize_price = data.authorize_price
					this.form.start = data.start
					this.form.end = data.end
					this.form.place_count = data.place_count

					this.form.address.address = data.address[0]['address']
					this.form.address.map_address = data.address[0]['map_address']
					this.form.address.remark = data.address[0]['remark']
					this.form.address.province_id = data.address[0]['province_id']
					this.form.address.city_id = data.address[0]['city_id']
					this.form.address.district_id = data.address[0]['district_id']
					this.form.address.lat = data.address[0]['lat']
					this.form.address.lng = data.address[0]['lng']
				}
			},


			//新增，编辑招聘会
			async saveHeadhuntersJobfair() {
				this.form.manage_user_id = this.executor.map(item => item.id)
				this.form.coordination_user_id = this.collaborator.map(item => item.id)
				let params = {}
				if(this.id){//id存在表示编辑
					params = {
						...this.form,
						id:this.id
					}
				}else{
					params = {
						...this.form
					}
				}

				console.log("报名参数", params)

				const {
					status,
					status_code,
					data,
					message
				} = await saveHeadhuntersJobfair(params)
				if (status_code == 200 || status=='SUCCESS' || status == 200) {
					uni.$u.toast(message || '成功')
					this.clear()
					uni.navigateBack()
				} else {
					uni.$u.toast(message || '失败')
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #FFFFFF;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding-bottom: 170rpx;
	}

	.tabs {
		display: flex;
		justify-content: space-between;
		background-color: #FFFFFF;
		padding: 32rpx;

		.tab {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			display: flex;
			flex: 1;
			background: #F5F5F7;
			font-weight: 400;
			font-size: 28rpx;
			color: #999999;

			&:first-child {
				border-radius: 44rpx 0 0 44rpx;
			}

			&:last-child {
				margin-left: -40rpx;
				border-radius: 0 44rpx 44rpx 0;
			}
		}

		.pg_1 {
			clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
		}

		.pg_2 {
			clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
		}

		.active {
			background-color: #4F8CF0;
			color: #FFFFFF;
			font-weight: 600;
		}
	}

	.wrap {
		padding: 0 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					::v-deep uni-data-picker {
						width: 100%;
					}

					::v-deep .arrow-area {
						transform: rotate(-135deg);
					}

					::v-deep .input-arrow {
						width: 20rpx;
						height: 20rpx;
						border-left: 1px solid #606266;
						border-bottom: 1px solid #606266;
					}

					::v-deep .input-value-border {
						border: none;
					}

					::v-deep .input-value {
						padding: 0;
					}

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}



					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}



				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.footer {
		display: flex;
		justify-content: space-around;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 16rpx 16rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 16rpx;
			font-weight: 600;
			font-size: 28rpx;
			color: #FFFFFF;
		}

		.save {
			border: 1px solid #4F8CF0;
			color: #4F8CF0;
		}

		.pub {
			background: #4F8CF0;
			border: 1px solid #4F8CF0;
			color: #FFFFFF;
		}
	}
</style>
