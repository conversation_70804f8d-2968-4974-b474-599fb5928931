<template>
	<view class="item" @click="go(item.id)">
		<view :class="['status',item.status==1?'ing':item.status==2?'over':item.status==3?'cg':'']">
			{{item.status==1?'进行中':item.status==2?'已结束':item.status==3?'草稿':''}}
		</view>
		<view class="name">
			{{item.title}}
		</view>
		<view class="time">
			{{item.start_at}}--{{item.end_at}}
		</view>
		<view class="desc">
			<view :class="['type',item.type=='job'?'blue':item.type=='custom'?'yellow':item.type=='job_active'?'green':'']">
				{{item.type_name}}
			</view>
			<view class="per">
				{{item.unit_total}}/每位
			</view>
		</view>
		
		<!-- <view :class="['status',item.status==1?'ing':item.status==2?'over':item.status==3?'cg':'']" >
			{{item.status==1?'进行中':item.status==2?'已结束':item.status==3?'草稿':''}}
		</view> -->
	</view>
</template>

<script>
	export default {
		name:"jyTaskItem",
		props:{
			item:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {
				
			};
		},
		methods:{
			go(id){
				console.log('item',this.item)
				uni.navigateTo({
					url:"/pagesA/details/jyManaTaskDetails?id="+id
				})
			}
		}
	}
</script>

<style lang="less" scoped>
.item{
	display: flex;
	flex-direction: column;
	padding: 0 32rpx;
	background: #FFFFFF;
	border-radius: 24rpx;
	margin-bottom: 24rpx;
	padding: 32rpx;
	position: relative;
	
	// .status{
	// 	display: flex;
	// 	justify-content: center;
	// 	align-items: center;
	// 	position: absolute;
	// 	right: 0;
	// 	top: 0;
	// 	height: 64rpx;
	// 	padding: 0 24rpx;
	// 	border-radius: 0 24rpx 0 24rpx;
		
	// 	font-weight: 400;
	// 	font-size: 28rpx;
	// 	color: #FFFFFF;
	// }
	.status{
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		right: 0;
		top: 0;
		font-size: 28rpx;
		width: 132rpx;
		height: 62rpx;
		border-radius: 0 24rpx 0 24rpx;
	}
	
	.ing{
		background: #4F8CF0;
		color: #FFFFFF;
	}
	.over{
		background: #CCCCCC;
		color: #FFFFFF;
	}
	.cg{
		background: #F9AD14;
		color: #FFFFFF;
	}
	.name{
		font-weight: 600;
		font-size: 32rpx;
		color: #333333;
	}
	.time{
		font-weight: 400;
		font-size: 24rpx;
		color: #999999;
		margin-top: 16rpx;
	}
	.desc{
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 24rpx;
		.type{
			font-weight: 600;
			font-size: 24rpx;
			
		}
		
		.per{
			font-weight: 600;
			font-size: 32rpx;
			color: #F98A14;
		}
		
		.yellow{
			color: #F9AD14;
		}
		.blue{
			color: #1690FF;
		}	
		.green{
			color: #57D51C;
		}
	}
}
</style>