<template>
    <view class="ad">
    	<view class="ad-cl-1" @click="goqlmList">
    		<image src="https://api-test.zhaopinbei.com/storage/uploads/images/fpxw6RU2VZdEPCNLg8JoLa8KOPfyK2NA1LEBii3u.png" mode=""></image>
    	</view>
    	<view class="ad-cl-1">
    		<image src="https://api-test.zhaopinbei.com/storage/uploads/images/RrMevSkdrD80RmJjWmKdC3phIwiJ7xxhNf4bcPJo.png" mode="" @click="goObtain"></image>
    	</view>
    	<view class="ad-cl-2">
    		<image src="https://api-test.zhaopinbei.com/storage/uploads/images/rihjYuSAoFrcwIHzg5giiD65I9L3q8LWNvVQRdUV.png" mode="" @click="goJobFair"></image>
    	</view>
    </view>
</template>

<script>
    export default {
        name:"qyNavBottom",
        data() {
            return {

            };
        },
        methods:{
            goqlmList(){
              uni.navigateTo({
                url:"/pagesA/list/qlm_list?tabIndex="+0
              })
            },
            goObtain() {
            	uni.navigateTo({
            		url: "/pagesA/list/obtain_management_list"
            	})
            },
            goJobFair() {
            	uni.navigateTo({
            		url:'/pagesA/list/job_fair_list'
            	})
            },
        }
    }
</script>

<style scoped lang="less">
.ad {
		display: flex;
		justify-content: space-between;
		padding: 0 32rpx;
		margin-top: 32rpx;

		.ad-cl-1 {
			display: flex;
			width: 196rpx;
			height: 176rpx;
			margin-right: 16rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.ad-cl-2 {
			display: flex;
			flex: 1;

			image {
				width: 100%;
				height: 176rpx;
			}
		}
	}
</style>
