<template>
  <view id="app">
    <view class="iptBoxCla" v-if="pageType == 'keywords'">
      <view class="iptBoxCla_top">
        <view class="iptBoxCla_title"
          >签署关键字 <text class="redCla">*</text>
        </view>
        <input
          type="text"
          placeholder="请输入签署关键字"
          placeholderStyle="color:#ccc;"
          class="iptBoxCla_title_ipt"
          v-model="key_word"
        />
      </view>
      <view class="hrBox"></view>
      <view class="iptBoxCla_top">
        <view class="iptBoxCla_title">摘要</view>
        <input
          type="text"
          placeholder="请输入摘要"
          placeholderStyle="color:#ccc;"
          class="iptBoxCla_title_ipt"
          v-model="summary"
        />
      </view>
    </view>

    <view class="iptBoxCla" v-if="pageType == 'seal'">
      <view class="iptBoxCla_top">
        <view class="iptBoxCla_title"
          >印章标题 <text class="redCla">*</text>
        </view>
        <input
          type="text"
          placeholder="请输入印章标题"
          placeholderStyle="color:#ccc;"
          class="iptBoxCla_title_ipt"
          v-model="title"
        />
      </view>
    </view>

    <view style="height: 220rpx"></view>
    <view class="bottomBtn">
      <view class="bottomBtn_text" @click="save">保存</view>
    </view>
  </view>
</template>

<script>
import {
  keyWordStore,
  keyWordShow,
  sealStore,
  sealShow,
} from "../../config/api.js";
export default {
  data() {
    return {
      pageType: "keywords",
      id: "",
      title: "",
      summary: "",
      key_word: "",
    };
  },
  onLoad(option) {
    // 编辑用的
    this.id = option.id;
    this.pageType = option.type;
    if (option.type == "keywords") {
      uni.setNavigationBarTitle({
        title: "添加关键字",
      });
      if (this.id) {
        this.keyWordShow();
      }
    }
    if (option.type == "seal") {
      uni.setNavigationBarTitle({
        title: "添加印章",
      });
      if (this.id) {
        this.sealShow();
      }
    }
  },
  methods: {
    // 关键字回显
    async keyWordShow() {
      let params = {
        id: this.id,
      };
      const res = await keyWordShow(params);
      this.summary = res.data.summary;
      this.key_word = res.data.key_word;
    },
    // 印章回显
    async sealShow() {
      let params = {
        id: this.id,
      };
      const res = await sealShow(params);
      this.title = res.data.title;
    },
    async save() {
      // 关键字
      if (this.pageType == "keywords") {
        let params = {
          summary: this.summary,
          key_word: this.key_word,
          id: this.id,
        };
        const res = await keyWordStore(params);
        if (res.status_code == 200) {
          uni.showToast({
            title: "保存成功",
            icon: "none",
          });
          uni.navigateTo({
            url: "/pagesA/contract/contract?id=1",
          });
          //this.$parent.tabClick(1); // 调用父组件的方法
        }
      }
      // 印章
      if (this.pageType == "seal") {
        let params = {
          title: this.title,
          id: this.id,
        };
        const res = await sealStore(params);
        if (res.status_code == 200) {
          uni.showToast({
            title: "保存成功",
            icon: "none",
          });
          uni.navigateTo({
            url: "/pagesA/contract/contract?id=2",
          });
        }
        console.log("印章", res);
      }
    },
  },
};
</script>
<style>
page {
  background: #f5f5f7;
}
</style>
<style scoped lang="less">
view {
  box-sizing: border-box;
}
#app {
  width: 100%;
  padding: 32rpx;
}

.iptBoxCla {
  width: 100%;
  // height: 278rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 24rpx 32rpx;
}

.hrBox {
  width: 100%;
  height: 2rpx;
  background: #f5f5f7;
  margin: 24rpx 0;
}

.iptBoxCla_title {
  font-weight: 400;
  font-size: 22rpx;
  color: #666666;
  margin-bottom: 16rpx;
}

.iptBoxCla_title_ipt {
  font-weight: 400;
  font-size: 32rpx;
  // color: #CCCCCC;
}

.redCla {
  padding-left: 8rpx;
  color: #fe4d4f;
}

.bottomBtn {
  width: 100%;
  height: 196rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 24rpx 32rpx;
}

.bottomBtn_text {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  text-align: center;
  line-height: 80rpx;
  font-weight: 600;
  font-size: 28rpx;
  color: #ffffff;
}
</style>
