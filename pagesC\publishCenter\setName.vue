<template>
	<view class="warp">
		<view class="title">
			创作账号名称
		</view>
		<view class="txt">
			请勿使用含有特殊符号或明显推销推广意图的名称
		</view>
		<u--textarea placeholder="输入名称" border="bottom" height="22" v-model="value" maxlength="10" count
			@change="change"></u--textarea>
		<view class="tips">
			<img src="/static/images/publishCenter/warning-icon.png" alt="" /> <text>每月可修改两次（还剩余2次）</text>
		</view>
		<view class="btn">
			保存
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value: '',
			}
		},
		methods: {

		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 32rpx 32rpx;

		.title {
			font-size: 32rpx;
			font-weight: bold;
			margin-bottom: 12rpx;
		}

		.txt {
			font-size: 24rpx;
			margin-bottom: 12rpx;
		}

		.tips {
			margin-top: 12rpx;
			font-size: 28rpx;
			color: #999;
			display: flex;
			align-items: center;
			img{
				width: 48rpx;
				height: 48rpx;
				margin-right: 8rpx;
			}
		}

		.btn {
			width: 686rpx;
			height: 80rpx;
			background: #4F8CF0;
			font-size: 28rpx;
			color: #FFFFFF;
			text-align: center;
			line-height: 80rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			position: fixed;
			bottom: 100rpx;
		}
	}
</style>