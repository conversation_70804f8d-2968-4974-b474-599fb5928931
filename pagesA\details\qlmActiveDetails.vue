<template>
  <view class="home-index">
    <view class="swiper">
      <image :src="details.job_active.image.path_url" mode=""></image>
    </view>

    <view class="wrap">
      <view class="active-info">
        <view class="title">
          <view class="name">
            {{ details.job_active.title }}
          </view>
          <view class="status ing">
            {{ details.job_active.active_status_name }}
          </view>
        </view>
        <view class="time">创建时间：{{ details.job_active.created_at }}</view>
        <view class="notice">
          <u-notice-bar
            bgColor="#F5F5F7"
            color="#333333"
            :text="text1"
          ></u-notice-bar>
        </view>
      </view>
    </view>
    <view class="inn">
      <view class="tabs-container">
        <u-tabs
          :current="tabsIndex"
          :list="tabsList"
          @click="tabClick"
          :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
          :inactiveStyle="{ color: '#333', transform: 'scale(0.9)' }"
        ></u-tabs>
      </view>
      <template v-if="tabsIndex == 0">
        <view class="job-list">
          <view class="job-item" v-for="(item, index) in jobList" :key="index">
            <view class="job-header">
              <view class="job-title-location">
                <text class="job-title">{{ item.title }}</text>
                <text class="job-location">-{{ item.location }}</text>
              </view>
              <view class="job-salary">{{ item.salary }}</view>
            </view>

            <view class="job-tags">
              <view
                class="job-tag"
                v-for="(tag, tagIndex) in item.tags"
                :key="tagIndex"
                >{{ tag }}</view
              >
            </view>

            <view class="job-description">
              {{ item.description }}
            </view>
          </view>
        </view>
      </template>
      <template v-if="tabsIndex == 1">
        <view class="company-list">
          <view
            class="company-item"
            v-for="(item, index) in companyList"
            :key="index"
          >
            <view class="company-logo">
              <image
                src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"
                mode="aspectFit"
              ></image>
            </view>
            <view class="company-info">
              <view class="company-name">腾讯视频有限公司</view>
              <view class="company-basic-info">
                <text>学创北京</text>
                <text class="separator">|</text>
                <text>A轮</text>
                <text class="separator">|</text>
                <text>5000人</text>
              </view>
              <view class="company-tags">
                <view
                  class="company-tag"
                  v-for="(tag, tagIndex) in 3"
                  :key="tagIndex"
                  >{{ tag }}</view
                >
              </view>
            </view>
          </view>
        </view>
      </template>
      <template v-if="tabsIndex == 2">
        <view class="sub-wrap">
          <view class="title">活动简介</view>
          <view class="desc">
            {{ details.job_active.intro }}
          </view>
        </view>
        <view class="sub-wrap">
          <view class="title">活动介绍</view>
          <view class="desc">
            {{ details.job_active.intro }}
          </view>
        </view>

        <view class="sub-wrap">
          <view class="title">活动时间</view>
          <view class="times">
            <view class="time">
              {{ details.job_active.start }}
            </view>
            <view class="line">----></view>
            <view class="time time1">
              {{ details.job_active.end }}
            </view>
          </view>
        </view>

        <view class="sub-wrap">
          <view class="title">活动地点</view>
          <view class="pos">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png"
              mode=""
            ></image>
            {{ details.address[0].address }}
          </view>
          <view class="map">
            <map
              style="width: 100%; height: 300rpx"
              :latitude="latitude"
              :longitude="longitude"
              :markers="covers"
            ></map>
          </view>
        </view>

        <view class="sub-wrap">
          <view class="title">其他要求</view>

          <view class="desc">
            <img class="qiye-img" src="" alt="" />
          </view>
        </view>
      </template>
    </view>
    <view class="" style="height: 190rpx"> </view>
    <view class="btn-box">
      <view class="btn1"> 联系负责人 </view>
      <view class=""> 报名活动 </view>
    </view>
  </view>
</template>

<script>
import { getQLMJobfairDetails } from "../../config/api.js";
import { activeCompany } from "../../config/common_api";
export default {
  data() {
    return {
      companyList: [],
      details: {},
      text1: "*****公司已加入本次招聘会，欢迎各位同学踊跃参加！",
      latitude: 39.909,
      longitude: 116.39742,
      tabsIndex: 0,
      tabsList: [
        {
          key: "1",
          name: "参展职位",
        },
        {
          key: "2",
          name: "参展企业",
        },
        {
          key: "0",
          name: "活动详情",
        },
      ],
      covers: [
        {
          latitude: 39.909,
          longitude: 116.39742,
          iconPath: "../../../static/location.png",
        },
        {
          latitude: 39.9,
          longitude: 116.39,
          iconPath: "../../../static/location.png",
        },
      ],
      jobList: [
        {
          title: "游戏UI设计",
          location: "北京",
          salary: "12-50k",
          tags: ["世界500强", "上市公司", "游戏大厂"],
          description:
            "这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介.",
        },
        {
          title: "游戏UI设计",
          location: "北京",
          salary: "12-50k",
          tags: ["世界500强", "上市公司", "游戏大厂"],
          description:
            "这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介这个是简介.",
        },
      ],
    };
  },
  onLoad(option) {
    this.getQLMJobfairDetails(option.id);
    this.getActiveCompany(option.id);
  },
  methods: {
    tabClick(tab) {
      this.tabsIndex = tab.index;
    },
    // 招聘会详情
    async getQLMJobfairDetails(id) {
      let params = {
        id: id * 1,
      };
      const { status_code, data, message } = await getQLMJobfairDetails(params);
      if (status_code == 200) {
        this.details = data;
        console.log(data);
        //重新定位地图
        this.longitude = data.address[0].lng;
        this.latitude = data.address[0].lat;
        this.covers[0]["latitude"] = data.address[0].lat;
        this.covers[0]["longitude"] = data.address[0].lng;
      }
    },
    async getActiveCompany(id) {
      let params = {
        id: id * 1,
      };
      const { status_code, data, message } = await activeCompany(params);
      if (status_code == 200) {
        this.companyList = data;
        console.log(data);
      }
    },
    go() {
      uni.redirectTo({
        url: "/pagesA/details/qlmActiveCompDetails",
      });
    },
  },
};
</script>
<style>
body {
  background: #f5f5f7;
}
</style>
<style lang="less" scoped>
.swiper {
  image {
    width: 100%;
    height: 376rpx;
  }
}

.wrap {
  position: relative;
  background-color: #ffffff;
  display: flex;
  flex-direction: column;
  padding: 24rpx 32rpx;
  border-radius: 24rpx;
  margin-top: -60rpx;
  z-index: 100;

  .active-info {
    display: flex;
    flex-direction: column;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .name {
        font-size: 40rpx;
        color: #333333;
      }

      .status {
        font-size: 24rpx;
        color: #ffffff;
        width: 120rpx;
        height: 50rpx;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .ing {
        background: #57d51c;
      }

      .wks {
        background: #f9ad14;
      }
    }

    .time {
      font-weight: 400;
      font-size: 22rpx;
      color: #999999;
      margin: 16rpx 0;
    }
  }
}
.inn {
  background: #f5f5f7;
  padding: 32rpx;
  box-sizing: border-box;
  .sub-wrap {
    display: flex;
    flex-direction: column;
    margin-top: 32rpx;

    .title {
      font-size: 32rpx;
      color: #333333;
    }

    .desc {
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      margin-top: 16rpx;
    }

    .times {
      display: flex;
      align-items: center;
      margin-top: 16rpx;

      .time {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        height: 50rpx;
        font-size: 22rpx;
        color: #47bd60;
        border-radius: 8rpx;
      }
      .time1 {
        font-size: 22rpx;
        color: #fe4d4f;
      }

      .line {
        color: #4f8cf0;
      }
    }

    .pos {
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      margin: 16rpx 0;

      image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 16rpx;
      }
    }

    .comp-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 16rpx;

      .comp-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #f5f5f7;
        margin-bottom: 24rpx;
        width: 45%;
        height: 280rpx;

        image {
          width: 108rpx;
          height: 108rpx;
        }

        .name {
          font-size: 28rpx;
          color: #333333;
          margin: 26rpx 0;
        }

        .look {
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 8rpx;
          width: 88rpx;
          height: 56rpx;
          background: #4f8cf0;
          font-size: 28rpx;
          color: #ffffff;
        }
      }
    }
    .qiye-img {
      width: 120rpx;
      height: 120rpx;
      background: #d9d9d9;
      border-radius: 24rpx 24rpx 24rpx 24rpx;
    }
  }
}
.job-list {
  margin-top: 32rpx;
  background-color: #f5f5f7;
}

.job-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.job-title-location {
  display: flex;
  align-items: center;
}

.job-title {
  font-size: 32rpx;
  color: #333333;
}

.job-location {
  font-size: 32rpx;
  color: #333333;
}

.job-salary {
  font-size: 32rpx;
  color: #ff9500;
}

.job-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.job-tag {
  background-color: #f5f5f7;
  color: #999999;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}

.job-description {
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
}
.company-list {
  margin-top: 32rpx;
  background-color: #f5f5f7;
}

.company-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.company-logo {
  width: 96rpx;
  height: 96rpx;
  margin-right: 24rpx;

  image {
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
  }
}

.company-info {
  flex: 1;
}

.company-name {
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.company-basic-info {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 16rpx;

  .separator {
    margin: 0 8rpx;
  }
}

.company-tags {
  display: flex;
  flex-wrap: wrap;
}

.company-tag {
  background-color: #f5f5f7;
  color: #999999;
  font-size: 24rpx;
  padding: 4rpx 16rpx;
  border-radius: 4rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}
.btn-box {
  position: fixed;
  bottom: 0;
  width: 750rpx;
  height: 190rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  border-radius: 24rpx 24rpx 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  box-sizing: border-box;
  view {
    width: 308rpx;
    height: 80rpx;
    border-radius: 16rpx;
    background: #4f8cf0;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .btn1 {
    background: #ebf3ff;
    color: #4f8cf0;
  }
}
</style>
