<template>
	<view class="">
		<u-sticky bgColor="#F5F5F5">
			<view class="search-wrap">
				<u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
			</view>
		</u-sticky>
		<view class="list">
			<auth-employ-item v-for="item in list" :item="item"></auth-employ-item>
		</view>
	</view>
</template>

<script>
	import {
		getCompanyAuthEmployList
	} from "../../config/api.js"
	import AuthEmployItem from '../components/authEmployItem.vue'
	export default {
		components: {
			AuthEmployItem
		},
		data() {
			return {
				page:1,
				limit:10,
				list:[]
			}
		},
    onLoad() {
      this.getCompanyAuthEmployList()
    },
		methods:{
			//获取授权就业管家列表
			async getCompanyAuthEmployList(){
				let params = {
					page:this.page,
					limit:this.limit
				}
				const {status_code,data,message} = await getCompanyAuthEmployList(params)
				if(status_code==200){
					this.list = data.data;
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.search-wrap {
		padding: 32rpx;
	}

	.list {
		padding: 0 32rpx 32rpx 32rpx;
	}
</style>