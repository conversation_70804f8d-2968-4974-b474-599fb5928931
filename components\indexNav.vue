<template>
	<view class="wrap">
		<!-- <view class="tj">
			<view class="sup">推荐</view>
			<view class="sub">为你推荐</view>
		</view> -->
		<scroll-view class="scroll-W" :scroll-x="true">
			<view v-for="(item,index) in list" :key="index" @click.stop="changeNav(item,index)"
				:class="navIndex == index ? 'nav-item active' : 'nav-item'">
				<view class="sup" style="width: 160rpx; white-space: nowrap; overflow: hidden;
				text-overflow: ellipsis;">{{item.name}}</view>
				<view class="sub">{{item.tag}}</view>
				<image style="width: 16rpx; height: 16rpx;" @click.stop="delItem(index)"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/n98wmRJka2arjd3qD8gaxwNYJJfFb7XvApH0bHP9.png" v-if="index !== 0" mode=""></image>
			</view>
			<view class="nav-item1" @click.stop="editJobClass()">
				<image src="../static/images/index/plus.png" mode=""></image>
			</view>
		</scroll-view>

	</view>
</template>

<script>
	import {
		saveTourist,
		updateLoginInfo
	} from "@/config/api.js"
	export default {
		name: "indexNav",
		props: {
			list: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				navIndex: 0,
				flag: '',
				roleType: ''
			};
		},
		methods: {
			changeNav(item, ind) {
				this.navIndex = ind;
				this.$emit('jobClassSearch', {
					item
				})
			},
			editJobClass() {
				uni.navigateTo({
					url: "/pagesA/tourist/selectJob"
				})
			},
			delItem(index) {
				// console.log(index)
				this.list.splice(index, 1);
				if (this.navIndex == index) {
					this.navIndex = 0;
				}
				const ids = this.list.map(item => item.id * 1);
				const params = {
					flag: uni.getStorageSync('userInfo').member.flag,
					job_class_id: ids
				};
				// console.log("params",params)

				saveTourist(params).then(response => {
					const {
						status_code
					} = response;
					if (status_code == 200) {
						uni.$u.toast('删除成功');
						// console.log('执行方法')
						updateLoginInfo().then(res => {
							if (res.status_code == 200) {
								const userInfo = uni.getStorageSync('userInfo');
								uni.setStorageSync('userInfo', userInfo);
								this.$store.commit('setUserInfo', res.data)
								uni.setStorageSync('userInfo', res.data)
								this.$emit('update:list', this.list);
							}
						})
					} else {
						uni.$u.toast('删除失败');
					}
				}).catch(error => {
					// console.error('删除失败', error);
					uni.$u.toast('删除失败');
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	.wrap {
		display: flex;
		// height: 140rpx;
		padding-right: 32rpx;
		align-items: center;

		.scroll-W {
			width: 100%;
			white-space: nowrap;
			position: relative;

			.nav-item1 {
				display: inline-block;
				// width: 180rpx;
				position: absolute;
				right: 10rpx;
				top: 20rpx;

				image {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.nav-item {
				display: inline-block;
				text-align: center;
				width: 170rpx;
				height: 100%;
				color: #999999;
				position: relative;

				image {
					position: absolute;
					top: 0;
					right: 8rpx;
				}

				.sup {
					font-weight: 600;
					font-size: 28rpx;
				}

				.sub {
					font-weight: 600;
					font-size: 20rpx;
					margin-top: 8rpx;
				}

				&.active {
					color: #000000;
				}
			}
		}

		.tj {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 180rpx;

			.sup {
				font-weight: 600;
				font-size: 28rpx;
			}

			.sub {
				font-weight: 600;
				font-size: 20rpx;

				margin-top: 8rpx;
			}
		}



		.plus {
			display: flex;
			margin-left: 32rpx;


		}
	}
</style>
