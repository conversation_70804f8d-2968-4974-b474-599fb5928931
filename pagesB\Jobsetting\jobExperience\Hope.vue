<template>
	<!-- 期望岗位 -->
	<view class="container">
		<view class="context">
			<radio-group @change="chang" class="radio-group">
				<view class="recruitment-item" v-for="(item, index) in recruitmentList" :key="item.id">
					<scroll-view class="scroll-view_H" scroll-x="true">
						<view class="item-recrutment">
							<view class="item-box">
								<radio value="" :checked="item.isChecked" style="margin-left: 36rpx;" />
								<view class="item-context">
									<view class="description">
										<text>{{item.content.postsType}}</text>
									</view>
									<view class="description-item">
										<view class="description-text">
											<text> {{ item.content.industry.join('/') }}</text>
											<text style="color: rgba(204, 204, 204, 1);"> | </text>
											<text> {{ item.content.salary.salaryBegin }} - {{item.content.salary.salaryEnd}}</text>
											<text style="color: rgba(204, 204, 204, 1);"> | </text>
											<text>{{item.content.city}}</text>
										</view>
										<image :src="editor" @click="editItem(index)"
											style="width: 32rpx; height: 32rpx; align-self: center; margin-right: 32rpx;">
										</image>
									</view>
								</view>
							</view>
							<view class="delete-btn" @click="deleteItem(index)">
								<text style="width: 56rpx; font-size: 28rpx;">删除</text>
							</view>
						</view>
					</scroll-view>

				</view>
				<view class="add" @click="add(e)">
					添加
				</view>
			</radio-group>
		</view>
		<view class="footer">
			<view class="confirm-btn" @click="confirmAction">确定</view>
		</view>
	</view>
</template>

<script>
	import { getStrength } from '../../../config';
import editor from '../../../static/jobsetting/editor-setting.png'
	export default {
		data() {
			return {
				recruitmentList: [],
				activeRadio: '',
				editor
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onShow() {
			this.onGetStrength();
		},
		methods: {
			async onGetStrength () {
				const params = {
					type: '3',
					...this.userTypeParams
				}
				const res = await getStrength(params);
				if(res.status_code !== '200') return;
				this.recruitmentList = res.data.map(el => {
					return {
						...el,
						content: JSON.parse(el.content),
						isChecked: false
					}
				});
				console.log(this.recruitmentList);
			},
			editItem(index) {
				uni.navigateTo({
					// url: '/pagesB/Jobsetting/personalresume/plan'
					url: '/pagesB/Jobsetting/personalresume/hopePosition'
				})
			},
			deleteItem(index) {
				this.recruitmentList.splice(index, 1);
			},
			confirmAction() {
				// 确定操作逻辑
				console.log('执行确定操作');
			},
			chang(e) {
				this.activeRadio = e.detail.value;
				console.log(this.activeRadio);
			},
			add(e) {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/hopePosition'
				})
			}
		}
	};
</script>

<style scoped>
	.container {
		/* padding: 20rpx; */
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		background-color: rgba(245, 245, 247, 1);
	}

	.context {
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.description-item {
		display: flex;
		justify-content: space-between;
	}


	.recruitment-item {
		width: 100%;
		margin-bottom: 42rpx;
	}

	.item-recrutment {
		display: flex;
		/* margin: 20rpx auto; */
		background-color: #fff;
		border-radius: 24rpx;
		/* padding: 0rpx 20rpx; */
		margin-bottom: 20rpx;
		align-items: center;
		width: 686rpx;
		/* height: 312rpx; */
	}

	.scroll-view_H {
		/* white-space: nowrap; */
		width: 100%;
	}

	.item-box {
		width: 686rpx;
		height: 172rpx;
		display: flex;
		align-items: center;
	}

	.item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
		margin-top: 32rpx;
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.time {
		font-size: 28rpx;
		color: #999;
	}

	.item-content {
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
		align-items: flex-start;
		flex: 1;
	}

	.item-context {
		width: 575rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		height: 172rpx;
		margin-left: 32rpx;
	}

	.tag {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 174rpx;
		height: 64rpx;
		background-color: rgba(243, 243, 243, 1);
		color: rgba(51, 51, 51, 1);
		font-size: 28rpx;
		border-radius: 8rpx;
	}

	.tag-inner {
		width: 30rpx;
		height: 30rpx;
		border: 2rpx solid #ccc;
		border-radius: 50%;
		margin-right: 10rpx;
	}

	.checked {
		background-color: #007AFF;
	}

	.tag-text {
		font-size: 30rpx;
	}

	.description {
		/* flex: 1; */
		height: 80rpx;
		width: 562rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.description-text {
		width: 416rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		font-size: 28rpx;
		justify-content: space-between;
	}

	.delete-btn {
		background-color: #FF5050;
		color: #fff;
		text-align: center;
		border-radius: 24rpx;
		padding: 48rpx;
		/* width: 112rpx; */
		height: 80rpx;
		margin-left: 23rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.add {
		color: rgba(79, 140, 240, 1);
		font-size: 26rpx;
	}
</style>