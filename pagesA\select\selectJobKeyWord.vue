<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="title">UI设计师岗位招聘偏好</view>
      <view class="subtitle">你的偏好选择将用于为你推荐更合适的候选人</view>
    </view>

    <!-- 选择区域 -->
    <view class="select-area">
      <!-- UI设计经验 -->
      <view class="select-group">
        <view class="group-title">
          该岗位要求具备UI设计经验/技能吗？
          <text class="required">[单选]</text>
        </view>
        <view class="options">
          <view
            class="option-item"
            :class="{ active: experienceType === item.value }"
            v-for="item in experienceOptions"
            :key="item.value"
            @click="selectExperience(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </view>

      <!-- 公司类型 -->
      <view class="select-group">
        <view class="group-title">
          该岗位所属的公司类型是？
          <text class="required">[单选]</text>
        </view>
        <view class="options">
          <view
            class="option-item"
            :class="{ active: companyType === item.value }"
            v-for="item in companyOptions"
            :key="item.value"
            @click="selectCompany(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </view>

      <!-- 福利待遇 -->
      <view class="select-group">
        <view class="group-title">
          该岗位提供哪些福利待遇？
          <text class="required">[多选]</text>
        </view>
        <view class="options">
          <view
            class="option-item"
            :class="{ active: benefits.includes(item.value) }"
            v-for="item in benefitOptions"
            :key="item.value"
            @click="selectBenefit(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </view>

      <!-- 作息安排 -->
      <view class="select-group">
        <view class="group-title">
          该岗位提供哪些作息安排？
          <text class="required">[单选]</text>
        </view>
        <view class="options">
          <view
            class="option-item"
            :class="{ active: workSchedule === item.value }"
            v-for="item in scheduleOptions"
            :key="item.value"
            @click="selectSchedule(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </view>

      <!-- 工作方式 -->
      <view class="select-group">
        <view class="group-title">
          该岗位的工作方式是？
          <text class="required">[多选]</text>
        </view>
        <view class="options">
          <view
            class="option-item"
            :class="{ active: workModes.includes(item.value) }"
            v-for="item in workModeOptions"
            :key="item.value"
            @click="selectWorkMode(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </view>
    </view>

    <!-- 自定义招聘偏好 -->
    <view class="select-group">
      <view class="group-title"> 您可以自定义招聘偏好 </view>
      <view class="custom-keywords">
        <view class="keyword-list">
          <view
            class="keyword-item"
            v-for="(item, index) in customKeywords"
            :key="index"
          >
            {{ item }}
            <text class="delete-btn" @click="deleteKeyword(index)">×</text>
          </view>
        </view>
        <view class="add-keyword" @click="showKeywordPopup">
          <text class="add-text">添加关键词</text>
          <text class="add-icon">+</text>
        </view>
      </view>
    </view>

    <view style="height: 196rpx"></view>
    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next" @click="next">下一步</view>
    </view>

    <!-- 添加关键词弹窗 -->
    <uni-popup ref="keywordPopup" type="center">
      <view class="keyword-popup">
        <view class="popup-title">添加关键词</view>
        <view class="popup-input">
          <input
            type="text"
            v-model="newKeyword"
            placeholder="请输入关键词，不超过20个字"
            maxlength="20"
          />
        </view>
        <view class="popup-buttons">
          <view class="cancel-btn" @click="hideKeywordPopup">取消</view>
          <view class="confirm-btn" @click="addKeyword">确认</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      customKeywords: [], // 自定义关键词列表
      newKeyword: "", // 新增关键词

      experienceType: "", // UI设计经验
      companyType: "", // 公司类型
      benefits: [], // 福利待遇（多选）
      workSchedule: "", // 作息安排
      workModes: [], // 工作方式（多选）

      // 选项数据
      experienceOptions: [
        { label: "有UI设计经验/技能", value: "has_experience" },
        { label: "接受无相关经验/技能", value: "no_experience" },
      ],
      companyOptions: [
        { label: "甲方公司", value: "company_a" },
        { label: "乙方公司", value: "company_b" },
      ],
      benefitOptions: [
        { label: "五险", value: "insurance" },
        { label: "五险一金", value: "insurance_fund" },
        { label: "带薪休假", value: "paid_leave" },
        { label: "年终奖", value: "annual_bonus" },
        { label: "餐补", value: "meal" },
        { label: "包住", value: "accommodation" },
        { label: "包吃", value: "food" },
        { label: "包吃住", value: "food_accommodation" },
        { label: "车补", value: "transport" },
        { label: "加班费", value: "overtime_pay" },
        { label: "房补", value: "housing" },
        { label: "其他", value: "others" },
      ],
      scheduleOptions: [
        { label: "周末双休", value: "weekend_off" },
        { label: "周末单休", value: "one_day_off" },
        { label: "大小周", value: "flexible_weekend" },
        { label: "月休四天", value: "four_days_off" },
        { label: "其他", value: "others" },
      ],
      workModeOptions: [
        { label: "现场办公", value: "office" },
        { label: "远程办公", value: "remote" },
      ],
    };
  },

  methods: {
    // 选择UI设计经验
    selectExperience(value) {
      this.experienceType = value;
    },
    // 选择公司类型
    selectCompany(value) {
      this.companyType = value;
    },
    // 选择福利待遇（多选）
    selectBenefit(value) {
      const index = this.benefits.indexOf(value);
      if (index > -1) {
        this.benefits.splice(index, 1);
      } else {
        this.benefits.push(value);
      }
    },
    // 选择作息安排
    selectSchedule(value) {
      this.workSchedule = value;
    },
    // 选择工作方式（多选）
    selectWorkMode(value) {
      const index = this.workModes.indexOf(value);
      if (index > -1) {
        this.workModes.splice(index, 1);
      } else {
        this.workModes.push(value);
      }
    },
    // 下一步
    next() {
      console.log("选择的UI设计经验：", this.experienceType);
      console.log("选择的公司类型：", this.companyType);
      console.log("选择的福利待遇：", this.benefits);
      console.log("选择的作息安排：", this.workSchedule);
      console.log("选择的工作方式：", this.workModes);
      uni.navigateBack();
    },

    // 显示添加关键词弹窗
    showKeywordPopup() {
      this.newKeyword = "";
      this.$refs.keywordPopup.open();
    },

    // 隐藏添加关键词弹窗
    hideKeywordPopup() {
      this.$refs.keywordPopup.close();
    },

    // 添加关键词
    addKeyword() {
      if (this.newKeyword.trim()) {
        this.customKeywords.push(this.newKeyword.trim());
        this.hideKeywordPopup();
      }
    },

    // 删除关键词
    deleteKeyword(index) {
      this.customKeywords.splice(index, 1);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
  padding: 32rpx;
}

.header {
  margin-bottom: 48rpx;

  .title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 16rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #666;
  }
}

.select-area {
  .select-group {
    margin-bottom: 40rpx;

    .group-title {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 24rpx;

      .required {
        color: #999;
        margin-left: 8rpx;
      }
    }

    .options {
      display: flex;
      flex-wrap: wrap;
      gap: 24rpx;

      .option-item {
        padding: 12rpx 32rpx;
        background: #f5f5f5;
        border-radius: 8rpx;
        font-size: 24rpx;
        color: #333;
        border: 2rpx solid transparent;

        &.active {
          background: #e8f1ff;
          border-color: #4f8cf0;
          color: #4f8cf0;
        }
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  border-radius: 24rpx 24rpx 0 0;

  .next {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    width: 90%;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
    margin-top: 24rpx;
  }
}

.custom-keywords {
  padding: 20rpx;

  .keyword-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 20rpx;
  }

  .keyword-item {
    background: #f5f5f5;
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
    font-size: 24rpx;
    color: #333333;
    display: flex;
    align-items: center;

    .delete-btn {
      margin-left: 10rpx;
      color: #999;
    }
  }

  .add-keyword {
    display: inline-flex;
    align-items: center;
    color: #4f8cf0;
    font-size: 24rpx;
    padding: 12rpx 32rpx;
    background: #f5f5f5;
    border-radius: 8rpx;

    .add-text {
      margin-right: 10rpx;
    }

    .add-icon {
      font-size: 32rpx;
    }
  }
}

.keyword-popup {
  background: #fff;
  width: 600rpx;
  border-radius: 16rpx;
  padding: 32rpx 32rpx 0 32rpx;

  .popup-title {
    font-size: 32rpx;
    text-align: center;
    margin-bottom: 40rpx;
  }

  .popup-input {
    background: #f3f3f3;
    padding: 20rpx;
    border-radius: 8rpx;
    margin-bottom: 40rpx;

    input {
      width: 100%;
      font-size: 28rpx;
    }
  }

  .popup-buttons {
    display: flex;
    border-top: 1rpx solid #eeeeee;

    view {
      flex: 1;
      text-align: center;
      padding: 20rpx 0;
      font-size: 32rpx;
    }

    .cancel-btn {
      color: #999;
      border-right: 1rpx solid #eeeeee;
    }

    .confirm-btn {
      color: #4f8cf0;
    }
  }
}
</style>
