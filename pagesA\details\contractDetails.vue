<template>
	<view class="home-index">
		<view class="header">
			<view class="logo">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/NKgeqfH5GSaU1krUtQWZoJx6bFlxeBQEb1NoxBps.png" mode=""></image>
				<view class="type">
					合同类型
				</view>
			</view>
			<view class="info">
				<view class="name">
					文档名称
				</view>
				<view class="status">
					<view class="dot">
					</view>
					<view class="desc">
						未签署
					</view>
				</view>
				<view class="time">
					签署截止日至期：<text>2023/12/12</text>
				</view>
				<view class="time">
					备注：<text>这是一份合同</text>
				</view>
			</view>
		</view>

		<view class="container">
			<view class="wrap">
				<view class="title">
					附件
				</view>
				<view class="file-list">
					<view class="file-item">
						<image src="/pagesA/static/images/file.png" mode=""></image>
						<text>委托待招聘协议合同</text>
					</view>
					<view class="file-item">
						<image src="/pagesA/static/images/file.png" mode=""></image>
						<text>关于授权的独家协议授权</text>
					</view>
				</view>
			</view>

			<view class="wrap">
				<view class="title">
					签署方
				</view>
				<view class="list">
					<view class="item">
						<view class="pic">
							<image src="https://api-test.zhaopinbei.com/storage/uploads/images/9LKPyv7h3diO5slskL4dH1ysbhTPCtASgEXsOoxo.png" mode="widthFix"></image>
						</view>

						<view class="cont">
							<view class="name">
								刘金鹏
							</view>
							<view class="phone">
								15932178945
							</view>
						</view>

						<view class="status fq">
							发起方
						</view>
					</view>
					<view class="item">
						<view class="pic">
							<image src="https://api-test.zhaopinbei.com/storage/uploads/images/oCPwLNe3lZhy5h47XKR2cRYV1r1dA3mTkdu1Gyls.png" mode="widthFix"></image>
						</view>

						<view class="cont">
							<view class="name">
								<text>刘金鹏</text>  <text>于2023/12/12 12:12:12</text>
							</view>
							<view class="phone">
								15932178945
							</view>
						</view>

						<view class="status qs">
							签收方
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="btn">
			签署合同
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return{

			}
		}
	}
</script>
<style>
	page{
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index{
		padding-bottom: 164rpx;
	}
	.header{
		display: flex;
		background-color: #FFFFFF;
		padding: 32rpx;
		.logo{
			width: 216rpx;
			height: 216rpx;
			position: relative;
			image{
				width: 100%;
				height: 100%;
			}
			.type{
				position: absolute;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 48rpx;
				left: 0;
				bottom: 0;
				color: #FFFFFF;
				background: #1BB327;
				z-index: 10;
				font-weight: 400;
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}

		.info{
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			flex: 1;
			padding-left: 32rpx;
			.name{
				font-weight: 500;
				font-size: 32rpx;
				color: rgba(0,0,0,0.9);
			}

			.status{
				display: flex;
				align-items: center;
				.dot{
					width: 16rpx;
					height: 16rpx;
					border-radius: 50%;
					background: #DDDDDD;
					margin-right: 8rpx;
				}
				.desc{
					font-weight: 400;
					font-size: 28rpx;
					color: rgba(0,0,0,0.9);
				}
			}

			.time{
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(0,0,0,0.6);
				text{
					color: rgba(0,0,0,0.9);;
				}
			}
		}
	}
	.container{
		display: flex;
		flex-direction: column;
		background-color: #FFFFFF;
		padding: 0 32rpx;
		margin-top: 32rpx;
		.wrap{
			padding: 32rpx 0;
			&:first-child{
				border-bottom: 1px solid #F5F5F7;
			}
			.title{
				font-weight: 400;
				font-size: 32rpx;
				color: rgba(0,0,0,0.9);
			}
			.file-list{
				display: flex;
				flex-direction: column;
				.file-item{
					display: flex;
					align-items: center;
					margin-top: 24rpx;
					image{
						width: 24rpx;
						height: 24rpx;
						margin-right: 16rpx;
					}
					text{
						font-weight: 600;
						font-size: 28rpx;
						color: #4F8CF0;
					}
				}
			}

			.list{
				display: flex;
				flex-direction: column;
				.item{
					display: flex;
					align-items: center;
					background: #F5F5F7;
					padding: 24rpx 24rpx 52rpx 24rpx;
					margin-top: 36rpx;
					border-radius: 24rpx;
					position: relative;
					.pic{
						display: flex;
						justify-content: center;
						align-items: center;
						width: 56rpx;
						height: 56rpx;
						background: #FFFFFF;
						border-radius: 12rpx;
						image{
							width: 56rpx;
							height: 56rpx;
							border-radius: 12rpx;
						}
					}

					.cont{
						display: flex;
						flex-direction: column;
						flex: 1;
						padding-left: 16rpx;
						.name{
							display: flex;
							justify-content: space-between;
							font-weight: 500;
							font-size: 28rpx;
							color: rgba(0,0,0,0.9);
							.time{
								font-weight: 400;
								font-size: 24rpx;
								color: rgba(0,0,0,0.6)
							}
						}

						.phone{
							font-weight: 400;
							font-size: 26rpx;
							color: rgba(0,0,0,0.9);
							margin-top: 12rpx;
						}
					}

					.status{
						position: absolute;
						display: flex;
						justify-content: center;
						align-items: center;
						left: 0;
						bottom: 0;
						padding: 0 16rpx;
						height: 48rpx;
						border-radius: 0 24rpx 0 24rpx;
						font-weight: 400;
						font-size: 20rpx;
						color: #FFFFFF;
					}

					.fq{
						background-color: #4787F0;
					}

					.qs{
						background-color: #00A870;
					}
				}
			}
		}
	}

	.btn{
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		background: linear-gradient( 135deg, #4F8CF0 0%, #1E6DEE 100%);
		height: 100rpx;
		width: 90%;
		left: 5%;
		bottom: 32rpx;
		border-radius: 16rpx;
		font-weight: 600;
		font-size: 34rpx;
		color: #FFFFFF;
	}

</style>
