<template>
    <view id="app">
        <view class="listBox" v-for="(item,index) in page.data" :key="index">
            <view class="listBox_top">
                <image :src="item.member_info.image.path_url" mode="" class="peopleHead"></image>
                <view class="rightBox">
                    <view class="listDetailTop">
                        <view class="listDetailTop_title">
                            <view class="listDetailTop_title_name">{{item.certification.name}}</view>
                            <view :class="item.member.certification_status === 1 ? 'translateCla1' : 'translateCla'">
                                {{item.member.certification_status_name }}
                            </view>
                        </view>
                        <view class="rightType">
                            <text :class="item.member_info.job_status===1 ?  'dian' : (item.member_info.job_status === 2 ? 'dian1' : 'dian2' )">
                                ·
                            </text>
                            <text class="status">{{ item.member_info.job_status_name }}
                            </text>
                        </view>
                    </view>
                    <!--| {{item.jobAge}}年工作经验-->
                    <view class="rightBox_bottom">{{ item.member_info.sex_str }} |
                        {{item.member_info.age}}岁 | {{ item.member_info.education_type_name }}
                    </view>
                </view>
            </view>

            <view class="hrBox"></view>

            <view class="listBox_bottom">
                <view class="listBox_bottom_text">{{item.text}}</view>
                <!-- v-if="item.sign_status=='singed'" -->
                <view class="btn agree" @click.stop="approve(item)">
                    审核
                </view>
                <view class="btnBox" @click="communicate('member',item.member.id)">聊聊呗</view>
            </view>

            <view :class="['UserStatus', item.project_member.use_status == 1?'greenBtn' : 'redBtn' ]">
                {{ item.project_member.use_status_name }}
            </view>
        </view>

        <Pages :status="page.status"></Pages>
    </view>
</template>

<script>
    import {communicate} from "../../common/common";
    import {
        projectMembers,
        projectAuditMembers
    } from '../../config/headhunterList_api.js'

    export default {
        data() {
            return {
                options: ['通过', '未通过'],
                page: {
                    form: {
                        page: 1,
                        limit: 10,
                        id: 0,
                        push_user_id: 0,
                    },
                    data: [],
                    status: 'nomore',
                }
            }
        },
        onLoad(option) {
            // 首次加载启动
            this.page.form.id = option.project_id;
            this.page.form.push_user_id = option.user_id;
            this.getList()
        },

        onReachBottom() {
            var _this = this;
            if (_this.page.more) {
                _this.page.form.page++;
                _this.getList()
            }
        },
        methods: {
            communicate,
            //审核
            approve(item) {
                var _this = this;
                var projectMemberId = item.project_member.id;
                uni.showActionSheet({
                    itemList: this.options, // 显示的选项
                    // async 
                    success: (res) => {
                        let useStatus = res.tapIndex == 0 ? '1' : '3'

                        // 变更成对应的审核接口
                        projectAuditMembers({
                            id: projectMemberId,
                            use_status: useStatus
                        }).then(response => {
                            uni.$u.toast(response.message);

                            if (response.status_code == 200) {
                                _this.page.data = [];
                                _this.page.form.page = 1;
                                _this.getList();
                            }
                        });
                    },
                    fail: (err) => {
                        console.error(err); // 处理错误
                    },
                });
            },
            getList() {
                var _this = this;
                projectMembers(this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.more ? 'loading' : 'nomore';
                    }
                })
            }
        }
    }
</script>

<style scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
        min-height: 100vh;
        height: auto;
        background: #F5F5F7;
    }

    .listBox {
        width: 100%;
        /* height: 274rpx; */
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 32rpx;
        margin-bottom: 32rpx;
        position: relative;
    }

    .listBox_top {
        display: flex;
        align-items: center;
    }

    .peopleHead {
        width: 104rpx;
        height: 104rpx;
        background: #D9D9D9;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        margin-right: 24rpx;
    }

    .rightBox {
        flex: 1;
    }

    .listDetailTop {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 24rpx;
    }

    .listDetailTop_title {
        display: flex;
        align-items: center;
    }

    .listDetailTop_title_name {
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        margin-right: 16rpx;
    }

    .translateCla {
        width: 84rpx;
        height: 40rpx;
        background: rgba(87, 213, 28, 0.1);
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-weight: 600;
        font-size: 20rpx;
        color: #57D51C;
        text-align: center;
        line-height: 40rpx;
    }

    .translateCla1 {
        width: 84rpx;
        height: 40rpx;
        background: rgba(249, 173, 20, 0.1);
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-weight: 600;
        font-size: 20rpx;
        color: #F9AD14;
        text-align: center;
        line-height: 40rpx;
    }

    .rightType {
        display: flex;
        align-items: center;
    }

    .dian {
        font-size: 38rpx;
        margin-right: 8rpx;
        color: #57D51C;
    }

    .dian1 {
        font-size: 38rpx;
        margin-right: 8rpx;
        color: #F9AD14;
    }

    .dian2 {
        font-size: 38rpx;
        margin-right: 8rpx;
        color: #4F8CF0;
    }

    .status {
        font-size: 24rpx;
        color: #333333;
    }

    .rightBox_bottom {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .hrBox {
        width: 100%;
        height: 2rpx;
        background: #F5F5F7;
        margin: 24rpx 0;
    }

    .listBox_bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .listBox_bottom_text {
        flex: 1;
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
    }

    .btnBox {
        width: 144rpx;
        height: 56rpx;
        background: #4F8CF0;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #FFFFFF;
        text-align: center;
        line-height: 56rpx;
    }

    .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 24rpx;
        border-radius: 8rpx;
        font-weight: 600;
        padding: 0 24rpx;
        height: 56rpx;
        font-size: 24rpx;
        color: #FFFFFF;
        margin-right: 10rpx;
    }

    .agree {
        background-color: #4F8CF0;
        color: #FFFFFF;
    }

    .UserStatus {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 0;
        top: 0;
        font-size: 28rpx;
        width: 132rpx;
        height: 62rpx;
        border-radius: 0 24rpx 0 24rpx;
    }

    .greenBtn {
        background: #57D51C;
        color: #FFFFFF;
    }

    .redBtn {
        background: #FE4D4F;
        color: #FFFFFF;
    }
</style>