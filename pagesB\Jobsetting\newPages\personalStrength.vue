<template>
	<!-- 个人优势 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="text-btn" @click="onRouteSubmit({})">新增 </view>
				<u-radio-group v-model="activeValue" placement="column">
					<rice-swipe-action custom-style="display:flex;flex-direction:column;gap:24rpx;">
						<rice-swipe-action-item v-for="v in tableList" :key="v.id">
							<template #default>
								<view class="swipe-container" @click="onTableItemClick(v)">
									<view class="swipe-start">
										<u-radio :name="v.id"></u-radio>
										<view class="content">{{v.content}}</view>
									</view>
									<image class="image"
										src="https://api-test.zhaopinbei.com/storage/uploads/images/nbOIafDAL7d9e8Aet5dJfrZqcOlXu3HMAfhGy6S7.png"
										@click.stop="onRouteSubmit(v)">
									</image>
								</view>
							</template>
							<template #right>
								<view class="btn" @click="onDeleteStrength(v)">删除</view>
							</template>
						</rice-swipe-action-item>
					</rice-swipe-action>
				</u-radio-group>
			</view>

			<u-toast ref="toast"></u-toast>
		</scroll-view>

		<view class="btn-container">
			<view class="btn" @click="onSelectConfirm">上传</view>
		</view>
	</view>
</template>

<script>
	import {
		deleteStrength,
		getStrength
	} from '@/config';

	export default {
		data() {
			return {
				swipeActionStyle: {
					display: 'flex',
					flexDirection: 'column',
					gap: '24rpx',
				},
				tableList: [],
				activeValue: null,
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onShow() {
			this.onGetStrength();
		},
		methods: {
			async onGetStrength() {
				const params = {
					type: '1',
					...this.userTypeParams
				}
				const res = await getStrength(params);
				if (res.status_code !== '200') return;
				this.tableList = res.data;
			},
			async onDeleteStrength(v) {
				const params = {
					strength_id: v.id,
					type: '1',
					...this.userTypeParams
				}
				const res = await deleteStrength(params);
				if (res.status_code !== '200') return;
				await this.onGetStrength();
				this.$refs.toast.show({
					duration: 1000,
					message: '删除成功'
				});
			},
			onRouteSubmit(v) {
				uni.$u.route({
					url: '/pagesB/Jobsetting/newPages/personalStrengthSubmit',
					params: v
				})
			},
			onTableItemClick(v) {
				this.activeValue = v.id;
			},
			onSelectConfirm() {
				const find = this.tableList.find(el => el.id === this.activeValue);
				const pages = getCurrentPages();
				const prevPage = pages[pages.length - 2];
				prevPage.$vm.onBackStrengthParams(JSON.stringify(find));
				uni.$u.route({type: 'back'});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;

		.btn-container {
			background-color: #FFFFFF;
			padding-block-start: 24rpx;
			padding-inline: 64rpx;
			padding-block-end: calc(24rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			border-start-start-radius: 16rpx;
			border-start-end-radius: 16rpx;
			box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);

			.btn {
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				border-radius: 16rpx;
				text-align: center;
				color: #FFFFFF;
				padding-block: 20rpx;
				font-size: 28rpx;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding-block: 32rpx;

				.text-btn {
					color: #4F8CF0;
					font-size: 26rpx;
					margin-inline: 32rpx;
				}

				.swipe-container {
					padding: 32rpx;
					background-color: #FFFFFF;
					border-radius: 24rpx;
					margin-inline: 32rpx;
					display: flex;
					flex-direction: column;
					gap: 16rpx;

					.swipe-start {
						display: flex;
						align-items: center;
						gap: 24rpx;

						.content {
							color: #333333;
							font-size: 28rpx;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 3;
						}

					}

					.image {
						width: 32rpx;
						height: 32rpx;
						object-fit: contain;
						align-self: flex-end;
					}
				}

				.btn {
					width: 152rpx;
					height: 100%;
					background-color: #FE4D4F;
					border-radius: 24rpx;
					color: #FFFFFF;
					margin-inline-end: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}
	}
</style>