//就业管家独有接口
import {
    request
} from "./request"

//应聘人详情
export const resumeShow = (data) => {
    return request({
        url: `/member/resume/show`,
        data,
        method: 'post'
    })
}

//草稿列表
export const resumeList = (data) => {
    return request({
        url: `/member/resume`,
        data,
        method: 'post'
    })
}

// 变更审核
export const getAuditList = (data) => {
    return request({
        url: `/company/user-authorizes/index`,
        data,
        method: 'post'
    })
}

export const memberChangeAuditList = (data) => {
    return request({
        url: `/member/authorize/change-list`,
        data,
        method: 'post'
    })
}

// 发布任务
export const publishTask = (data) => {
    return request({
        url: `/task/store`,
        data,
        method: 'post'
    })
}

// 任务列表
export const getTaskList = (data) => {
    return request({
        url: `/task/index`,
        data,
        method: 'post'
    })
}

// 任务详情
export const getTaskListDetail = (data) => {
    return request({
        url: `/task/show`,
        data,
        method: 'post'
    })
}

// 领取记录
export const getTaskUser = (data) => {
    return request({
        url: `/task/task-user`,
        data,
        method: 'post'
    })
}

// 领取记录详情
export const getTaskUserDetail = (data) => {
    return request({
        url: `/task/task-user/show`,
        data,
        method: 'post'
    })
}

// 审核领取记录
export const auditPromotions = (data) => {
    return request({
        url: `/task/task-user/audit-promotions`,
        data,
        method: 'post'
    })
}

// 任务人列表
export const taskUserPromotions = (data) => {
    return request({
        url: `/task/task-user/promotions`,
        data,
        method: 'post'
    })
}


// 任务职位
export const taskJobs = (data) => {
    return request({
        url: `/task/jobs`,
        data,
        method: 'post'
    })
}

// 结束任务
export const taskClose = (data) => {
    return request({
        url: `/task/close`,
        data,
        method: 'post'
    })
}

// 报名企业列表
export const jobActiveCompany = (data) => {
    return request({
        url: `/job/active/company`,
        data,
        method: 'post'
    })
}

export const projectStore = (data) => {
    return request({
        url: `/project/store`,
        data,
        method: 'post'
    })
}

export const projectList = (data) => {
    return request({
        url: `/project/index`,
        data,
        method: 'post'
    })
}

export const projectShow = (data) => {
    return request({
        url: `/project/show`,
        data,
        method: 'post'
    })
}

export const projectUsers = (data) => {
    return request({
        url: `/project/users`,
        data,
        method: 'post'
    })
}
export const projectJobs = (data) => {
    return request({
        url: `/project/jobs`,
        data,
        method: 'post'
    })
}

export const userProjects = (data) => {
    return request({
        url: `/project/user-projects`,
        data,
        method: 'post'
    })
}
// 保存协同
export const saveCooperate = (data) => {
    return request({
        url: `/coordination/store`,
        data,
        method: 'post'
    })
}

// 协同列表
export const cooperateList = (data) => {
    return request({
        url: `/coordination/index`,
        data,
        method: 'post'
    })
}

// 协同详情
export const cooperateDetails = (data) => {
    return request({
        url: `/coordination/index`,
        data,
        method: 'post'
    })
}

// 删除协同
export const cooperateDestroy = (data) => {
    return request({
        url: `/coordination/destroy`,
        data,
        method: 'post'
    })
}

// 注销账号
export const userDisable = (data) => {
    return request({
        url: `/user/disable`,
        data,
        method: 'post'
    })
}

// 保存员工
export const userStore = (data) => {
    return request({
        url: `/user/store`,
        data,
        method: 'post'
    })
}

// 简历公开状态
export const resumeOpen = (data) => {
    return request({
        url: `/member/resume/open`,
        data,
        method: 'post'
    })
}

// 职位报名
export const signUpJob = (data) => {
    return request({
        url: `/member/job-report/store`,
        data,
        method: 'post'
    })
}

// 职位报名列表
export const jobReportList = (data) => {
    return request({
        url: `/member/job-report`,
        data,
        method: 'post'
    })
}

// 分享的简历
export const memberShareCompany = (data) => {
    return request({
        url: `/member/member-share/company`,
        data,
        method: 'post'
    })
}

// 分享简历至企业
export const pushCompanyStore = (data) => {
    return request({
        url: `/member/member-share/company-store`,
        data,
        method: 'post'
    })
}

// 面试结果
export const interviewResult = (data) => {
    return request({
        url: `/interview/result`,
        data,
        method: 'post'
    })
}

export const headhunterJobActiveReport = (data) => {
    return request({
        url: `/job/active/report-store`,
        data,
        method: 'post',
    })
}

//招聘会报名记录
export const activeAudit = (data) => {
    return request({
        url: `/job/active/audit/report`,
        data,
        method: 'post'
    })
}
//招聘会审核
export const activeHandle = (data) => {
    return request({
        url: `/job/active/audit/handle`,
        data,
        method: 'post'
    })
}
//招聘会职位报名记录
export const headhunterJobActiveReportList = (data) => {
    return request({
        url: `/job/active/report/index`,
        data,
        method: 'post'
    })
}

//项目-参与详情
export const settlementList = (data) => {
    return request({
        url: `/project/settlement-list`,
        data,
        method: 'post'
    })
}

//人员列表选择使用
export const selectPeopleJYGJ = (data) => {
    return request({
        url: `/user/index`,
        data,
        method: 'post'
    })
}

//项目千里马列表
export const projectMembers = (data) => {
    return request({
        url: `/project/project-members`,
        data,
        method: 'post'
    })
}

export const projectAuditMembers = (data) => {
    return request({
        url: `/project/audit-member`,
        data,
        method: 'post'
    })
}

//项目面试审核
export const interViewResult = (data) => {
    return request({
        url: `/interview/result`,
        data,
        method: 'post'
    })
}

//授权应聘人详情
export const authorizeShow = (data) => {
    return request({
        url: `/member/authorize/show`,
        data,
        method: 'post'
    })
}
//授权应聘人详情
export const auditMemberAuthorizeHandel = (data) => {
    return request({
        url: `/member/authorize/audit-handle`,
        data,
        method: 'post'
    })
}

//简历分享详情
export const memberShareShow = (data) => {
    return request({
        url: `/member/member-share/show`,
        data,
        method: 'post'
    })
}

//推送面试
export const pushInterview = (data) => {
    return request({
        url: `/interview/push-interview`,
        data,
        method: 'post'
    })
}






