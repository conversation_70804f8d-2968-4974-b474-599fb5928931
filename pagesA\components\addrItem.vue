<template>
    <view class="item" @click="go(item,itemIndex)">
        <view class="name">
            {{item.address_info}}
        </view>
        <view class="action">
            <view class="area">
                {{item.province_name}}·{{item.city_name}}·{{item.district_name}}
            </view>
            <view class="mr" @click.stop="setDefaultAddress">
                设为默认
                <image v-if="item.is_default==1" src="/static/images/plat/selected.png" mode=""></image>
                <image v-else src="/static/images/plat/unselected.png" mode=""></image>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        name: "addrItem",
        props: {
            itemIndex: {
                type: Number,
            },
            item: {
                type: Object,
                default: () => {
                }
            }
        },
        data() {
            return {};
        },
        methods: {
            setDefaultAddress() {
                this.$emit('setDefaultAddress', {
                    item: this.item,
                    itemIndex: this.itemIndex,
                })
            },

            go(item, itemIndex) {
                uni.setStorageSync('addressItemIndex', itemIndex);
                uni.navigateTo({
                    url: "/pagesA/add/addAddress?id=" + this.item.id
                })
            }
        }
    }
</script>

<style lang="scss" src="../../static/css/pagesA/components/addrItem.scss"></style>