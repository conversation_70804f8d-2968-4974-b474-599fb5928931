<template>
	<view class="container">
		<view style="padding:0rpx 32rpx">
			<!-- 问题及说明 -->
			<view style="
		    margin: 0 auto;">
				<view class="info-card">
					<p class="font-bold">
						填写公司主营业务有什么用？
					</p>
					<p class="font-blod-subtitle">
						添加主营业务，方便千里马快速了解公司。平台也将根据你所填写的业务，更好的为你推荐符合要求的千里马。
					</p>
				</view>
			</view>

			<!-- 已选业务区域 -->
			<view class="selected-business">
				<view class="title">
					<text>已选业务</text>
					<view class="add-btn" @click="showAddModal">添加主营业务</view>
				</view>
				<view class="business-tags">
					<view class="tag" v-for="(tag, index) in selectedTags" :key="index">
						<text class="tag-text">
							{{tag}}
						</text>
						<text class="close-icon" @click="removeTag(index)">×</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 保存按钮 -->
		<view class="footview">
			<view class="footer-card">
				<button class="save" @click="saveBusiness">保存</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selectedTags: [
					'人力资源服务',
					'技术服务',
					'技术推广',
					'技术开发',
					'技术咨询',
					'技术交流',
					'技术转让'
				],
				// showAddModal: false
			};
		},
		methods: {
			removeTag(index) {
				this.selectedTags.splice(index, 1);
			},
			saveBusiness() {
				// 这里可以添加保存业务到后端的逻辑，比如使用uni.request
				console.log('保存的主营业务:', this.selectedTags);
			},
			showAddModal() {
				uni.navigateTo({
					url: '/pagesA/bolecompany/companyviews/searchBusiness'
				})
			}
		}
	};
</script>

<style>
	.container {
		/* padding: 20rpx; */
		padding-bottom: 0px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 100vh;
	}

	.info-card {
		width: 686rpx;
		/* padding: 32rpx 0rpx; */
		display: flex;
		height: 166rpx;
		flex-direction: column;
		justify-content: center;
		margin: 20px auto;
		position: relative;
		background-image: linear-gradient(to bottom, rgba(242, 248, 255, 1), rgba(255, 255, 255, 1));
		border-radius: 16rpx;
		overflow: hidden;
	}

	.info-card::before {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 1px;
		left: 1px;
		content: '';
		border: 2rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	}

	.info-card::after {
		position: absolute;
		width: 100%;
		height: 100%;
		bottom: 1px;
		right: 1px;
		/* margin: 0px auto; */
		content: '';
		border: 2rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	}


	.font-bold {
		padding: 10rpx 0;
		font-size: 24rpx;
		line-height: 28.12rpx;
		margin-left: 40rpx;
	}

	.font-blod-subtitle {
		color: rgba(141, 154, 170, 1);
		padding: 10rpx 0;
		font-size: 20rpx;
		line-height: 23.44rpx;
		/* width: 606rpx; */
		margin-left: 40rpx;
	}

	.title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
		/* width: 686rpx; */
	}

	.add-btn {
		background-color: transparent;
		color: #1890ff;
		padding: 0;
		font-size: 28rpx;
		border: none;
	}

	.business-tags {
		display: flex;
		flex-wrap: wrap;

	}

	.tag {
		background-color: #f0f0f0;
		/* padding: 8rpx 15rpx; */
		border-radius: 8rpx;
		margin-right: 24rpx;
		margin-bottom: 24rpx;
		font-size: 20rpx;
		position: relative;
		/* width: 158rpx;
		height: 52rpx; */
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 12rpx 24rpx;
	}

	.tag-text {
		font-size: 24rpx;
		color: rgba(119, 119, 119, 1);
	}

	.close-icon {
		margin-left: 6rpx;
		cursor: pointer;
		width: 24rpx;
		height: 24rpx;
		font-size: 20rpx;
		color: rgba(119, 119, 119, 1);
	}

	.save-btn {
		background-color: #1890ff;
		color: white;
		padding: 15rpx;
		border-radius: 10rpx;
		width: 100%;
		font-size: 32rpx;
	}

	.footview {
		display: flex;
		justify-content: center;
	}

	.footer-card {
		/* width: 90vw; */
		/* margin-left: -9px; */
		background-color: white;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 1.1);
		/* 卡片阴影 */
		width: 100vw;
		padding: 20px;
		/* height: 196rpx; */
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.save {
		width: 285px;
		height: 40px;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 8pt 8pt 8pt 8pt;
		font-size: 14px;
	}
</style>