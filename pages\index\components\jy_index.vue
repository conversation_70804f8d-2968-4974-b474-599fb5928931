<template>
	<view class="warp">
		<view class="btm">
			
		</view>
	</view>
</template>

<script>
	
	export default {
		components: {
			// TradingHall,
			// Tlent,
			// Enterprises,
			// JobFair,
			// Position,
			// StudentZone
		},
		data() {
			return {
				tab: [{
					title: '交易大厅',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/QJl8nukldIuMqsJVDm9DlmAhih2CwnyqUNHggum9.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/QJl8nukldIuMqsJVDm9DlmAhih2CwnyqUNHggum9.png",
					ref: 'hallRef',
					id: 1
				}, {
					title: '搜·人才',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/rwlMzJc8QM31KGlF1KvOeaiRAAU6luG0hY30LlEl.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/rZXtWMP7sQgq5dUexhbsV0ucEIHeCsgTNHqA9gYh.png",
					ref: 'tlentRef',
					id: 2
				}, {
					title: '看·企业',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/9SxKr5uLbm6lfw7sSItIuogBZehVfnW7h7pCDwEX.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/9SxKr5uLbm6lfw7sSItIuogBZehVfnW7h7pCDwEX.png",
					ref: 'enterprisesRef',
					id: 3
				}, {
					title: '寻·招聘会',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/9SxKr5uLbm6lfw7sSItIuogBZehVfnW7h7pCDwEX.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/9SxKr5uLbm6lfw7sSItIuogBZehVfnW7h7pCDwEX.png",
					ref: 'jobFairRef',
					id: 4
				}, {
					title: '搜·岗位',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/KbbPzSpnlHT3YkowTxAYAlZyyZSl7hoL12db7QTR.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/KbbPzSpnlHT3YkowTxAYAlZyyZSl7hoL12db7QTR.png",
					ref: 'positionRef',
					id: 5
				}, {
					title: '大学生专区',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/IH1WLbUhoKibyMhachW4pdqE5Cr3oSySgKUS80rI.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/IH1WLbUhoKibyMhachW4pdqE5Cr3oSySgKUS80rI.png",
					ref: 'studentZoneRef',
					id: 6
				}, ],
				selTab: 1
			}
		},
		methods: {
			changeTab(item) {
				this.selTab = item.id
			},
			onScrolltolower() {
				this.$refs[find.ref].onScrollGetList();
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		padding-block-end: calc(108rpx + constant(safe-area-inset-bottom));
		padding-block-end: calc(108rpx + env(safe-area-inset-bottom));

		

		.btm {
			height: 100%;
			background: #F5F5F7;
			padding: 24rpx 0;
			border-radius: 16rpx 16rpx 0rpx 0rpx;
			margin-top: -24rpx;



			.scroll-y-view {
				margin-top: 48rpx;
				padding: 0 32rpx 0 32rpx;
			}
		}
	}
</style>