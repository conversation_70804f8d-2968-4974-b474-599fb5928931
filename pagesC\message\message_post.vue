<template>
	<view class="container" @touchstart="onTouchStart">
		<view class="sticky-container">
			<u-navbar bgColor="transparent" leftIconColor="#FFFFFF" placeholder :autoBack="true">
				<template #center>
					<view class="navbar-container">
						<text class="title">张瑞显</text>
						<view class="sub-title__box">
							<text class="sub-title">UI设计</text>
							<image class="sub-icon" src="https://api-test.zhaopinbei.com/storage/uploads/images/qiZL6Q9oM9YhDu7UKzF9xB3vI8JKOotyiKnXN4s7.png"></image>
						</view>
					</view>
				</template>
			</u-navbar>
			<view class="total-container">
				<view class="total-item" @click="postMenuShow = true" id="post-menu">
					<image class="icon" src="https://api-test.zhaopinbei.com/storage/uploads/images/UHGFFNDUwdk5q7U4GyyJpJBHSYiDpLGGxQTnbyFm.png"></image>
					<text class="text">岗位申请</text>
					<view v-show="postMenuShow" class="total-menu__container">
						<view class="menu-item">
							<text class="item-text__1">前端</text>
							<text class="item-text__2">查看详情</text>
							<text class="item-text__3" @click="postResumePopup = true">申请(并发送简历)</text>
						</view>
					</view>
				</view>
				<view class="total-item">
					<image class="icon" src="https://api-test.zhaopinbei.com/storage/uploads/images/QVwHXQF8YyuBxXL5sDXGFnSniEdzjUJrS1UFXYmJ.png"></image>
					<text class="text">交换微信</text>
				</view>
				<view class="total-item">
					<image class="icon" src="https://api-test.zhaopinbei.com/storage/uploads/images/ndS1D3vnYwFplwLKuDrnAFsXPgNh43mfrAIYu3mp.png"></image>
					<text class="text">交换联系方式</text>
				</view>
				<view class="total-item">
					<image class="icon" src="https://api-test.zhaopinbei.com/storage/uploads/images/AW5Vvvkhmpuk4RCZ85Zq2JUO0rbmSi0Hg3wAxli5.png"></image>
					<text class="text">查看公司</text>
				</view>
			</view>
		</view>

		<view class="message-container">
			<scroll-view class="message-content__scroll" :scroll-with-animation="true" id="scroll-view" :scroll-y="true"
				:scroll-top="scrollTop">
				<view class="message-content">
					<view class="message-item" v-for="_ in 5">
						<view class="avater">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"></image>
						</view>
						<view class="content-box">
							<view class="empty"></view>
							<view class="content party">
								工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。
							</view>
						</view>
					</view>
					<view class="message-item message-item__own">
						<view class="content-box">
							<view class="empty"></view>
							<view class="content own">
								工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。工作地点我可以接受。
							</view>
						</view>
						<view class="avater">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"></image>
						</view>
					</view>
				</view>
			</scroll-view>
			<view :class="{'message-total': true, 'message-total__ios': osName === 'ios' && !!keyboardHeight}">
				<view class="input-box" :style="{ marginBlockEnd: `${keyboardHeight}px` }">
					<input class="input" type="text" :adjustPosition="false" />
					<image id="total-open" class="icon" src="/static/new/Group <EMAIL>" @click="onTotalBoxOpen">
					</image>
				</view>
				<view
					:class="{ 'total-box': true, 'total-box__open': totalBoxShow, 'total-box__close': !totalBoxShow }">
					<view class="total-content" v-if="totalType === 'all'">
						<view class="total-item">
							<view class="image-box">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/5YtY23REVnHjSnyNctqoH99wCipQ5gNYAwLybr4l.png"></image>
							</view>
							<text class="text">表情</text>
						</view>
						<view class="total-item" @click="onTotalImage">
							<view class="image-box">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/yYvHpaaiNVuHe0bUtjfKizNpvIr166kozMidv2qo.png"></image>
							</view>
							<text class="text">图片</text>
						</view>
						<view class="total-item">
							<view class="image-box">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/3No9XajkeQ3XKOkLrsB3x7Rqnf0QdtUmHV685ENm.png"></image>
							</view>
							<text class="text">视频</text>
						</view>
						<view class="total-item" @click="onTotalExpress">
							<view class="image-box">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/RDKzegn59UyQu8227W6UG0UKF6EBk2pdJ3D5YRGh.png"></image>
							</view>
							<text class="text">常用语</text>
						</view>
						<view class="total-item">
							<view class="image-box">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/ZK5ERuikgLSe16RA83HHFBwu87xGhlqC3aIuSRTO.png"></image>
							</view>
							<text class="text">收藏夹</text>
						</view>
					</view>

					<view class="express-content" v-if="totalType === '1'">
						<view class="title-box">常用语</view>
						<view class="express-list">
							<view class="item" v-for="v in commonlyList" :key="v.id">
								<view class="text">{{v.content}}</view>
								<view class="btn">发送</view>
							</view>

							<view class="express-total">
								<view class="total-item" @click="onExpressAdd">
									<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/agM1RzuFwuXxBsGfPtsSDlOTRvMIPCHsGM1ijfiA.png"></image>
									<text>添加</text>
								</view>
								<u-line direction="col" length="60rpx"></u-line>
								<view class="total-item" @click="onExpress">
									<image class="image" src="/static/new/管理***********"></image>
									<text>管理</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view>
			<u-popup :show="postResumePopup" :round="12" mode="bottom" closeable :safeAreaInsetBottom="false"
				@close="onPostResumePopupClose">
				<view class="popup-container__resume">
					<text class="title">请选择要发送的文件</text>
					<view class="resume-container">
						<view class="item" v-for="_ in 5">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/jQa24yfWNNm0bSPtKvOuhcdkfDCqnJVh2lnYg4yv.png"></image>
							<view class="info">
								<text class="info_text__1">简历名称.pdf</text>
								<text class="info_text__2">更新于2025.03.14 14:06</text>
							</view>
							<view class="action">预览</view>
						</view>
					</view>
					<view class="btn">投递</view>
				</view>
			</u-popup>

			<u-popup :show="postDetailPopup" :round="12" mode="center" closeable :safeAreaInsetBottom="false"
				@close="onPostDetailPopupClose">
				<view class="popup-container__detail">
					<text class="title">岗位详情</text>

					<view class="detail-post">
						<text class="detail-post__1">游戏UI设计师｜双休｜高薪工作</text>
						<text class="detail-post__2">12-20K</text>
					</view>
					<view class="detail-salary">
						<text>5000-20000｜天使轮｜高新科技企业｜全职</text>
					</view>

					<view class="detail-local">
						<view class="item">
							<image class="image" src="/static/new/定位***********" mode=""></image>
							<text>郑州</text>
						</view>
						<view class="item">
							<image class="image" src="/static/new/经验***********" mode=""></image>
							<text>1-3年</text>
						</view>
						<view class="item">
							<image class="image" src="/static/new/学历***********" mode=""></image>
							<text>本科及以上</text>
						</view>
					</view>

					<view class="detail-tags">
						<view class="item">世界500强</view>
						<view class="item">上市公司</view>
						<view class="item">游戏大厂</view>
					</view>

					<text class="title">职位详情</text>

					<view class="detail-text">
						<view class="item">1.负责游戏各功能界面的设计，包括风格、布局、色彩、字体等搭配；</view>
						<view class="item">2. 游戏ICON、LOGO、图标、字体等元素的设计及制作；</view>
						<view class="item">3. 配合策划程序，持续跟进设计和实现效果，质量提升工作品质；</view>
						<view class="item">4. 参与游戏界面优化，提出对游戏界面规划，把握游戏最终界面实现效果；</view>
					</view>

					<text class="title">任职要求</text>

					<view class="detail-text">
						<view class="item">1、美术或设计专业，本科及以上学历；</view>
						<view class="item">2、3年以上界面设计相关工作经验，有王者项目或者类似风格项目经验者优先；</view>
						<view class="item">3、对用户界面所涉及到的人机交互、操作逻辑、美观的整体设计有深刻的理解；</view>
						<view class="item">4、对平面构成、色彩构成有成熟的认知，具备优秀的审美能力，及较强的设计能力；</view>
						<view class="item">5、热爱游戏，做事认真，有良好的沟通协调能力和团队合作精神。</view>
					</view>
				</view>
			</u-popup>
		</view>

		<view class="fixed" v-if="isFixed" @click="onFixedClick"></view>
	</view>
</template>

<script>
	import {
		getInterviewHelloWordList
	} from '@/config';

	export default {
		data() {
			return {
				osName: 'ios',
				scrollTop: 9999999,
				keyboardHeight: 0,
				totalBoxShow: false,
				postMenuShow: false,
				postResumePopup: false,
				postDetailPopup: false,
				isFixed: false,
				totalType: 'all',

				commonlyList: [],
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onShow() {
			const systemInfo = uni.getSystemInfoSync();
			this.osName = systemInfo.osName;

			uni.onKeyboardHeightChange(res => {
				this.keyboardHeight = res.height > 0 ? res.height : 0;
				this.onTotalBoxClose();
			});
			this.onGetInterviewCommonlyList();
		},
		onHide() {
			uni.offKeyboardHeightChange();
		},
		methods: {
			async onGetInterviewCommonlyList() {
				const res = await getInterviewHelloWordList({
					type: '1',
					...this.userTypeParams
				});
				if (res.status_code !== '200') return;
				this.commonlyList = res.data;
			},
			onFixedClick() {
				this.onTotalBoxClose();
			},
			onTotalBoxOpen() {
				this.totalBoxShow = true;
				this.totalType = 'all';
				this.isFixed = true;
				this.onScrollBottom();
			},
			onTotalBoxClose() {
				this.totalBoxShow = false;
				this.isFixed = false;
				this.onScrollBottom();
			},
			onTotalExpress() {
				this.totalType = '1';
			},
			onTotalImage() {
				uni.chooseImage();
			},
			onTouchStart(e) {
				this.postMenuShow = e.target.id === 'post-menu';
			},
			onPostResumePopupClose() {
				this.postResumePopup = false;
			},
			onPostDetailPopupClose() {
				this.postDetailPopup = false;
			},
			onExpressAdd() {
				uni.$u.route({
					url: '/pagesC/message/express_submit',
				});
			},
			onExpress() {
				uni.$u.route({
					url: '/pagesC/message/express',
				});
			},
			onScrollBottom() {
				setTimeout(() => {
					this.scrollTop = this.scrollTop + 200;
				}, 300);
			},
		},
	};
</script>
<style lang="scss" scoped>
	.popup-container__detail {
		width: calc(100vw - 100rpx);
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		gap: 32rpx;
		background-color: #ffffff;
		border-radius: 24rpx;
		font-size: 24rpx;
		overflow-y: auto;
		max-height: 700rpx;

		.title {
			color: #333333;
			font-size: 28rpx;
		}

		.detail-post {
			display: flex;
			align-items: center;
			gap: 40rpx;

			.detail-post__1 {
				color: #333333;
			}

			.detail-post__2 {
				color: #f98a14;
			}
		}

		.detail-salary {
			display: flex;
			align-items: center;
			color: #333333;
		}

		.detail-local {
			display: flex;
			align-items: center;
			color: #666666;
			gap: 24rpx;

			.item {
				display: flex;
				align-items: center;
				gap: 8rpx;

				.image {
					width: 32rpx;
					height: 32rpx;
				}
			}
		}

		.detail-tags {
			display: flex;
			align-items: center;
			gap: 24rpx;
			font-size: 22rpx;
			color: #333333;

			.item {
				padding: 12rpx;
				background-color: #f5f5f5;
				border-radius: 8rpx;
			}
		}

		.detail-text {
			font-size: 20rpx;
			color: #333333;
		}
	}

	.popup-container__resume {
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		gap: 32rpx;
		background-color: #f5f5f7;
		padding-block-end: calc(32rpx + env(safe-area-inset-bottom));
		border-radius: 24rpx;

		.title {
			color: #333333;
			font-size: 32rpx;
		}

		.btn {
			width: 440rpx;
			height: 92rpx;
			border-radius: 999rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #ffffff;
			font-size: 32rpx;
			background-color: #4f8cf0;
			margin-inline: auto;
		}

		.resume-container {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			max-height: 400rpx;
			overflow-y: auto;

			.item {
				display: flex;
				align-items: center;
				gap: 32rpx;
				background-color: #ffffff;
				border-radius: 24rpx;
				padding: 24rpx;
				border: 1rpx #ffffff solid;

				.image {
					width: 72rpx;
					height: 72rpx;
				}

				.info {
					display: flex;
					flex-direction: column;
					gap: 8rpx;

					.info_text__1 {
						font-size: 28rpx;
						color: #333333;
					}

					.info_text__2 {
						font-size: 24rpx;
						color: #999999;
					}
				}

				.action {
					color: #333333;
					font-size: 28rpx;
					margin-inline-start: auto;
				}
			}
		}
	}

	.container {
		height: 100vh;
		background-color: #f5f5f7;
		display: flex;
		flex-direction: column;

		.fixed {
			position: fixed;
			width: 100vw;
			height: 100vh;
			z-index: 1;
		}

		.message-container {
			flex: 1;
			display: flex;
			flex-direction: column;
			overflow: hidden;

			.message-content__scroll {
				overflow-y: auto;
				flex: 1;

				.message-content {
					flex: 1;
					padding-block: 24rpx;
					padding-inline: 32rpx;
					display: flex;
					flex-direction: column;
					gap: 32rpx;
					box-sizing: border-box;

					.message-item {
						display: flex;
						gap: 24rpx;

						.avater {
							.image {
								width: 72rpx;
								height: 72rpx;
								border-radius: 999rpx;
							}
						}

						.content-box {
							display: flex;
							flex-direction: column;

							.empty {
								height: 28rpx;
							}

							.content {
								max-width: 440rpx;
								padding-inline: 24rpx;
								padding-block: 16rpx;
								font-size: 28rpx;
								color: #333333;
								border-radius: 0rpx 24rpx 24rpx 24rpx;
								background-color: #ffffff;
							}

							.own {
								border-radius: 24rpx 0rpx 24rpx 24rpx;
								background-color: rgba(160, 221, 255, 0.5);
							}
						}
					}

					.message-item__own {
						justify-content: flex-end;
					}
				}
			}


			.message-total {
				background-color: #ffffff;
				padding-block-start: 24rpx;
				border-start-start-radius: 32rpx;
				border-start-end-radius: 32rpx;
				padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
				box-sizing: border-box;
				position: relative;
				z-index: 999;

				.input-box {
					padding-inline: 32rpx;
					display: flex;
					align-items: center;
					gap: 24rpx;

					.input {
						flex: 1;
						height: 64rpx;
						background-color: #f5f5f7;
						border-radius: 999rpx;
						padding-inline: 24rpx;
						padding-block-start: 4rpx;
						box-sizing: border-box;
					}

					.icon {
						width: 56rpx;
						height: 56rpx;
					}
				}

				.total-box {
					overflow: hidden;
					box-sizing: border-box;

					.express-content {
						height: 460rpx;
						padding-block-start: 32rpx;
						color: #333333;
						box-sizing: border-box;
						display: flex;
						flex-direction: column;

						.title-box {
							font-size: 28rpx;
							padding-block: 24rpx;
							padding-inline: 32rpx;
						}

						.express-list {
							flex: 1;
							overflow-y: auto;
							position: relative;

							.item {
								padding-inline: 32rpx;
								padding-block: 16rpx;
								display: flex;
								align-items: center;
								gap: 8rpx;
								font-size: 24rpx;
								border-block-start: 1rpx #e6e6e6 solid;

								.text {
									flex: 1;
									overflow: hidden;
									display: -webkit-box;
									-webkit-box-orient: vertical;
									-webkit-line-clamp: 2;
									color: #333333;
								}

								.btn {
									width: 96rpx;
									height: 48rpx;
									border-radius: 999rpx;
									border: 1rpx #4f8cf0 solid;
									color: #4f8cf0;
									background-color: #ffffff;
									display: flex;
									align-items: center;
									justify-content: center;
								}
							}

							.express-total {
								position: sticky;
								bottom: 0;
								left: 0;
								width: 100%;
								height: 100rpx;
								display: flex;
								align-items: center;
								justify-content: space-around;
								color: #333333;
								font-size: 28rpx;
								background-color: #ffffff;
								box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
								padding-inline: 32rpx;
								box-sizing: border-box;

								.total-item {
									display: flex;
									align-items: center;
									gap: 12rpx;

									.image {
										width: 40rpx;
										height: 40rpx;
									}
								}
							}
						}
					}

					.total-content {
						padding: 32rpx;
						box-sizing: border-box;
						display: grid;
						grid-template-columns: repeat(4, 1fr);
						gap: 24rpx;

						.total-item {
							display: flex;
							flex-direction: column;
							align-items: center;
							gap: 24rpx;

							.image-box {
								width: 116rpx;
								height: 116rpx;
								background-color: #f5f5f7;
								border-radius: 24rpx;
								display: flex;
								align-items: center;
								justify-content: center;

								.image {
									width: 68rpx;
									height: 68rpx;
								}
							}

							.text {
								color: #666666;
								font-size: 28rpx;
							}
						}
					}
				}

				.total-box__close {
					height: 0rpx;
				}

				.total-box__open {
					height: 460rpx;
				}
			}

			.message-total__ios {
				padding-block-end: 24rpx;
			}
		}

		.sticky-container {
			background-image: linear-gradient(to right, #36a5de, #4787f0);
			border-end-end-radius: 32rpx;
			border-end-start-radius: 32rpx;
			box-sizing: border-box;
			padding-inline: 32rpx;
			padding-block: 32rpx;

			.navbar-container {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 4rpx;

				.title {
					font-size: 32rpx;
					color: #ffffff;
				}

				.sub-title__box {
					display: flex;
					align-items: center;
					gap: 6rpx;

					.sub-title {
						font-size: 20rpx;
						color: #ffffff;
					}

					.sub-icon {
						width: 60rpx;
						height: 30rpx;
					}
				}
			}

			.total-container {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.total-item {
					display: flex;
					flex-direction: column;
					align-items: center;
					gap: 8rpx;
					position: relative;

					.icon {
						width: 40rpx;
						height: 40rpx;
					}

					.text {
						font-size: 20rpx;
						color: #ffffff;
					}

					.total-menu__container {
						position: absolute;
						top: 124rpx;
						left: 0;
						z-index: 1;
						width: 550rpx;
						max-height: 200rpx;
						background-color: #ffffff;
						border-radius: 24rpx;
						box-sizing: border-box;
						padding: 32rpx;

						.menu-item {
							color: #666666;
							font-size: 28rpx;
							display: grid;
							align-items: center;
							grid-template-columns: 1fr 1fr 2fr;
							gap: 24rpx;

							.item-text__3 {
								color: #4f8cf0;
							}
						}
					}
				}
			}
		}
	}
</style>
