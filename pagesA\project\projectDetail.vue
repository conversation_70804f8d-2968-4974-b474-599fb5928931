<template>
    <view id="app">
        <view class="projectNameBox">
            <view class="projectName_til">{{ detail.name }}</view>
            <view class="hrBox"></view>
            <view class="projectNameBox_bottom">
                <view class="projectNameBox_bottom_left">
                    <image :src="detail.user_member_info.image.path_url" mode="" class="projectPeopleHead"></image>
                    <view class="projectName">{{ detail.user_member_certification.name }}</view>
                </view>
                <view class="projectName">{{ detail.type_name }}</view>
            </view>
        </view>

        <view class="projectNameBox">
            <view class="projectName_til">关联职位</view>
            <view class="jobName" @click="selectJob(jobItem)" v-for="(jobItem,index) in jobList" :key="index"
                  :item="jobItem">
                <view>{{ jobItem.title }}</view>
                <image :src="jobItem.logo.path_url" mode="" class="rightIcon"></image>
            </view>
        </view>

        <view class="projectProgress">
            <view class="projectName_til">项目进度</view>
            <view class="projectName_til">{{ detail.push_member_count }}/{{ detail.count }}</view>
        </view>

        <view class="projectNameBox">
            <view class="projectName_til">项目详情</view>
            <view class="projectDetail">
                {{ detail.description }}
            </view>
        </view>

        <view class="projectName_til">
            参与人
        </view>

        <view class="peopleBox">
            <!-- 每一位参与人 -->
            <view class="peopleChild" v-for="(item,index) in peopleList" :key="index" @click="goPeopleDetail(item)">
                <view class="peopleChild_top">
                    <image :src="item.user_member_info.image.path_url" mode="" class="peopleChild_top_head"></image>
                    <view class="peopleChild_top_text">
                        <view class="peopleChild_top_text_title">{{item.user_member_certification.name}}</view>
                        <view class="peopleChild_top_text_num">已完成数：{{ item.push_member_count }}</view>
                    </view>
                </view>
                <view class="progressDetail">进度详情</view>
            </view>

        </view>
        
        <view style="height: 196rpx;"></view>
        <view class="bottomBox" v-if="detail.active_status == 1">
            <view class="bottomBox_btn" @click="getErCode">生成二维码</view>
        </view>
        
        <u-overlay :show="show" @click="show=false">
            <view class="erCodeCla">
                <image :src="erImg" mode="" class="erCodeImg"></image>
                <view class="upDateBtn" @click.stop="downLoad()">点击下载二维码</view>
            </view>
        </u-overlay>

    </view>
</template>

<script>
    import {
        getErCode
    } from '../../config/common_api.js'
    import {projectShow, projectUsers, projectJobs} from "../../config/headhunterList_api";

    export default {
        data() {
            return {
                peopleList: [],
                detail: {},
                jobList: [],
                show:false,
                erImg:''
            }
        },
        onLoad(options) {
            var _id = options.id;
            var _this = this;
            if (_id > 0) {
                projectShow({
                    id: _id,
                }).then(response => {
                    _this.detail = response.data;
                });

                projectUsers({
                    id: _id,
                }).then(response => {
                    _this.peopleList = response.data;
                })
                projectJobs({
                    id: _id,
                    limit: 1,
                    page: 1,
                }).then(response => {
                    _this.jobList = response.data.data;
                })
            }
        },
        methods: {
            async getErCode() {
                // 获取屏幕信息
                const {
                    screenWidth
                } = uni.getSystemInfoSync();
                const id = this.jobList[0].id
                const model_user_id = uni.getStorageSync('userInfo').login_user.id
                const model_id = this.detail.id;
                const model_type = 'project'
                // 转换公式
                const px = Math.round(140 / 750 * screenWidth);
                let params = {
                    path: `pagesA/details/memberJobDetails`,
                    size: px,
                    scene: `${model_type},${model_id},${model_user_id},${id}`
                }
            
                const res = await getErCode(params)
                if (res.status_code == '200') {
                    this.erImg = res.data.code
                    this.show = true
                } else {
                    uni.showToast({
                        title: '获取失败',
                        icon: 'none'
                    })
                }
            
            },
            downLoad() {
                uni.downloadFile({
                    url: this.erImg, //仅为示例，并非真实的资源
                    success: (res) => {
                        if (res.statusCode === 200) {
                            // 需要将图片保存到相册
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath, // 图片文件路径，可以是临时文件路径也可以是永久文件路径，不支持网络图片路径
                                success(res) {
                                    uni.showToast({
                                        title: '保存成功',
                                        icon: 'none'
                                    })
                                    // 如果保存成功需要打开查看，请使用以下方法，同时也支持打开文件
                                    // uni.openDocument({
                                    //     filePath: res.savedFilePath,
                                    //     success(res) {},
                                    //     fail(err) {}
                                    // })
                                },
                                fail(err) {
                                    uni.showToast({
                                        title: '图片保存失败',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    }
                });
            
            },
            selectJob(item) {
                uni.navigateTo({
                    url: '/pagesA/details/memberJobDetails?id=' + item.id
                })
            },
            goPeopleDetail(item) {
                var _url = '/pagesA/project/progressDeatils?project_id=' + this.detail.id + '&user_id=' + item.user.id;

                uni.navigateTo({
                    url: _url,
                })
            }
        }
    }
</script>

<style scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
        min-height: 100vh;
        height: auto;
        background: #F5F5F7;
    }

    .projectNameBox {
        width: 100%;
        /* height: 198rpx; */
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 24rpx 32rpx;
        margin-bottom: 24rpx;
    }

    .projectName_til {
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
    }

    .hrBox {
        width: 100%;
        height: 2rpx;
        background: #F5F5F7;
        margin: 24rpx 0;
    }

    .projectPeopleHead {
        width: 48rpx;
        height: 48rpx;
        background: #D9D9D9;
        border-radius: 24rpx;
        margin-right: 16rpx;
    }

    .projectNameBox_bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .projectNameBox_bottom_left {
        display: flex;
        align-items: center;
    }

    .projectName {
        font-weight: bold;
        font-size: 24rpx;
        color: #333333;
    }

    .jobName {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        margin-top: 24rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .rightIcon {
        width: 32rpx;
        height: 32rpx;
    }

    .projectProgress {
        width: 100%;
        padding: 24rpx 32rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24rpx;
    }

    .projectDetail {
        width: 100%;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        margin-top: 24rpx;
        line-height: 38rpx;
    }

    .peopleBox {
        width: 100%;
        margin-top: 24rpx;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        /* 每行2个项目 */
        gap: 24rpx 22rpx;
        /* 上下间距24rpx，左右间距22rpx */
    }

    .peopleChild {
        width: 332rpx;
        height: 218rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .peopleChild_top {
        display: flex;
        /* justify-content: space-between; */
        align-items: center;
    }

    .peopleChild_top_head {
        width: 82rpx;
        height: 82rpx;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        margin-right: 16rpx;
    }

    .peopleChild_top_text {
        height: 82rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .peopleChild_top_text_title {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
    }

    .peopleChild_top_text_num {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .progressDetail {
        width: 100%;
        height: 56rpx;
        background: #4F8CF0;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 56rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
    }

    .bottomBox {
        width: 100%;
        height: 196rpx;
        padding: 24rpx 32rpx;
        background: #FFFFFF;
        box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
        position: fixed;
        bottom: 0;
        left: 0;
    }

    .bottomBox_btn {
        width: 100%;
        height: 80rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 80rpx;
        color: #FFFFFF;
    }

    .erCodeCla {
        width: 612rpx;
        height: 758rpx;
        // background: red;
        background-image: url('https://api-test.zhaopinbei.com/images/erCodeBg.png');
        background-size: cover;
        /* 背景图片覆盖整个元素 */
        background-position: center;
        /* 背景图片居中 */
        background-repeat: no-repeat;
        /* 不重复背景图片 */
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
    }
    
    .erCodeImg {
        width: 280rpx;
        height: 280rpx;
        position: absolute;
        top: 218rpx;
        left: 166rpx;
    }
    
    .upDateBtn {
        position: absolute;
        bottom: 64rpx;
        left: 96rpx;
        width: 420rpx;
        height: 64rpx;
        background: #4F8CF0;
        border-radius: 82rpx 82rpx 82rpx 82rpx;
        text-align: center;
        line-height: 64rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #FFFFFF;
    }
</style>