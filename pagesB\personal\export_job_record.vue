<template>
	<!-- 求职记录导出 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="title">求职记录导出</view>
				<view class="sub-title">历史的求职记录数据（包含我看过。沟通过、交换过、面试）将整理成文件发送到你的邮箱。</view>
				<u-form labelPosition="top" labelWidth="300" :labelStyle="{color: '#666666', fontSize: '24rpx'}">
					<u-form-item label="邮箱地址" borderBottom>
						<u-input border="none" placeholder="请输入邮箱地址"></u-input>
					</u-form-item>
					<u-form-item label="验证码" borderBottom>
						<template #right>
							<text class="code-text">发送验证码</text>
						</template>
						<u-input border="none" placeholder="请输入验证码"></u-input>
					</u-form-item>
				</u-form>
				<view class="btn" @click="onRoute('export_user_info_results')">提交</view>
				<view class="desc">注：导出的数据将会以excel文件发送至你的邮箱，导出的数据其中我看过、沟通过是近来=1年、最多200条；交换过是近90天、最多200条；面试是全部的数量</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			onRoute(url) {
				uni.$u.route({
					url: `/pagesB/personal/${url}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #FFFFFF;
		display: flex;
		flex-direction: column;

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
				padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

				.code-text {
					color: #4F8CF0;
					font-size: 20rpx;
				}

				.title {
					color: #333333;
					font-size: 32rpx;
				}

				.sub-title {
					color: #777777;
					font-size: 24rpx;
				}

				.btn {
					padding-block: 20rpx;
					border-radius: 12rpx;
					background-color: #4F8CF0;
					color: #FFFFFF;
					font-size: 28rpx;
					text-align: center;
				}

				.desc {
					color: #999999;
					font-size: 20rpx;
				}
			}
		}
	}
</style>