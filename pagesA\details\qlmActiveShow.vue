<template>
	<view>
		<view class="flex-tabs">
			<view @click="isShow('detailsShow')" :class="{ 'active': currentTab === 'detailsShow' }">特准招聘</view>
			<view @click="isShow('qlmActiveShow')" :class="{ 'active': currentTab === 'qlmActiveShow' }">招聘会</view>
			<view @click="isShow('takePartShow')" :class="{ 'active': currentTab === 'takePartShow' }">宣讲会</view>
		</view>
		<view>
			<view class="flex-show">
				<view @click="isswitch('work')" :class="{ 'view-selected': selected == 'work' }">
					职位展示
				</view>
				<view @click="isswitch('event')" :class="{ 'view-selected': selected == 'event' }">
					活动介绍
				</view>
			</view>
			<view class="box">
				<Event v-if="eventhide" />
				<Work v-else-if="workhide" />
			</view>
		</view>
	</view>
</template>

<script>
	import Event from './QlmjobFair/Event.vue'
	import Work from './QlmjobFair/work.vue'
	export default {
		components: {
			Event,
			Work
		},
		data() {
			return {
				qlmActiveShow: false,
				detailsShow: false,
				takePartShow: false,
				currentTab: '',
				// aaa: '',
				selected: '',
				eventhide: false,
				workhide: false,

			}
		},
		// 	
		created() {
			this.selected = 'work'
			this.workhide = true
			this.currentTab = 'detailsShow'
		},
		methods: {
			isShow(name) {
				this.qlmActiveDetails = false;
				this.detailsShow = false;
				this.takePartShow = false;

				if (name === 'qlmActiveShow') {
					this.currentTab = name
					// this.qlmActiveDetails = true;
					uni.redirectTo({
						// url: '/pagesA/details/qlmActiveDetails?id=' + this.aaa
						url: "/pagesA/list/job_fair_list"
					})
					this.qlmActiveDetails = true;
				} else if (name === 'detailsShow') {
					this.currentTab = name
					this.detailsShow = true;
					console.log(this.detailsShow);
				} else if (name === 'takePartShow') {
					uni.redirectTo({
						url: '/pagesA/details/QlmjobFairTalk/TalkList'
					})
				}
			},
			isswitch(name) {
				// 点击时重置所有隐藏状态
				this.eventhide = false
				this.workhide = false
				console.log(name);
				if (name == 'event') {
					this.selected = name;
					this.eventhide = true
					console.log(this.selected);
				} else if (name == 'work') {
					this.selected = name;
					this.workhide = true
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.flex-tabs {
		width: 508rpx;
		height: 44rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-left: 32rpx;
		margin-top: 32rpx;
		margin-bottom: 62rpx;
		font-size: 28rpx;
		font-weight: bold;
	}

	.active {
		color: rgba(79, 140, 240, 1);
		font-size: 32rpx;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.active:before {
		content: '';
		position: absolute;
		width: 32rpx;
		height: 6rpx;
		background-color: rgba(79, 140, 240, 1);
		border-radius: 50rpx;
		bottom: -8rpx;
	}

	.flex-show {
		width: 256rpx;
		height: 56rpx;
		display: flex;
		font-size: 28rpx;
		justify-content: space-between;
		margin-right: 32rpx;
		margin-left: 32rpx;
		margin-top: 40rpx;
	}

	.flex-show view {
		color: rgba(67, 67, 67, 1);
		cursor: pointer;
		/* 鼠标悬停时显示指针样式 */
		font-size: 28rpx;
	}

	.flex-show .view-selected {
		color: rgba(79, 140, 240, 1);
	}

	.box {
		background-color: rgba(245, 245, 247, 1);
	}
</style>