<template>
	<view class="warp">
		<view class="box">
			<view class="">
				头像
			</view>
			<img class="ava" :src="info.ava" alt="" />
		</view>
		<view class="box" v-for="item in list" @click="jump(item)">
			<view class="left">
				<view class="top">
					{{item.title}}
				</view>
				<view class="btm">
					{{item.txt}}
				</view>
			</view>
			<img class="arrow" :src="item.img" alt="" />
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info: {
					name: '张三',
					ava: 'https://api-test.zhaopinbei.com/storage/uploads/images/7Md0Ku73yZXCUOENs3uRGMcZPkuGoUNdMIpFvY2b.png',
					ip: '河南'
				},
				list: [{
						title: '名称',
						txt: '张三',
						img: '/static/images/project/rightIcon.png'
					},
					{
						title: '简介',
						txt: '张三',
						img: '/static/images/project/rightIcon.png'
					},
					{
						title: '账号类型',
						txt: '个人'
					},
				]
			}
		},
		methods: {
			// 跳转
			jump(item) {
				if (item.title == '名称') {
					uni.navigateTo({
						url: './setName'
					})
				} else if (item.title == '简介') {
					uni.navigateTo({
						url: './setInt'
					})
				}
			}
		}
	}
</script>
<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 32rpx 32rpx;

		.box {
			width: 686rpx;
			height: 160rpx;
			border-bottom: 1px solid #eaeaea;
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 28rpx;

			.ava {
				width: 80rpx;
				height: 80rpx;
				border-radius: 40rpx;
			}

			.top {
				margin-top: -20rpx;
			}

			.btm {
				margin-top: 20rpx;
				font-size: 24rpx;
			}

			.arrow {
				width: 48rpx;
				height: 48rpx;
			}
		}
	}
</style>
