<template>
	<view class="container">
		<view class="content">
			<view class="section">
				<text class="label">期望岗位</text>
			</view>
			<!-- 求职类型 -->
			<view class="form-box">

				<view class="form-item">
					<text class="form-label">求职类型</text>
					<picker :range="jobTypes" @change="onJobTypeChange">
						<view class="form-content">
							<u--input placeholder="请选择求职类型" :value="jobType" suffixIcon="arrow-right"
								suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
						</view>
					</picker>
				</view>
				<!-- 期望岗位 -->
				<view class="form-item">
					<text class="form-label">期望岗位</text>
					<view class="form-content" @click="onExpectedPositionChange">
						<u--input placeholder="请选择期望岗位" :value="expectedPosition" suffixIcon="arrow-right"
							suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
					</view>
				</view>
				<!-- 期望岗位详情 -->
				<view class="form-item" @click="routerplan">
					<text class="form-label">期望岗位详情</text>
					<u--input placeholder="期望岗位详情" :value="positionDetails" suffixIcon="arrow-right"
						suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
				</view>
				<!-- 工作城市 -->
				<view class="form-item" @click="onWorkCityChange">
					<text class="form-label">工作城市</text>
					<picker :range="workCities" <view class="form-content">
						<u--input placeholder="请选择工作城市" :value="workCity" suffixIcon="arrow-right"
							suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
				</view>
				</picker>
			</view>
			<!-- 薪资要求 -->
			<view class="form-item">
				<text class="form-label">薪资要求</text>
				<picker :range="salaryRequirements" @change="onSalaryRequirementChange">
					<view class="form-content">
						<u--input placeholder="请选择薪资要求" :value="salaryRequirement" suffixIcon="arrow-right"
							suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
					</view>
				</picker>
			</view>
			<!-- 期望行业 -->
			<view class="form-item">
				<text class="form-label">期望行业</text>
				<picker :range="expectedIndustries" @change="onExpectedIndustryChange">
					<view class="form-content">
						<u--input placeholder="请选择期望行业" :value="expectedIndustry" suffixIcon="arrow-right"
							suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
					</view>
				</picker>
			</view>
		</view>
	</view>
	<view class="footer">
		<button class="confirm-btn" @click="saveForm">保存</button>
	</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				jobTypes: ['全职', '兼职', '实习'],
				expectedPositions: ['前端开发', '后端开发', '测试工程师'],
				positionDetails: '',
				workCities: ['北京', '上海', '广州', '深圳'],
				salaryRequirements: ['5k-10k', '10k-15k', '15k-20k'],
				expectedIndustries: ['互联网', '金融', '教育'],
				jobType: '',
				expectedPosition: '',
				workCity: '',
				salaryRequirement: '',
				expectedIndustry: ''
			};
		},
		methods: {
			onJobTypeChange(e) {
				this.jobType = this.jobTypes[e.detail.value];
			},
			onExpectedPositionChange(e) {
				// this.expectedPosition = this.expectedPositions[e.detail.value];
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/Position/jobList'
				})
			},
			onWorkCityChange(e) {
				// this.workCity = this.workCities[e.detail.value];
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/Position/location'
				})
			},
			onSalaryRequirementChange(e) {
				this.salaryRequirement = this.salaryRequirements[e.detail.value];
			},
			onExpectedIndustryChange(e) {
				this.expectedIndustry = this.expectedIndustries[e.detail.value];
			},
			routerplan() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/plan'
				})
			},
			saveForm() {
				console.log('保存的数据', {
					jobType: this.jobType,
					expectedPosition: this.expectedPosition,
					positionDetails: this.positionDetails,
					workCity: this.workCity,
					salaryRequirement: this.salaryRequirement,
					expectedIndustry: this.expectedIndustry
				});
			}
		}
	};
</script>

<style lang="scss">
	.container {
		padding: 0rpx 20rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		background-color: rgba(245, 245, 247, 1);
	}

	.content {
		flex: 1;
		width: 622rpx;
		padding: 32rpx;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.section {
		align-self: start;
	}

	.form-item {
		margin-bottom: 20rpx;
		width: 622rpx;
		border-bottom: 1rpx solid rgba(230, 230, 230, 1);
		padding-bottom: 32rpx;
	}

	.label {
		display: block;
		margin-bottom: 5rpx;
		height: 114rpx;
	}


	/* .input {
			width: 100%;
			padding: 10rpx;
			border: 1rpx solid #ccc;
			border-radius: 5rpx;
		} */

	::v-deep .u-input {
		border: none;
		padding: 0rpx !important;
		width: 622rpx;
	}

	.picker-view {
		width: 100%;
		/* padding: 10rpx; */
		/* border: 1rpx solid #ccc; */
		color: rgba(204, 204, 204, 1);
		margin-top: 16rpx;
		border-radius: 5rpx;
		font-size: 28rpx;
	}

	.selected-con {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 622rpx;
		height: 40rpx;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}
</style>