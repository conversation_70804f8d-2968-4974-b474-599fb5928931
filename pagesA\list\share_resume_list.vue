<template>
	<view class="home-index">
		<u-sticky bgColor="#F5F5F5">
			<view class="search-wrap">
				<u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
			</view>
		</u-sticky>
		<view class="list">
			<share-resume-item v-for="item in list" :key="item" :item="item"></share-resume-item>
		</view>
	</view>
</template>

<script>
    import {memberShareCompany} from '../../config/headhunterList_api.js'
	import ShareResumeItem from '../components/shareResumeItem.vue'
	export default{
		components:{
			ShareResumeItem
		},
		data(){
			return{
				page: 1,
				limit: 10,
				status: 'loadmore',
				more: false,
				list:[]
			}
		},
        onLoad() {
            this.memberShareCompany()
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.memberShareCompany()
            } else {
                this.status = 'nomore'
            }
        },
        methods:{
           async memberShareCompany() {
               let params = {
                   page:this.page,
                   limit:this.limit
               }
               const res = await memberShareCompany(params)
               this.list = this.list.concat(res.data.data);
               this.more = res.more;
               this.status = this.more ? "loadmore" : "nomore"
           }
        }
	}
</script>
<style>
	page{
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.search-wrap{
		padding: 32rpx;
	}
	.list{
		padding: 0 32rpx 32rpx 32rpx;
	}
</style>