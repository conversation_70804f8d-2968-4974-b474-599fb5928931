<template>
  <view>
    <u-sticky bgColor="#F5F5F5">
      <view class="header">
        <view class="search-wrap">
          <u-search
            placeholder="请输入手机号"
            bgColor="#FFFFFF"
            :showAction="false"
            @search="enterClick"
            v-model="keyword"
          ></u-search>
        </view>
      </view>
    </u-sticky>
    <view style="padding: 0 32rpx; margin-bottom: 24rpx"
      ><u-subsection
        :list="tabList"
        inactiveColor="#999999"
        :current="current"
        @change="changeTab"
      ></u-subsection
    ></view>

    <!--    <block v-if="type == 'send'">-->
    <!--      <send-list  v-for="(item,index) in list" :key="index" :item="item"></send-list>-->
    <!--    </block>-->
    <!--    <block v-if="type == 'received'">-->
    <!--      <received-list v-for="(item,index) in list" :key="index" :item="item"></received-list>-->
    <!--    </block>-->

    <view class="list" v-if="type == 'send'" v-for="(item, index) in list">
       <u-swipe-action>
            <u-swipe-action-item
                    :options="options1">
          <view class="item">
                <image
                        src="https://api-test.zhaopinbei.com/storage/uploads/images/41R1LgOXldsXlyAjelHeNXsb0SuBPKE7ANcgobdG.png"
                        mode=""
                ></image>
                <view class="info">
                  <view class="name">
                    {{
                    item.to.member.certification_status == 2
                    ? item.to.member.name
                    : item.to.member_certification.name
                    }}
                    {{ item.to.member.cellphone }}
                  </view>
                  <view class="desc"> {{ item.note }} {{ item.created_at }} </view>
                </view>
                <view class="actions" v-if="item.status == 1">
                  <view class="desc send"> 已添加 </view>
                </view>
                <view class="actions" v-if="item.status == 3">
                  <view class="desc send"> 对方已拒绝 </view>
                </view>
                <view class="actions" v-if="item.status == 2">
                  <view class="desc cancel"> 等待对方同意 </view>
                </view>
          </view>
       </u-swipe-action-item>
          </u-swipe-action>

    </view>
    <view class="list" v-if="type == 'received'" v-for="(item, index) in list">
        <u-swipe-action>
            <u-swipe-action-item
                    :options="options1">
      <view class="item">
        <image
          src="https://api-test.zhaopinbei.com/storage/uploads/images/41R1LgOXldsXlyAjelHeNXsb0SuBPKE7ANcgobdG.png"
          mode=""
        ></image>
        <view class="info">
          <view class="name">
            {{ item.from.member_certification.name }}
            {{ item.from.member.cellphone }}
          </view>
          <view class="desc">
            {{ item.note }}
            {{ item.created_at }}
          </view>
        </view>
        <view class="actions" v-if="item.status == 2 && type == 'received'">
          <view class="action refuse" @click="checkFriend(index, item, 3)">
            拒绝
          </view>
          <view class="action agree" @click="checkFriend(index, item, 1)">
            同意
          </view>
        </view>
        <view class="actions" v-if="item.status == 1">
          <view class="desc send"> 已添加 </view>
        </view>
        <view class="actions" v-if="item.status == 3">
          <view class="desc send"> 已拒绝 </view>
        </view>
      </view>
            </u-swipe-action-item>
        </u-swipe-action>
    </view>
  </view>
</template>

<script>
import { friendAgree, friendNew } from "../../config/common_api";
import QlmNav from "../../components/qlmNav.vue";

export default {
  components: { QlmNav },
  data() {
    return {
      tabList: ["我添加的", "添加我的"],
      type: "send", //received  接收的邀请  send 发起的邀请
      list: {},
      current: 0,
      keyword:'',
      options1: [{
        text: '删除'
      }]
    };
  },
  onLoad() {
    this.friendNew();
  },
  methods: {
    changeTab(i) {
      this.current = i;
      this.list = [];
      this.type = i === 0 ? "send" : "received";
      this.friendNew();
    },
    async friendNew() {
      let params = {
        type: this.type, //received  接收的邀请  send 发起的邀请
        phone:this.keyword,
      };
      const { status_code, data } = await friendNew(params);
      if (status_code == "200") {
        this.list = data;
      }
    },
    async checkFriend(index, e, status) {
      let params = {
        status: status,
        id: e.id,
      };
      const { status_code, data } = await friendAgree(params);
      if (status_code == "200") {
        e.status = status;
        this.$set(this.list, index, e);
      }
    },
    enterClick(value){
      //checkFriend
     //console.log(this.keyword,'11111111111111111111111')
      this.friendNew();
    },
  },
};
</script>
<style>
page {
  background-color: #f5f5f7;
}
</style>
<style lang="less" scoped>
.header {
  padding: 32rpx;
}
.list {
  display: flex;
  flex-direction: column;

  .item {
    display: flex;
    align-items: center;
    background-color: #ffffff;
    padding: 0 32rpx;
    height: 120rpx;
    border-bottom: 1px solid #f5f5f7;
    &:last-child {
      border-bottom: none;
    }
    image {
      width: 72rpx;
      height: 72rpx;
    }

    .info {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding-left: 16rpx;
      .name {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
      }

      .desc {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
      }
    }

    .actions {
      display: flex;

      .action {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 24rpx;
        height: 56rpx;
        border-radius: 8rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #ffffff;

        &:last-child {
          margin-left: 24rpx;
        }
      }

      .refuse {
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        border: 2rpx solid #cccccc;
        font-size: 24rpx;
        color: #999999;
      }

      .agree {
        background: #4f8cf0;
        color: #ffffff;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
      }

      .desc {
        font-weight: 500;
        font-size: 24rpx;
        color: #333333;
      }

      .send {
        color: #000000;
      }
      .cancel {
        color: #fe4d4f;
      }
    }
  }
}
</style>
