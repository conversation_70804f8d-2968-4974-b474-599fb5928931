page {
  background-color: #f5f5f7;
}

.content {
  padding: 32rpx 32rpx 196rpx 32rpx;
}

.title {
  font-weight: Medium;
  font-size: 32rpx;
  width: 130rpx;
  height: 50rpx;
  background-image: linear-gradient(
      to bottom,
      transparent 50%,
      rgba(0, 0, 0, 0) 50%
    ),
    url("https://api-test.zhaopinbei.com/images/titleBg.png");
  background-size: 100% 50%;
  background-repeat: no-repeat;
  background-position: center bottom;
}

.topCon {
  background-color: rgba(255, 255, 255, 0.7);
  padding: 32rpx;
  opacity: 0.7;
  border-radius: 16rpx;
  .infoCon {
    display: flex;
    image {
      width: 124rpx;
      height: 124rpx;
      border-radius: 50%;
      margin-right: 30rpx;
    }
    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .name {
        font-weight: Bold;
        font-size: 40rpx;
      }
      .comp {
        font-size: 24rpx;
        font-weight: Regular;
      }
    }
  }
  .tags {
    display: flex;
    margin-top: 16rpx;

    .tag {
      display: flex;
      align-items: center;
      background: rgba(255,255,255,0.5);
      border-radius: 8rpx;
      height: 46rpx;
      padding: 0 12rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #7286A6;
      margin-right: 16rpx;
    }
  }
}

.centerCon {
  background-color: rgba(255, 255, 255, 0.6);
  padding: 32rpx;
  border-radius: 16rpx;
  margin: 32rpx 0;
  .tags {
    display: flex;
    .tag {
      display: flex;
      align-items: center;
      background: rgba(255,255,255,0.5);
      border-radius: 8rpx;
      height: 46rpx;
      padding: 0 12rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #7286A6;
      margin-right: 16rpx;
    }
  }
  .auth {
    display: flex;
    align-items: center;
    color: #4787f0;
    padding-bottom: 26rpx;
    border-bottom: 1rpx solid white;
    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 16rpx;
    }
  }
}

.bottomCon {
  background-color: rgba(255, 255, 255, 0.5);
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  .tags {
    display: flex;
    margin-top: 16rpx;

    .tag {
      display: flex;
      align-items: center;
      background: rgba(255,255,255,0.5);
      border-radius: 8rpx;
      height: 46rpx;
      padding: 0 12rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #7286A6;
      margin-right: 16rpx;
    }
  }
  .jobList {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
    .title-right {
      display: flex;
      align-items: center;
      color: #999999;
      text {
        font-weight: 400;
        font-size: 28rpx;
      }
    }
  }
  .comment {
    .infoCom {
      display: flex;
      image {
        width: 104rpx;
        height: 104rpx;
        border-radius: 50%;
        margin-right: 12rpx;
      }
      .custom {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .infoCustom {
          display: flex;
          align-items: center;
          .name {
            margin-right: 10rpx;
            font-size: 28rpx;
          }
          .time {
            color: #4f8cf0;
            font-size: 24rpx;
          }
        }
      }
    }
    .commentCon {
      display: flex;
      justify-content: flex-end;
      .con {
        width: 82%;
        color: #777777;
        font-size: 24rpx;
        display: -webkit-box; /* 将元素设置为弹性盒子 */
        -webkit-box-orient: vertical; /* 设置文本垂直排列 */
        -webkit-line-clamp: 2; /* 限制文本显示两行 */
        overflow: hidden; /* 超出部分隐藏 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
      }
    }
  }
}
