<template>
	<view class="container">
		<view style="height: 194rpx;">
			<view class="title">期望岗位</view>
			<view class="search-box">
				<u--input placeholder="搜索岗位名称" prefixIcon="search" prefixIconStyle="font-size: 22px;color: #909399"
					style=""></u--input>
			</view>
		</view>
		<view class="job-list-container">
			<view class="joblist-left">
				<scroll-view scroll-y="true" class="scroll-Y">
					<view
						style="display: flex; flex-direction: column; justify-content: space-evenly; align-items: center; width: 260rpx;">
						<view v-for="(category, index) in jobList" :key="index" class="job-category"
							@click="toggleChecked(index)" :class="category.checked ? 'active':'job-category' ">
							<text class="category-name"
								:class="category.checked ? 'active-item':'category-name' ">{{category.name}}</text>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="joblist-right">
				<scroll-view scroll-y="true" class="scroll-Y">
					<view style="display: flex;justify-content: space-evenly; align-items: center;">
						<view v-for="(category, index) in jobList" :key="index"
							style="display: flex; flex-wrap: wrap; justify-content: space-between;">
							<!-- <radio-group v-model="category.list">
							</radio-group> -->
							<view class="job-item" v-for="(job, jobIndex) in category.list" :key="jobIndex"
								v-if="category.checked">
								<label class="job-label">
									<!-- <radio :checked="job.checked"
											style="transform: scale(0.7); activeBackgroundColor:rgba(79, 140, 240, 1);">
										</radio> -->
									<text>{{job.name}}</text>
								</label>
								<!-- <view class="context-box" @click="toggle('bottom',job)">
										<text class="job-context">{{job.context}}</text>
										<img src="/static/images/project/rightIcon.png" alt="" />
									</view> -->
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>

		<uni-popup ref="popup" background-color="#fff" @close="handleClose">
			<view class="popup-content" v-for="item in job" :key="item.name">
				<view class="popup-header">
					<view class="header">
						<text class="popup-title">{{item.name}}</text>
						<u-icon name="close" @click="close"></u-icon>
					</view>
				</view>
				<view class="popup-body">
					<text>{{item.context}}</text>
				</view>
			</view>
		</uni-popup>

		<view class="footer">
			<button class="confirm-btn" @click="submitInfo">提交</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				jobList: [{
						name: '行业分类',
						checked: false,
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: false
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: false
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: true
							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: false
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					},
					{
						name: '热门岗位',
						checked: true,
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: true
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: false
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: false
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					},
					{
						name: '互联网/AI',
						checked: false,
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: false
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: false
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: true
							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: false
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					},
					{
						name: '产品',
						checked: false,
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: false
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: false
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: true
							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: false
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					},
					{
						name: '运营/客服',
						checked: false,
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: false
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: false
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: true
							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: false
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					},
					{
						name: '设计',
						checked: false,
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: false
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: false
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: true
							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: false
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					},
					{
						name: '影视/传媒',
						checked: false,
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: false
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: false
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: true
							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: false
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					},
					{
						name: '人力/行政/前台/法务',
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: false
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: false
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: true
							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: false
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					},
					{
						name: '财务/审计/会计/',
						checked: false,
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: false
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: false
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: true
							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: true
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					},
					{
						name: '销售',
						checked: false,
						list: [{
								name: '销售',
								context: '负责产品或服务的推广与销售，通过各种销售策略和...',
								checked: false
							},
							{
								name: '客服',
								context: '客服人员主要负责解答用户的问题，处理用户反馈，提供优质的服务，',
								checked: true
							},
							{
								name: '行政',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false

							},
							{
								name: '前台',
								context: '前台人员主要负责接待访客、接听电话、处理邮箱等工作，他们是',
								checked: false
							},
							{
								name: '店员/营业员',
								context: '行政人员主要负责公司的日常行政事务，如文件管理、会议安排、',
								checked: false
							}
						]
					}
				],
				job: []
			}
		},
		methods: {

			toggleChecked(index) {
				this.jobList.forEach((item) => {
					item.checked = false;
				});
				this.jobList[index].checked = true;
			},
			toggle(type, item) {
				this.type = type
				// open 方法传入参数 等同在 uni-popup 组件上绑定 type属性
				this.$refs.popup.open(type)
				console.log(item);
				this.job.push(item)
				console.log(this.job);
			},
			close() {
				this.job = []
				this.$refs.popup.close();
			},
			handleClose() {
				this.job = [];
				this.showPopup = false;
			},
			submitInfo() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/hopePosition'
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		height: 100vh;
		/* padding: 32rpx; */
		/* background-color: rgba(245, 245, 247, 1); */
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.title {
		font-size: 32rpx;
		margin-left: 32rpx;
		margin-top: 32rpx;
	}

	.scroll-Y {
		height: 100%;
	}

	.search-box {
		height: 64rpx;
	}

	.job-list-container {
		padding: 10px;
		display: flex;
		flex: 1;
		justify-content: space-around;
	}

	.joblist-left {
		display: flex;
		flex-direction: column;
		background: rgba(245, 245, 245, 1);
		/* justify-content: space-evenly; */
	}

	.joblist-right {
		width: 426rpx;
		/* padding: 0rpx 32rpx; */
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.job-category {
		height: 88rpx;
		/* margin-bottom: 15px; */
		display: flex;
		justify-content: start;
		align-items: center;
		width: 234rpx;
		padding-left: 24rpx;
	}

	.category-name {
		font-size: 28rpx;
		color: rgba(119, 119, 119, 1);
		margin-bottom: 5px;
		overflow: hidden;
		/* 隐藏溢出的文本 */
		white-space: nowrap;
		/* 保持文本在一行内显示 */
		text-overflow: ellipsis;

	}

	.active {
		color: rgba(79, 140, 240, 1);
		;
	}

	.job-item {
		width: 141rpx;
		margin-bottom: 10px;
		background-color: #f5f5f5;
		border-radius: 8px;
		padding: 16rpx 32rpx;
		display: flex;
		justify-content: space-around;
	}

	.job-label {
		/* width: 108rpx; */
		height: 40rpx;
		/* justify-content: space-between; */
		display: flex;
		align-items: center;
	}

	.job-label text {
		font-size: 24rpx;
		color: rgba(51, 51, 51, 1);
	}


	.job-context {
		margin-left: 10px;
		font-size: 14px;
		line-height: 1.4;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.context-box {
		width: 362rpx;
		height: 68rpx;
		display: flex;
		justify-content: space-between;
		align-items: end;
	}

	.context-box text {
		width: 230rpx;
		font-size: 28rpx;
		display: -webkit-box;
		/*弹性伸缩盒子模型显示*/
		-webkit-box-orient: vertical;
		/*排列方式*/
		-webkit-line-clamp: 2;
		/*显示文本行数(这里控制多少行隐藏)*/
		overflow: hidden;
		/*溢出隐藏*/
	}

	.context-box img {
		width: 32rpx;
		height: 32rpx;
		align-self: self-end;
	}

	::v-deep .u-input {
		width: 686rpx;
		height: 64rpx;
		background-color: rgba(243, 243, 243, 1);
		border-radius: 126rpx;
		padding: 0rpx !important;
		margin: 32rpx auto;
	}

	.active {
		background-color: rgba(255, 255, 255, 1);
		position: relative;
		/* 点击后改变的背景颜色 */
	}

	.active-item {
		color: rgba(79, 140, 240, 1);
	}

	.active:before {
		content: '';
		width: 6rpx;
		height: 88rpx;
		background-color: rgba(79, 140, 240, 1);
		position: absolute;
		left: 0rpx;
	}

	.popup-content {
		background-color: #fff;
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
		box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
		padding: 15px;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}

	.popup-title {
		font-size: 18px;
		font-weight: 500;
	}

	.popup-close {
		width: 20px;
		height: 20px;
	}

	.popup-body {
		line-height: 1.5;
		font-size: 14px;
	}

	.header {
		width: 686rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
</style>