<template>
    <view class="home-index">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="search-wrap">
                    <u-search placeholder="请输入职位名称或发布人" bgColor="#FFFFFF" :showAction="false" v-model="title"
                              @search='getCompanyJobList'></u-search>
                </view>
                <view class="filters">
                    <view class="filter">
                        <picker @change="changeEdu" :value="eduIndex" :range="eduList" range-key="name">
                            <view class="d-picker">{{eduList[eduIndex]['name']}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker @change="changeJobExperience" :value="jobExperienceIndex" :range="jobExperienceList"
                                range-key="name">
                            <view class="d-picker">
                                {{jobExperienceList[jobExperienceIndex]['name']}}
                            </view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker mode="date" :value="date" @change="bindDateChange">
                            <view class="d-picker">{{date?date:"发布时间"}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker @change="changeStatus" :value="statusIndex" :range="statusList" range-key="name">
                            <view class="d-picker">{{statusList[statusIndex]['name']}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                </view>
            </view>
        </u-sticky>

        <view class="list">
            <block>
                <u-swipe-action>
                    <u-swipe-action-item :options="options" v-for="(item,index) in list" :name="index" :key="index"
                                         @click="handleSwipe(item,$event)">
                        <company-pub-job-item :item="item" @expose="expose" @backTop="backTop"></company-pub-job-item>
                    </u-swipe-action-item>
                </u-swipe-action>

            </block>
        </view>
        <view style="height: 196rpx;"></view>
        <view class="footer">
            <view class="next sure" @click="add">
                发布职位
            </view>
        </view>
    </view>
</template>

<script>
    import {
        topCompanyJob,
        exposeCompanyJob,
        delCompanyJob,
        getCompanyJobList
    } from "../../config/api.js"
    import CompanyPubJobItem from '../components/companyPubJobItem.vue'
    import CompanyDraftJobItem from '../components/companyDraftJobItem.vue'

    export default {
        components: {
            CompanyPubJobItem,
            CompanyDraftJobItem
        },
        data() {
            return {
                job_active_id: '',
                title: '',
                page: 1,
                limit: 10,
                show: false,
                date: '',
                options: [{
                    text: '删除',
                    style: {
                        backgroundColor: '#FE4D4F',
                        borderRadius: '24rpx',
                        bottom: '32rpx',
                        height: '100%',
                        width: '150rpx',
                        marginLeft: '24rpx'
                    }
                }],
                list: [],
                list_1: [{
                    name: '招聘中',
                    status: 1,
                },
                    {
                        name: '已下架',
                        status: 3,
                    }
                ],
                eduList: [{
                    name: '学历',
                    value: 0
                }, {
                    name: '初中及以下',
                    value: 1
                }, {
                    name: '高中',
                    value: 2
                }, {
                    name: '大专',
                    value: 3
                }, {
                    name: '本科',
                    value: 4
                }, {
                    name: '研究生及以上',
                    value: 5
                }],
                eduIndex: 0,

                jobExperienceList: [{
                    value: 0,
                    name: '工作经验'
                }, {
                    value: 1,
                    name: '1年'
                }, {
                    value: 3,
                    name: '3年'
                }, {
                    value: 5,
                    name: '5年'
                }, {
                    value: 8,
                    name: '8年'
                }, {
                    value: 10,
                    name: '10年及以上'
                }],
                jobExperienceIndex: 0,
                statusList: [{
                    value: 'all',
                    name: '全部',
                }, {
                    value: 'active',
                    name: '招聘中',
                }, {
                    value: 'draft',
                    name: '草稿',
                }, {
                    value: 'off_shelf',
                    name: '已下架',
                }],
                statusIndex: 0
            }
        },

        onLoad(opts) {
            this.job_active_id = opts.job_active_id;
            this.date = uni.$u.timeFormat(this.start, 'yyyy-mm-dd');

        },
        onShow() {
            this.getCompanyJobList()
        },

        methods: {
            open() {
                this.show = true
            },
            close() {
                this.show = false
            },
            openDetails(item) {
                this.show = true
            },
            changeEdu(e) {
                console.log(e)
                this.eduIndex = e.detail.value
            },
            changeJobExperience(e) {
                this.jobExperienceIndex = e.detail.value
            },

            changeStatus(e) {
                this.statusIndex = e.detail.value
            },

            bindDateChange(e) {
                this.date = e.detail.value
            },

            add() {
                uni.navigateTo({
                    url: '/pagesA/add/pubJobOne?type=job_active&job_active_id='+this.job_active_id,
                })
            },

            //获取列表
            async getCompanyJobList() {
                let params = {
                    type: 'job_active',
                    job_active_id:this.job_active_id,
                    page: this.page,
                    limit: this.limit,
                    title: this.title, //职位名称
                    status: '', //审核状态：1-通过，2-待审核，3-驳回
                    hot_status: '', //热门状态：1-开启，2-关闭，可不填，不填查全部职位
                    list_status: this.statusList[this.statusIndex][
                        'value'
                        ], //列表状态：默认：alll，全部： all	当前登录账号， 所有已发布的职位	招聘中 active	当前账号发布职位中， 状态为 已上架的职位	下架 off_shelf	当前账号发布职位中， 状态为 已下架的职位	草稿 draft	当前账号发布职位中， 提交状态为草稿的职位
                }
                const {
                    status_code,
                    data
                } = await getCompanyJobList(params)
                if (status_code == 200) {
                    this.list = data.data
                }
            },

            handleSwipe(item, e) {
                console.log(item, e)
                let self = this;
                //表示点击了删除按钮
                if (e.index == 0) {
                    uni.showModal({
                        title: '确定要删除该职位吗？',
                        success: async (res) => {
                            if (res.confirm) {
                                console.log('用户点击确定');
                                let params = {
                                    id: item.id
                                }
                                const {
                                    status_code,
                                    data,
                                    message
                                } = await delCompanyJob(params)
                                if (status_code == 200) {
                                    self.page = 1
                                    self.getCompanyJobList()
                                    return uni.$u.toast('成功')
                                } else {
                                    return uni.$u.toast(message || '失败')
                                }
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    })
                }
            },

            //曝光
            async expose(item) {
                let self = this;
                uni.showModal({
                    title: '确定要曝光该职位吗？',
                    success: async (res) => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            let params = {
                                id: item.id
                            }
                            const {
                                status_code,
                                data,
                                message
                            } = await exposeCompanyJob(params)
                            if (status_code == 200) {
                                self.page = 1
                                self.getCompanyJobList()
                                return uni.$u.toast('成功')
                            } else {
                                return uni.$u.toast('失败' || message)
                            }
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                })
            },

            //置顶
            async backTop(item) {
                let self = this;
                uni.showModal({
                    title: '确定要置顶该职位吗？',
                    success: async (res) => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            let params = {
                                id: item.id,
                                day: 10
                            }
                            const {
                                status_code,
                                data,
                                message
                            } = await topCompanyJob(params)
                            if (status_code == 200) {
                                self.page = 1
                                self.getCompanyJobList()
                                return uni.$u.toast('成功')
                            } else {
                                return uni.$u.toast('失败' || message)
                            }
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                })
            }
        }
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    // .home-index{
    // 	padding: 32rpx;
    // }
    .header {
        padding: 32rpx;
        // .search-wrap {
        // 	margin-top: 32rpx;
        // }

        .filters {
            display: flex;
            align-items: center;
            margin-top: 32rpx;

            .filter {
                display: flex;
                align-items: center;
                height: 48rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 0 16rpx;
                margin-right: 12rpx;
                border-radius: 8rpx;
                background: #FFFFFF;

                image {
                    width: 24rpx;
                    height: 24rpx;
                }
            }

            .search-wrap {
                flex: 1;
            }
        }
    }

    .list {
        padding: 0 32rpx 32rpx 32rpx;

        ::v-deep .u-swipe-action-item__right {
            bottom: 32rpx;
            border-radius: 24rpx;
        }

        ::v-deep .u-swipe-action-item__content {
            background: transparent;
        }
    }

    .footer {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: fixed;
        left: 0;
        bottom: 0;
        height: 120rpx;
        width: 100%;
        background-color: #FFFFFF;
        border-radius: 24rpx 24rpx 0 0;
        z-index: 10;

        .next {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28rpx;
            color: #FFFFFF;
            height: 88rpx;
            width: 90%;
            border-radius: 16rpx;
        }

        .sure {
            background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
            color: #FFFFFF;
        }
    }
</style>