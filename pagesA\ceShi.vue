<template>
    <view>
        <!-- 唤起列表 -->
        <button @click="showPopup1 = true">切换职位</button>
        <Popup :visible="showPopup1" :data="jobList" @close="showPopup1 = false" @select="handleSelect" />
        <!-- 面试详情 -->
        <button @click="showPopup2=true">面试详情弹窗</button>
        <InterviewDetails v-if="showPopup2" :show="showPopup2" :currentItem="currentItem" @response="handleResponse"
            @close="showPopup2 = false" />
        <!-- 选择简历 -->
        <button @click="showResumePopup = true">发送简历</button>
        <resumePop v-if="showResumePopup" :show="showResumePopup" :resumeList="resumeList" @confirm="handleConfirm"
            @close="showResumePopup = false" />
        <!-- 将数据传递回上个页面的方法 -->
        <button @click="goPage">去往表单页面</button>
        <button @click="showPageData">查看上个页面数据</button>
    </view>
</template>

<script>
    import Popup from '@/components/Popup.vue';
    import InterviewDetails from '@/components/chat/interviewDetails.vue';
    import resumePop from '@/components/chat/resumePop.vue';
    import {
        getCompanyJobList
    } from '@/config/api.js'
    export default {
        components: {
            Popup,
            InterviewDetails,
            resumePop
        },
        data() {
            return {
                showPopup1: false, // 切换职位展示隐藏
                jobList: [], // 切换职位数据
                showPopup2: false, // 面试弹窗展示隐藏
                // 面试弹窗数据
                currentItem: {
                    contact_name: '张三',
                    contact_cellphone: '13800000000',
                    interviewed_at: '2023-10-01 10:00',
                    addresses: [{
                        map_address: '某地'
                    }],
                    remark: '准备好面试'
                },
                // 简历弹窗
                showResumePopup: false,
                resumeList: [{
                        resume_name: '简历1'
                    },
                    {
                        resume_name: '简历2'
                    },
                    {
                        resume_name: '简历3'
                    },
                    {
                        resume_name: '简历4'
                    }
                ],
                ceShi2Data: ''
            }
        },
        onLoad() {
            // 获取切换职位的数据
            this.loadData()
        },
        // 页面展示时触发。不同于onload只触发一次。这个生命周期只要展示就触发配套的还有个onHide隐藏
        onShow() {
            if (uni.getStorageSync('ceshi')) {
                // 拿去本地中上个页面的数据，并留在当前页使用
                this.ceShi2Data = uni.getStorageSync('ceshi');
                // 删除本地中上个页面的数据
                uni.removeStorageSync('ceshi')
            }
        },
        methods: {
            // 查看上个页面拿到的数据的数据
            showPageData() {
                uni.showToast({
                    title: this.ceShi2Data,
                    icon: 'none'
                })
                console.log("上个页面的数据", this.ceShi2Data)
            },
            // 前往下个页面添加数据
            goPage() {
                uni.navigateTo({
                    url: '/pagesA/ceShi2'
                })
            },
            // 切换职位功能
            handleSelect(item) {
                console.log('选中的项:', item); // 处理选中的数据
            },
            // 职位数据
            // async loadData() {
            //     const res = await getCompanyJobList()
            //     this.jobList = res.data
            // },
            // 模拟职位数据
            loadData() {
                // 模拟数据加载
                this.jobList = Array.from({
                    length: 100
                }, (v, k) => ({
                    id: k,
                    job_describe: `Item ${k + 1}`
                }));
            },
            // 邀请面试弹窗
            handleResponse(response) {
                console.log('用户选择:', response);
                // 处理用户选择
            },
            // 选择简历
            handleConfirm(selectedResume) {
                console.log('选中的简历:', selectedResume);
                // 在这里进行相应的处理，比如发送简历等
            },
        }
    }
</script>

<style>
</style>