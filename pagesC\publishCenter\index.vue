<template>
	<view class="warp">
		<view class="top">
			<view class="arrow">
				<u-icon name="arrow-left" color="#fff" size="20" @click="back"></u-icon>
			</view>
			<view class="top-title">
				<view class="name-box">
					<img :src="info.ava" alt="" />
					<view class="name-right">
						<view class="">
							{{ info.name }}
						</view>
						<view class="ip"><text style="margin-right: 32rpx;">0 粉丝</text>0关注</view>
					</view>
				</view>
				<view class="info-btn" @click="goSet">编辑资料</view>
			</view>
			<view class="ip-box">
				IP 河南
			</view>
			<view class="btm-tag">
				<view class="tag-inner" @click="goData">
					<u-icon name="grid" color="#fff" size="26"></u-icon>
					<view class="">数据总览</view>
				</view>
				<view class="tag-inner" @click="gocollect">
					<u-icon name="heart" color="#fff" size="26"></u-icon>
					<view class="">收藏</view>
				</view>
				<view class="tag-inner" @click="routerEssay">
					<u-icon name="grid" color="#fff" size="26"></u-icon>
					<view class="">规则中心</view>
				</view>
			</view>
		</view>

		<view class="inner">
			<view class="neirong">
				<view class="nr-box" :class="item == nowTab ? 'now-btn' : ''" v-for="item in list"
					@click="changeTab(item)">
					{{ item }}
				</view>
			</view>
			<view class="pub-box" v-if="cont.length > 0">
				<view class="box" v-for="item in cont" @click="goDetail(item)">
					<view class="title" v-if="item.form==1||item.form==2">
						{{ item.content }}
					</view>
					<view class="title" v-if="item.form==3||item.form==4">
						{{ item.title }}
					</view>
					<view class="title" v-if="item.form==3||item.form==4">
						{{ item.content }}
					</view>
					<view class="btm">
						<view v-if="item.cover" class="cont-inn">
							<image v-if="item.form!=4" class="image" :src="item.cover" alt="" />
							<video v-if="item.form==4" :src="item.cover" controls></video>
						</view>
					</view>
					<view class="time">
						已发布 {{ item.created_at }}
					</view>
					<view class="shuju-warp">
						<view class="shuju">
							<view class="" @click="goDetail(item)">
								<img src="/static/images/publishCenter/msg_icon.png" alt="" />
								<text>{{ item.msg }}</text>
							</view>
							<view class="" @click.stop="dianzanClick(item)">
								<!--<img src="/static/images/publishCenter/good_icon.png" alt="" />-->
								<u-icon size="14" :name="item.namey"></u-icon>
								<text style="margin-left: 3px;">{{ item.like_count }}</text>
							</view>
							<view class="" @click="goDetail(item)">
								<img src="/static/images/publishCenter/view_icon.png" alt="" />
								<text>{{ item.view }}</text>
							</view>
							<view class="" @click.stop="shoucangClick(item)">
								<!--<img src="/static/images/publishCenter/star_icon.png" alt="" />-->
								<u-icon size="14" :name="item.namet"></u-icon>
								<text style="margin-left: 3px;">{{ item.love_count }}</text>
							</view>
						</view>
						<view class="" @click.stop="showDel = true">
							<!--<u-icon name="more-dot-fill" color="#555555" size="26"></u-icon>-->
						</view>
					</view>

				</view>
			</view>

			<view class="empty" v-else>
				<img src="https://api-test.zhaopinbei.com/storage/uploads/images/EhizJahlEXJ8NkDggqEZfhj1J0NLKxvnEa03lBvg.png"
					alt="" />
			</view>
		</view>
		<view class="add" @click="clickAdd">
			<img src="https://api-test.zhaopinbei.com/storage/uploads/images/P9IBWVSsIdqtXIasGZvS8Ay1q8ty1yDf8Gm9Dngd.png"
				alt="" />
		</view>
		<u-popup :show="show" @close="show = false" mode="bottom">
			<view class="pub-warp">
				<view class="pub-inner" v-for="item in pubList" @click="goPublish(item)">
					<img :src="item.icon" alt="" />
					<view class="">
						{{ item.title }}
					</view>
				</view>
			</view>
		</u-popup>
		<u-popup :show="showDel" @close="showDel = false" mode="bottom">
			<view class="del-warp">
				<view class="line" @click="del">
					删除
				</view>
				<view class="" @click="showDel = false">
					取消
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		getArticleList,
		getCommentLikeList,
		storeFunt,
		destroyFunt,
	} from "../../config/api.js"
	export default {
		data() {
			return {
				info: {
					name: '张三',
					ava: 'https://api-test.zhaopinbei.com/storage/uploads/images/7Md0Ku73yZXCUOENs3uRGMcZPkuGoUNdMIpFvY2b.png',
					ip: '河南',
				},
				list: ['全部', '文章', '视频', '动态', '提问'],
				nowTab: '全部',
				show: false,
				cont: [],
				pubList: [{
						title: '发文章',
						icon: '/static/images/publishCenter/fabu_icon2.png',
						path: 'publishArticle',
					},
					{
						title: '发视频',
						icon: '/static/images/publishCenter/fabu_icon1.png',
						path: 'publishVideo',
					},
					{
						title: '发动态',
						icon: '/static/images/publishCenter/fabu_icon3.png',
						path: 'publishDynamic',
					},
					{
						title: '发提问',
						icon: '/static/images/publishCenter/fabu_icon4.png',
						path: 'publishAsk',
					},
				],
				userType: '', //判断是从哪个身份过来的，千里马还是伯乐
				company_id: '', //公司id
				member_id: '', //会员id
			};
		},
		onLoad(item) {
			this.userType = uni.getStorageSync('roleType');
			this.company_id = uni.getStorageSync('userInfo')?.company.id;
			this.member_id = uni.getStorageSync('userInfo')?.member_info.member_id;
			this.getLists();
			//this.getCommentLikeLists();
		},
		methods: {
			back() {
				uni.navigateBack();
			},
			// 切换
			changeTab(item) {
				this.nowTab = item;
				this.getLists(item);
			},
			async getLists(val) {
				let params = {
					form: '',
				}
				if (val == '全部') {
					params.form = '';
				} else if (val == '文章') {
					params.form = 1;
				} else if (val == '视频') {
					params.form = 4;
				} else if (val == '动态') {
					params.form = 2;
				} else if (val == '提问') {
					params.form = 3;
				}
				if (this.userType == 'company') { //伯乐
					params.member_id = this.company_id;
				} else if (this.userType == 'member') { //千里马
					params.member_id = this.member_id;
				} else if (this.userType == 'headhunters') { //就业管家

				}
				const {
					status_code,
					data
				} = await getArticleList(params)
				if (status_code == 200) {
					this.cont = data;
					this.cont.forEach((item, index) => {
						item.title = item.name;
						item.msg = 0;
						//item.love_count = 0;// good
						item.view = 0;
						//item.start = 0; like_count has_loved 点赞的false true  has_liked 收藏的
						item.status = 0;
						item.msg = 2;
						item.namet = 'heart';
						//判断点过收藏，就让图标换实体图标
						if (item.has_liked) {
							item.namey = 'thumb-up-fill';
						} else {
							item.namey = 'thumb-up';
						}
						//判断点过赞，就让图标换实体图标
						if (item.has_loved) {
							item.namet = 'heart-fill';
						} else {
							item.namet = 'heart';
						}

					});
					this.$forceUpdate();
				}
			},
			async getCommentLikeLists(val) {
				let params = {
					form: '',
				}
				if (val == '全部') {
					params.form = '';
				} else if (val == '文章') {
					params.form = 1;
				} else if (val == '视频') {
					params.form = 4;
				} else if (val == '动态') {
					params.form = 2;
				} else if (val == '提问') {
					params.form = 3;
				}
				if (this.userType == 'company') { //伯乐
					params.member_id = this.company_id;
				} else if (this.userType == 'member') { //千里马
					params.member_id = this.member_id;
				} else if (this.userType == 'headhunters') { //就业管家

				}
				const {
					status_code,
					data
				} = await getCommentLikeList(params)
				if (status_code == 200) {
					console.log(data)
				}
			},
			// 显示发布内容
			clickAdd() {
				this.show = true;
			},
			// 发布
			goPublish(item) {
				uni.navigateTo({
					url: `./${item.path}`,
				});
			},
			// 详情
			goDetail(item) {
				item.names = '张三'
				uni.navigateTo({
					url: './detail?key=' + encodeURIComponent(JSON.stringify(item)),
				});
			},
			// 数据
			goData() {
				uni.navigateTo({
					url: './viewData',
				});
			},
			// 设置
			goSet() {
				uni.navigateTo({
					url: './setting',
				});
			},
			//点赞点击事件
			dianzanClick(item) {
				if (item.has_liked) { //销毁点赞 先这样判断吧，没办法item.namey == 'thumb-up-fill'
					const ls = {
						article_id: item.id,
						type: '1',
					}
					if (this.userType == 'company') { //伯乐
						ls.member_id = this.company_id;
					} else if (this.userType == 'member') { //千里马
						ls.member_id = this.member_id;
					} else if (this.userType == 'headhunters') { //就业管家

					}
					destroyFunt(ls).then(({
						status_code,
						data
					}) => {
						if (status_code == '200') {
							this.getLists(this.nowTab);
							this.$forceUpdate();
						}
					})
				} else { //添加点赞 item.namey == 'thumb-up'
					const ls = {
						article_id: item.id,
						type: '1',
					}
					if (this.userType == 'company') { //伯乐
						ls.member_id = this.company_id;
					} else if (this.userType == 'member') { //千里马
						ls.member_id = this.member_id;
					} else if (this.userType == 'headhunters') { //就业管家

					}
					storeFunt(ls).then(({
						status_code,
						data
					}) => {
						if (status_code == '200') {
							this.getLists(this.nowTab);
							this.$forceUpdate();
						}
					})
				}
			},
			//收藏点击事件
			shoucangClick(item) {
				if (item.has_loved) { //销毁收藏 item.namet == 'heart-fill heart'
					const ls = {
						article_id: item.id,
						type: '2',
					}
					if (this.userType == 'company') { //伯乐
						ls.member_id = this.company_id;
					} else if (this.userType == 'member') { //千里马
						ls.member_id = this.member_id;
					} else if (this.userType == 'headhunters') { //就业管家

					}
					destroyFunt(ls).then(({
						status_code,
						data
					}) => {
						if (status_code == '200') {
							this.getLists(this.nowTab);
							this.$forceUpdate();
						}
					})
				} else { //添加收藏if(item.namet == 'heart')
					const ls = {
						article_id: item.id,
						type: '2',
					}
					if (this.userType == 'company') { //伯乐
						ls.member_id = this.company_id;
					} else if (this.userType == 'member') { //千里马
						ls.member_id = this.member_id;
					} else if (this.userType == 'headhunters') { //就业管家

					}
					storeFunt(ls).then(({
						status_code,
						data
					}) => {
						if (status_code == '200') {
							this.getLists(this.nowTab);
							this.$forceUpdate();
						} else {
							alert(1)
						}
					})
				}
			},
			routerEssay() {
				uni.navigateTo({
					url: '/pagesC/essay/index'
				})
			}

		},
	};
</script>
<style>
	page {
		background: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.warp {
		width: 100vh;
		background: #f5f5f7;
		position: relative;
		top: 0;

		.top {
			height: 520rpx;
			width: 686rpx;
			z-index: -1;
			background: linear-gradient(360deg, #9DD3FF 0%, #4F75F0 100%);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			// filter: blur(3.5999999046325684px);
			position: absolute;
			top: 0;
			padding: 0 32rpx;

			.arrow {
				margin-top: 116rpx;
			}

			.top-title {
				font-size: 32rpx;
				margin-top: 52rpx;
				color: #ffffff;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.name-box {
					display: flex;
					align-items: center;

					img {
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
					}

					.name-right {
						margin-left: 16rpx;

						.ip {
							margin-top: 24rpx;
							font-size: 24rpx;
						}
					}
				}

				.info-btn {
					height: 58rpx;
					font-size: 24rpx;
					line-height: 58rpx;
					padding: 0 12rpx;
					border-radius: 52rpx 52rpx 52rpx 52rpx;
					border: 2rpx solid #fff;
				}
			}

			.ip-box {
				width: 110rpx;
				height: 50rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 100rpx 100rpx 100rpx 100rpx;
				font-size: 24rpx;
				color: #FFFFFF;
				text-align: center;
				line-height: 50rpx;
				margin-top: 14rpx;
				margin-bottom: 24rpx;
			}

			.btm-tag {
				font-size: 28rpx;
				margin-top: 20rpx;
				color: #ffffff;
				display: flex;

				.tag-inner {
					width: 230rpx;
					height: 80rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					border-radius: 16rpx;
					background: rgba(245, 245, 247, 0.2);
					margin-right: 24rpx;

					view {
						margin-left: 10rpx;
					}
				}
			}
		}

		.inner {
			width: 750rpx;
			position: absolute;
			top: 510rpx;
			border-radius: 24rpx 24rpx 0 0;
			background: #f5f5f7;
			border-radius: 24rpx;

			.neirong {
				width: 638rpx;
				margin: 32rpx 32rpx;
				display: flex;
				align-items: center;


				.nr-box {
					height: 40rpx;
					width: 80rpx;
					text-align: center;
					line-height: 40rpx;
					font-size: 24rpx;
					color: #333333;
				}

				.now-btn {
					font-size: 28rpx;
					background-image: url('/static/images/publishCenter/vector-icon.png');
					background-repeat: no-repeat;
					background-size: 60rpx 20rpx;
					background-position: center bottom;
				}
			}

			.empty {
				width: 638rpx;
				margin: 32rpx 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				img {
					width: 500rpx;
					height: 440rpx;
				}
			}

			.box {
				width: 638rpx;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				padding: 32rpx 24rpx 10rpx 24rpx;
				margin-bottom: 20rpx;
				background: #ffffff;
				margin-left: 32rpx;

				.btm {
					// margin-bottom: 40rpx;
				}

				.title {
					font-size: 28rpx;
					color: #333333;
				}

				.tip {
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 24rpx;
				}

				.box-top {
					width: 638rpx;
					height: 152rpx;
					background: #d9d9d9;
					border-radius: 24rpx 24rpx 0 0;
					margin-bottom: 24rpx;
				}

				.cont-inn {
					.image {
						width: 300rpx;
						object-fit: contain;
						/*width:93%;*/
						height: 300rpx;
						border-radius: 40rpx;
					}
				}

				.time {
					margin-top: 12rpx;
					font-size: 24rpx;
					color: #999999;
				}

				.shuju {
					margin-top: 12rpx;
					font-size: 24rpx;
					color: #999999;
					display: flex;
					align-items: center;

					&>view {
						display: flex;
						align-items: center;
						margin-right: 20rpx;

						img {
							width: 26rpx;
							height: 26rpx;
							margin-right: 10rpx;
						}
					}
				}
			}
		}

		.add {
			width: 124rpx;
			height: 124rpx;
			position: fixed;
			right: 42rpx;
			bottom: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			img {
				width: 124rpx;
				height: 124rpx;
			}
		}

		.pub-warp {
			width: 686rpx;
			padding: 32rpx;
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			justify-content: space-around;
			background: linear-gradient(180deg, #9DC2FF 0%, #FAFAFA 80%);

			.pub-inner {
				width: 300rpx;
				height: 200rpx;
				background: #fff;
				border-radius: 24rpx;
				margin-bottom: 20rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				img {
					width: 100rpx;
					height: 100rpx;
				}
			}
		}
	}
</style>