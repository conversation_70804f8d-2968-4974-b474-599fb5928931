<template>
    <view id="app">
        <u-sticky bgColor="#F5F5F5">
            <u-search placeholder="请输入职位名称或千里马姓名" bgColor="#FFFFFF" showAction @custom="custom"
                      v-model="keyword"></u-search>
            <view class="filterBox">
                <view class="filter">开始时间
                    <image src="../static/images/down.png" mode="" class="downImg"></image>
                </view>
                <view class="filter">结束时间
                    <image src="../static/images/down.png" mode="" class="downImg"></image>
                </view>
            </view>
        </u-sticky>

        <view class="content" v-for="(item,index) in page.data" :key="index" :item="item">
            <view class="content_top">
                <image :src="item.member_info.image.path_url" mode="" class="userHead"></image>
                <view style="flex: 1;">
                    <view class="userTop">
                        <view class="userText">
                            <view class="userName">{{ item.member_certification.name }}</view>
                            <view class="userAction">{{ item.member.certification_status_name }}</view>
                        </view>
                        <view class="userNum">期望薪资：{{ item.resume.expect_salary_yuan }}</view>
                    </view>
                    <!--| 3年工作经验-->
                    <view class="userLabel">{{ item.member_info.sex_str }} | {{ item.member_info.age }}岁 | {{
                        item.member_info.education_type_name }}
                    </view>
                </view>
            </view>
            <view class="signUpJob">
                <view class="signUpJob_title">报名职位</view>
                <view>{{ item.job_basic.title }}</view>
            </view>
            <view class="signUpJob">
                <view class="signUpJob_title">关联任务</view>
                <view>{{ item.task.title }}</view>
            </view>

            <view class="bottomBox">
                <view v-if="item.report_status=='cancel'"
                      :class="['fontCla',item.report_status=='cancel'?'redCla':type==2?'blueCla':'']">拒绝原因:{{
                    item.cancel_reason }}
                </view>
                <!-- 这个空盒子是占位用的 -->
                <view></view>
                <view class="btnBox">
                    <view class="btn chat" @click.stop="communicate('job_report',item.id)">聊聊呗</view>
                    <view class="btn no" v-if="item.but_status.cancel_status ==1" @click.stop="cancelSignup(item)">拒绝
                    </view>
                    <view class="btn invit" v-if="item.but_status.interview_status ==1"
                          @click.stop="inviteInterview(item)">邀请面试
                    </view>
                </view>
            </view>
        </view>
        <Pages :status="page.status"></Pages>
        <u-popup :show="showReason" :round="10" bgColor="#F5F5F5" mode="center" closeOnClickOverlay @close="closeReason"
                 @open="openReason">
            <view class="credit" style="width: 70vw;">
                <view class="title comp">
                    拒绝原因
                </view>
                <view class="content">
                    <u--textarea v-model="reason" placeholder="请输入内容"></u--textarea>
                </view>

                <view class="agree" @click="sureCancel">
                    确定
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
    import {jobReports} from "../../config/common_api";
    import {
        cancelCompanySignup,
    } from "../../config/api.js"
    import {communicate} from "../../common/common.js";
    export default {
        data() {
            return {
                type: 2,
                page: {
                    form: {
                        limit: 10,
                        page: 1,
                        send_company_id: 0,
                        model_type: 'task',
                    },
                    data: [],
                    more: false,
                    status: 'nomore',
                },
                showReason: false,
                currentItem: {},
                reason: ''
            }
        },
        onLoad() {
            var _this = this;
            _this.getList();
        },
        onReachBottom() {
            var _this = this;
            if (_this.page.more) {
                _this.page.form.page++;
                _this.getList()
            }
        },

        methods: {
            communicate,
            openReason() {
                this.showReason = true
            },

            closeReason() {
                this.reason = ''
                this.showReason = false
            },
            cancelSignup(item) {
                this.showReason = true
                this.currentItem = item
            },
            async sureCancel() {
                let params = {
                    report_id: this.currentItem.id,
                    cancel_reason: this.reason
                }
                const {
                    status_code,
                    data,
                    message
                } = await cancelCompanySignup(params)
                this.closeReason()
                if (status_code == 200) {
                    uni.$u.toast('已拒绝')
                    this.jobReports()
                }
            },
            inviteInterview(item) {
                console.log(item)
                uni.navigateTo({
                    url: "/pagesA/add/inviteInterview?report_id=" + item.id + "&job_id=" + item.job_id + "&member_id=" + item.member_id
                })
            },
            getList() {
                var _this = this;
                const res_userInfo = uni.getStorageSync('userInfo');
                _this.page.form.send_company_id = res_userInfo.company.id;
                jobReports(_this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.more ? 'loadmore' : 'nomore';
                    }
                })
            }
        }
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .downImg {
        width: 32rpx;
        height: 32rpx;
    }

    .filterBox {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
    }

    .filter {
        width: 160rpx;
        height: 50rpx;
        background: #FFFFFF;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 24rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        margin-right: 24rpx;
    }

    .content {
        width: 100%;
        padding: 32rpx;
        background: #fff;
        border-radius: 24rpx;
        margin-bottom: 24rpx;
    }

    .content_top {
        display: flex;
        align-items: center;
    }

    .userHead {
        width: 104rpx;
        height: 104rpx;
        background: #D9D9D9;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        margin-right: 24rpx;
    }

    .userTop {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .userText {
        display: flex;
        align-items: center;
    }

    .userName {
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        margin-right: 16rpx;
    }

    .userAction {
        width: 84rpx;
        height: 40rpx;
        background: rgba(87, 213, 28, 0.1);
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 40rpx;
        font-weight: 600;
        font-size: 20rpx;
        color: #57D51C;
    }

    .userLabel {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        margin-top: 24rpx;
    }

    .userNum {
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
    }

    .signUpJob {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        // margin-bottom: 16rpx;
        margin-top: 16rpx;
    }

    .signUpJob_title {
        color: #999 !important;
    }

    .bottomBox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16rpx;
    }

    .btnBox {
        float: right;
        display: flex;
        align-items: right;
    }

    .btn {
        // width: 144rpx;
        // height: 56rpx;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        padding: 12rpx 24rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #4F8CF0;
        margin-left: 24rpx;
    }

    .chat {
        color: #4F8CF0;
        background: rgba(79, 140, 240, 0.1);
    }

    .no {
        background: #FE4D4F;
        color: #FFFFFF;
    }

    .invit {
        background: #4F8CF0;
        color: #FFFFFF;
    }

    .fontCla {
        font-weight: 400;
        font-size: 24rpx;
    }

    .redCla {
        color: #FE4D4F;
    }

    .blueCla {
        color: #4F8CF0;
    }

    .credit {
        display: flex;
        flex-direction: column;
        padding: 40rpx 48rpx;

        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .tip {
                image {
                    width: 48rpx;
                    height: 48rpx;
                }
            }
        }

        .comp {
            justify-content: center;
        }

        .content {
            display: flex;
            flex-direction: column;
            margin-top: 40rpx;

            .sub-title {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                color: #000000;
            }
        }

        .agree {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 44rpx;
            height: 88rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #FFFFFF;
            background: #4F8CF0;
            margin-top: 32rpx;
        }
    }
</style>