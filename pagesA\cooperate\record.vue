<template>
    <view id="app">
        <u-sticky bgColor="#F5F5F5">
            <u-search placeholder="请输入协同名称和发起人" bgColor="#FFFFFF" showAction @custom="custom"
                v-model="keyword"></u-search>
        </u-sticky>
        <view style="height: 32rpx;"></view>
        <view class="content" v-for="(item,index) in list" :key="item.id" @click.stop="goDetails(item.id)">
            <view class="content_title">{{item.title}}</view>
            <view class="timesBox">
                <view>发起时间</view>
                <view class="timesText">{{item.created_at}}</view>
            </view>
            <view class="hrBox"></view>
            <view class="content_user">
                <view class="content_userText">
                    <image :src="item.member_info.image.thumbnail_path_url" mode="" class="content_userHead"></image>
                    <view>{{item.member_certification.name}}</view>
                </view>
                <!-- 颜色变化 -->
                <view :class="['addBtn',type==2?'greyColor':'']" @click.stop="add(item.user_id)" v-if="userId!=item.user_id">加入协同</view>
            </view>
        </view>
        <Pages :status="status"></Pages>

        <view class="bottomBox">
            <view class="bottomBtn" @click="release">发起协同</view>
        </view>


    </view>
</template>

<script>
    import Pages from "../../components/pages.vue";
    import {
        addFriendStore
    } from '../../config/common_api.js'
    import {
        cooperateList
    } from '../../config/headhunterList_api.js'
    export default {
        components: {
            Pages
        },
        data() {
            return {
                keyword: '',
                type: 1,
                page: 1,
                limit: 10,
                status: 'loadmore',
                more: false,
                list: [],
                userId:'', // 登录用户的id
            }
        },
        onLoad() {
            this.cooperateList()
            this.userId = uni.getStorageSync('userInfo').login_user.id
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.cooperateList()
            } else {
                this.status = 'nomore'
            }
        },
        methods: {
            async cooperateList() {
                let params = {
                    page: this.page,
                    limit: this.limit
                }

                const res = await cooperateList(params)
                this.list = this.list.concat(res.data.data);
                // 返回false代表没有下一页
                this.more = res.data.more;
                this.status = this.more ? "loadmore" : "nomore"
                console.log(res)
            },
            custom() {
                console.log('点击搜索')
            },
            goDetails(id) {
                uni.navigateTo({
                    url: '/pagesA/cooperate/cooperateDetails?id=' + id
                })
            },
            add(id) {
                // 弹窗输入
                uni.showModal({
                    title: '是否加入协同', // 标题
                    editable: true, // 开启输入框
                    placeholderText: '请输入加入原因', // 输入框提示语
                    success: async (res) => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            console.log("获取输入的内容", res.content)
                            let params = {
                                user_id:id,
                                note:res.content
                            }
                            // 后续处理
                            const result = await addFriendStore(params)
                            console.log("res",result)
                            if(result.status_code==200) {
                                uni.$u.toast('已发起申请')
                            } else {
                                uni.$u.toast(result.message)
                            }
                            
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                })
                console.log('加入协同')
            },
            release() {
                uni.navigateTo({
                    url: '/pagesA/cooperate/release'
                })
            }
        },
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .content {
        width: 100%;
        height: 270rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        margin-bottom: 24rpx;
        padding: 32rpx;
    }

    .content_title {
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        margin-bottom: 16rpx;
    }

    .timesBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
    }

    .timesText {
        color: #333333 !important;
    }

    .hrBox {
        width: 622rpx;
        height: 2rpx;
        background: #F5F5F7;
        margin: 24rpx 0;
    }

    .content_userText {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }

    .content_userHead {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        margin-right: 16rpx;
    }

    .content_user {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .addBtn {
        width: 144rpx;
        height: 56rpx;
        background: #4F8CF0;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 56rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #FFFFFF;
    }

    .greyColor {
        background: #CCCCCC !important;
    }

    .bottomBox {
        width: 100%;
        height: 196rpx;
        padding: 24rpx 22rpx 92rpx 42rpx;
        background: #FFFFFF;
        position: fixed;
        bottom: 0;
        left: 0;
        border-radius: 24rpx 24rpx 0 0;
    }

    .bottomBtn {
        width: 100%;
        height: 80rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #FFFFFF;
    }
</style>