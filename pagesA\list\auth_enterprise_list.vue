<template>
	<view class="main">
		<u-sticky bgColor="#F5F5F5">
			<view class="search-wrap">
				<u-search placeholder="请输入企业名称" bgColor="#FFFFFF" :showAction="true" actionText="搜索" v-model="keyword" @custom="custom" @clear="clear"></u-search>
			</view>
		</u-sticky>
		<view class="list">
			<auth-enterprise-item v-for="(item,index) in list" :key="index" :item="item" @sign="sign"
				@cancel="cancel"></auth-enterprise-item>
		</view>

		<view class="footer">
			<view class="next sure" @click="go">
				委托授权
			</view>
		</view>
	</view>
</template>

<script>
	import {
		cancelAuth,
		getHeadhuntersAuthEnterpriseList
	} from "../../config/api.js"
	import AuthEnterpriseItem from '../components/authEnterpriseItem.vue'
	export default {
		components: {
			AuthEnterpriseItem
		},
		data() {
			return {
				keyword: '',
				page: 1,
				limit: 10,
				list: []
			}
		},
		onShow() {
			this.getHeadhuntersAuthEnterpriseList()
		},
		methods: {
			go() {
				uni.navigateTo({
					url: "/pagesA/add/entrustAuth"
				})
			},
			sign() {},
			//取消
			cancel(item) {
				let self = this;
				uni.showModal({
					title: '确定要取消授权吗？',
					success: async (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							let params = {
								id: item.id
							}
							const {
								status_code,
								data,
								message
							} = await cancelAuth(params)
							if (status_code == 200) {
								self.page = 1
								self.list = []
								self.getHeadhuntersAuthEnterpriseList()
								return uni.$u.toast('成功')
							} else {
								return uni.$u.toast(message || '失败')
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				})
			},
			clear() {
				this.keyword="";
				this.custom()
			},
			custom() {
				this.page =1;
				this.list = []
				this.getHeadhuntersAuthEnterpriseList()
			},
			async getHeadhuntersAuthEnterpriseList() {
				let params = {
					page: this.page,
					limit: this.limit,
					company_name:this.keyword
				}
				const {
					status_code,
					data,
					message
				} = await getHeadhuntersAuthEnterpriseList(params)

				if (status_code == 200) {
					this.list = data.data;
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.main {
		padding-bottom: 120rpx;
	}

	.search-wrap {
		padding: 32rpx;
	}

	.list {
		padding: 0 32rpx 32rpx 32rpx;
	}

	.footer {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		height: 120rpx;
		width: 100%;
		background-color: #FFFFFF;
		z-index: 10;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 600;
			font-size: 28rpx;
			color: #FFFFFF;
			height: 88rpx;
			width: 90%;
			border-radius: 16rpx;
		}

		.sure {
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	}
</style>