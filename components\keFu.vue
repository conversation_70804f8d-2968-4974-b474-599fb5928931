<template>
	<view class="kefu">
		<image src="/static/images/index/kefu.png" mode=""></image>
	</view>
</template>

<script>
	export default {
		name:"keFu",
		data() {
			return {
				
			};
		}
	}
</script>

<style lang="less">
.kefu{
	position: fixed;
	z-index: 999;
	bottom: 158rpx;
	right: 32rpx;
	background: #FFFFFF;
	width: 108rpx;
	height: 108rpx;
	box-shadow: 0px 1px 1px 1px rgba(0,0,0,0.1);
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	image{
		width: 60rpx;
		height: 60rpx;
	}
}
</style>