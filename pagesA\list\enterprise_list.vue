<template>
    <view class="" :class="isForbidden?'forbidden':''">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="filters">
                    <view class="filter" @click="selectCity">
                        {{userCity.name}}
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="search-wrap">
                        <u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="true"
                            v-model="keyword" @clear="clear" @custom="custom"></u-search>
                    </view>
                    <!-- <view class="filter" @click="openFilter">
                        <image src="/pagesA/static/images/filter1.png" mode=""></image>
                    </view> -->
                </view>
            </view>
        </u-sticky>

        <view class="list">
            <enterprise-item v-for="(item,index) in list" :key="index" :item="item"></enterprise-item>
        </view>

        <filter ref="filterRef" @close='close'></filter>
        <Pages :status="status"></Pages>
    </view>
</template>

<script>
    import {
        getEnterpriseList,
		getJobFairCompany
    } from "../../config/api.js"
    import EnterpriseItem from "../components/enterpriseItem.vue"
    import Filter from "../../components/filter.vue"
    import Pages from "../../components/pages.vue";
    export default {
        components: {
            Pages,
            EnterpriseItem,
            Filter
        },
        data() {
            return {
                userCity: {
                    name: '全国',
                    id: 0
                },
                status: 'loadmore',
                more: false,
                page: 1,
                limit: 10,
                list: [],
                isForbidden: false,
                keyword:''
            }
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getEnterpriseList()
            } else {
                this.status = 'nomore'
            }
        },
        onLoad() {
            // 用户选择的城市从本地获取name、id
            if (uni.getStorageSync('userCity')) {
                this.userCity = uni.getStorageSync('userCity');
            }
            this.getEnterpriseList()
        },
        onShow() {
            const cityOld = this.userCity.id; // 当前城市ID
            const cityNew = uni.getStorageSync('userCity').id; // 从缓存中获取选中的城市ID

            if (uni.getStorageSync('userCity') && cityNew != cityOld) {
                this.userCity = uni.getStorageSync('userCity'); // 更新当前城市
                this.page = 1;
                this.list = [];
                this.getEnterpriseList(); // 刷新列表数据
            }
        },
        methods: {
            selectCity() {
                uni.navigateTo({
                    url: '/pagesA/components/selectCitys'
                })
            },
            openFilter() {
                this.isForbidden = true
                this.$refs.filterRef.open()
            },
            close(v) {
                this.isForbidden = v
            },
            clear() {
                this.keyword = '';
                this.page = 1
                this.list = []
                this.getEnterpriseList();
            },
            custom() {
                console.log("keyword",this.keyword)
                this.page = 1
                this.list = []
                this.getEnterpriseList();
            },
            async getEnterpriseList() {

                let params = {
                    page: this.page,
                    limit: this.limit,
                    city_id:this.userCity.id,
                    name: this.keyword,
                }

                const {
                    status_code,
                    data,
                    message
                } = await getEnterpriseList(params)

                if (status_code == 200) {
                    this.more = data.more
                    this.list = this.list.concat(data.data);
                }
            }
        }
    }
</script>
<style>
    page {
        background-color: #f5f5f7;
    }
</style>
<style lang="less" scoped>
    .forbidden {
        overflow: hidden;
        position: fixed;
    }

    .header {
        .filters {
            display: flex;
            align-items: center;
            justify-content: space-between;
            // margin-top: 32rpx;
            padding: 22rpx 32rpx;

            .search-wrap {
                flex: 1;
                margin: 0 16rpx;
            }

            .filter {
                display: flex;
                align-items: center;
                height: 48rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 0 12rpx;
                border-radius: 8rpx;

                image {
                    width: 32rpx;
                    height: 32rpx;
                }
            }
        }
    }

    .list {
        padding: 0 32rpx;
    }
</style>