<template>
	<view class="register-one">

		<view class="wrap">
			<view class="pic">
				<view class="title">
					营业执照<text class="star">*</text>
				</view>

				<view class="pic-list">
					<view class="pic-item">
						<image src="../../static/images/index/up.png" mode=""></image>
					</view>
				</view>
			</view>
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						是否法人本人认证
					</view>
					<view class="in se">
						<!-- <picker @change="changeHerSelf" :value="herSelfIndex" :range="herSelfList"
							range-key="name">
							<view class="d-picker">{{herSelfList[herSelfIndex]['name']}}</view>
						</picker> <u-icon name="arrow-right"></u-icon> -->
						
						
						<picker @change="changeHerSelf" :value="herSelfIndex" :range="herSelfList" range-key="name">
							<view class="d-picker" :style="{color:herSelfIndex==0?'#c0c4cc':'#303133'}">{{herSelfList[herSelfIndex]['name']}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						法人姓名<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入企业名称" fontSize="32rpx" :placeholderStyle="placeholderStyle"
							border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						法人身份证号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入社会统一代码" fontSize="32rpx" :placeholderStyle="placeholderStyle"
							border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						法人手机号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入手机号" fontSize="32rpx" :placeholderStyle="placeholderStyle"
							border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						所在地区<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="bindPickerChange" :value="eduIndex" :range="edu">
							<view class="d-picker">{{edu[eduIndex]}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						详细地址<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入邮箱" fontSize="32rpx" :placeholderStyle="placeholderStyle"
							border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						门牌号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入企业简称" fontSize="32rpx" :placeholderStyle="placeholderStyle"
							border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>
			</view>

			<view class="pic">
				<view class="title">
					法人身份证正反面<text class="star">*</text>
				</view>

				<view class="pic-list">
					<view class="pic-item">
						<image src="../../static/images/index/up.png" mode=""></image>
					</view>
					<view class="pic-item">
						<image src="../../static/images/index/up.png" mode=""></image>
					</view>
				</view>
			</view>

			<view class="pic">
				<view class="title">
					上传资质<text class="star" v-if="type=='headhunters'">*</text>
				</view>

				<view class="pic-list">
					<view class="pic-item">
						<image src="../../static/images/index/up.png" mode=""></image>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="next gray">
				注册
			</view>
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: 'company',
				showStart: false,
				showEnd: false,
				tips: '',
				value: '',
				herSelfIndex: 1,
				herSelfList: [
					{
						value: 0,
						name: '请选择'
					}, {
						value: 1,
						name: '本人'
					}, {
						value: 2,
						name: '非本人'
					}
				],
				placeholderStyle: {
					'fontSize': '32rpx'
				},
				formList: [{
					id: 1,
					name: '武汉大学',
					start: '2024-11-04',
					end: '2024-11-04'
				}, ]
			}
		},
		onLoad(options) {
			this.type = options.type
			this.start = uni.$u.timeFormat(this.start, 'yyyy-mm-dd');
			this.end = uni.$u.timeFormat(this.end, 'yyyy-mm-dd');
		},
		methods: {
			bindStartDateChange(item, e) {
				let id = item.id
				let index = this.formList.findIndex(v => v.id == id)
				this.formList[index]['start'] = e.detail.value
			},
			bindEndDateChange(item, e) {
				let id = item.id
				let index = this.formList.findIndex(v => v.id == id)
				this.formList[index]['end'] = e.detail.value
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			change(e) {
				console.log('change', e);
			},
			add() {
				this.formList.push({
					id: this.formList.length + 1,
					name: '',
					start: '',
					end: ''
				})
			},
			
			changeHerSelf(e){
				console.log(e,"点击")
				this.herSelfIndex = e.detail.value
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
		/* background-image: url('/static/images/login/bg.png');
		background-position: 100% 100%;
		background-size: 100% 100%;
		background-repeat: no-repeat; */
		font-family: PingFang SC, PingFang SC;
	}

	/* .placeholderClass{
		font-weight: 400;
		font-size: 32rpx;
		color: red;
	} */
</style>
<style lang="less" scoped>
	.register-one {
		display: flex;
		flex-direction: column;
		padding-bottom: 178rpx;
	}

	.wrap {
		padding: 30rpx;

		.pic {
			display: flex;
			flex-direction: column;
			margin-bottom: 32rpx;

			.title {

				font-weight: 500;
				font-size: 32rpx;
				color: #333333;
				margin: 16rpx 0;

				.star {
					font-weight: 600;
					font-size: 22rpx;
					color: #FE4D4F;
					margin-left: 8rpx;
				}
			}

			.pic-list {
				display: flex;
				flex-wrap: wrap;

				.pic-item {
					margin-right: 24rpx;

					&:last-child {
						margin-right: 0;
					}

					image {
						width: 200rpx;
						height: 180rpx;
					}
				}
			}
		}

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			margin-bottom: 32rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}



				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;
					
					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}
					
					::v-deep picker{
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;
						.d-picker{
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}

				.fb {
					font-size: 32rpx;
				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 24rpx;
		height: 64rpx;
		width: 260rpx;
		// margin-left: 32rpx;
		background: #4F8CF0;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
		border-radius: 8rpx;
	}

	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}

		.gray {
			background-color: #cccccc;
			color: #FFFFFF;
		}
	}
</style>