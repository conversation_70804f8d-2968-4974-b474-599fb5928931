<template>
	<view class="home-index">
<!--		<view class="pos">-->
<!--			<view class="name">-->
<!--				是否能获取我的定位-->
<!--			</view>-->
<!--			<u-switch v-model="value" activeColor="#4F8CF0" size="18" @change="change"></u-switch>-->
<!--		</view>-->
		<view class="list">
			<view class="item" v-for="(item,index) in list" @click="goFavor(item.id)" >
				<view class="item-left">
					<text>{{ item.title }}</text>
				</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
		</view>
	</view>
</template>

<script>
  import {articleList} from "../../config/common_api";

  export default{
		data(){
			return{
        list:[],
				value:false
			}
		},
    onLoad() {
      this.articleList()
    },
    methods: {
      goFavor(id) {
		uni.navigateTo({
		  url: "/pagesA/list/privacyDetails?id="+id
		})
      },
      async articleList() {
        let params = {
          category_tag_name: ['YS','GZ']
        }
        const {
          status_code,
          data
        } = await articleList(params)
        if (status_code == 200) {
          this.list = data;
		  console.log('this.list',this.list)
        }
      },
    }
	}
</script>
<style>
	page{
		background: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.home-index{
		padding: 32rpx;
	}
	.pos{
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-family: PingFang SC, PingFang SC;
		height: 100rpx;
		font-weight: 600;
		font-size: 28rpx;
		color: #333333;
		background: #FFFFFF;
		margin-bottom: 32rpx;
		padding: 0 48rpx;
		border-radius: 50px;
	}
	.list{
		display: flex;
		flex-direction: column;
		background: #FFFFFF;
		padding: 0 48rpx;
		border-radius: 24rpx;
		z-index: 10;
		.item{
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 100rpx;
			.item-left{
				display: flex;
				align-items: center;
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;
				text{
					margin-left: 24rpx;
				}
			}
		}
	}
</style>