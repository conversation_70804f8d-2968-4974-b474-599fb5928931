<template>
    <view id="app">
        <view class="releaseBox">
            <view class="releaseBox_top">
                <view class="releaseBox_top_left">
                    <image :src="details.send_member_info.image.path_url" mode="" class="releaseBox_head"></image>
                    <view>{{details.send_member_certification.name}}</view>
                </view>
                <view class="releaseBox_top_right">{{details.member.cellphone}}</view>
            </view>
            <view class="releaseBox_bottom">
                <view>已招人数</view>
                <view class="blueNum">{{details.need_count}}</view>
            </view>
        </view>


        <view class="contentBox">
            <view class="gridItem" v-for="(taskPromotion,index) in taskPromotions" :key="index" :item="taskPromotion">
                <view class="gridItem_top">
                    <image :src="taskPromotion.member_info.image.path_url" mode="" class="peopleHeadCla"></image>
                    <view style="width: 100%;">
                    	<view style="display: flex; justify-content: space-between;">
                    	    <view class="peopleNameCla">{{ taskPromotion.member_certification.name?taskPromotion.member_certification.name:taskPromotion.member_info.nick_name }}</view>
                    	    <view class="gridItem_btm">
                    	        <view class="gridItem_btm_color">{{ taskPromotion.audit_status_name }}</view>
                    	    </view>
                    	</view>
                    	<view  class="gridItem_right">
                    		<view class="peopleTextCla">
                    		    {{ taskPromotion.member_info.sex_str }} | {{ taskPromotion.member_info.education_type_name
                    		    }} | {{ taskPromotion.member_info.age }}岁
                    		</view>
                    		
                    		<view class="gridItem_btm">
                    		    <view class="gridItem_btm_color">{{ taskPromotion.created_at }}</view>
                    		</view>
                    	</view>
                    </view>
                </view>
                <!--gridItemBtn_blue 审核 已入职  gridItemBtn_grey 未满足 已满足 -->
                <!-- <view class="gridItemBtn gridItemBtn_blue" @click="showActionSheet">审核</view> -->

            </view>
        </view>
        <u-action-sheet :actions="list" :closeOnClickOverlay="true" :closeOnClickAction="true"
                        :show="show"></u-action-sheet>
    </view>
</template>

<script>

    import {
        getTaskIndexDetail, getTaskPromotion
    } from "../../config/member_api";

    export default {
        data() {
            return {
                task_user_id: 0,
                options: ['已入职', '未入职', '已满足条件', '未满足条件'], // 选项列表
                details: {},
                taskPromotions: [],
            }
        },
        onLoad(option) {
            var _this = this;

            _this.task_user_id = option.task_user_id;
            this.getTaskIndexDetail();
            this.taskPromotionList();
        },
        methods: {
            // 获取领取任务记录
            async getTaskIndexDetail(id) {
                var _this = this;
                _this.show = true
                let params = {
                    task_user_id: _this.task_user_id
                }

                const res = await getTaskIndexDetail(params)

                _this.details = res.data;
            },
            taskPromotionList() {
                var _this = this;

                getTaskPromotion({
                    task_user_id: _this.task_user_id
                }).then(response => {
                    _this.taskPromotions = response.data;
                });
            },
            showActionSheet() {
                uni.showActionSheet({
                    itemList: this.options, // 显示的选项
                    success: (res) => {
                        console.log("选中的条件下标", res.tapIndex)
                    },
                    fail: (err) => {
                        console.error(err); // 处理错误
                    },
                });
            },
        }
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .releaseBox {
        padding: 32rpx;
        background: #FFFFFF;
        border-radius: 24rpx;
        margin-bottom: 32rpx;
    }

    .releaseBox_top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2rpx solid #F5F5F7;
        padding-bottom: 24rpx;
    }

    .releaseBox_head {
        width: 48rpx;
        height: 48rpx;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        margin-right: 24rpx;
    }

    .releaseBox_top_left {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
    }

    .releaseBox_top_right {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .releaseBox_bottom {
        margin-top: 24rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }

    .blueNum {
        color: #4F8CF0;
    }

    .contentBox {
        width: 100%;
        grid-template-columns: repeat(2, 1fr);
        gap: 24rpx 22rpx;
    }

    .gridItem {
        width: 100%;
        height: auto;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 24rpx;
		margin-bottom: 30rpx;
    }

    .gridItem_top {
        display: flex;
        align-items: center;
    }
	.gridItem_right {
		display: flex;
		justify-content: space-between;
	}

    .peopleHeadCla {
        width: 82rpx;
        height: 82rpx;
        background: #D9D9D9;
        border-radius: 10rpx;
        margin-right: 16rpx;
    }

    .peopleNameCla {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 8rpx;
    }

    .peopleTextCla {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .gridItem_btm {
        display: flex;
        align-items: center;
		align-content: space-between;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .gridItem_btm_color {
        color: #333333;
    }

    .gridItemBtn {
        width: 100%;
        height: 56rpx;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 56rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
    }

    .gridItemBtn_blue {
        background: #4F8CF0;
    }

    .gridItemBtn_grey {
        background: #CCCCCC;
    }
</style>