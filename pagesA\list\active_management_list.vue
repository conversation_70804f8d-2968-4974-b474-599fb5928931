<template>
	<view class="">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="search-wrap">
					<u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
				</view>
				<view class="filters">
					<view class="filter">
						郑州<image src="/static/images/index/down.png" mode=""></image>
					</view>
					<view class="filter">
						筛选<image src="/static/images/index/down.png" mode=""></image>
					</view>
				</view>
			</view>
		</u-sticky>
		
		<view class="list">
			<active-item v-for="(item,index) in list" :key="index" :item="item"></active-item>
		</view>
	</view>
</template>

<script>
	import ActiveItem from '../components/activeItem.vue'
	export default{
		components:{
			ActiveItem
		},
		data(){
			return{
				list:[
					{
						status:1//进行中
					},
					{
						status:2//已结束
					},
					{
						status:3//审核中
					},
					{
						status:4//已驳回
					},
					{
						status:5//未开始
					}
				]
			}
		}
	}
</script>
<style>
	page{
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.header {
		padding: 32rpx;
		.search-wrap {
		}
	
		.filters {
			display: flex;
			margin-top: 32rpx;
	
			.filter {
				display: flex;
				align-items: center;
				height: 48rpx;
				background-color: #FFFFFF;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				padding: 0 12rpx;
				margin-right: 12rpx;
				border-radius: 8rpx;
	
				image {
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
	}
	
	.list{
		padding: 0 32rpx 32rpx 32rpx;
	}
</style>