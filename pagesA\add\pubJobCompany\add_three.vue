<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="top"> 完善客户关系 </view>
    <view class="header">
      <view class="title">该公司下属品牌</view>
    </view>
    <view class="inp">
      <view class="inp-item" @click="goIn">
        <view class="title"> 合作模式 </view>
        <view class="in se">
          <view class="d-picker" :style="{ color: '#c0c4cc' }">
            {{ keyword.name ? keyword.name : "请选择合作模式" }}
          </view>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
    </view>
    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next" @click="next"> 下一步 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      keyword: "",
    };
  },

  onLoad() {
    if (uni.getStorageSync("hezuo_mode")) {
      this.keyword = uni.getStorageSync("hezuo_mode");
      console.log("hezuo_mode", uni.getStorageSync("hezuo_mode"));
    }
  },

  methods: {
    goIn() {
      uni.navigateTo({
        url: "/pagesA/add/pubJobCompany/add_four",
      });
    },
    next() {
      uni.removeStorageSync("hezuo_mode");
      uni.navigateTo({
        url: "/pagesA/add/pubJobCompany/add_five",
      });
    },
  },
};
</script>
<style lang="scss">
@import "../../../static/css/pagesA/add/pubJobOne";
</style>
<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
}
.top {
  height: 124rpx;
  background: #4f8cf0;
  font-size: 32rpx;
  line-height: 124rpx;
  padding-left: 32rpx;
  color: #ffffff;
}
.header {
  margin-bottom: 32rpx;
  padding: 24rpx 32rpx 0 24rpx;

  .title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 24rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

.search-box {
  padding: 32rpx 0;

  position: relative;
  margin-bottom: 32rpx;

  .count {
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24rpx;
    color: #999;
  }
}

.company-list {
  padding: 32rpx 0;
  .company-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx 0rpx;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;

    .company-logo {
      width: 88rpx;
      height: 88rpx;
      background: #d9d9d9;
      border-radius: 8rpx;
      margin-right: 24rpx;
    }

    .company-info {
      flex: 1;

      .company-name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;
      }
      .tag {
        font-size: 20rpx;
        color: #4f8cf0;
        background-color: #ecf5ff;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
      }
      .company-type {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 12rpx;
      }
      .tags {
        width: 176rpx;
        height: 50rpx;
        background: #f4f4f4;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-size: 24rpx;
        color: #777777;
        text-align: center;
        line-height: 50rpx;
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  border-radius: 24rpx 24rpx 0 0;

  .next {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    width: 90%;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
    margin-top: 24rpx;
  }
}
</style>
