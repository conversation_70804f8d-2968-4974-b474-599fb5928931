page {
    background-color: #f5f5f7;
}

.main {
}

.list {
    padding: 32rpx;
}

.footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    bottom: 0;
    height: 120rpx;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 10;
    border-radius: 24rpx 24rpx 0 0;
    .next {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        color: #FFFFFF;
        height: 88rpx;
        width: 90%;
        border-radius: 16rpx;
    }
    
    .sure {
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        color: #FFFFFF;
    }
}