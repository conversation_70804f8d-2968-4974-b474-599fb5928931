<template>
	<view class="register-one">
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						账号 <text class="star">*</text>
					</view>
					<view class="in">
						{{ formData.cellphone }}
					</view>
				</view>
			</view>
		</view>
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						密码<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入密码" clearable placeholderClass="placeholderClass" type="password"
							border="none" v-model="formData.password" ></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						确认密码<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入确认密码" clearable placeholderClass="placeholderClass" type="password"
							border="none" v-model="formData.password_confirmation" ></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						验证码<text class="star">*</text>
					</view>
					<view class="in">
						<!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
						<!-- #ifndef APP-NVUE -->
						<u-input placeholder="请输入验证码" clearable placeholderClass="placeholderClass" border="none"
							v-model="formData.code">
						<!-- #endif -->
							<!-- #ifdef APP-NVUE -->
							<u--input placeholder="请输入验证码" clearable placeholderClass="placeholderClass">
							<!-- #endif -->
								<template slot="suffix">
									<u-code ref="uCode" @change="codeChange" seconds="60" changeText="X秒重新获取"></u-code>
									<u-button @tap="getCode" :text="tips" type="success" size="mini"></u-button>
								</template>
						<!-- #ifndef APP-NVUE -->
						</u-input>
						<!-- #endif -->
						<!-- #ifdef APP-NVUE -->
						</u--input>
						<!-- #endif -->
					</view>
				</view>
			</view>
		</view>


		<view class="footer">
			<view class="next save" @click="but">
				更改完成
			</view>
		</view>
	</view>
</template>

<script>
import {resetPassword} from "@/config/api.js"
import {login} from "../../config/api";
	export default {
		data() {
			return {
        formData:{
          cellphone: "", //登录用的手机号
          code: '', //验证码
          password: '', //密码
          password_confirmation: '', //确认密码
        },
				tips: '',
				value: '',
				form: {
					password: '', //密码
					repassword: '', //重复密码
				},
				placeholderStyle: {
					color: '#999999'
				}
			}
		},
    computed: {

      userInfo(){
        let userInfo = this.$store.state.userInfo|| uni.getStorageSync('userInfo')
        this.formData.cellphone = userInfo.member.cellphone
        return userInfo;
      }

    },
		methods: {
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
      async but(){
        if(this.formData.password != this.formData.password_confirmation){
          uni.$u.toast('两次密码不一致');
          return
        }
        await resetPassword(this.formData).then(res=>{
          if(res.status_code == 200){
            uni.$u.toast('修改成功');
            setTimeout(() => {
              uni.switchTab({
                url: "/pages/my/my"
              })
            }, 1000);
          }else{
            uni.$u.toast(res.message);

          }
        })

      }
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
		/* background-image: url('/static/images/login/bg.png');
		background-position: 100% 100%;
		background-size: 100% 100%;
		background-repeat: no-repeat; */
	}
</style>
<style lang="less" scoped>
	.register-one {
		padding-bottom: 178rpx;
	}

	.wrap {
		margin-top: 32rpx;
		padding: 0 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}

					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}



				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #c0c4cc;
				}
			}
		}
	}

	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 24rpx;
		}

		.save {
			background: #4F8CF0;
			color: #FFFFFF;
		}

		.gray {
			background-color: #cccccc;
			color: #FFFFFF;
		}
	}
</style>