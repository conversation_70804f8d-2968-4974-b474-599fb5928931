<template>
	<view id="app">
		<view class="content">
			<view class="titleBox">姓名 <text class="redIcon">*</text> </view>
			<input type="text" placeholder="请输入姓名" class="ipt"/>
			<view class="hrBox"></view>
			
			<view class="titleBox">手机号码 <text class="redIcon">*</text> </view>
			<input type="number" placeholder="请输入手机号码" class="ipt"/>
			<view class="hrBox"></view>
			
			<view class="titleBox">邮箱 <text class="redIcon">*</text> </view>
			<input type="text" placeholder="请输入邮箱" class="ipt"/>
			<view class="hrBox"></view>
			
			<view class="titleBox">公司或学校 <text class="redIcon">*</text> </view>
			<input type="text" placeholder="请输入公司或学校名称" class="ipt"/>
			<view class="hrBox"></view>
			
			<view class="titleBox">备注 <text class="redIcon">*</text> </view>
			<input type="text" placeholder="请输入备注" class="ipt"/>
			<view class="hrBox"></view>
		</view>
		
		
		<view style="height: 196rpx;"></view>
		
		<view class="bottomBox">
			<view class="bottomBox_btn">确定</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	view {
		box-sizing: border-box;
	}
	
	#app {
		width: 100%;
		padding: 32rpx;
	}
	.content {
		background: #fff;
		padding: 32rpx;
		border-radius: 24rpx;
	}
	
	.titleBox {
		font-weight: 400;
		font-size: 22rpx;
		color: #666666;
		margin-bottom: 16rpx;
	}
	
	.redIcon {
		color: #FE4D4F;
		padding-left: 8rpx;
	}
	
	.ipt {
		font-weight: 400;
		font-size: 32rpx;
		color: #999999;
	}
	
	.hrBox {
		width: 100%;
		height: 2rpx;
		background: #F5F5F7;
		margin: 24rpx 0;
	}
	
	.bottomBox {
		width: 100%;
		height: 196rpx;
		background: #FFFFFF;
		box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0,0,0,0.05);
		border-radius: 24rpx 24rpx 0 0;
		position: fixed;
		bottom: 0;
		left: 0;
		padding: 24rpx 32rpx 92rpx 32rpx ;
	}
	
	.bottomBox_btn {
		width: 100%;
		height: 80rpx;
		background: linear-gradient( 135deg, #4F8CF0 0%, #1E6DEE 100%);
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		text-align: center;
		line-height: 80rpx;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
	}
</style>