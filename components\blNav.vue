<template>
	<view>
		<view class="pannel">
			<view class="pannel-item" v-if="roleType == 'company'" @click="goObtain">
				<image
					class="pannel-item-img"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/lBJpgI0XRMLo17xqaHmHxvr17futeNLhBJUtLdVe.png"
					mode=""></image>
				<text class="name">优秀就业管家</text>
			</view>
			<view class="pannel-item" v-if="roleType == 'headhunters'" @click="goEnterprise">
				<image
					class="pannel-item-img"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/eJTeETpVCkukhvtH5c74vRpVjNeZPtjQb5l58Hub.png"
					mode=""></image>
				<text class="name">优秀企业</text>
			</view>
			<view class="pannel-item" @click="goQlmJob(0)">
				<image
					class="pannel-item-img"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/FXqIXlVPd9NbAWqwxf1kvryfDum5ViskMFm40BgH.png"
					mode=""></image>
				<text class="name">推荐社招</text>
			</view>
			<view class="pannel-item" @click="goQlmJob(1)">
				<image
					class="pannel-item-img"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/1UnMQOMClr2MlvjgE4rgaFN2BIWXzQ1Svm3X2ya2.png"
					mode=""></image>
				<text class="name">校园实习</text>
			</view>
			<view class="pannel-item" @click="goQlmJob(2)">
				<image
					class="pannel-item-img"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/LQv9cRVLHUzLE2HXqVDuMsVuymOFfZZa8G2im7OT.png"
					mode=""></image>
				<text class="name">零工市场</text>
			</view>
			<view class="pannel-item" @click="goJobfair">
				<image
					class="pannel-item-img"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/IVjwbZrsKidQBEyvAbNNafdWYrVCbpLVVIvORcHP.png"
					mode=""></image>
				<text class="name">招聘会</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'blNav',
	props: {},
	computed: {
		roleType() {
			return this.$store.state.roleType || uni.getStorageSync('roleType');
		},
	},
	data() {
		return {};
	},
	methods: {
		goQlmJob(index) {
			uni.navigateTo({
				url: '/pagesA/list/qlm_list?tabIndex=' + index,
			});
		},
		goObtain() {
			uni.navigateTo({
				url: '/pagesA/list/obtain_management_list',
			});
		},
		goEnterprise() {
			uni.navigateTo({
				url: '/pagesA/list/enterprise_list',
			});
		},
		goJobfair() {
			uni.navigateTo({
				// url: "/pagesA/list/job_fair_list"
				url: '/pagesA/details/qlmActiveShow',
			});
		},
	},
};
</script>

<style lang="less" scoped>
.pannel {
	display: flex;
	padding: 0 32rpx;
	margin-top: 32rpx;

	.pannel-item {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		flex: 1;

		.pannel-item-img {
			width: 78rpx;
			height: 78rpx;
		}

		.name {
			font-weight: 400;
			font-size: 22rpx;
			color: #000000;
			margin-top: 16rpx;
		}
	}
}
</style>
