<template>
	<view class="item" @click="openDetails(item)">
		<!-- <block v-if="item.status==2 || item.status==3 || item.status==4">
			<view :class="['status',item.status==2||item.status==3?'wait':item.status==4?'yqx':'']">
				{{item.status==2?'等待面试':item.status==3?'等待结果':item.status==4?'已取消':''}}
			</view>
		</block> -->
		<!-- <block v-if="item.sign_status=='singed' && item.interview_result.length <= 0">
            <view class="status wait">
                等待面试
            </view>
        </block> -->
		<!-- v-else -->
		<block>
			<view class="status greenBtn" v-if="item.interview_result.result_status=='pass'">
				通过
			</view>
			<view class="status redBtn" v-if="item.interview_result.result_status=='eliminate'">
				淘汰
			</view>
			<view class="status yellowBtn" v-if="item.interview_result.result_status=='alternate'">
				备选
			</view>
			<view class="status greyBtn" v-if="item.interview_result.result_status=='none'">
				未面试
			</view>
		</block>

		<view class="item-up">
			<view class="info">
				<image :src="item.member_info.image.path_url" mode=""></image>
				<view class="cont">
					<view class="sup">
						<view class="name">
							{{ item.member_certification.name ? item.member_certification.name : item.member_info.nick_name}}
						</view>
						<!-- <view class="money">
							{{item.job.salary_info_str}}
						</view> -->
					</view>
					<view class="sub">
						{{item.job.title}}
					</view>
				</view>
			</view>
			<view class="date">
				<view class="time">
					面试时间：{{item.interviewed_at}}
				</view>
				<view class="more">
					<u-icon name="arrow-right" size="24rpx"></u-icon>
				</view>
			</view>
		</view>

		<view :class="['item-down',item.status==1||item.status==2||item.status==3?'flexRow':'']">
			<view class="reason" v-if="item.cancel_status==1">
				取消原因：{{item.cancel_reason}}
			</view>
			<view class="btns">
				<view class="btn agree" @click.stop="openRes">
					结果反馈
				</view>
				<view class="btn talk" @click.stop="communicate('interview',item.id)">
					聊聊呗
				</view>
				<view class="btn agree" v-if="item.sign_status=='singed'" @click.stop="openApprove">
					审核
				</view>
			</view>
		</view>
		<!-- 审核理由 -->
		<u-popup :show="isShow" round="10" mode="center" @close="closeApprove">
			<view class="popup-content">
				<view class="popup-title">请输入审核理由</view>
				<u-input v-model="inputText" placeholder="请输入理由" type="text" :border="true" />
				<view class="popup-buttons">
					<u-button @click="closeApprove">取消</u-button>
					<u-button type="primary" @click="confirmApprove">确定</u-button>
				</view>
			</view>
		</u-popup>
		<!-- 结果反馈 -->
		<u-popup :show="isShowtwo" round="10" mode="center" @close="closeApprove">
			<view class="popup-content">
				<view class="popup-title">结果反馈</view>
				<view class="popup-title">{{item.member_certification.name}}：{{item.remark}}</view>
				<view class="popup-buttons">
					<u-button type="primary" @click="confirmtwo">确定</u-button>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		communicate
	} from "../../common/common";
	export default {
		name: "companyInterViewItem",
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
				inputText: '',
				isShow: false,
				isShowtwo: false
			};
		},
		methods: {
			communicate,
			openDetails() {
				this.$emit('open', this.item)
			},

			// 打开弹窗
			openApprove() {
				this.isShow = true; // 显示弹窗
			},
			openRes() {
				this.isShowtwo = true
			},

			// 用户点击弹窗中的确定按钮
			confirmApprove() {
				this.isShow = false; // 关闭弹窗
				this.$emit('approve', {
					item: this.item,
					status: 1, // 审核状态
					examine_data: this.inputText, // 用户输入的文本
				});
				this.inputText = ''; // 清空输入框
			},

			// 关闭弹窗
			closeApprove() {
				this.isShow = false;
				this.inputText = ''; // 清空输入框
			},
			closetwo() {
				this.isShowtwo = false
			},
			confirmtwo() {
				this.isShowtwo = false
			}
			// approve(status) {
			// 	this.$emit('approve', {
			// 		item: this.item,
			// 		status: status
			// 	})
			// },
		}
	}
</script>

<style lang="less" scoped>
	.item {
		position: relative;
		display: flex;
		flex-direction: column;
		padding: 0 32rpx;
		margin-bottom: 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;

		.item-up {
			display: flex;
			flex-direction: column;
			padding: 24rpx 0;

			.info {
				display: flex;

				&>image {
					width: 96rpx;
					height: 96rpx;
				}

				.cont {
					display: flex;
					justify-content: space-between;
					flex-direction: column;
					padding-left: 24rpx;
					flex: 1;

					.sup {
						display: flex;

						.name {
							font-weight: 600;
							font-size: 32rpx;
							color: #333333;
						}

						.money {
							font-weight: 600;
							font-size: 32rpx;
							color: #F98A14;
							margin-left: 16rpx;
						}
					}

					.sub {
						font-weight: 400;
						font-size: 24rpx;
						color: #999999;
					}
				}

			}

			.date {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-top: 24rpx;

				.time {
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
				}
			}

		}



		.item-down {
			display: flex;
			// justify-content: space-between;
			align-items: center;
			padding: 0 0 24rpx 0;

			.btns {
				display: flex;

				.btn {
					display: flex;
					justify-content: center;
					align-items: center;
					margin-left: 24rpx;
					border-radius: 8rpx;
					font-weight: 600;
					padding: 0 24rpx;
					height: 56rpx;
					font-size: 24rpx;
					color: #FFFFFF;

					&:first-child {
						margin-left: 0;
					}
				}

				.agree {
					background-color: #4F8CF0;
					color: #FFFFFF;
				}

				.refuse {
					background-color: #FE4D4F;
					color: #FFFFFF;
				}

				.talk {
					background: rgba(79, 140, 240, 0.1);
					color: #4F8CF0;
				}
			}

			.reason {
				font-weight: 600;
				font-size: 24rpx;
				color: #FE4D4F;
			}
		}

		.flexRow {
			display: flex;
			flex-direction: row-reverse;
		}


		.status {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			right: 0;
			top: 0;
			font-size: 28rpx;
			width: 132rpx;
			height: 62rpx;
			border-radius: 0 24rpx 0 24rpx;
		}

		.wait {
			background: #4F8CF0;
			color: #FFFFFF;
		}

		.yqx {
			background: #cccccc;
			color: #FFFFFF;
		}

	}

	.greenBtn {
		background: #57D51C;
		color: #FFFFFF;
	}

	.redBtn {
		background: #FE4D4F;
		color: #FFFFFF;
	}

	.yellowBtn {
		background: #F9AD14;
		color: #FFFFFF;
	}

	.greyBtn {
		background: #cccccc;
		color: #FFFFFF;
	}
	.popup-content {
	    padding: 32rpx;
	    width: 600rpx;
	    background-color: #fff;
	    border-radius: 24rpx;
	
	    .popup-title {
	        font-size: 32rpx;
	        font-weight: bold;
	        margin-bottom: 24rpx;
	        text-align: center;
	    }
	
	    .popup-buttons {
	        display: flex;
	        justify-content: space-between;
	        margin-top: 32rpx;
	
	        u-button {
	            flex: 1;
	            margin: 0 16rpx;
	        }
	    }
	}
</style>