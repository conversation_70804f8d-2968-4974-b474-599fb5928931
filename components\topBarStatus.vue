<template>
	<!-- <view class="top"> -->
	<!-- 顶部占用30px,导致空间不够 -->
	<view class="top" :style="{ 'padding-top': searchHeight }">
		<!-- <view :style="{'height':navHeight}">
			<view :style="{'margin-top':searchMarginTop, 'height': searchHeight,'width': searchWidth}"></view>
		</view> -->
		<image src="https://api-test.zhaopinbei.com/storage/uploads/images/S02FNzZONTp10mb1VOc0vWdoeyiIIQsVXL7GB2li.png" mode=""></image>
		<view class="">
			<view class="statusBarHeight"></view>
			<!-- <view class="top_box" :style="{padding: `${menuPaddingTB}px ${menuPaddingLF}px`,height: `${menuHeight}px`, width: `${menuWidth}px`}">
				<view style="height: 100%;">
					<slot />
				</view>
			</view> -->
			<view class="top_box" :style="{ paddingLeft: `${menuPaddingLF}px`, height: `${menuHeight}px` }">
				<view style="height: 100%">
					<slot />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			statusHeight: 0,
			menuPaddingTB: 0,
			menuPaddingLF: 0,
			menuHeight: 0,
			menuWidth: 0,
			disHeight: 0,

			menuButtonInfo: {},
			navHeight: 0,
			searchMarginTop: 0, // 搜索框上边距
			searchWidth: 0, // 搜索框宽度
			searchHeight: 0, // 搜索框高度
		};
	},
	mounted() {
		const app = getApp().globalData;
		const { systemInfo, MenuButtonInfo } = app;
		this.statusHeight = systemInfo.statusBarHeight;
		this.menuPaddingTB = MenuButtonInfo.top - systemInfo.statusBarHeight;
		this.menuPaddingLF = systemInfo.screenWidth - MenuButtonInfo.right;
		this.menuHeight = MenuButtonInfo.height + this.menuPaddingTB * 2;
		this.menuWidth = MenuButtonInfo.left;
		this.disHeight = this.menuHeight + this.statusHeight;

		var systeminfo = uni.getSystemInfoSync();
		(this.movehight = systeminfo.windowHeight), (this.movehight2 = systeminfo.windowHeight - 100);

		this.menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		const { top, width, height, right } = this.menuButtonInfo;
		uni.getSystemInfo({
			success: res => {
				const { statusBarHeight } = res;
				const margin = top - statusBarHeight;
				(this.navHeight = height + statusBarHeight + margin * 2 + 'px'),
					(this.searchMarginTop = statusBarHeight + margin + 'px'), // 状态栏 + 胶囊按钮边距
					(this.searchHeight = height + 'px'), // 与胶囊按钮同高
					(this.searchWidth = right - width - 30 + 'px'); // 胶囊按钮右边坐标 - 胶囊按钮宽度 = 按钮左边可使用宽度
			},
		});
	},
	// onLoad() {
	// 	const app = getApp().globalData
	// 	console.log("app:",app)
	// },
	// onShow() {
	// 	const app = getApp().globalData
	// 	console.log("app:",app)
	// 	const { systemInfo, MenuButtonInfo } = app
	// 	console.log("~~~~~~~~",MenuButtonInfo,"打印")
	// 	this.statusHeight = systemInfo.statusBarHeight
	// 	this.menuPaddingTB = MenuButtonInfo.top - systemInfo.statusBarHeight
	// 	this.menuPaddingLF = systemInfo.screenWidth - MenuButtonInfo.right
	// 	this.menuHeight = MenuButtonInfo.height + menuPaddingTB.value * 2
	// 	this.menuWidth = MenuButtonInfo.left
	// }
};
</script>

<style lang="less" scoped>
.top {
	// background-color: red;
	position: relative;

	& > image {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		z-index: -100;
	}
}

.top_box {
	// background-color: green;
	position: relative;
}

.statusBarHeight {
	height: var(--status-bar-height);
	width: 100%;
}
</style>
