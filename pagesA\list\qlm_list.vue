<template>
    <view class="container">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <u-tabs :current="tabIndex" lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" @click="changeTab"></u-tabs>
                <view class="search-wrap">
                    <u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="true"
                              v-model="page.form.keyword" @clear="clear" @custom="custom"></u-search>
                </view>
                <view class="filters">
                    <view class="filter" @click="selectCity">
                        {{userCity.name}}
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <!-- <view class="filter">
                        筛选
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view> -->
                </view>
            </view>

        </u-sticky>

        <view class="list">
            <qlm-job-item v-for="(item,index) in page.data" :key="index" :item="item"></qlm-job-item>
        </view>
        <Pages :status="page.status"></Pages>

    </view>

</template>

<script>
    import TopBarStatus from "../../components/topBarStatus.vue"
    import QlmJobItem from "../../components/qlmJobItem.vue"
    import Pages from "../../components/pages.vue";

    import {getWorkType} from "../../config/api";
    import {data2opt} from "../../common/common";
    import {
        getCompanyMemberList,
    } from "../../config/api.js"

    export default {
        components: {
            Pages,
            TopBarStatus,
            QlmJobItem
        },
        data() {
            return {
                page: {
                    form: {
                        work_type_id: [],
                        page: 1,
                        limit: 10,
                        keyword: "",
                        city_id:0
                    },
                    status: 'loadmore',
                    more: false,
                    data: [],
                },
                isScroll: false,
                tabIndex: 0,
                tabs: [],
                userCity: {
                    name: '全国',
                    id: 0
                },
            }
        },
        //触底加载更多
        onReachBottom() {
            if (this.page.more) {
                this.page.status = 'loading';
                this.page.form.page++;
                this.getMemberList()
            }
        },
        onPageScroll(e) {
            this.isScroll = e.scrollTop > 0
        },
        onLoad(options) {
            this.userCity = uni.getStorageSync('userCity');
            this.page.form.city_id = this.userCity.id
            this.tabIndex = parseInt(options.tabIndex)
            this.workTypeList();
            this.getMemberList();
        },
        onShow() {
            // 用户选择的城市从本地获取name、id
            if (this.userCity.name != uni.getStorageSync('userCity').name) {
                this.userCity = uni.getStorageSync('userCity');
                this.page.form.city_id = this.userCity.id
                this.custom()
            }
        },
        methods: {
            selectCity() {
                uni.navigateTo({
                    url: '/pagesA/components/selectCitys'
                })
            },
            clear() {
                this.page.form.keyword = '';
            },
            custom() {
                this.initPage();
                this.getMemberList();
            },

            workTypeList() {
                getWorkType().then(response => {
                    if (response.status_code == '200') {
                        var data = response.data;
                        this.tabs = data2opt(data);
                        this.tabs.unshift({
                            value: '',
                            name: '全部',
                        });
                    }
                });
            },
            changeTab(e) {
                this.tabIndex = e.index;
                if([this.tabs[this.tabIndex]['name']]!='全部') {
                    this.page.form.work_type_id = [this.tabs[this.tabIndex]['value']];
                } else {
                    this.page.form.work_type_id = [];
                }
                this.initPage();
                this.getMemberList();
            },

            initPage() {
                this.page.data = [];
                this.page.form.page = 1;
                this.page.status = 'loadmore';
            },
            getMemberList() {
                getCompanyMemberList(this.page.form).then(response => {
                    if (response.status_code == '200') {
                        this.page.more = response.data.more
                        this.page.data = this.page.data.concat(response.data.data);
                        this.page.status = this.page.more ? 'loadmore' : 'nomore';
                    }
                });

            },
        }
    }
</script>
<style lang="scss" src="../../static/css/pagesA/list/qlm_list.scss"></style>