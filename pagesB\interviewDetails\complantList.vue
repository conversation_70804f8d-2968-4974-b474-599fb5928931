<template>
	<view class="container">
		<view class="report-item" v-for="(item, index) in reportList" :key="index" @click="open">
			<view class="title">
				<text class="title-text">职位虚假</text>
				<text class="reply-status" v-if="item.isReplied">已回复</text>
			</view>
			<view class="info">
				<text class="time">举报时间: {{ item.reportTime }}</text>
			</view>
			<hr />
			<view class="user-info">
				<view class="avatar">
					<!-- <image :src="item.avatarUrl" mode="aspectFill"></image> -->
				</view>
				<view class="user-details">
					<text class="user-name">{{ item.userName }}</text>
					<text class="user-identity">{{ item.identity }} · 招聘者</text>
				</view>
			</view>
		</view>


	</view>
</template>

<script>
	export default {

		data() {
			return {
				reportList: [{
						isReplied: true,
						reportTime: '2025-04-15 17:13:23',
						avatarUrl: '', // 这里需替换为实际头像地址，若无可用默认占位图地址
						userName: '王哈哈',
						identity: '北京学创联盟'
					},
					{
						isReplied: true,
						reportTime: '2025-04-16 17:13:23',
						avatarUrl: '',
						userName: '王哈哈',
						identity: '北京学创联盟'
					}
				]
			};
		},
		methods: {

		}
	};
</script>

<style scoped>
	.container {
		padding: 20rpx 0px;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: rgba(245, 245, 247, 1);
		height: 100vh;
	}

	.report-item {
		padding: 32rpx;
		height: 306rpx;
		width: 750rpx;
		display: flex;
		flex-direction: column;
		background: rgba(255, 255, 255, 1);
		display: flex;
		margin-bottom: 16rpx;
	}

	.title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 16rpx;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: bold;
	}

	.reply-status {
		color: #108ee9;
		font-size: 28rpx;
	}

	hr {
		width: 686rpx;
		height: 0px;
		border: 1rpx solid rgba(230, 230, 230, 1);
		margin: 40rpx auto;
	}

	.info {
		margin-top: 5px;
		color: #666;
		font-size: 14px;
		padding-left: 16rpx;
	}

	.user-info {
		display: flex;
		align-items: center;
		margin-top: 10px;
		padding-left: 32rpx;
	}

	.avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		overflow: hidden;
		margin-right: 10px;
		background-color: rgba(217, 217, 217, 1);
	}

	.user-details {
		height: 86rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding-left: 32rpx;
	}

	.user-name {
		font-size: 28rpx;
		color: rgba(51, 51, 51, 1);
	}

	.user-identity {
		font-size: 24rpx;
		color: #999;
	}

	.time {
		font-size: 24rpx;
		color: rgba(153, 153, 153, 1);
	}
</style>