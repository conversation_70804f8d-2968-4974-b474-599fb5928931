<template>
    <view class="container">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="search-wrap">
                    <u-search placeholder="请输入面试人姓名" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
                </view>
                <view class="filters">
                    <view class="filter">
                        <picker mode="date" :value="start_date" @change="bindDateChange">
                            <view class="d-picker">{{start_date?start_date:'面试开始时间'}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker mode="date" :value="end_date" @change="bindDateChange">
                            <view class="d-picker">{{start_date?start_date:'面试结束时间'}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker @change="changeStatus" :value="statusIndex" :range="statusList" range-key="name">
                            <view class="d-picker">{{statusList[statusIndex]['name']}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                </view>
            </view>
        </u-sticky>

        <view class="list">
            <inter-view-item v-for="(item,index) in list" :key="index" :item="item" @open="openDetails"
                             @approve="approve" @punch="punch"></inter-view-item>
        </view>


        <u-popup :show="show" round="10" mode="center" @close="close" @open="open">
            <view>
                <view class="wrap">
                    <view class="title">
                        面试详情
                    </view>
                    <view class="items">
                        <view class="item">
                            <text class="lab">面试人：</text>
                            <text class="name">{{currentItem.contact_name}}</text>
                        </view>
                        <view class="item">
                            <text class="lab">报名渠道：</text>
                            <text class="name">活动报名</text>
                        </view>
                        <view class="item">
                            <text class="lab">联系电话：</text>
                            <text class="name">{{currentItem.contact_cellphone}}</text>
                        </view>
                        <view class="item">
                            <text class="lab">面试时间：</text>
                            <text class="name">{{currentItem.interviewed_at}}</text>
                        </view>
                        <view class="item">
                            <text class="lab">面试地址：</text>
                            <text class="name"
							@click="openNavigation(currentItem.addresses[0])">
							{{currentItem.addresses[0]['map_address']}}</text>
                        </view>
                        <view class="item">
                            <text class="lab">面试备注：</text>
                            <text class="name">{{currentItem.remark}}</text>
                        </view>
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
    import {
        interviewSignup,
    } from "../../config/api.js"
    import {commonInterviewList} from "../../config/api";
    import {interviewResult} from "../../config/headhunterList_api.js"
    import InterViewItem from "../components/interViewItem.vue"

    export default {
        components: {
            InterViewItem,
        },
        computed: {},
        data() {

            return {
                page: 1,
                limit: 10,
                status: 'loadmore',
                more: false,
                currentItem: {},
                show: false,
                start_date: '',
                end_date: '',
                tabIndex: 0,
                list: [],
                statusIndex: 0,
                statusList: [{
                    value: 0,
                    name: '面试状态'
                },
                    {
                        value: 1,
                        name: '通过'
                    },
                    {
                        value: 2,
                        name: '备选'
                    },
                    {
                        value: 3,
                        name: '淘汰'
                    }
                ],
                options: ['通过', '备选', '淘汰']
            }
        },
        onLoad() {
            this.getInterviewList()
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getInterviewList()
            } else {
                this.status = 'nomore'
            }
        },
        methods: {
            open() {
                this.show = true
            },
            close() {
                this.show = false
            },

            changeStatus(e) {
                this.statusIndex = e.detail.value
            },

            openDetails(item) {
                this.currentItem = item;
                this.show = true
            },
			
			// 导航方法
			openNavigation(address) {
				wx.openLocation({
					latitude: parseFloat(address.lat), // 纬度，浮点数，范围为-90~90
					longitude: parseFloat(address.lng), // 经度，浮点数，范围为-180~180
					name: address.map_address || '面试地点', // 位置名
					scale: 18 // 地图缩放级别
				})
			},

            //审核
            approve(op) {
                uni.showActionSheet({
                    itemList: this.options, // 显示的选项
                    success: async (res) => {
                        let resultStatus = res.tapIndex == 0 ? 'pass' : res.tapIndex == 1 ? 'alternate' : 'eliminate'
                        let params = {
                            interview_id: op.item.id,
                            result_status: resultStatus
                        }
                        const data = await interviewResult(params)
                        if(data.status_code==200) {
                            this.list.splice(itemIndex,1,data.data)
                        }
                        // this.getInterviewList()
                    },
                    fail: (err) => {

                        console.error(err); // 处理错误
                    },
                });
                // let self = this;
                // const {
                // 	item,
                // 	status
                // } = op
                // uni.showModal({
                // 	title: status == 1 ? '是否同意该面试' : '是否取消该面试',
                // 	success: async function(res) {
                // 		if (res.confirm) {
                // 			let params = {
                // 				interview_id: item.id,
                // 				status,
                // 				remark: status == 1 ? '同意' : '拒绝'
                // 			}
                // 			const {
                // 				status_code,
                // 				data,
                // 				message
                // 			} = await interviewApprove(params)
                // 			if (status_code == 200) {
                // 				uni.$u.toast(message || '成功')
                // 				self.getInterviewList()
                // 			}
                // 			console.log('用户点击确定');
                // 		} else if (res.cancel) {
                // 			console.log('用户点击取消');
                // 		}
                // 	}
                // })
            },


            //打卡
            punch(item) {
                let self = this;
                uni.getLocation({
                    type: 'wgs84',
                    success: async function (res) {
                        let params = {
                            lng: res.longitude,
                            lat: res.latitude,
                            interview_id: item.id
                        }
                        const {
                            status_code,
                            data,
                            message
                        } = await interviewSignup(params)
                        if (status_code == 200) {
                            uni.$u.toast(message || '成功')
                            self.getInterviewList()
                        }
                    }
                });
            },


            //面试列表
            async getInterviewList() {
                const res_userInfo = uni.getStorageSync('userInfo');


                let params = {
                    page: this.page,
                    limit: this.limit,
                    model_type: 'task',
                    send_company_id: res_userInfo.company.id,
                }

                const {
                    status_code,
                    data
                } = await commonInterviewList(params)
                if (status_code == 200) {
                    this.list = this.list.concat(data.data);
                    // 返回false代表没有下一页
                    this.more = data.more;
                    this.status = this.more ? "loadmore" : "nomore"
                }
            }
        }
    }
</script>
<style>
    page {
        background-color: #f5f5f5;

        /* background-image: url('/static/images/login/bg.png'); */
        /* background-image: url('https://h5.zhaopinbei.cn/1.png');
        background-position: 100% 100%;
        background-size: 100% 100%;
        background-repeat: no-repeat; */
    }
</style>
<style lang="less" scoped>
    .list {
        padding: 0 32rpx;
    }

    .header {
        padding: 0 32rpx 32rpx 32rpx;

        .search-wrap {
            margin-top: 32rpx;
        }

        .filters {
            display: flex;
            margin-top: 32rpx;

            .filter {
                display: flex;
                align-items: center;
                height: 48rpx;
                background-color: #FFFFFF;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 0 12rpx;
                margin-right: 12rpx;
                border-radius: 8rpx;

                image {
                    width: 24rpx;
                    height: 24rpx;
                }
            }
        }
    }

    .wrap {
        display: flex;
        flex-direction: column;
        width: 600rpx;

        .title {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 92rpx;
            background: rgba(79, 140, 240, 0.1)
        }

        .items {
            display: flex;
            flex-direction: column;

            .item {
                display: flex;
                justify-content: space-between;
                padding: 24rpx;

                .lab {
                    display: flex;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                }

                .name {
                    flex: 1;
                    flex-wrap: wrap;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #333333;
                }
            }
        }
    }
</style>