<template>
	<view class="container">
		<view style="flex: 1;">
			<view style="width: 686rpx;
				    margin: 0 auto;">
				<view class="info-card">
					<p class="font-bold">
						上传公司相册有什么用?
					</p>
					<p class="font-blod-subtitle">
						在公司主页展示亮眼的照片，体现企业氛围与文化魅力。最多可上20张
					</p>
				</view>
			</view>
			<view class="contentPhoto">

				<view class="photo-upload-section" @click="openImagePicker">
					<view class="add-photo-btn">
						<view class="icon-plus"></view>
						<view class="text">添加照片</view>
					</view>
				</view>
				<view class="note" @click="showCareful">
					<text class="foottext">上传注意事项</text>
					<image :src="help" class="note-icon"></image>
				</view>
			</view>
		</view>
		<view v-if="showActionSheet" class="action-sheet">
			<view class="action-sheet-item" @click="takePhoto">拍照</view>
			<view class="action-sheet-item" @click="chooseFromAlbum">从相册中选择</view>
			<view class="action-sheet-item" @click="hideActionSheet" style="border-bottom: none;
    margin-top: 8px; height: 90px;">取消</view>
		</view>
		<!-- 注意事项区域 -->
		<!-- <view class="note" @click="showCareful">
			<text class="foottext">上传注意事项</text>
			<image :src="help" class="note-icon"></image>
		</view> -->
		<view class="footview">
			<Careful v-if="carefulhide"></Careful>
			<view class="footer-card">
				<button class="add-button" @click="saveAlbum">保存</button>
			</view>
		</view>
	</view>
</template>

<script>
	/* import help from 'https://api-test.zhaopinbei.com/storage/uploads/images/rYxkkwuyCNnqb4YUwWfJXzktOukmQ2dF2OuFvchb.png' */
	import Careful from '../companyNotice/carefulNotice.vue'

	export default {
		components: {
			Careful
		},
		data() {
			return {
				selectedImages: [], // 用于存储选择的图片路径
				help: 'https://api-test.zhaopinbei.com/storage/uploads/images/rYxkkwuyCNnqb4YUwWfJXzktOukmQ2dF2OuFvchb.png',
				carefulhide: false,
				showActionSheet: false,
				imageList: [] // 用于存储选择的图片路径数组
			};
		},
		methods: {
			// 打开图片选择器
			showImagePicker() {
				// uni.chooseImage({
				// 	count: 20 - this.selectedImages.length, // 限制最多选择剩余可上传数量
				// 	success: (res) => {
				// 		this.selectedImages = this.selectedImages.concat(res.tempFilePaths);
				// 	}
				// });
				this.showActionSheet = true;
				console.log(this.showActionSheet);
			},
			// 保存相册逻辑（这里只是简单模拟，实际需与后端交互）
			saveAlbum() {
				uni.showToast({
					title: '保存成功',
					icon: 'none'
				});
				// 添加上传图片到服务器的代码uni.uploadFile
			},
			// 打开图片选择菜单
			openImagePicker() {
				this.showActionSheet = true;
				console.log(this.showActionSheet);
			},
			// 关闭图片选择菜单
			hideActionSheet() {
				this.showActionSheet = false;
			},
			// 调用相机拍照
			takePhoto() {
				uni.chooseImage({
					count: 1, // 最多选1张
					sourceType: ['camera'],
					success: (res) => {
						this.imageList = this.imageList.concat(res.tempFilePaths);
						this.hideActionSheet();
					},
					fail: (err) => {
						uni.showToast({
							title: '拍照失败',
							icon: 'none'
						});
					}
				});
			},
			// 从相册选择图片
			chooseFromAlbum() {
				uni.chooseImage({
					count: 20 - this.imageList.length, // 限制最多选20张
					sourceType: ['album'],
					success: (res) => {
						this.imageList = this.imageList.concat(res.tempFilePaths);
						this.hideActionSheet();
					},
					fail: (err) => {
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},
			showCareful() {
				this.carefulhide = !this.carefulhide
			}
		}
	};
</script>

<style>
	.container {
		/* padding: 20rpx; */
		padding-bottom: 0px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		height: 100vh;
	}

	.info-card {
		width: 686rpx;
		display: flex;
		height: 166rpx;
		flex-direction: column;
		justify-content: center;
		margin: 20px auto;
		position: relative;
		background-image: linear-gradient(to bottom, rgba(242, 248, 255, 1), rgba(255, 255, 255, 1));
		overflow: hidden;
		border-radius: 16rpx;
		border: 2rpx solid linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
	}

	/* .info-card::before {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 1px;
		left: 1px;
		content: '';
		border: 5rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 16rpx;
		z-index: 2;
	}

	.info-card::after {
		position: absolute;
		width: 100%;
		height: 100%;
		bottom: 1px;
		right: 1px;
		/* margin: 0px auto; */
	/* content: '';
	border: 5rpx solid;
	border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
	border-radius: 16rpx;
	z-index: 2;
	} */
	.font-bold {
		padding: 5px 0;
		font-size: 24rpx;
		line-height: 14.06px;
		margin-left: 40rpx;
		margin-left: 40rpx;
	}

	.font-blod-subtitle {
		color: rgba(141, 154, 170, 1);
		padding: 5px 0;
		font-size: 20rpx;
		line-height: 23.44rpx;
		width: 606rpx;
		margin-left: 40rpx;
	}

	.contentPhoto {
		/* height: 849rpx; */
		position: relative;
	}

	.photo-upload-section {
		width: 200rpx;
		height: 200rpx;
		background-color: #f5f5f5;
		padding: 20rpx;
		border-radius: 8rpx;
		margin-bottom: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		/* margin-left: 16px; */
	}

	.add-photo-btn {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 200rpx;
		height: 200rpx;
	}

	.icon-plus {
		width: 32rpx;
		height: 32rpx;
		/* border: 2rpx dashed #ccc; */
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.icon-plus::after {
		content: "";
		width: 32rpx;
		height: 4rpx;
		background-color: #ccc;
		position: absolute;
	}

	.icon-plus::before {
		content: "";
		width: 4rpx;
		height: 32rpx;
		background-color: #ccc;
		position: absolute;
	}

	.text {
		font-size: 24rpx;
		color: rgba(119, 119, 119, 1);
		margin-top: 10rpx;
	}

	.upload-notice-section {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.upload-notice {
		font-size: 28rpx;
		color: #666;
		margin-right: 10rpx;
	}

	.icon-info {
		width: 30rpx;
		height: 30rpx;
		border: 2rpx solid #ccc;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.icon-info::after {
		content: "!";
		font-size: 20rpx;
		color: #ccc;
	}

	.note {
		display: flex;
		align-items: center;
		/* margin-bottom: 10rpx; */
		font-size: 28rpx;
		color: #999;
		position: absolute;
		margin-top: 24rpx;
		/* margin-left: 16px; */
	}

	.note-icon {
		width: 30rpx;
		height: 30rpx;
		margin-left: 10rpx;
	}

	.action-sheet {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: rgba(237, 237, 237, 0.5);
		border-top-left-radius: 10rpx;
		border-top-right-radius: 10rpx;
		z-index: 4;
		width: 100vw;
		/* height: 112px; */
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
	}

	.action-sheet-item {
		padding-top: 16px;
		text-align: center;
		border-bottom: 1rpx solid #eee;
		font-size: 32rpx;
		/* margin-bottom: 8px; */
		background: white;
		align-self: center;
		justify-content: center;
		width: 375px;
		height: 52px;
	}

	.action-sheet-item:last-child {
		border-bottom: none;
	}

	.add-button {
		width: 686rpx;
		height: 80rpx;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 8pt 8pt 8pt 8pt;
		font-size: 28rpx;
	}

	.footview {
		/* margin-left: -27px; */
		position: relative;
	}

	.footer-card {
		background-color: white;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 1.1);
		/* 卡片阴影 */
		padding: 20px;
		/* height: 196rpx; */
		width: 100vw;
	}

	.foottext {
		color: rgba(153, 153, 153, 1);
		font-size: 24rpx;
		line-height: 28.12rpx;
	}
</style>
