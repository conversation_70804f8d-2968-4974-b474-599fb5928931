<template>
    <view class="home-index draft-resume">
        <block v-if="page.data.length>0">
            <view class="list">
                <view class="item" v-for="(item,index) in page.data" :key="index" :item="item">
                    <view :class="['status', statusClass[item.status]]">
                        {{ item.member_resume.status_name }}
                    </view>
                    <view class="up" @click="goDetail(item)">
                        <image src="https://api-test.zhaopinbei.com/storage/uploads/images/5Vb9rePKPy5xj8diaa2Ko4VLl6oJSU33d8qVJ8dx.png" mode=""></image>
                        <view class="info">
                            <view class="name">
                                {{item.member_resume.resume_name}}
                            </view>
                            <view class="time">
                                修改时间：{{item.member_resume.updated_at}}
                            </view>
                        </view>
                    </view>

                    <view class="bulerBoxBig">
                        <view class="butlerBox">
                            <image :src="item.send_member_info.image.path_url" mode="" class="butlerHeadImg"></image>
                            <view class="butlerText">
                                <view class="butlerNameCla">{{item.send_member_certification.name}}</view>
                                <view class="butlerCompanyCla">{{item.send_company.name}}</view>
                            </view>
                        </view>
                    </view>

                    <view :class="['down flexEnd']">
                        <view class="btn talk" v-if="item.member_resume.status == 2" @click="auditForm(item)">
                            审核
                        </view>
                    </view>
                </view>
            </view>

        </block>

        <block v-else>
            <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png"></u-empty>
        </block>
        <Pages v-if="page.data.length > 0" :status="page.status"></Pages>
    </view>
</template>

<script>
    import Pages from "../../components/pages.vue";


    import {
        memberDraftList,
        memberDraftAudit,
    } from "../../config/member_api";

    export default {
        components: {
            Pages,
        },
        watch: {
            list: {
                handler(newValue, oldValue) {
                },
                deep: true,
                immediate: true
            }
        },
        data() {
            return {
                statusClass: {
                    1: 'agree',
                    2: 'pending',
                    3: 'reject',
                },
                audit: {
                    form: {
                        status: 1,
                        remark: "",
                        id: 0,
                    },
                },
                page: {
                    form: {
                        page: 1,
                        limit: 10,
                        type: 'draft',
                    },
                    data: [],
                    status: 'nomore',
                },

            }
        },
        computed: {
            userInfo() {
                return this.$store.state.userInfo || uni.getStorageSync('userInfo')
            },
        },
        onLoad() {
            this.open_status = this.userInfo.member.open_status
            this.getResumeList()
        },
        methods: {
            goDetail(item) {
                uni.navigateTo({
                    url: "/pagesA/details/resumeDetails?id=" + item.member_resume.id
                })
            },
            auditForm(item) {
                var _this = this;
                _this.audit.form.id = item.id;
                // 弹窗输入
                uni.showModal({
                    title: '审核简历修改记录', // 标题
                    confirmText: "驳回",
                    cancelText: "同意",
                    editable: true, // 开启输入框
                    placeholderText: '请输入审核意见', // 输入框提示语

                    success: (res) => {
                        _this.audit.form.remark = res.content || '';

                        if (res.confirm) {
                            // 如果没有输入内容
                            if (!res.content) {
                                return uni.$u.toast('请输入审核意见')
                            }

                            _this.audit.form.status = 3;
                            _this.auditHandel();

                        } else if (res.cancel) {
                            _this.audit.form.status = 1;
                            _this.auditHandel();
                        }
                    }
                })

            },
            auditHandel() {
                var _this = this;
                memberDraftAudit(_this.audit.form).then(response => {
                    uni.$u.toast(response.message);
                    if (response.status_code == '200') {
                        _this.initPage();
                        _this.getResumeList();
                    }
                });
            },
            initPage() {
                this.page.page = 1;
                this.page.data = [];
                this.page.status = 'loadmore';
            },
            //简历列表
            async getResumeList() {
                var _this = this;

                memberDraftList(_this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.more ? 'loading' : 'nomore';
                    }
                });

            },

        }
    }
</script>


<style lang="scss">
    @import "../../static/css/pagesA/personal/myResume";
    @import "../../static/css/pagesA/components/onlineResumeItem";
</style>
