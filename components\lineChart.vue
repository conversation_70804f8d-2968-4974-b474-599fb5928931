<template>
	<view>
		<canvas canvas-id="canvasLineA" id="canvasLineA" class="charts" @touchmove="moveLineA"
			@touchend="moveLineA"></canvas>
		<!-- 画布，图表的HTML部分-->
	</view>
</template>

<script>
	import uCharts from '@/utils/u-charts.js'; //引入js文件
	var _self; //用于全局使用this
	var canvaLineA = null; //uCharts实例
	export default {
		data() {
			return {
				cWidth: '',
				cHeight: '', //画布的宽高
				data: { //数据
					categories: ["07-01", "07-02", "07-03", "07-04", "07-05", "07-06", "07-07"],
					series: [{
						name: "数据",
						data: [35, 8, 25, 37, 4, 20]
					}, {
						name: "数据",
						data: [70, 40, 65, 100, 44, 68]
					}, {
						name: "数据",
						data: [100, 80, 4, 150, 112, 132]
					}]
				}
			}
		},
		onLoad() {
			this.cWidth = uni.upx2px(750);
			this.cHeight = uni.upx2px(500); //设置宽高
			_self = this //声明this
			_self.showLineA("canvasLineA", _self.data); //触发执行函数
		},
		methods: {
			showLineA(canvasId, chartData) {
				canvaLineA = new uCharts({ //这些配置项的意思看这：https://www.kancloud.cn/qiun/ucharts/1172125
					$this: _self, //指针
					canvasId: canvasId, //id
					type: 'line', //类型
					colors: ['#facc14', '#f04864', '#90ed7d'], //每一条的颜色
					fontSize: 11, //字体大小
					padding: [15, 15, 0, 15], //空白区域值
					legend: { //图例相关配置
						show: true,
						padding: 5,
						lineHeight: 11,
						margin: 0,
					},
					dataLabel: false, //显示数据标签内容值
					categories: chartData.categories, //数据类别
					series: chartData.series, //数据列表
					xAxis: { //X轴配置
						gridColor: '#CCCCCC', //X轴网格颜色
						gridType: 'dash', //X轴网格线型 'solid'为实线、'dash'为虚线`
						dashLength: 8, //X轴网格为虚线时，单段虚线长度
					},
					yAxis: { //y轴配置
						gridType: 'dash',
						gridColor: '#CCCCCC',
						dashLength: 8,
					},
					width: _self.cWidth, //canvas宽度，单位为px
					height: _self.cHeight, //canvas高度，单位为px
					extra: { //扩展配置
						line: {
							type: 'curve' //曲线  curve曲线，straight直线
						}
					}
				});
			},
			moveLineA(e) {
				canvaLineA.showToolTip(e, { //详情框
					format: function(item, category) {
						return category + ' ' + item.name + ':' + item.data
					}
				});
			}
		}
	}
</script>

<style>
	.charts {
		width: 750rpx;
		height: 500rpx;
		background-color: #FFFFFF;
	}
</style>