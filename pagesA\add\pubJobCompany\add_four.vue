<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="title">请选择合作模式</view>
      <view class="subtitle"
        >请在以下三种合作模式中选择，如同涉及外包&派遣和代招的直接合作，可同时选中这两种合作模式</view
      >
    </view>

    <!-- 公司列表 -->
    <view class="company-list">
      <view
        class="company-item"
        v-for="(item, index) in list"
        :key="index"
        @click="selectCompany(item)"
      >
        <view class="radio-wrap">
          <u-radio-group v-model="selectedValue">
            <u-radio :name="index" @change="radioChange">
              <view class="company-info">
                <view class="company-name">
                  {{ item.name
                  }}<text style="color: #999"> | {{ item.tit }}</text>
                </view>
                <view class="company-type">
                  {{ item.cont }}
                </view>
              </view>
            </u-radio>
          </u-radio-group>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next" @click="next"> 下一步 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      keyword: "",
      selectedValue: "", // 选中的值
      sel: {},
      list: [
        {
          name: "派遣&外包",
          tit: "直接合作",
          cont: "我司与客户签订派遣或外包合同，员工与我司签订劳动合同",
        },
        {
          name: "代招",
          tit: "直接合作",
          cont: "我司与客户签订派遣或外包合同，员工与我司签订劳动合同",
        },
        {
          name: "代招",
          tit: "间接合作",
          cont: "未与客户公司签订合作，从其他业务方承揽业务",
        },
      ],
    };
  },

  onLoad() {},

  methods: {
    next() {
      uni.setStorageSync("hezuo_mode", this.sel);
      uni.navigateTo({
        url: "/pagesA/add/pubJobCompany/add_three",
      });
    },
    radioChange(e) {
      const selectedIndex = e;
      const selectedItem = this.list[selectedIndex];
      this.selectCompany(selectedItem);
    },
    selectCompany(item) {
      // 处理选中的数据
      console.log("选中的项目：", item);
      this.sel = item;
      console.log("选中的值：", this.sel);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
  padding: 32rpx;
}

.header {
  margin-bottom: 32rpx;

  .title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 24rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

.search-box {
  position: relative;
  margin-bottom: 32rpx;

  .count {
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24rpx;
    color: #999;
  }
}

.company-list {
  .company-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx 0rpx;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;
    .radio-wrap {
      display: flex;
      align-items: flex-start;
      width: 100%;

      :deep(.u-radio-group) {
        width: 100%;
      }

      :deep(.u-radio) {
        width: 100%;
        margin-right: 0;
      }
    }
    .company-logo {
      width: 88rpx;
      height: 88rpx;
      background: #d9d9d9;
      border-radius: 8rpx;
      margin-right: 24rpx;
    }

    .company-info {
      flex: 1;

      .company-name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;
      }
      .tag {
        font-size: 20rpx;
        color: #4f8cf0;
        background-color: #ecf5ff;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
      }
      .company-type {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 12rpx;
      }
      .tags {
        width: 176rpx;
        height: 50rpx;
        background: #f4f4f4;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-size: 24rpx;
        color: #777777;
        text-align: center;
        line-height: 50rpx;
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  border-radius: 24rpx 24rpx 0 0;

  .next {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    width: 90%;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
    margin-top: 24rpx;
  }
}
</style>
