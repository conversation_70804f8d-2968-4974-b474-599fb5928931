<template>
    <view class="">
        <view class="inp">
            <view class="inp-item">
                <view class="name">
                    员工名称
                </view>
                <u--input
                        placeholder="请输入员工姓名"
                        border="none"
                        fontSize="28rpx"
                        v-model="formData.name"

                ></u--input>

            </view>

            <view class="inp-item">
                <view class="name">
                    手机号
                </view>
                <u--input
                        placeholder="请输入手机号"
                        border="none"
                        fontSize="28rpx"
                        v-model="formData.cellphone"
                ></u--input>
            </view>
            <view class="inp-item">
                <view class="name">
                    身份证号
                </view>
                <u--input
                        placeholder="请输入身份证号"
                        border="none"
                        fontSize="28rpx"
                        v-model="formData.idcard"
                ></u--input>
            </view>
            

            <view class="inp-item">
                <view class="name">
                    <text>角色权限</text>
                    <!--<image src="/static/images/index/plus.png" mode="" @click="auth"></image>-->
                </view>
                <view class="list" v-if="mini_program_api_roles.length > 0">
                    <view class="item" v-for="(item,index) in mini_program_api_roles" :item="item" :key="index">
                        {{ item.name }}
                    </view>
                </view>
            </view>
        </view>

        <view class="footer">
            <view class="btns">
                <view class="cancel" @click="logOff()">
                    {{ disable_status_name }}
                </view>
                <view class="save" @click="submit">
                    保存
                </view>
            </view>
        </view>

    </view>
</template>

<script>
    import {staffShow, staffStore} from "../../config/company_api";
    import {userDisable} from "../../config/headhunterList_api.js";


    export default {
        data() {
            return {
                formData: {
                    idcard: "", //身份证号
                    cellphone: "", //登录用的手机号
                    name: '', //员工名称
                    id: 0,
                },
                disable_status: 2, //注销状态 1 注销 2 未注销
                disable_status_name: '注销',
                itemIndex: undefined,
                mini_program_api_roles: [],
            }
        },
        onLoad(options) {

            // this.formData.cellphone = options.data.cellphone
            let id = options.id;
            this.formData.id = id;
            this.itemIndex = options.index;

            if (id) {
                this.staffShow()
            }
        },
        methods: {
            submit() {
                staffStore(this.formData).then(response => {
                    if (response.status_code == '200') {
                        uni.setStorageSync('userItemIndex', this.itemIndex);
                        uni.setStorageSync('editUser', response.data);
                        uni.navigateBack();
                    }
                });
            },
            auth() {
                uni.showModal({
                    title: '该功能尚未开放',
                    showCancel: false,
                    success: function (res) {
                        if (res.confirm) {
                            console.log('用户点击确定');
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                });
            },
            async staffShow() {
                let params = {
                    id: this.formData.id,
                };
                const {
                    status_code,
                    data
                } = await staffShow(params)
                if (status_code == "200") {
                    this.disable_status = data.disable_status == 1 ? 2 : 1;
                    this.disable_status_name = data.disable_status == 1 ? '恢复' : '注销';
                    this.formData.name = data.member.certification_status == 1 ? data.member_certification.name : data.name;
                    this.formData.cellphone = data.member.cellphone;
                    this.formData.idcard = data.member_certification.id_no;
                    this.mini_program_api_roles = data.mini_program_api_roles;
                }
            },
            async logOff() {
                let params = {
                    id: this.formData.id,
                    disable_status: this.disable_status
                };
                const {
                    status_code,
                    data,
                    message
                } = await userDisable(params)

                uni.$u.toast(message);

                if (status_code == "200") {
                    uni.setStorageSync('userItemIndex', this.itemIndex);
                    uni.setStorageSync('editUser', data);
                    uni.navigateBack();
                }
            }
        }
    }
</script>
<style>
    page {
        background-color: #f5f5f7;
    }
</style>
<style lang="scss" src="../../static/css/pagesA/add/addStaff.scss"></style>