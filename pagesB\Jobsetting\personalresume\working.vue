<template>
	<view class="container">
		<view class="content">
			<view class="form-item" @click="routerName">
				<label>公司名称</label>
				<u--input placeholder="请输入公司名称" :value="dataform.companyName" suffixIcon="arrow-right"
					suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
			</view>
			<view class="form-item" @click="routerjoblist">
				<label>所在行业</label>
				<view class="selected-con">
					<view class="picker-view" v-if="dataform.industryOptions != ''">
						{{ dataform.industryOptions }}
					</view>
					<view class="picker-view" v-else>
						请选择所属行业
					</view>
					<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
				</view>
			</view>
			<view class="form-item">
				<label>在职时间</label>
				<view class="time-picker">
					<view class="selected-con">
						<picker class="form-input" mode="date" :value="dataform.startTime" :start="startDate"
							:end="dataform.endTime" @change="bindDateChange">
							<view class="form-input-placeholder" v-if="dataform.startTime===''">请选择开始时间</view>
							<view class="form-input-placeholder" v-else>{{dataform.startTime}}</view>
						</picker>
						<text>-</text>
						<picker class="form-input" mode="date" :value="dataform.endTime" :start="dataform.startTime"
							@change="bindDateChange2">
							<view class="form-input-placeholder" v-if="dataform.endTime===''">请选择结束时间</view>
							<view class="form-input-placeholder" v-else>{{dataform.endTime}}</view>
						</picker>
						<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
					</view>
				</view>
			</view>


			<view class="form-item" @click="routerjobname">
				<label>岗位名称</label>
				<view class="selected-con">
					<view class="picker-view" v-if="dataform.position != ''">
						{{ dataform.position }}
					</view>
					<view class="picker-view" v-else>
						请选择岗位名称
					</view>
					<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
				</view>
			</view>

			<view class="form-item" @click="routerwork">
				<label>工作内容</label>
				<view class="selected-con">
					<view class="picker-view" v-if="dataform.workContent != ''">
						{{ dataform.workContent }}
					</view>
					<u--input type="text" placeholder="请输入工作内容" :value="dataform.workContent" suffixIcon="arrow-right"
						suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
				</view>
			</view>
			<view class="form-item" @click="routerDivision">
				<label>所属部门（选填）</label>
				<u--input type="text" placeholder="请输入所属部门    例如：产品部" :value="dataform.department"
					suffixIcon="arrow-right" suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
			</view>
			<view class="form-item" @click="routerResult">
				<label>工作业绩（选填）</label>
				<u--input type="text" placeholder="填写完整、有吸引力的工作业绩，有助于您更多的吸引伯乐的关注 例如： 1.取得的成绩... 2.实现的突破... 3.活动的表彰..."
					:value="dataform.workAchievement" suffixIcon="arrow-right"
					suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>

				<text>

				</text>
				<!-- <input type="email" placeholder="请输入您的邮箱" v-model="email" /> -->
			</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" @click="submitForm">完成</button>
		</view>

	</view>
	</view>
</template>
</view>
<script>
	export default {
		data() {
			return {
				dataform: {
					companyName: '',
					industry: '',
					industryOptions: ['互联网', '金融', '教育', '制造业'],
					startTime: '',
					endTime: '',
					timeOptions: [], // 时间选项需根据实际需求填充，如日期选择器可使用插件
					position: '',
					positionOptions: [],
					workContent: '',
					department: '',
					workAchievement: ''
				}
			};
		},
		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		methods: {
			submitForm() {
				// 这里可以添加表单提交逻辑，例如调用接口发送数据等
			},
			// console.log('表单提交：', {
			// 	name: this.name,
			// 	jobStatus: this.jobStatus,
			// 	gender: this.gender,
			// 	identity: this.identity,
			// 	birthDate: this.birthDate,
			// 	phone: this.phone,
			// 	wechat: this.wechat,
			// 	email: this.email
			// });
			routerName() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/worlklist/companyname'
				})
			},
			routerwork() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/worlklist/workcontent'
				})
			},
			routerjoblist() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/worlklist/Fieldlist'
				})
			},
			routerDivision() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/worlklist/Division'
				})
			},
			routerResult() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/worlklist/Result'
				})
			},
			routerjobname() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/worlklist/jobname'
				})
			},
			// routerWX() {
			// 	uni.navigateTo({
			// 		url: '/pagesB/Jobsetting/personalresume/personalData/WX'
			// 	})
			// },
			// routerEmail() {
			// 	uni.navigateTo({
			// 		url: '/pagesB/Jobsetting/personalresume/personalData/email'
			// 	})
			// },
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			// 选择开始时间
			bindDateChange(e) {
				this.dataform.startTime = e.detail.value
			},
			// 选择结束时间
			bindDateChange2(e) {
				this.dataform.endTime = e.target.value;
			},
			// 按照时间查找
			findDate() {
				if (this.dataform.startTime === '' || this.dataform.endTime === '') {
					uni.showModal({
						title: '提示',
						content: `请选择起始时间和结束时间后，再点击查询`,
						showCancel: false
					});
				} else {
					getTask(this.dataform).then(res => {
						this.tasklistArr = JSON.parse(JSON.stringify(res.data.data));
					})
				}

			},
			// 清除时间
			cleardataform() {
				this.dataform.startTime = "";
				this.dataform.endTime = "";
				getTask(this.dataform).then(res => {
					this.tasklistArr = JSON.parse(JSON.stringify(res.data.data));
				})
			},

		}
	};
</script>

<style scoped>
	.container {
		padding: 0rpx 20rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		background-color: rgba(245, 245, 247, 1);
	}

	.content {
		flex: 1;
		width: 622rpx;
		padding: 32rpx;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.form-item {
		margin-bottom: 20rpx;
		width: 622rpx;
		border-bottom: 1rpx solid rgba(230, 230, 230, 1);
		padding-bottom: 32rpx;
	}

	.label {
		display: block;
		margin-bottom: 5rpx;
		height: 114rpx;
	}


	/* .input {
		width: 100%;
		padding: 10rpx;
		border: 1rpx solid #ccc;
		border-radius: 5rpx;
	} */

	::v-deep .u-input {
		border: none;
		padding: 0rpx !important;
		width: 622rpx;
	}

	.picker-view {
		width: 100%;
		/* padding: 10rpx; */
		/* border: 1rpx solid #ccc; */
		color: rgba(204, 204, 204, 1);
		margin-top: 16rpx;
		border-radius: 5rpx;
		font-size: 28rpx;
	}

	.selected-con {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 622rpx;
		height: 40rpx;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.time-picker {
		display: flex;
		align-items: center;
	}

	.time-picker text {
		margin: 0 10px;
	}

	.form-input-placeholder {
		color: #999;
	}
</style>