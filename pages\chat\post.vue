<template>
	<view class="container">
		<u-cell @click="onListItemClick" v-for="_ in 30">
			<template #icon>
				<view class="item-image__box">
					<image
						class="avater"
						src="https://api-test.zhaopinbei.com/storage/uploads/images/lBJpgI0XRMLo17xqaHmHxvr17futeNLhBJUtLdVe.png"></image>
				</view>
			</template>
			<template #title>
				<view class="item-info__box">
					<view class="info-start">
						<text class="name">寇国涛</text>
						<text class="company">学创联盟</text>
						<text class="time">刚刚</text>
					</view>
				</view>
			</template>
		</u-cell>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	},
	methods: {
		onListItemClick() {
			uni.$u.route({
				url: '/pagesC/message/message_post',
			});
		},
	},
};
</script>
<style lang="scss" scoped>
.container {
	.item-image__box {
		margin-inline-end: 24rpx;

		.avater {
			width: 88rpx;
			height: 88rpx;
			border-radius: 999rpx;
		}
	}

	.item-info__box {
		.info-start {
			display: flex;
			gap: 32rpx;
			align-items: center;

			.name {
				font-size: 28rpx;
				color: #041024;
			}

			.company,
			.time {
				font-size: 24rpx;
				color: #666666;
			}

			.time {
				margin-inline-start: auto;
			}
		}
	}
}
</style>
