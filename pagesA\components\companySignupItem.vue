<template>
    <view class="item" @click.stop="goQlmDetails">
        <view class="item-up">
            <view class="info">
                <image :src="item.member_info.image['path_url']||''" mode=""></image>
                <view class="cont">
                    <view class="sup">
                        <view class="userInfo">
                            <view class="name">
                                {{item.member_certification.name?item.member_certification.name:item.member_info.nick_name}}
                            </view>
                            <view :class="['cert',item.member.certification_status==1?'':'gray']">
                                {{item.member.certification_status==1?'已认证':'未认证'}}
                            </view>
                        </view>
                        <view class="money">
                            期待薪资：{{item.job_basic.salary_info_str}}
                        </view>
                    </view>

                    <view class="tags">
                        <view class="tag">
                            {{item.member_info.sex_str}}
                        </view>
                        <view class="tag">
                            {{item.member_info.age}}岁
                        </view>
                        <view class="tag">
                            {{item.job_basic.experience_name}}
                        </view>
                        <view class="tag">
                            {{item.member_info.education_type_name}}
                        </view>

                    </view>
                </view>
            </view>
            <!-- 普通报名 -->
            <view class="desc" v-if="tabIndex==0||tabIndex==1||tabIndex==2">
                <text>报名职位：</text>
                <text>{{item.job_basic.title}}</text>
            </view>
            <!-- 活动报名 -->
            <view class="desc" v-if="item.task=='job_active'">
                <text>报名活动：</text>
                <text>{{item.job_basic.title}}</text>
            </view>
            <!-- 代招报名 -->
            <view class="desc" v-if="tabIndex==2">
                <text>所属就业管家：</text>
                <text>就业管家名称</text>
            </view>
        </view>

        <view :class="['item-down',item.report_status=='reported' ||item.report_status=='interview' ?'flexRow':'']">
            <view class="reason" v-if="item.report_status=='cancel'">
                拒绝原因：{{item.cancel_reason}}
            </view>
            <view class="btns">
                <view class="btn talk" @click.stop="communicate('job_report',item.id)">
                    聊聊呗
                </view>

                <view class="btn refuse" v-if="item.but_status.cancel_status==1" @click.stop="cancel">
                    拒绝
                </view>

                <view class="btn agree" v-if="item.but_status.interview_status==1" @click.stop="invite">
                    邀请面试
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import {communicate} from "../../common/common";
    export default {
        name: "companySignupItem",
        props: {
            item: {
                type: Object,
                default: () => {}
            },
            tabIndex: {
                type: Number,
                default: () => {}
            }
        },
        data() {
            return {

            };
        },
        methods: {
            communicate,
            goQlmDetails() {
                uni.navigateTo({
                    url: "/pagesA/details/qlmDetails?id=" + this.item.member_info.id
                })
            },
            cancel() {
                this.$emit('cancelSignup', this.item)
            },
            invite() {
                this.$emit('inviteInterview', this.item)
            }
        }
    }
</script>

<style lang="less" scoped>
    .item {
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 0 32rpx;
        margin-bottom: 32rpx;
        background-color: #FFFFFF;
        border-radius: 24rpx;

        .item-up {
            display: flex;
            flex-direction: column;
            padding: 24rpx 0;

            .info {
                display: flex;

                &>image {
                    width: 104rpx;
                    height: 104rpx;
                    border-radius: 24rpx;
                }

                .cont {
                    display: flex;
                    justify-content: space-around;
                    flex-direction: column;
                    padding-left: 24rpx;
                    flex: 1;

                    .sup {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        .userInfo {
                            display: flex;
                            align-items: center;

                            .name {
                                font-weight: 600;
                                font-size: 32rpx;
                                color: #333333;
                            }

                            .cert {
                                display: flex;
                                align-items: center;
                                margin-left: 28rpx;
                                padding: 0 12rpx;
                                height: 40rpx;
                                background: rgba(87, 213, 28, 0.1);
                                border-radius: 8rpx;
                                font-weight: 600;
                                font-size: 20rpx;
                                color: #57D51C;
                            }

                            .gray {
                                background-color: #cccccc;
                                color: #FFFFFF;
                            }
                        }

                        .money {
                            // font-weight: 600;
                            // font-size: 32rpx;
                            // color: #F98A14;
							font-size: 24rpx;
                            margin-left: 16rpx;
                        }
                    }



                    .tags {
                        display: flex;

                        .tag {
                            display: flex;
                            align-items: center;
                            padding: 0 12rpx;
                            font-weight: 400;
                            font-size: 22rpx;
                            color: #999999;
                            // margin-right: 16rpx;
                            border-right: 1px solid #999999;

                            &:first-child {
                                padding-left: 0;
                            }

                            &:last-child {
                                border-right: none;
                            }
                        }
                    }
                }

            }

            .desc {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: 400;
                font-size: 24rpx;
                margin-top: 16rpx;

                text {
                    color: #333333;

                    &:first-child {
                        color: #999999;
                    }
                }

            }
        }



        .item-down {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 0 24rpx 0;

            .btns {
                display: flex;

                .btn {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-left: 24rpx;
                    border-radius: 8rpx;
                    font-weight: 600;
                    padding: 0 24rpx;
                    height: 56rpx;
                    font-size: 24rpx;
                    color: #FFFFFF;

                    &:first-child {
                        margin-left: 0;
                    }
                }

                .agree {
                    background-color: #4F8CF0;
                    color: #FFFFFF;
                }

                .refuse {
                    background-color: #FE4D4F;
                    color: #FFFFFF;
                }

                .talk {
                    background: rgba(79, 140, 240, 0.1);
                    color: #4F8CF0;
                }
            }

            .reason {
                width: 400rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                font-weight: 600;
                font-size: 24rpx;
                color: #FE4D4F;
            }
        }

        .flexRow {
            display: flex;
            flex-direction: row-reverse;
        }


        .status {
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            right: 0;
            top: 0;
            font-size: 28rpx;
            width: 132rpx;
            height: 62rpx;
            border-radius: 0 24rpx 0 24rpx;
        }

        .wait {
            background: #4F8CF0;
            color: #FFFFFF;
        }

        .yqx {
            background: #cccccc;
            color: #FFFFFF;
        }

    }
</style>