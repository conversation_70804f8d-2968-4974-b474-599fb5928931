<template>
	<view id="app">
		<u-sticky bgColor="#F5F5F5">
			<view class="search-wrap">
				<u-search placeholder="请输入姓名或手机号" bgColor="#FFFFFF" :showAction="true" actionText="搜索"  v-model="keyword" @custom="custom" @clear="clear"></u-search>
			</view>
		</u-sticky>
		<view class="list" style="margin-top: 24rpx;">
			<view @click="edit(item)" v-for="(item,index) in list" :key="item.id" :class="item.id == selectedInd ? 'item selectedCla' :'item'">
				<image :src="item.member_info.image.path_url"></image>
				<view class="info">
					<view class="user">
						<view class="name">
							{{ item.member.certification_status==1 ? item.member_certification.name : item.name }}
						</view>
						<view :class="['status',item.member.certification_status==1?'yrz':'wrz']">
							{{item.member.certification_status_name }}
						</view>
					</view>
					<view class="phone">
						手机号：{{ item.member.cellphone }}
					</view>
				</view>
			</view>
		</view>
		<Pages :status="status"></Pages>
		<view style="height: 120rpx;"></view>
		<view class="footer">
			<view class="next sure" @click="add">
				确定
			</view>
		</view>
	</view>
</template>

<script>
	// import {
	// 	staffList
	// } from "../../config/company_api";
    import {
    	staffList
    } from "../../config/api";
	import Pages from "../../components/pages.vue";
	export default {
		components:{
			Pages
		},
		data() {
			return {
				tabs: [{
						name: '已认证',
					},
					{
						name: '未注销',
					}
				],
				list: [],
				selectedInd:'',
				keyword:'',
				page:1,
				limit:10,
				status:'loadmore',
				more:false,
				name:'',
				cellphon:'',
                company_id:''
			}
		},
        onLoad(option) {
            this.company_id = option.companyId
            console.log('员工列表')
            this.staffList()
        },
		//触底加载更多
		onReachBottom() {
			if (this.more) {
				this.status = 'loading'
				this.page++
				this.staffList()
			} else {
				this.status = 'nomore'
			}
		},
		methods: {
			edit (item) {
				if (this.selectedInd == item.id) {
					this.selectedInd = '';
					this.cellphone='';
					this.name = '';
					this.head = '';
				} else {
					this.selectedInd = item.id;
					this.cellphone = item.member.cellphone;
					this.name = item.member_certification.name;
					this.head = item.member_info.image.thumbnail_path_url
				}
			},
			add() {
				if(this.selectedInd) {
					uni.setStorageSync('selectedInd',{id:this.selectedInd,phone:this.cellphone,name:this.name,head:this.head})
					uni.navigateBack()
				} else {
					uni.showToast({
						icon:'none',
						title:"未选择变更人",
						duration:2000
					})
				}
			},
			clear() {
				this.keyword="";
				this.custom()
			},
			custom() {
				this.page =1;
				this.list = []
				this.staffList()
			},
			async staffList() {
				const params = {
					page:this.page,
					limit:this.limit,
					member_certification_status:1,
					disable_status:2,
					search:this.keyword,
                    company_id:this.company_id
				}
				const {
					data,
					status_code,
					message
				} = await staffList(params)
				this.list = this.list.concat(data.data);
				this.more = data.more;
				this.status = this.more ? "loadmore" : "nomore"
			}
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	view {
		box-sizing: border-box;
	}

	#app {
		width: 100%;
		padding: 32rpx;
	}

	.tabs {
		margin-bottom: 32rpx;
	}

	.footer {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		height: 120rpx;
		width: 100%;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 28rpx;
			color: #FFFFFF;
			height: 88rpx;
			width: 90%;
			border-radius: 16rpx;
		}

		.sure {
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	}
	
	.selectedCla {
		border: 2rpx solid #4F8CF0 !important;
	}
	
	.item {
		display: flex;
		align-items: center;
		border-radius: 24rpx;
		background: #FFFFFF;
		padding: 32rpx;
		margin-bottom: 24rpx;
		border: 2rpx solid transparent;
		// &:last-child{
		// 	margin-bottom: 0;
		// }
		&>image {
			width: 104rpx;
			height: 104rpx;
		}

		.info {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			flex: 1;
			padding-left: 24rpx;

			.user {
				display: flex;
				align-items: center;

				.name {
					font-weight: 600;
					font-size: 32rpx;
					color: #333333;
				}

				.status {
					display: flex;
					align-items: center;
					padding: 0 12rpx;
					height: 40rpx;
					font-weight: 600;
					font-size: 20rpx;
					margin-left: 16rpx;
					border-radius: 8rpx;
				}

				.yrz {
					background: rgba(87, 213, 28, 0.1);
					color: #57D51C;
				}

				.wrz {
					background: rgba(249, 173, 20, 0.1);
					color: #F9AD14;

				}
			}

			.phone {
				font-weight: 400;
				font-size: 24rpx;
				color: #666666;
				margin-top: 24rpx;
			}
		}

		.reason {
			font-weight: 500;
			font-size: 24rpx;
			color: #FE4D4F;
		}
	}
</style>