page {
    background-color: #F5F5F7;
}

.wrap {
    padding: 30rpx;
    
    .inp {
        background: #FFFFFF;
        border-radius: 16rpx;
        
        .default {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 24rpx 32rpx;
            
            .name {
                font-weight: 500;
                font-size: 28rpx;
                color: #333333;
            }
        }
        
        .avatar {
            display: flex;
            align-items: center;
            
            .pic {
                padding: 0 30rpx 0 0;
                
                image {
                    width: 108rpx;
                    height: 108rpx;
                }
            }
        }
        
        .inp-item {
            display: flex;
            flex-direction: column;
            padding: 0 30rpx;
            flex: 1;
            
            .title {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 22rpx;
                color: #666666;
                margin: 16rpx 0 0 0;
                
                .star {
                    font-weight: 600;
                    font-size: 22rpx;
                    color: #FE4D4F;
                    margin-left: 8rpx;
                }
            }
            
            .in {
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-bottom: 1px solid #F5F5F7;
                height: 88rpx;
                font-size: 32rpx;
                
                ::v-deep uni-data-picker {
                    width: 100%;
                }
                
                ::v-deep .arrow-area {
                    transform: rotate(-135deg);
                }
                
                ::v-deep .input-arrow {
                    width: 20rpx;
                    height: 20rpx;
                    border-left: 1px solid #606266;
                    border-bottom: 1px solid #606266;
                }
                
                ::v-deep .input-value-border {
                    border: none;
                }
                
                ::v-deep .input-value {
                    padding: 0;
                }
                
                ::v-deep .placeholderClass {
                    font-weight: 400;
                    font-size: 32rpx;
                }
                
                ::v-deep picker {
                    display: flex;
                    flex-direction: column;
                    flex: 1;
                    height: 88rpx;
                    
                    .d-picker {
                        display: flex;
                        align-items: center;
                        // width: 60vw;
                        height: 88rpx;
                    }
                }
                
            }
            
            .se {
                color: #999;
            }
            
            .lab {
                font-weight: 400;
                font-size: 22rpx;
                color: #999999;
            }
        }
    }
}

.footer {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 158rpx;
    background-color: #FFFFFF;
    z-index: 10;
    border-radius: 16rpx 16rpx 0 0;
    
    .next {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 88rpx;
        width: 90%;
        font-weight: 600;
        font-size: 28rpx;
        border-radius: 16rpx;
    }
    
    .sure {
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        color: #FFFFFF;
    }
}