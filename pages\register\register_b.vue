<template>
	<view class="register-one">
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						手机号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入手机号" fontSize="32rpx" clearable :placeholderStyle="placeholderStyle" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						验证码<text class="star">*</text>
					</view>
					<view class="in">
						<!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
						<!-- #ifndef APP-NVUE -->
						<u-input placeholder="请输入验证码" fontSize="32rpx" clearable :placeholderStyle="placeholderStyle"  border="none" v-model="value">
						<!-- #endif -->
							<!-- #ifdef APP-NVUE -->
							<u--input placeholder="请输入验证码" clearable fontSize="32rpx">
							<!-- #endif -->
								<template slot="suffix">
									<u-code ref="uCode" @change="codeChange" seconds="20" changeText="X秒重新获取"></u-code>
									<u-button @tap="getCode" :text="tips" type="success" size="mini"></u-button>
								</template>
						<!-- #ifndef APP-NVUE -->
						</u-input>
						<!-- #endif -->
						<!-- #ifdef APP-NVUE -->
						</u--input>
						<!-- #endif -->
					</view>
				</view>


				<view class="inp-item">
					<view class="title">
						密码<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入密码" fontSize="32rpx" :placeholderStyle="placeholderStyle" clearable type="password" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						确认密码<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入确认密码" fontSize="32rpx" :placeholderStyle="placeholderStyle" clearable type="password" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="inp">
				<view class="avatar">
					<view class="inp-item">
						<view class="title fb">
							企业头像<text class="star">*</text>
						</view>
						<view class="in lab">
							请选择系统默认头像
						</view>
					</view>
					<view class="pic">
						<image src="https://api-test.zhaopinbei.com/storage/uploads/images/DjpYAiYx76keAZZls68bXyVPGHjYpURJJxA9YHcU.png" mode=""></image>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						企业名称<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入企业名称" fontSize="32rpx" clearable :placeholderStyle="placeholderStyle" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						社会统一代码<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入社会统一代码" fontSize="32rpx" clearable :placeholderStyle="placeholderStyle" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						联系人手机号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入手机号" fontSize="32rpx" clearable :placeholderStyle="placeholderStyle" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						联系人邮箱<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入邮箱" fontSize="32rpx" clearable :placeholderStyle="placeholderStyle" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						企业简称
					</view>
					<view class="in">
						<u--input placeholder="请输入企业简称" fontSize="32rpx" clearable :placeholderStyle="placeholderStyle" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="next gray" @click="next">
				下一步
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type:'company',
				tips: '',
				value: '',
				placeholderStyle:{
					color:'#999999'
				}
			}
		},
		onLoad(options) {
			this.type = options.type
		},
		methods: {
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			change(e) {
				console.log('change', e);
			},
			next(){
				uni.navigateTo({
					url:"/pages/register/register_b_next?type="+this.type
				})
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
		/* background-image: url('/static/images/login/bg.png');
		background-position: 100% 100%;
		background-size: 100% 100%;
		background-repeat: no-repeat; */
	}
</style>
<style lang="less" scoped>
	.register-one{
		padding-bottom: 178rpx;
	}
	.wrap {
		padding: 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			.avatar{
				display: flex;
				align-items: center;
				.pic{
					padding: 0 30rpx 0 0;
					image{
						width: 108rpx;
						height: 108rpx;
					}
				}
			}
			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;
				.title {
					display: flex;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;
					.star{
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}



				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;
				}

				.fb{
					font-size: 32rpx;
				}

				.se{
					color: #999;
				}

				.lab{
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.footer{
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;
		.next{
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}
		.gray{
			background-color: #cccccc;
			color: #FFFFFF;
		}
	}
</style>
