<template>
  <view class="cardBox" @click="goDetails(item.id)">
    <view class="cardBox_title">{{ item.title }}</view>
    <view class="cardBox_times">
      <view>合同创建时间：</view>
      <view>{{ item.created_at }}</view>
    </view>
    <view class="cardBox_times">
      <view>合同更新时间：</view>
      <view>{{ item.updated_at }}</view>
    </view>
    <!-- 已签署greenBtn  带签署blueBtn-->
    <view
      :class="[
        'bottomIcon',
        item.sign_status == 'pending' ? 'blueBtn' : 'greenBtn',
      ]"
      >{{ item.sign_status_name }}</view
    >
  </view>
</template>

<script>
export default {
  name: "signRecord",
  props: {
    item: Object,
  },
  data() {
    return {
      keyword: "",
    };
  },
  methods: {
    goDetails(id) {
      uni.navigateTo({
        url: "/pagesA/details/chatContractDetails?id=" + id,
      });
    },
  },
};
</script>

<style scoped lang="less">
view {
  box-sizing: border-box;
}

.cardBox {
  width: 100%;
  // height: 244rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 24rpx 32rpx;
  margin-top: 24rpx;
}

.cardBox_title {
  font-weight: bold;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
}

.cardBox_times {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 16rpx;
}

.bottomIcon {
  width: 84rpx;
  height: 40rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-weight: 600;
  font-size: 20rpx;
  text-align: center;
  line-height: 40rpx;
}

.greenBtn {
  background: rgba(87, 213, 28, 0.1);
  color: #57d51c;
}

.blueBtn {
  background: rgba(79, 140, 240, 0.1);
  color: #4f8cf0;
}

.search-wrap {
  padding: 0 32rpx;
}
</style>
