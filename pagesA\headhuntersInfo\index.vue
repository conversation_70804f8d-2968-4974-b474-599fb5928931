<template>
  <view class="home-index">
    <view class="tips">
      <text class="tip_title1">填写公司基本信息有什么用？</text>
      <text class="tip_title2"
        >详尽的公司信息呈现，能有效增强求职者信任，带来更多有效沟通。</text
      >
    </view>
    <view class="list">
      <view class="item" @click="goSlogan">
        <view class="item-left">
          <view class="left-box">
            <text>我的口号</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goInformation">
        <view class="item-left">
          <view class="left-box">
            <text>管家基本信息</text>
            <text class="left-inner">6/6</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goIndustries">
        <view class="item-left">
          <view class="left-box">
            <text>擅长行业</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goOccupation">
        <view class="item-left">
          <view class="left-box">
            <text>擅长职业</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goIntroduction">
        <view class="item-left">
          <view class="left-box">
            <text>个人介绍</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goPopulation">
        <view class="item-left">
          <view class="left-box">
            <text>服务人群</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goTerritory">
        <view class="item-left">
          <view class="left-box">
            <text>我的领地</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goTalent">
        <view class="item-left">
          <view class="left-box">
            <text>服务人才</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goEnterprise">
        <view class="item-left">
          <view class="left-box">
            <text>服务企业</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goMyTag">
        <view class="item-left">
          <view class="left-box">
            <text>我的标签</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
      <view class="item" @click="goEval">
        <view class="item-left">
          <view class="left-box">
            <text>我的评价</text>
            <text class="left-inner">未完善</text>
          </view>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
    </view>
    <view class="btm">
      请认真阅读并遵守<text class="xuzhi">《公司信息填写须知》</text>
    </view>
    <u-popup :show="show" mode="center" @close="show = false">
      <view class="tip-box">
        <view class="tip-tit">
          <view>
            <img
              src="https://api-test.zhaopinbei.com/storage/uploads/images/nFrqqeyJCCN6Fbvx5VSrOlw9MrpfsDm6qms8tsRh.png"
              alt=""
            />
            <text>管家信息填写须知</text>
          </view>
          <img
            @click="show = false"
            src="https://api-test.zhaopinbei.com/storage/uploads/images/Sl4y4bsafDNMgfpp372fiFnhD0jxCXPYoHzhs5aq.png"
            alt=""
          />
        </view>
        <view class="tip-txt"
          >您在本页面编辑、上传的管家信息将作为招聘场景下的管家主页展示，供求职者浏览。为保障求职者合法权益并确保您的招聘效果，您承诺并确认:
          您的填写行为已获得所在公司的授权，相关内容经过公司确认。
          填写、上传真实、合法、有效的管家信据实填写并及时更新管家介绍、服务人领地等，以免对求职者造成误导。填写、上传的图文、视频等资料不存在违法违规或涉嫌侵犯第三方合法权益的情形，如由此产生相关法律风险或造成损失，由您及所在公司承担法律责任
          您授权招聘呗以提供招聘服务为目的在其也场景下免费使用您在本页面填写的管家信以便为您提供更佳的服务体验</view
        >
        <view class="tip-btm">
          <view class="tip-btn" @click="show = false">我知道了</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { logout } from "../../config/api.js";
export default {
  data() {
    return {
      show: true,
    };
  },
  methods: {
    goSlogan() {
      uni.navigateTo({
        url: "./slogan",
      });
    },
    goInformation() {
      uni.navigateTo({
        url: "./information",
      });
    },
    goIndustries() {
      uni.navigateTo({
        url: "./industries?type=1",
      });
    },
    goOccupation() {
      uni.navigateTo({
        url: "./industries?type=2",
      });
    },
    goIntroduction() {
      uni.navigateTo({
        url: "./introduction",
      });
    },
    goPopulation() {
      uni.navigateTo({
        url: "./industries?type=3",
      });
    },
    goTerritory() {
      uni.navigateTo({
        url: "./territory",
      });
    },
    goTalent() {
      uni.navigateTo({
        url: "./talent",
      });
    },
    goEnterprise() {
      uni.navigateTo({
        url: "/pagesB/headhuntersInfo/enterprise",
      });
    },
    goMyTag() {
      uni.navigateTo({
        url: "./industries?type=4",
      });
    },
    goEval() {
      uni.navigateTo({
        url: "/pagesB/headhuntersInfo/evaluation",
      });
    },
  },
};
</script>
<style>
page {
  background: #f5f5f7;
}
</style>
<style lang="less" scoped>
.home-index {
  padding: 32rpx;
}

.tips {
  // width: 686rpx;
  height: 166rpx;
  display: flex;
  flex-direction: column;
  // align-items: center;
  padding-inline-start: 40rpx;
  justify-content: center;
  background: linear-gradient(180deg, #f2f8ff 0%, #ffffff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin-bottom: 24rpx;

  .tip_title1 {
    font-size: 24rpx;
    color: #333333;
    margin-bottom: 24rpx;
  }

  .tip_title2 {
    font-size: 20rpx;
    color: #8d9aaa;
  }
}

.list {
  display: flex;
  flex-direction: column;
  background: #ffffff;
  padding: 0 48rpx;
  border-radius: 24rpx;
  z-index: 10;

  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100rpx;

    .item-left {
      display: flex;
      align-items: center;
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;

      image {
        width: 32rpx;
        height: 32rpx;
      }

      .left-box {
        // flex: 1;
        width: 540rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .left-inner {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}

.btm {
  font-size: 24rpx;
  color: #777777;
  margin-top: 24rpx;

  .xuzhi {
    color: #4f8cf0;
  }
}
.tip-box {
  width: 500rpx;
  padding: 32rpx;
  border-radius: 24rpx;
}
.tip-tit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  img {
    width: 32rpx;
    height: 32rpx;
  }
  text {
    font-size: 28rpx;
    color: #333333;
    margin-left: 24rpx;
  }
}
.tip-txt {
  font-size: 24rpx;
  color: #777777;
  line-height: 48rpx;
  padding: 32rpx 0;
}
.tip-btm {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .tip-btn {
    width: 134rpx;
    height: 52rpx;
    background: #2370ee;
    border-radius: 8rpx;
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
    line-height: 52rpx;
  }
}
::v-deep .u-popup__content {
  border-radius: 24rpx;
}
</style>
