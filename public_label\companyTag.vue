<!--这个是企业卡片的样式 more==true 代表显示右边的更多-->
<template>
    <view class="item flexRow">
        <view class="item-up">
            <image :src="item.company_info.logo.thumbnail_path_url" mode=""></image>
            <view class="info">
                <view class="user">
                    <view class="userInfo">
                        <view class="name">
                            {{ item.company.name}}
                            <image src="/static/images/plat/vip.png" mode=""></image>
                        </view>
                    </view>
                </view>
                <view class="desc">
                    <view class="desc-item" v-if="item.company_info.short_name">
                        {{ item.company.short_name}}
                    </view>
                    <view class="desc-item" v-if="item.company_info.financing_type_name">
                        {{ item.company_info.financing_type_name}}
                    </view>
                    <view class="desc-item" v-if="item.company_info.size_type_name">
                        {{ item.company_info.size_type_name}}
                    </view>
                </view>

            </view>

            <view class="item-down" v-if="more" @click.stop>
                <view class="dot">
                    <u-icon name="more-dot-fill" @click.stop="clickMore"></u-icon>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        name: "companyTag",
        props: {
            item: {
                type: Object,
                default: () => {}
            },
            more: {
                type: Boolean,
                default: false
            }
        },
        data() {
            return {}
        },
        methods: {
            clickMore(item) {
                this.$emit('more', {
                    item,
                    show: true
                })
            }
        }
    }
</script>
<style lang="less" scoped>
    .wrap {
        display: flex;
        flex-direction: column;
        padding: 0 32rpx;
        margin-bottom: 32rpx;
        background-color: #FFFFFF;
        border-radius: 24rpx;
    }

    .item {
        display: flex;
        flex-direction: column;
        position: relative;
        padding: 24rpx 0;

        .item-up {
            display: flex;
            flex: 1;

            &>image {
                width: 96rpx;
                height: 96rpx;
                border-radius: 20rpx;
            }

            .info {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                flex: 1;
                padding-left: 24rpx;

                .user {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .userInfo {
                        display: flex;
                        align-items: center;

                        .name {
                            display: flex;
                            align-items: center;
                            font-weight: 600;
                            font-size: 32rpx;
                            color: #333333;

                            image {
                                margin-left: 16rpx;
                                width: 32rpx;
                                height: 32rpx;
                            }
                        }
                    }
                }

                .desc {
                    display: flex;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #999999;
                    margin-top: 16rpx;

                    .desc-item {
                        border-right: 1px solid #999999;
                        padding: 0 12rpx;

                        &:first-child {
                            padding-left: 0;
                        }

                        &:last-child {
                            border-right: none;
                        }
                    }
                }

                .flag {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #999999;
                }
            }
        }



        .item-down {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20rpx;

            .tags {
                display: flex;

                .tag {
                    display: flex;
                    align-items: center;
                    background: #F6F6F6;
                    border-radius: 8rpx;
                    height: 46rpx;
                    padding: 0 12rpx;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #666666;
                    margin-right: 16rpx;
                }
            }

            .dot {
                transform: rotate(90deg);
            }
        }

        .arrow {
            position: absolute;
            right: 0;
            top: 50%;
        }
    }

    .flexRow {
        display: flex;
        flex-direction: row;
    }

    .item-down {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;

        .tags {
            display: flex;

            .tag {
                display: flex;
                align-items: center;
                background: #F6F6F6;
                border-radius: 8rpx;
                height: 46rpx;
                padding: 0 12rpx;
                font-weight: 400;
                font-size: 22rpx;
                color: #666666;
                margin-right: 16rpx;
            }
        }

        .dot {
            transform: rotate(90deg);
        }
    }
</style>