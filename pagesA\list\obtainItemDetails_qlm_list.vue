<template>
    <view class="container">
        <view class="list">
            <qlm-job-item v-for="(item,index) in list" :key="index" :item="item"></qlm-job-item>
        </view>
        <Pages :status="status"></Pages>

    </view>

</template>

<script>
    import QlmJobItem from "../../components/qlmJobItem.vue"
    import {
        getHeadhunterUserMember,
    } from "../../config/api.js"
    import Pages from "../../components/pages.vue";
    export default {
        components: {
            Pages,
            QlmJobItem
        },
        data() {
            return {
                status: 'loadmore',
                more: false,
                list: [],
                keyword: '',
                page: 1,
                limit: 10,
                isScroll: false,
                tabIndex: 0,
                tabs: [{
                    name: '全部',
                }, {
                    name: '实习专区',
                }, {
                    name: '零工市场'
                }]
            }
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getHeadhunterUserMember()
            } else {
                this.status = 'nomore'
            }
        },
        onPageScroll(e) {
            this.isScroll = e.scrollTop > 0
        },
        onLoad(options) {
            this.getHeadhunterUserMember(options.id)
        },
        methods: {
            changeTab(e) {
                this.tabIndex = e.index
            },
            //company列表
            async getHeadhunterUserMember(id) {
                let params = {
                    limit: this.limit,
                    page: this.page,
                    id: id,
                }
                const {
                    status_code,
                    data
                } = await getHeadhunterUserMember(params)
                if (status_code == 200) {
                    this.more = data.more
                    this.list = this.list.concat(data.data);
                }
            },
        }
    }
</script>
<style>
    page {
        background-color: #f5f5f5;
    }
</style>
<style lang="less" scoped>
    .list {
        padding: 24rpx 32rpx;
    }

    .header {
        padding: 0 32rpx 32rpx 32rpx;

        .search-wrap {
            margin-top: 32rpx;
        }

        .filters {
            display: flex;
            margin-top: 32rpx;

            .filter {
                display: flex;
                align-items: center;
                height: 48rpx;
                background-color: #FFFFFF;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 0 12rpx;
                margin-right: 12rpx;
                border-radius: 8rpx;

                image {
                    width: 24rpx;
                    height: 24rpx;
                }
            }
        }
    }
</style>