<template>
	<view class="container">
		<!-- 标题栏 -->
		<view class="title-bar">
			<text class="title">宣讲会名称</text>
			<view v-if="isInProgress" class="status-tag">进行中</view>
		</view>
		<view class="create-time">
			<span>创建时间:</span>
			<span>2025年5月20日</span>
		</view>
		<view class="welcome-text">
			<text class="icon"></text>
			<text>****公司举办本次宣讲会，欢迎各位同学踊跃参加！</text>
		</view>

		<!-- 宣讲会介绍部分 -->
		<view class="section">
			<text class="section-title">宣讲会介绍</text>
			<view class="info-item">
				<text class="label">单位性质:</text>
				<text class="value">其他企业（含民营企业等）</text>
			</view>
			<view class="info-item">
				<text class="label">单位规模:</text>
				<text class="value">1000以上</text>
			</view>
			<view class="info-item">
				<text class="label">宣讲时间:</text>
				<text class="value">2025-05-23 18:30-20:00</text>
			</view>
			<view class="info-item">
				<text class="label">宣讲行业:</text>
				<text class="value">智能装备</text>
			</view>
			<view class="info-item">
				<text class="label">宣讲地址:</text>
				<text class="value">南京理工大学6号教学楼1303号</text>
			</view>
		</view>

		<!-- 宣讲会简述部分 -->
		<view class="section">
			<text class="section-title">宣讲会简述</text>
			<text class="description">
				软通动力信息技术（集团）股份有限公司（以下简称“软通动力”）是中国领先的软件与信息技术服务商，致力于成为具有全球影响力的数字技术服务领导企业，企业数字化转型可信赖合作伙伴。2005年，公司成立于北京，立足中国，服务全球市场。目前，在全球40余个城市设有近百个分支机构和超过20个全球交付中心，员工90000余人。
			</text>
		</view>

		<!-- 公司简介部分 -->
		<view class="section">
			<text class="section-title">公司简介</text>
			<text class="description">
				软通动力信息技术（集团）股份有限公司（以下简称“软通动力”）是中国领先的软件与信息技术服务商，致力于成为具有全球影响力的数字技术服务领导企业，企业数字化转型可信赖合作伙伴。2005年，公司成立于北京，立足中国，服务全球市场。目前，在全球40余个城市设有近百个分支机构和超过20个全球交付中心，员工90000余人。
			</text>
		</view>

		<!-- 操作按钮部分 -->
		<view class="action-buttons">
			<img
				class="contact-btn"
				src="https://api-test.zhaopinbei.com/storage/uploads/images/kU4YGKWxEZuVKAPicML8dilsEyRIiXE23GbQMeS6.png"
				alt="" />
			<img
				class="register-btn"
				src="https://api-test.zhaopinbei.com/storage/uploads/images/GgWNKXYbOTl3g5UNxw0UPt2HphPReCMi02aiSPwJ.png"
				alt=""
				srcset="" />
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isInProgress: true,
		};
	},
};
</script>

<style scoped>
.container {
	padding: 30rpx;
}

/* 标题栏样式 */
.title-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
}

.status-tag {
	background-color: #4caf50;
	color: white;
	padding: 10rpx 20rpx;
	border-radius: 10rpx;
}

.create-time {
	color: #999;
	font-size: 28rpx;
	margin-bottom: 20rpx;
	display: flex;
	justify-content: space-between;
}

.welcome-text {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
	font-size: 28rpx;
}

.welcome-text .icon {
	font-family: 'iconfont';
	/* 需引入图标字体库，这里假设已引入 */
	margin-right: 10rpx;
}

/* 信息项样式 */
.section {
	margin-bottom: 40rpx;
	display: flex;
	flex-direction: column;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.info-item {
	display: flex;
	margin-bottom: 20rpx;
}

.label {
	color: rgba(102, 102, 102, 1);
	font-size: 28rpx;
}

.value {
	color: rgba(102, 102, 102, 1);
	font-size: 28rpx;
}

.description {
	line-height: 1.6;
	color: rgba(102, 102, 102, 1);
	font-size: 28rpx;
}

/* 按钮样式 */
.action-buttons {
	display: flex;
	justify-content: space-around;
}

.contact-btn {
	width: 308rpx;
	height: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.register-btn {
	color: white;
	border: none;
	border-radius: 10rpx;
	width: 308rpx;
	height: 80rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>
