<template>
	<view class="">
		<view class="flex-tabs">
			<view @click="isShow('detailsShow')" :class="{ 'active': currentTab === 'detailsShow' }">特准招聘</view>
			<view @click="isShow('qlmActiveShow')" :class="{ 'active': currentTab === 'qlmActiveShow' }">招聘会</view>
			<view @click="isShow('takePartShow')" :class="{ 'active': currentTab === 'takePartShow' }">宣讲会</view>
		</view>
		<view class="list">
			<job-fair-item v-for="(item,index) in list" :key="index" :item="item"></job-fair-item>
		</view>
		<Pages :status="status"></Pages>
	</view>

</template>

<script>
	import {
		getJobfairList
	} from "../../config/api.js"
	import JobFairItem from "../components/jobFairItem.vue"
	import Pages from "../../components/pages.vue";
	export default {
		components: {
			JobFairItem,
			Pages,
		},
		data() {
			return {
				status: 'loadmore',
				more: false,
				page: 1,
				limit: 10,
				list: [],
				currentTab: '',
			}
		},
		computed: {
			// list() {
			// 	return this.$store.state.roleType == 'member' ? this.list1 : this.list2
			// }
		},
		onLoad() {
			this.getJobfairList()
			this.currentTab = 'qlmActiveShow'
		},
		//触底加载更多
		onReachBottom() {
			if (this.more) {
				this.status = 'loading'
				this.page++
				this.getJobfairList()
			} else {
				this.status = 'nomore'
			}
		},
		methods: {
			async getJobfairList() {
				let params = {
					page: this.page,
					limit: this.limit
				}
				const {
					status_code,
					data,
					message
				} = await getJobfairList(params)
				if (status_code == 200) {
					this.list = this.list.concat(data.data);
					// 返回false代表没有下一页
					this.more = data.more;
					this.status = this.more ? "loadmore" : "nomore"
				}
			},
			isShow(name) {
				this.qlmActiveDetails = false;
				this.detailsShow = false;
				this.takePartShow = false;

				if (name === 'qlmActiveShow') {
					this.currentTab = name
					uni.redirectTo({
						url: "/pagesA/list/job_fair_list"
					})
					this.qlmActiveDetails = true;
				} else if (name === 'detailsShow') {
					this.currentTab = name
					uni.redirectTo({
						url: '/pagesA/details/qlmActiveShow'
					})
					console.log(this.detailsShow);
				} else if (name === 'takePartShow') {
					this.currentTab = name
					uni.redirectTo({
						url: '/pagesA/details/QlmjobFairTalk/TalkList'
					})
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.list {
		padding: 32rpx;
	}

	.flex-tabs {
		width: 508rpx;
		height: 44rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-left: 32rpx;
		margin-top: 32rpx;
		margin-bottom: 62rpx;
		font-size: 28rpx;
		font-weight: bold;
	}

	.active {
		color: rgba(79, 140, 240, 1);
		font-size: 32rpx;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.active:before {
		content: '';
		position: absolute;
		width: 32rpx;
		height: 6rpx;
		background-color: rgba(79, 140, 240, 1);
		border-radius: 50rpx;
		bottom: -8rpx;
	}
</style>