<template>
	<view>
		<view class="container">
			<view class="userInfo">
				<view class="avatar">
					<image
						:src="userInfo.member_info.image.path_url?userInfo.member_info.image.path_url:'https://api-test.zhaopinbei.com/storage/uploads/images/41R1LgOXldsXlyAjelHeNXsb0SuBPKE7ANcgobdG.png'"
						mode=""></image>
				</view>
				<view class="user" @click="goPersonalCenter">
					<view class="user-up">
						<view class="name">
							{{userInfo.member_certification.name ? userInfo.member_certification.name: '请完善个人信息'}}
						</view>
						<view class="cert" v-if="userInfo.member.certification_status==1">
							已认证
						</view>
						<view class="cert gray" v-else>
							未认证
						</view>
					</view>
					<view class="user-down">
						<view class="lab">
							千里马
						</view>
						<view class="tags"
							v-if="userInfo.member_info.age&&userInfo.member_info.nation&&userInfo.member_info.education_type_name">
							<view class="tag">
								{{userInfo.member_info.age}}岁
							</view>
							<view class="tag">
								{{userInfo.member_info.nation}}
							</view>
							<view class="tag">
								{{userInfo.member_info.education_type_name}}
							</view>
						</view>
						<view v-else class="tags">
							<view class="tag">
								请完善个人信息
							</view>
						</view>
					</view>
				</view>

				<view class="personal" @click="goPersonalCenter">
					个人信息
				</view>
			</view>

			<view class="packageCard">

				<view class="package-wrap">
					<view class="package">
						<view class="title">
							我的招聘币 <image src="/static/images/my/eye.png" mode="" @click="toggleEye"></image>
						</view>
						<view class="num">
							{{isEye?'88480.99':'******'}}
						</view>

						<view class="lab" @click="goPackage">
							我的钱包 <u-icon name="arrow-right" size="24rpx"></u-icon>
						</view>
					</view>
				</view>

				<view class="package-job">
					<view class="jobModule" @click="getjobViewed">
						<h3>36</h3>
						<p class="jobModule-title">浏览历史</p>
					</view>
					<view class="jobModule" @click="getjobList">
						<h3>22</h3>
						<p class="jobModule-title">职位管理</p>
					</view>
					<view class="jobModule" @click="getjobHelper">
						<h3>3</h3>
						<p class="jobModule-title">我的就业管家</p>
					</view>
					<view class="jobModule" @click="getjobCollect">
						<h3>3</h3>
						<p class="jobModule-title">收藏</p>
					</view>
				</view>
			</view>
			<scroll-view scroll-y="true" class="scroll-Y">
				<view class="list">
					<view class="item" @click="goResume">
						<view class="item-left">
							<image src="/static/images/my/jl.svg" mode=""></image>
							<text>我的简历</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>

					<view class="item" @click="gojobsetting">
						<view class="item-left">
							<image src="/static/images/my/mysetting.png" mode=""></image>
							<text>我的求职设置</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>
					<view class="item" @click="gofllow">
						<view class="item-left">
							<image src="https://api-test.zhaopinbei.com/storage/uploads/images/V4Fcjjdp7kPJLNnPVc0gKi9JtUUXWVaNAFUhw6Fn.png" mode=""></image>
							<text>我的关注</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>
					<view class="item" @click="gojobsetting1">
						<view class="item-left">
							<image src="/static/images/my/mysetting.png" mode=""></image>
							<text>活动记录管理</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>
					<view class="item" @click="onHello">
						<view class="item-left">
							<image src="/static/images/my/createMyCom.svg" mode=""></image>
							<text>常用语/招呼语</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>

					<!-- <view class="item" @click="applyHeadhunter">
					<view class="item-left">
						<image src="https://api-test.zhaopinbei.com/storage/uploads/images/HV8XmMgOm71Zci6PuSuwAdJJYwhr0yoYMZSw2vfj.png" mode=""></image>
						<text>申请就业管家</text>
					</view>
					<u-icon name="arrow-right" size="24rpx"></u-icon>
				</view> -->
					<!-- <view class="item">
					<view class="item-left">
						<image src="/static/images/my/friends.png" mode=""></image>
						<text>邀请好友</text>
					</view>
					<u-icon name="arrow-right" size="24rpx"></u-icon>
				</view> -->
					<view class="item" @click="goChuangzuo">
						<view class="item-left">
							<image src="/static/images/my/createdcenter.png" mode=""></image>
							<text>创作中心</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>
					<view class="item" @click="createCom">
						<view class="item-left">
							<image src="/static/images/my/createMyCom.svg" mode=""></image>
							<text>创建我的企业</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>
					<view class="item">
						<view class="item-left">
							<image src="/static/images/my/kf.svg" mode=""></image>
							<text>在线客服</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>
					<view class="item" @click="goSetting">
						<view class="item-left">
							<image src="/static/images/my/setting.svg" mode=""></image>
							<text>设置</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>
					<view class="item">
						<view class="item-left">
							<image src="/static/images/my/friends.svg" mode=""></image>
							<text>申请成为就业管家</text>
						</view>
						<u-icon name="arrow-right" size="24rpx"></u-icon>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		isAuth
	} from '@/common/common.js'
	export default {
		name: "qlmPersonalCenter",
		data() {
			return {

			};
		},
		computed: {
			userInfo() {
				return this.$store.state.userInfo || uni.getStorageSync('userInfo')
			}
		},
		methods: {
			goFavor() {
				uni.navigateTo({
					url: "/pagesA/list/qlm_collect_list"
				})
			},
			toggleEye() {
				this.isEye = !this.isEye
			},
			goPersonalCenter() {
				uni.navigateTo({
					url: "/pagesA/personal/personalCenter"
				})
			},
			goSetting() {
				uni.navigateTo({
					url: "/pages/my/setting"
				})
			},
			goPackage() {
				// uni.navigateTo({
				// 	url:"/pagesA/personal/package"
				// })
				uni.$u.toast('暂未开放')
			},

			goResume() {
				uni.navigateTo({
					url: "/pagesA/personal/myResume"
				})
			},
			createCom() {
				if (!isAuth(["auth"])) return
				uni.navigateTo({
					url: "/pages/my/company_cert"
				})
			},
			goChuangzuo() {
				uni.navigateTo({
					url: '/pagesC/publishCenter/index'
				})
			},
			// /pages/register/register_headhunters
			// applyHeadhunter() {
			// 	if (!isAuth(['auth'])) return
			// 	uni.navigateTo({
			// 		url:"/pages/register/register_headhunters"
			// 	})
			// }
			getjobList() {
				uni.navigateTo({
					//url: '/pagesA/getJob/jobList'
					url: '/pagesB/personal/find_job_record?id=1'
				})
			},
			getjobHelper() {
				uni.navigateTo({
					//url: '/pagesA/getJob/jobHelper'
					url: '/pagesA/member/details/authorizeDetail'
				})
			},
			getjobCollect() {
				uni.navigateTo({
					//url: '/pagesA/getJob/jobCollected'
					url: '/pagesB/personal/find_favor_record'
				})
			},
			getjobViewed() {
				uni.navigateTo({
					//url: '/pagesA/getJob/jobViewed'
					url: '/pagesB/personal/history?id=1'
				})
			},
			gojobsetting() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/myJobsetting'
				})
			},
			gofllow() {
				uni.navigateTo({
					url: '/pagesB/personal/myFllow?id=1'
				})
			},
			gojobsetting1() {
				uni.navigateTo({
					url: '/pagesB/personal/activitie'
				})
			},

			onHello() {
				uni.navigateTo({
					url: '/pagesC/message/express'
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.container {
		display: flex;
		flex-direction: column;
		padding: 0 32rpx;

		.userInfo {
			// background-color: red;
			display: flex;
			position: relative;
			// padding: 0 32rpx;
			margin-top: 32rpx;

			.personal {
				position: absolute;
				display: flex;
				justify-content: center;
				align-items: center;
				right: 0;
				top: 50%;
				margin-top: -30rpx;
				height: 60rpx;
				width: 136rpx;
				font-weight: 500;
				font-size: 20rpx;
				color: #333333;
				border-radius: 30rpx 0 0 30rpx;
				background: linear-gradient(112deg, #FFDB7B 0%, #F9AD14 100%);
			}

			.avatar {
				display: flex;
				justify-content: center;
				align-items: center;
				background: #FFFFFF;
				width: 108rpx;
				height: 108rpx;
				border-radius: 50%;

				image {
					width: 98rpx;
					height: 98rpx;
				}
			}

			.user {
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				flex: 1;
				padding-left: 16rpx;

				.user-up {
					display: flex;
					align-items: center;

					.name {
						font-weight: 500;
						font-size: 32rpx;
						color: #333333;
					}

					.cert {
						display: flex;
						align-items: center;
						height: 40rpx;
						background: #57D51C;
						font-weight: 600;
						font-size: 20rpx;
						color: #FFFFFF;
						padding: 0 12rpx;
						border-radius: 8rpx;
						margin-left: 12rpx;
					}

					.gray {
						background-color: #cccccc;
					}
				}

				.user-down {
					display: flex;
					align-items: center;

					.lab {
						display: flex;
						justify-content: center;
						align-items: center;
						background-color: #57D51C;
						padding: 0 12rpx;
						height: 30rpx;
						border-radius: 15rpx;
						font-weight: 400;
						font-size: 20rpx;
						color: #FFFFFF;
						margin-right: 12rpx;
						font-style: italic;
					}

					.tags {
						display: flex;

						.tag {
							display: flex;
							align-items: center;
							padding: 0 12rpx;
							font-weight: 400;
							font-size: 22rpx;
							color: #999999;
							// margin-right: 16rpx;
							border-right: 1px solid #999999;

							&:first-child {
								padding-left: 0;
							}

							&:last-child {
								border-right: none;
							}
						}
					}
				}
			}
		}




		.packageCard {
			width: 100%;
			height: 187rpx;
			position: relative;
			margin-bottom: 32rpx;
		}

		.package-wrap {
			padding: 0 32rpx;
			margin-top: 30rpx;

			// background-color: red;
			.package {
				// background: linear-gradient( 90deg, #42B0EC 0%, #4F8CF0 100%);
				position: relative;
				border-radius: 24rpx;
				filter: blur(0.3px);
				background: linear-gradient(90deg, #42B0EC 0%, #4F8CF0 100%);
				border-radius: 12px 12px 12px 12px;
				padding: 24rpx 32rpx;

				.title {
					font-weight: 400;
					font-size: 24rpx;
					color: #FFFFFF;

					image {
						width: 24rpx;
						height: 24rpx;
						margin-left: 10rpx;
					}
				}

				.num {
					font-weight: bold;
					font-size: 64rpx;
					color: #FFFFFF;
					margin-top: 8rpx;
				}

				.lab {
					position: absolute;
					display: flex;
					justify-content: center;
					align-items: center;
					right: -32rpx;
					top: 50%;
					margin-top: -40rpx;
					height: 80rpx;
					width: 168rpx;
					font-weight: 500;
					font-size: 24rpx;
					color: #333333;
					border-radius: 16rpx;
					backdrop-filter: blur(25.6px);
					background: rgba(255, 255, 255, 0.7);
				}
			}


		}

		.package-job {
			width: 100%;
			height: 86%;
			background-color: rgba(255, 255, 255, 0.7);
			border-radius: 16rpx;
			-webkit-backdrop-filter: blur(25.6rpx);
			backdrop-filter: blur(25.6rpx);
			font-size: 24rpx;
			position: absolute;
			display: flex;
			justify-content: space-around;
			align-items: center;
			z-index: 28;
			margin-top: -19rpx;
			border-radius: 16rpx;

			.jobModule {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				.jobModule-title {
					margin-top: 8rpx;
				}
			}
		}
	}


	.list {
		// 背景颜色变更
		display: flex;
		flex-direction: column;
		backdrop-filter: blur(25.6px);
		background: rgba(255, 255, 255, 0.7);
		box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
		padding: 0 48rpx;
		border-radius: 24rpx;
		margin-top: 137rpx;
		z-index: 10;

		//
		.item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 100rpx;

			.item-left {
				display: flex;
				align-items: center;
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;

				image {
					width: 32rpx;
					height: 32rpx;
				}

				text {
					margin-left: 24rpx;
				}
			}
		}
	}

	.scroll-Y {
		height: 850rpx;
	}
</style>