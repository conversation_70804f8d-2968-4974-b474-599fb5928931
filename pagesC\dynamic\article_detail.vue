<template>
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="article-content">
					<view class="user-info">
						<image class="avatar"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png">
						</image>
						<text>娃哈哈</text>
						<view class="attention"
							:style="{ backgroundColor: isfocus? '#4F8CF0' : '', color: isfocus? '#FFFFFF' : '#4F8CF0' }"
							@click="focusON">{{isfocus ? '已关注' : '关注'}}</view>
					</view>
					<view class="article-container">
						<view class="text">{{info.content}}</view>
					</view>
				</view>
				<u-gap height="12" bgColor="#f5f5f7"></u-gap>
				<view class="comment-content">
					<view class="comment-bar">
						<text>339</text>
						<text>条评论</text>
					</view>

					<view class="comment-container">
						<template v-if="!commentList.length">
							<view>暂无数据</view>
						</template>
						<view class="item" v-for="v in commentList" :key="v.id">
							<view class="item-start">
								<view class="user-info">
									<image class="avatar"
										src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png">
									</image>
									<text>娃哈哈</text>
								</view>
								<view class="like-box">
									<!-- https://api-test.zhaopinbei.com/storage/uploads/images/m3cxrDZGgPDeMLYs4c7FYGzKzX5DlxYTIqHtUJF6.png -->
									<image class="image"
										src="https://api-test.zhaopinbei.com/storage/uploads/images/WvDtiWHzkzOXTCoxhIIXbnYxTMGDKr4XKr7d4NPS.png">
									</image>
									<text>204</text>
								</view>
							</view>
							<view class="item-end">
								<view class="content">{{v.content}}</view>
								<view class="time">{{v.created_at}}</view>
								<view class="reply" v-if="false">
									<template v-for="_ in 12">
										<view class="item-start">
											<view class="user-info">
												<image class="avatar"
													src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png">
												</image>
												<text>娃哈哈</text>
											</view>
											<view class="like-box">
												<!-- https://api-test.zhaopinbei.com/storage/uploads/images/m3cxrDZGgPDeMLYs4c7FYGzKzX5DlxYTIqHtUJF6.png -->
												<image class="image"
													src="https://api-test.zhaopinbei.com/storage/uploads/images/WvDtiWHzkzOXTCoxhIIXbnYxTMGDKr4XKr7d4NPS.png">
												</image>
												<text>204</text>
											</view>
										</view>
										<view class="item-end">
											<view class="content">
												机了阿拉斯加福利卡上飞机了阿拉斯加福利卡上飞机了阿拉斯加福利卡上飞机了阿拉斯加福利卡上飞机了阿拉斯加福利卡上飞机了阿拉斯加福利卡上飞机了
											</view>
											<view class="time">12/24 11:39</view>
										</view>
									</template>
									<view class="more">
										<text>展开 1 条回复</text>
										<u-icon color="#4F8CF0" size="12" name="arrow-down"></u-icon>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="actions-container">
			<input class="input" type="text" v-model="review" @confirm="handleEnter">
			<view class="item">
				<image class="image"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/WvDtiWHzkzOXTCoxhIIXbnYxTMGDKr4XKr7d4NPS.png"
					mode=""></image>
				<text>869</text>
			</view>
			<view class="item">
				<image class="image"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/WvDtiWHzkzOXTCoxhIIXbnYxTMGDKr4XKr7d4NPS.png"
					mode=""></image>
				<text>869</text>
			</view>
			<view class="item">
				<image class="image"
					src="https://api-test.zhaopinbei.com/storage/uploads/images/WvDtiWHzkzOXTCoxhIIXbnYxTMGDKr4XKr7d4NPS.png"
					mode=""></image>
				<text>869</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getArticleDetail,
		getCommentIndexList
	} from '@/config';
	export default {
		data() {
			return {
				params: {
					id: null,
				},
				info: {},
				commentList: [],
				review: '',
				isfocus: false,
			};
		},
		onLoad(options) {
			this.params.id = options.id;
			this.onGetArticleDetail();
			this.onGetCommentIndexList();
		},
		methods: {
			onBack() {
				uni.$u.route({
					type: 'back'
				});
			},
			async onGetCommentIndexList() {
				const res = await getCommentIndexList({
					article_id: this.params.id
				});
				if (res.status_code !== '200') return;
				this.commentList = res.data;
			},
			async onGetArticleDetail() {
				const res = await getArticleDetail(this.params);
				if (res.status_code !== '200') return;
				this.info = res.data;
			},
			handleEnter() {
				const date = new Date();

				// 2. 格式化时间为 MM/dd HH:mm 格式
				const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份（0-11 → 转1-12，补零）
				const day = String(date.getDate()).padStart(2, '0'); // 日期（补零）
				const hours = String(date.getHours()).padStart(2, '0'); // 小时（24小时制，补零）
				const minutes = String(date.getMinutes()).padStart(2, '0'); // 分钟（补零）

				// 组合成目标格式
				const formattedTime = `${month}/${day} ${hours}:${minutes}`;
				console.log(this.review);
				this.commentList.push({
					id: Date.now(),
					name: '娃哈哈',
					imgUrl: 'https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png',
					Zan: 204,
					content: this.review,
					created_at: formattedTime,
					// 假定的回复
					// replay: [{
					// 	id: Date.now(),
					// 	name: '娃哈哈',
					// 	imgUrl: 'https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png',
					// 	Zan: 204,
					// 	content: this.review,
					// 	created_at: formattedTime,
					// }]
				})
				this.review = ''
			},
			focusON() {
				this.isfocus = !this.isfocus
			}
		},
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #FFFFFF;
		display: flex;
		flex-direction: column;

		.actions-container {
			background-color: #FFFFFF;
			border-block-start: 1rpx #f5f5f7 solid;
			padding: 24rpx;
			padding-block-end: calc(24rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			display: flex;
			align-items: center;
			gap: 24rpx;

			.input {
				background-color: #f5f5f7;
				border-radius: 999rpx;
				padding-block: 8rpx;
				padding-inline: 24rpx;
				font-size: 24rpx;
				flex: 1;
			}

			.item {
				display: flex;
				flex-direction: column;
				align-items: center;
				font-size: 24rpx;
				color: #666666;

				.image {
					width: 38rpx;
					height: 38rpx;
					object-fit: contain;
				}
			}
		}

		.nav-container {
			display: flex;
			align-items: center;
			gap: 32rpx;

			.attention {
				display: inline-flex;
				border: 2rpx #4F8CF0 solid;
				border-radius: 999rpx;
				padding-block: 4rpx;
				padding-inline: 32rpx;
				background-color: #FFFFFF;
				color: #4F8CF0;
				font-size: 24rpx;
			}

			.user-info {
				display: flex;
				align-items: center;
				color: #333333;
				font-size: 28rpx;
				gap: 24rpx;

				.avatar {
					width: 56rpx;
					height: 56rpx;
					object-fit: contain;
					border-radius: 999rpx;
				}
			}
		}


		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				padding-block: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 32rpx;

				.comment-content {
					display: flex;
					flex-direction: column;
					gap: 48rpx;
					padding-inline: 32rpx;

					.comment-bar {
						display: flex;
						align-items: center;
						gap: 12rpx;
						color: #333333;
						font-size: 28rpx;
					}

					.comment-container {
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.item {
							display: flex;
							flex-direction: column;
							gap: 16rpx;

							.item-end {
								display: flex;
								flex-direction: column;
								gap: 16rpx;
								padding-inline-start: 78rpx;

								.reply {
									display: flex;
									flex-direction: column;
									gap: 16rpx;

									.item-start {
										.avatar {
											width: 48rpx;
											height: 48rpx;
											object-fit: contain;
										}
									}

									.more {
										display: flex;
										align-items: center;
										gap: 6rpx;
										color: #4F8CF0;
										font-size: 24rpx;
										padding-inline-start: 78rpx;
									}
								}

								.content {
									color: #666666;
									font-size: 26rpx;
								}

								.time {
									color: #999999;
									font-size: 24rpx;
								}
							}

							.item-start {
								display: flex;
								align-items: center;
								justify-content: space-between;

								.user-info {
									display: flex;
									align-items: center;
									color: #333333;
									font-size: 28rpx;
									gap: 24rpx;

									.avatar {
										width: 56rpx;
										height: 56rpx;
										object-fit: contain;
										border-radius: 999rpx;
									}
								}

								.like-box {
									color: #666666;
									font-size: 24rpx;
									display: flex;
									align-items: center;
									gap: 8rpx;

									.image {
										width: 38rpx;
										height: 38rpx;
										object-fit: contain;
									}
								}
							}
						}
					}
				}

				.article-content {
					display: flex;
					flex-direction: column;
					gap: 24rpx;
					padding-inline: 32rpx;

					.user-info {
						display: flex;
						align-items: center;
						color: #333333;
						font-size: 32rpx;
						gap: 24rpx;

						.attention {
							display: inline-flex;
							border: 2rpx #4F8CF0 solid;
							border-radius: 999rpx;
							padding-block: 4rpx;
							padding-inline: 32rpx;
							background-color: #FFFFFF;
							color: #4F8CF0;
							font-size: 24rpx;
							margin-inline-start: auto;
						}

						.avatar {
							width: 96rpx;
							height: 96rpx;
							object-fit: contain;
							border-radius: 999rpx;
						}
					}

					.article-container {
						.text {
							word-break: break-word;
							white-space: normal;
							color: #333333;
							font-size: 28rpx;
						}
					}
				}
			}
		}
	}
</style>