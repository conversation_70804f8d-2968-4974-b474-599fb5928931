<template>
	<view class="">
		<u-sticky bgColor="#F5F5F5">
			<view class="search-wrap">
				<u-search placeholder="请输入职位名称" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
			</view>
		</u-sticky>
		<view class="list">
			<job-apply-item v-for="item in list" :key="item.id" :item="item"></job-apply-item>
		</view>
        <Pages :status="status"></Pages>
	</view>
</template>

<script>
    import Pages from "../../components/pages.vue";
    import {jobReportList} from '../../config/headhunterList_api.js'
	import JobApplyItem from '../components/jobApplyItem.vue'
	export default{
		components:{
			JobApplyItem,
            Pages
		},
		data(){
			return{
				page: 1,
				limit: 10,
                status: 'loadmore',
                more: false,
                list:[]
			}
		},
        onLoad() {
            this.jobReportList()
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.jobReportList()
            } else {
                this.status = 'nomore'
            }
        },
        methods:{
            async jobReportList() {
                let params = {
                    page:this.page,
                    limit:this.limit
                }
                const res = await jobReportList(params)
                this.list = this.list.concat(res.data.data);
                this.more = res.more;
                this.status = this.more ? "loadmore" : "nomore"
            }
        }
	}
</script>
<style>
	page{
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.search-wrap{
		padding: 32rpx;
	}
	.list{
		padding: 0 32rpx 32rpx 32rpx;
	}
</style>