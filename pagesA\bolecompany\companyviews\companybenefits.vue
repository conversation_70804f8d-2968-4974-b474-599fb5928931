<template>
	<view style="      height: 100vh;  display: flex;
    flex-direction: column;
    justify-content: space-between;">
		<view class="container">
			<uni-section type="line" padding>
				<uni-steps :options="list1" active-icon="checkbox" :active="active" />
			</uni-section>
			<view style="margin: 0 auto;">
				<scroll-view class="scroll-view_H" scroll-x="true" @scroll="scroll">
					<view class="scroll-view-item_H">保险</view>
					<view class="scroll-view-item_H">薪资权限</view>
					<view class="scroll-view-item_H">度假休假</view>
					<view class="scroll-view-item_H">生活补贴</view>
					<view class="scroll-view-item_H">自定义</view>
				</scroll-view>
			</view>

			<view style="height: 1000rpx; overflow-y: auto;">
				<scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltoupper="upper"
					@scrolltolower="lower" @scroll="scroll">
					<view class="scroll-view-item uni-bg-red">
						<!-- 保险部分 -->
						<view class="section">
							<text class="section-title">保险</text>
							<view class="insurance-item" v-for="(insurance, index) in insuranceList" :key="index">
								<view class="check-label">
									<view class="checktitle">
										<view class="check-svg">
										</view>
										<view class="checktitle-item">
											<text>
												{{insurance.name}}
											</text>
										</view>
									</view>
									<view>
										<image v-if="insurance.checked" :src="isselected" class="check-img"
											@click="handleCancel(index, 'insurance')"></image>
										<image v-else :src="add" class="check-img"
											@click="handleAdd(index, 'insurance')">
										</image>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="scroll-view-item uni-bg-green">
						<!-- 薪资提交部分 -->
						<view class="section">
							<text class="section-title">薪资提交</text>
							<view class="salary-item" v-for="(salary, index) in salaryList" :key="index">
								<view class="check-label">
									<view class="checktitle">
										<view class="check-svg">
										</view>
										<view class="checktitle-item">
											{{salary.name}}
										</view>
									</view>
									<view>
										<image v-if="salary.checked" :src="isselected" class="check-img"
											@click="handleCancel(index, 'salary')"></image>
										<image v-else :src="add" class="check-img" @click="handleAdd(index, 'salary')">
										</image>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="scroll-view-item uni-bg-blue">
						<!-- 度假休假部分 -->
						<view class="section">
							<text class="section-title">度假休假</text>
							<view class="holiday-item" v-for="(holiday, index) in holidayItems" :key="index">
								<view class="check-label">
									<view class="checktitle">
										<view class="check-svg">
										</view>
										<view class="checktitle-item">
											{{holiday.title}}
										</view>
									</view>
									<view>
										<image v-if="holiday.checked" :src="isselected" class="check-img"
											@click="handleCancel(index, 'holiday')"></image>
										<image v-else :src="add" class="check-img" @click="handleAdd(index, 'holiday')">
										</image>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="scroll-view-item uni-bg-blue">
						<!-- 生活补贴部分  -->
						<view class="section">
							<text class="section-title">生活补贴部分</text>
							<view class="allowance-item" v-for="(allowance, index) in allowanceItems" :key="index">
								<view class="check-label">
									<view class="checktitle">
										<view class="check-svg">
										</view>
										<view class="checktitle-item">
											{{allowance.title}}
										</view>
									</view>
									<view>
										<image v-if="allowance.checked" :src="isselected" class="check-img"
											@click="handleCancel(index, 'allowance')"></image>
										<image v-else :src="add" class="check-img"
											@click="handleAdd(index, 'allowance')">
										</image>
									</view>
								</view>
							</view>
						</view>
					</view>
					<view class="customize-container">
						<view class="title">自定义</view>
						<view class="allowance-item" v-for="(allowance, index) in allowanceItems" :key="index">
							<view class="check-label">
								<view class="checktitle">
									<view class="check-svg">
									</view>
									<view class="checktitle-item">
										{{allowance.title}}
									</view>
								</view>
								<view>
									<image v-if="allowance.checked" :src="isselected" class="check-img"
										@click="handleCancel(index, 'allowance')"></image>
									<image v-else :src="add" class="check-img" @click="handleAdd(index, 'allowance')">
									</image>
								</view>
							</view>
						</view>
						<view class="add-new-container">
							<view class="add-new-box" @click="openAddNewModal">
								<image class="add-icon" :src="add1"></image>
								<text class="add-text">添加新福利</text>
							</view>
						</view>
					</view>


					<view class="selected-courses">
						<view
							style="width: 96rpx; height: 42rpx; margin-left: 8rpx;font-size: 24rpx;text-align: center;display: flex; align-items: center;">
							已选：
						</view>
						<scroll-view class="scroll-view_H" scroll-x="true">
							<view style="display: flex; align-items: center;">
								<view v-for="(item, index) in selectedItems" :key="index" style="padding:6rpx 8rpx; font-size: 20rpx; background-color: rgba(232, 241, 255, 1); color: rgba(79, 140, 240, 1);
																 margin: 0rpx 8rpx; display: flex; align-items: center;">
									{{item}}
									<image class="close-icon" :src="close" @click="removeItem(index)">
									</image>
								</view>
							</view>
						</scroll-view>
					</view>
				</scroll-view>
			</view>
		</view>

		<view class="page-container">
			<view class="card-container">
				<!-- 这里放置除按钮外卡片内的其他内容 -->
				<button class="prev-button" @click="goback">
					<text style="font-size: 28rpx;">
						上一步
					</text>
				</button>
				<button class="save-button" @click="saveForm">
					<text style="font-size: 28rpx;">
						保存
					</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	//import isselected from 'https://api-test.zhaopinbei.com/storage/uploads/images/4fp1OMTdhrEdUoANrUPtu67XSLgCnaOtXGTboTa0.png'
	/*import add1 from '../../static/images/add1.png'*/
	/*import close from '../../static/images/close.png'*/

	export default {
		data() {
			return {
				active: 1,
				list1: [{
					title: '工作时间'
				}, {
					title: '公司福利 1/20'
				}],
				insuranceList: [{
						name: '五险一金',
						value: 'insurance',
						checked: false,
						showAddIcon: true
					},
					{
						name: '补充医疗保险',
						value: 'insurance',
						checked: true,
						showAddIcon: true
					},
					{
						name: '意外险',
						value: 'insurance',
						checked: false,
						showAddIcon: true
					},
					{
						name: '定期体检',
						value: 'insurance',
						checked: false,
						showAddIcon: true
					}
				],
				salaryList: [{
						name: '年终奖',
						value: 'salary',
						checked: true,
						showAddIcon: true
					},
					{
						name: '绩效奖金',
						value: 'salary',
						checked: false,
						showAddIcon: true
					},
					{
						name: '保底工资',
						value: 'salary',
						checked: false,
						showAddIcon: true
					},
					{
						name: '底薪加提成',
						value: 'salary',
						checked: false,
						showAddIcon: true
					},
					{
						name: '股票期权',
						value: 'salary',
						checked: false,
						showAddIcon: true
					}
				],
				allowanceItems: [{
						title: '包吃',
						value: 'allowance',
						description: '补贴员工因公实际发生的饮食费用',
						checked: true,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/baochiicon_slices/包吃icon.png')
					},
					{
						title: '餐补',
						value: 'allowance',
						description: '工资餐的额外补贴',
						checked: true,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/餐补icon_slices/餐补icon.png')

					},
					{
						title: '零食下午茶',
						value: 'allowance',
						description: '为员工提供零食、饮料、水果作为下午茶',
						checked: true,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/零食下午茶icon_slices/零食下午茶icon.png')
					},
					{
						title: '团建聚餐',
						value: 'allowance',
						description: '提供给员工一定的团建聚餐经费',
						checked: true,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/团建聚餐icon_slices/团建聚餐icon.png')
					},
					{
						title: '包住',
						value: 'allowance',
						description: '提供居住宿舍,且不额外收费',
						checked: true,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/包住icon_slices/包住icon.png')
					},
					{
						title: '宿舍有空调',
						value: 'allowance',
						description: '所提供居住宿舍中有空调',
						checked: true,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/宿舍有空调icon_slices/宿舍有空调icon.png')
					},
					{
						title: '住房补贴',
						value: 'allowance',
						description: '每月员工因公实际发生的住宿费用',
						checked: true,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/住房补贴icon_slices/住房补贴icon.png')
					},
					{
						title: '有无线网',
						value: 'allowance',
						description: '宿舍提供无线网',
						checked: true,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/有无线网icon_slices/有无线网icon.png')

					},
					{
						title: '免费工装',
						value: 'allowance',
						description: '统一发放工装，且不额外收费',
						checked: true,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/免费工装icon_slices/免费工装icon.png')
					}
				],
				holidayItems: [{
						title: '带薪年假',
						value: 'holiday',
						description: '为员工提供带薪年休假',
						checked: false,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/带薪年假icon_slices/带薪年假icon.png'),
					},
					{
						title: '员工旅游',
						value: 'holiday',
						description: '组织员工旅游活动',
						checked: false,
						showAddIcon: true,
						//imgUrl: fileAssetsImage('company/companybenefits/员工旅游icon_slices/员工旅游icon.png'),
					}
				],
				scrollTop: 0,
				old: {
					scrollTop: 0
				},
				add: 'https://api-test.zhaopinbei.com/storage/uploads/images/0pf04fMH3Rvbv0AOHEgGi2ASe9CRmNlbx23vX0GL.png',
				isselected: 'https://api-test.zhaopinbei.com/storage/uploads/images/4fp1OMTdhrEdUoANrUPtu67XSLgCnaOtXGTboTa0.png',
				add1: 'https://api-test.zhaopinbei.com/storage/uploads/images/DWz5dfnUmI0aKDWhKzc72Qa1Rd83FwmZ7sE3q45O.png',
				close: 'https://api-test.zhaopinbei.com/storage/uploads/images/oTSzUI2bt0qYqMFDRcEudXVx3LrlynCxZAC2jWKD.png',
				selectedItems: ['五险一金', '五险一金', '五险一金', '五险一金', '五险一金']
			}
		},
		methods: {
			handleAdd(index, type) {
				if (type === 'insurance') {
					const item = this.insuranceList[index];
					item.checked = !item.checked;
				} else if (type === 'salary') {
					const item = this.salaryList[index];
					item.checked = !item.checked;
				} else if (type === 'holiday') {
					const item = this.holidayItems[index];
					item.checked = !item.checked;
					console.log(item.checked);
				} else if (type === 'allowance') {
					const item = this.allowanceItems[index];
					item.checked = !item.checked;
				}
			},

			handleCancel(index, type) {

				if (type === 'insurance') {
					const item = this.insuranceList[index];
					item.checked = false;
				} else if (type === 'salary') {
					const item = this.salaryList[index];
					item.checked = false;
				} else if (type === 'holiday') {
					const item = this.holidayItems[index];
					item.checked = false;
				} else if (type === 'allowance') {
					const item = this.allowanceItems[index];
					item.checked = false;
				}
			},

			upper: function(e) {
				console.log(e)
			},
			lower: function(e) {
				console.log(e)
			},
			scroll: function(e) {
				console.log(e)
				this.old.scrollTop = e.detail.scrollTop
			},
			goTop: function(e) {
				this.scrollTop = this.old.scrollTop
				this.$nextTick(function() {
					this.scrollTop = 0
				});
				uni.showToast({
					icon: "none",
					title: "纵向滚动 scrollTop 值已被修改为 0"
				})
			},
			openAddNewModal() {
				// console.log('打开添加新福利模态框');
			},
			removeItem(index) {
				this.selectedItems.splice(index, 1);
			},
			saveForm() {
				uni.navigateBack({
					delta: 1
				})
			},
			goback() {
				uni.navigateBack({
					delta: 1
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.scroll-Y {
		height: 1224rpx;
	}

	.scroll-view_H {
		white-space: nowrap;
		width: 100%;
	}

	.scroll-view-item {
		text-align: center;
		font-size: 36rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.scroll-view-item_H {
		display: inline-block;
		width: 48rpx;
		height: 62rpx;
		text-align: center;
		font-size: 22rpx;
		color: rgba(102, 102, 102, 1);
		margin-right: 92rpx;
		margin-left: 32rpx;
	}

	.section {
		margin-bottom: 12px;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		font-size: 14px;
		color: rgba(51, 51, 51, 1);
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 15rpx;
	}

	.insurance-item,
	.salary-item,
	.holiday-item,
	.allowance-item {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.check-label {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 686rpx;
		height: 104rpx;
	}


	.checktitle {
		height: 36px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.checktitle-item {
		margin-left: 12px;
		font-size: 28rpx;
	}


	.check-svg {
		width: 72rpx;
		height: 72rpx;
		margin-right: 24rpx;
		margin-left: 24rpx;
		background: rgba(217, 217, 217, 1);
	}

	.checkbox {
		color: rgba(51, 51, 51, 1);
		font-size: 14px;
		line-height: 16.41px;
	}

	.check-img {
		width: 64rpx;
		height: 36rpx;
	}


	.page-container {
		width: 750rpx;
		/* height: 196rpx; */
		background-color: rgba(255, 255, 255, 1);
		/* margin-top: 10px; */
		/* 页面背景色 */
	}

	.card {
		box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.05);
		/* margin-top: 20rpx; */
		display: flex;
		justify-content: space-around;
		width: 100%;
		height: 128rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 16rpx;
	}

	.prev-button {
		background-color: #ecf5ff;
		color: #3a7bd5;
		border: none;
		border-radius: 10rpx;
		padding: 5px 20px;
		width: 264rpx;
		height: 70rpx;
		font-size: 24rpx;
		margin-top: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.save-button {
		width: 154px;
		height: 42px;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		background-color: #1890ff;
		color: white;
		border-radius: 10rpx;
		padding: 5px 20px;
		width: 264rpx;
		height: 70rpx;
		font-size: 24rpx;
		margin-top: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	/* 自定义部分 */


	.customize-container {
		padding: 20rpx;
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}

	.add-new-container {
		border: 2rpx dashed rgba(79, 140, 240, 1);
		border-radius: 10rpx;
		padding: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.add-new-box {
		display: flex;
		align-items: center;
	}

	.add-icon {
		width: 30rpx;
		height: 30rpx;
		margin-right: 10rpx;
	}

	.add-text {
		font-size: 28rpx;
		color: #999;
	}

	.selected-container {
		display: flex;
		flex-wrap: wrap;
	}

	.selected-item {
		background-color: #f2f2f2;
		border-radius: 5rpx;
		padding: 8rpx 12rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		font-size: 26rpx;
		position: relative;
	}

	.close-icon {
		width: 20rpx;
		height: 20rpx;
		/* position: absolute; */
		right: 5rpx;
		top: 5rpx;
	}

	.card-container {
		background-color: white;
		border-radius: 16rpx;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 1.1);
		/* 卡片阴影 */
		height: 128rpx;
		display: flex;
	}

	.selected-courses {
		margin-top: 30rpx;
		display: flex;
	}

	.scroll-view_H {
		white-space: nowrap;
		/* flex: 1; */
		width: 88%;
	}
</style>


<style>
	.uni-section .uni-section-header__decoration.line {
		display: none;
	}

	.uni-steps__row {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center
	}

	.uni-steps__row-text-container {
		display: flex;
		flex-direction: row;
		align-items: flex-end;
		margin-bottom: 16rpx;
		width: 100%;
	}

	.uni-steps__row-title {
		font-size: 34rpx !important;
		line-height: 32rpx;
		text-align: center;
		/* align-items: end; */
	}

	.uni-steps__row-container {
		display: flex;
		flex-direction: row;
		position: absolute;
		width: 34% !important;
		/* height: 38rpx; */
		/* align-self: center; */
		margin: 0 auto;
		/* margiin-right: 35rpx; */
		right: 35%;
	}

	.uni-steps__row-line-item {
		display: inline-flex;
		flex-direction: row;
		flex: 1;
		height: 28rpx;
		line-height: 30rpx;
		align-items: center;
		justify-content: center;
	}

	.uni-steps__row-circle {
		width: 26rpx !important;
		height: 26rpx !important;
	}

	.uni-steps__row-line {
		flex: 1;
		height: 8rpx;
	}

	.uni-icons {
		font-size: 36rpx !important;
	}

	.uni-steps__row-check {
		margin: 0rpx !important;
	}
</style>
