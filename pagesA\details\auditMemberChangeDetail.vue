<template>
    <view id="app">

        <view class="changeTitle">
            申请变更
        </view>

        <view class="companyCla">
            <image :src="detail.member.member_info.image.path_url" mode="" class="companyLogoImg"></image>
            <view class="companyCla_right">
                <view class="companyCla_companyName">{{detail.member.member_certification.name}}</view>
            </view>
        </view>

        <view class="changeTitle">
            现任人员
        </view>

        <view class="bulerBoxBig">
            <view class="butlerBox">
                <image :src="detail.old_headhunter.member_info.image.path_url" mode="" class="butlerHeadImg">
                </image>
                <view class="butlerText">
                    <view class="butlerNameCla">{{detail.old_headhunter.member_certification.name}}</view>
                    <view class="butlerCompanyCla">{{detail.authorized_company.company.name}}</view>
                </view>
            </view>
            <view class="labelBox">
                <view class="labelBox_child" v-for="(item,index) in detail.authorized_company.tags" :key="index">
                    {{item.title}}
                </view>
            </view>
        </view>

        <view class="changeTitle">
            变更人员
        </view>


        <view class="butlerBox" @click="selectButler" style="padding: 32rpx;">
            <!--<view class="selectPeoCla">-->
            <!--<view>选择人员</view>-->
            <!--<image src="/static/images/project/rightIcon.png" mode="" class="rightIcon"></image>-->
            <!--</view>-->
            <image :src="detail.change_headhunter.member_info.image.path_url" mode="" class="butlerHeadImg"></image>
            <view class="butlerText">
                <view class="butlerNameCla">{{detail.change_headhunter.member_certification.name}}</view>
                <view class="butlerCompanyCla">{{detail.change_headhunter.member.cellphone}}</view>
            </view>
        </view>

        <view class="btnBox1" v-if="detail.member_authorized_change.status == 2">
            <view class="btnCla btnRed" @click="submit(3)">拒绝</view>
            <view :class="'btnCla btnBlue'" @click="submit(1)">同意</view>
        </view>
    </view>
</template>

<script>
    import {
        memberChangeShow,
        memberAuthorizeAuditChange
    } from "../../config/api";

    export default {
        data() {
            return {
                report: {
                    form: {
                        id: 0,
                        authorize_audit_status: 1,
                        remark: "",
                    }
                },
                detail: {},
            }
        },
        onLoad(option) {
            this.report.form.id = option.id;
            this.changeAuditUserShow()
        },
        onShow() {
        },
        methods: {
            submit(_status) {
                var _this = this;
                _this.report.form.authorize_audit_status = _status;

                if (_status == 1) {
                    _this.audit();
                    return;
                }

                _this.rejectFun();
            },
            audit() {
                var _this = this;
                memberAuthorizeAuditChange(_this.report.form).then(response => {
                    uni.$u.toast(response.message);
                    if (response.status_code == '200') {
                        uni.navigateBack();
                    }
                })
            },
            rejectFun() {
                var _this = this;
                uni.showModal({
                    title: '拒绝原由',
                    content: '',
                    cancelText: '取消',
                    confirmText: '确定',
                    editable: true,
                    success: (res) => {
                        if (res.confirm) {
                            _this.report.form.remark = res.content;
                            _this.audit();
                        } else if (res.cancel) {
                        }
                    },
                });
            },
            selectButler(){

            },
            // 提交申请
            async changeAuditUserShow() {
                memberChangeShow({
                    id: this.report.form.id,
                }).then(response => {
                    this.detail = response.data;
                });
            }
        }
    }
</script>
<style>
    page {
        background-color: #F5F5F7;
    }
</style>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .changeTitle {
        font-weight: bold;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
    }

    .bulerBoxBig {
        width: 100%;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        background: #FFFFFF;
        margin-bottom: 24rpx;
        padding: 32rpx;
    }

    .butlerBox {
        display: flex;
        align-items: center;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        background: #FFFFFF;
        margin-bottom: 16rpx;

        .butlerHeadImg {
            width: 88rpx;
            height: 88rpx;
            background: #D9D9D9;
            border-radius: 88rpx 88rpx 88rpx 88rpx;
            margin-right: 16rpx;
        }

        .butlerText {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .butlerNameCla {
                font-weight: 600;
                font-size: 28rpx;
                color: #333333;
                margin-bottom: 16rpx;
            }

            .butlerCompanyCla {
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
            }
        }
    }

    .reasonBox {
        width: 100%;
        min-height: 166rpx;
        height: auto;
        padding: 32rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        margin: 24rpx 0;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        line-height: 32rpx;
        text-align: justify;
        box-sizing: border-box;
    }

    .rightIcon {
        width: 32rpx;
        height: 32rpx;
    }

    .selectPeoCla {
        width: 100%;
        padding: 32rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 32rpx;
    }

    .btnBox {
        width: 100%;
        height: 196rpx;
        padding: 24rpx 32rpx;
        border-radius: 24rpx 24rpx 0 0;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 34rpx;
        color: #FFFFFF;
        position: fixed;
        bottom: 0;
        left: 0;
        box-sizing: border-box;
    }

    .upCla {
        width: 100%;
        height: 80rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 80rpx;
    }

    .companyCla {
        width: 100%;
        padding: 32rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
    }

    .companyLogoImg {
        width: 144rpx;
        height: 144rpx;
        margin-right: 24rpx;
    }

    .companyCla_right {
        flex: 1;
        height: 144rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .companyCla_companyName {
        width: 450rpx;
        font-weight: bold;
        font-size: 28rpx;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .companyCla_center {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .labelBox {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 22rpx;
        color: #666666;
    }

    .labelBox_child {
        min-width: 130rpx;
        max-width: 150rpx;
        text-align: center;
        padding: 12rpx;
        background: #F6F6F6;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        margin-right: 16rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .btnBox1 {
        width: 100%;
        height: 196rpx;
        padding: 28rpx 42rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 24rpx 24rpx 0 0;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 34rpx;
        color: #FFFFFF;
        position: fixed;
        bottom: 0;
        left: 0;
        box-sizing: border-box;
    }

    .btnCla {
        width: 330rpx;
        height: 100rpx;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        text-align: center;
        line-height: 100rpx;
    }

    .btnRed {
        background: #FE4D4F;
        margin-right: 26rpx;
    }

    .btnGrey {
        background: #CCCCCC;
    }

    .btnBlue {
        background: #4F8CF0;
    }
</style>
