<template>
	<view class="item" @click.stop="goDetails(item.member_share.id)">
		<view class="up">
			<view class="item-up">
				<image :src="item.member_info.image.thumbnail_path_url" mode=""></image>
				<view class="info">
					<view class="user">
						<view class="userInfo">
							<view class="name">
								{{ item.member_certification.name ? item.member_certification.name : item.member_info.nick_name }}
							</view>
							<view class="cert">
								{{ item.member.certification_status_name }}
							</view>
						</view>
						<view class="status" v-if="item.member_info.job_status_name">
							<view class="dot line"></view>
							{{ item.member_info.job_status_name }}
						</view>
					</view>
					<view class="tags">
						<view class="tag">
							{{ item.member_info.sex_str }}
						</view>
						<view class="tag">{{ item.member_info.age }}岁</view>
						<!-- <view class="tag">
							3年工作经验
						</view> -->
						<view class="tag">
							{{ item.member_info.education_type_name }}
						</view>
					</view>
				</view>
			</view>

			<view class="pos">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png" mode=""></image>
				地址地址地址地址地址地址地址地址
			</view>
		</view>

		<view class="item-down">
			<view class="addr">分享至：</view>
			<view class="name">
				{{ item.company.name }}
			</view>
		</view>
		<view class="btnBox">
			<view v-if="item.interview_result.result_status_name" style="color: #4f8cf0; font-size: 28rpx">
				面试结果：{{ item.interview_result.result_status_name }}
			</view>
			<view v-else></view>
			<view class="btnBoxs">
				<view class="btns" @click.stop="shareInterview(item.member_share.id)">推荐记录</view>
				<view class="chatBtn1" @click.stop="pushInterview(item.interview.id)" v-if="item.interview.member_show == 2">立即通知</view>
			</view>
		</view>
	</view>
</template>

<script>
import { pushInterview } from '../../config/headhunterList_api.js';
export default {
	name: 'shareResumeItem',
	props: {
		item: Object,
	},
	data() {
		return {};
	},
	methods: {
		async pushInterview(id) {
			let params = {
				interview_id: id,
			};
			const res = await pushInterview(params);
			uni.$u.toast(res.message);
		},
		shareInterview(id) {
			uni.navigateTo({
				url: '/pagesA/list/recommendedRecords?id=' + id,
			});
		},
		goDetails(id) {
			uni.navigateTo({
				url: '/pagesA/details/share_resume_details?id=' + id,
			});
		},
	},
};
</script>

<style lang="less" scoped>
.item {
	display: flex;
	flex-direction: column;
	padding: 32rpx;
	margin-bottom: 32rpx;
	background-color: #ffffff;
	border-radius: 24rpx;

	.up {
		display: flex;
		flex-direction: column;

		.item-up {
			display: flex;
			padding-bottom: 28rpx;

			& > image {
				width: 104rpx;
				height: 104rpx;
				border-radius: 16rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				flex: 1;
				padding-left: 24rpx;

				.user {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.userInfo {
						display: flex;
						align-items: center;

						.name {
							font-weight: 600;
							font-size: 32rpx;
							color: #333333;
						}

						.cert {
							display: flex;
							align-items: center;
							margin-left: 28rpx;
							padding: 0 12rpx;
							height: 40rpx;
							background: rgba(87, 213, 28, 0.1);
							border-radius: 8rpx;
							font-weight: 600;
							font-size: 20rpx;
							color: #57d51c;
						}
					}

					.status {
						display: flex;
						align-items: center;
						font-weight: 400;
						font-size: 24rpx;
						color: #333333;

						.dot {
							width: 10rpx;
							height: 10rpx;
							border-radius: 50%;
							background: #999999;
							margin-right: 10rpx;
						}

						.line {
							background: #57d51c;
						}
					}
				}

				.tags {
					display: flex;

					.tag {
						display: flex;
						align-items: center;
						padding: 0 12rpx;
						font-weight: 400;
						font-size: 22rpx;
						color: #999999;
						// margin-right: 16rpx;
						border-right: 1px solid #999999;

						&:first-child {
							padding-left: 0;
						}

						&:last-child {
							border-right: none;
						}
					}
				}
			}
		}

		.pos {
			display: flex;
			font-weight: 400;
			font-size: 24rpx;
			color: #666666;
			margin-bottom: 24rpx;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 16rpx;
			}
		}
	}

	.item-down {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 28rpx 0 0 0;
		border-top: 1px solid #f5f5f5;

		.addr {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
		}

		.name {
			font-weight: 500;
			font-size: 24rpx;
			color: #333333;
		}
	}
}

.btnBox {
	width: 100%;
	display: flex;
	justify-content: space-between;
	margin-top: 16rpx;
}

.btnBoxs {
	display: flex;
	align-items: center;
}

.btns {
	padding: 8rpx 12rpx;
	background: #f5f5f7;
	color: #4f8cf0;
	font-weight: 600;
	font-size: 24rpx;
	border-radius: 8rpx;
	margin-right: 16rpx;
}

.chatBtn1 {
	padding: 8rpx 12rpx;
	background: #4f8cf0;
	color: #ffffff;
	font-weight: 600;
	font-size: 24rpx;
	border-radius: 8rpx;
}
</style>
