<template>
	<view id="app">
		<view class="detailsBox">
			<rich-text :nodes="list.content"></rich-text>
		</view>
		<view class="btn" @click="goBack" v-if="tag">
			我已阅读并同意协议
		</view>
	</view>
</template>

<script>
	import {
		getShowDetails,getTagShowDetails
	} from "../../config/common_api";

	export default {
		data() {
			return {
				list:'',
				tag:''
			}
		},
		onLoad(option) {
			if(option.id) {
				this.getShowDetails(option.id)
			}
			if(option.tag) {
				this.tag = option.tag;
				this.getTagShowDetails(option.tag)
			}
		},
		methods: {
			goBack() {
				uni.navigateBack()
			},
			// 跳转
			goFavor(e) {
				uni.navigateTo({
					url: "/pagesA/list/privacyDetails"
				})
			},
			async getTagShowDetails(tag) {
				let params = {
					tag
				}
				const {
					status_code,
					data
				} = await getTagShowDetails(params)
				if (status_code == 200) {
					console.log("获取数据",data)
					this.list = data;
				}
			},
			async getShowDetails(id) {
				let params = {
					id
				}
				const {
					status_code,
					data
				} = await getShowDetails(params)
				if (status_code == 200) {
					this.list = data
				}
			},
		}
	}
</script>

<style scoped>
	view {
		box-sizing: border-box;
	}

	#app {
		width: 100%;
		padding: 32rpx;
		min-height: 100vh;
		height: auto;
		background-color: #F5F5F7;
	}

	.detailsBox {
		width: 100%;
		/* height: 1614rpx; */
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 32rpx;
	}

	.privacyTil {
		width: 100%;
		font-weight: 600;
		font-size: 40rpx;
		color: #000000;
		line-height: 47rpx;
		text-align: center;
	}
	
	.btn {
		width: 100%;
		color: #FFFFFF;
		background: #4F8CF0;
		height: 88rpx;
		border-radius: 44rpx;
		width: 100%;
		font-size: 28rpx;
		text-align: center;
		line-height: 88rpx;
		margin-top: 32rpx;
	}
</style>