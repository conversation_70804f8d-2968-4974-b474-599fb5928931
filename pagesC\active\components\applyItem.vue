<template>
  <view class="item" @click="go">
    <block v-if="index != '0'">
      <view class="status ing"> 已通过 </view>
    </block>
    <block v-if="item.list_status == 'draft'">
      <view class="status cg"> 申请中 </view>
    </block>
    <block v-if="item.list_status == 'off_shelf'">
      <view class="status wait"> 待申请 </view>
    </block>
    <block v-if="index == '0'">
      <view class="status no"> 未通过 </view>
    </block>

    <view class="item-up">
      <view class="title"> 郑大特推招聘 </view>
      <view class="item-up-one">
        <view class="name"> {{ index == 0 ? "招聘会" : "简介" }} </view>
      </view>
      <view class="item-up-one">
        <view class="name"> 2022.10.12-2022-12-12 </view>
      </view>
    </view>
    <block>
      <u-line color="#F5F5F7" length="100%"></u-line>
      <view class="item-down flexRow">
        <view class="btns">
          <view class="btn zd" v-if="sta == 0" @click.stop="edit(item)">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/YqgQKajQ46SxUZkUpVM5pOL8FYIBJ6TFvYv6c8Gy.png"
              mode=""
            ></image>
            编辑
          </view>
          <view class="btn zd" v-if="sta == 1" @click.stop="apply(item)">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/40DfuyXS3dbEMZ9H7w4Ik8LDtUDxIlp0rpUhxh3g.png"
              mode=""
            ></image>
            审批
          </view>
          <view class="btn agree" @click.stop="detail(index)">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/FWlA6lhuUPTg38U7GCDCNLeyFcAHba59fInOM5WV.png"
              mode=""
            ></image>
            详情
          </view>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    index: {
      type: Number,
      default: 0,
    },
    sta: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {};
  },
  computed: {
    roleType() {
      console.log("当前用户的身份：", uni.getStorageSync("roleType"));
      return this.$store.state.roleType || uni.getStorageSync("roleType");
    },
  },

  methods: {
    go() {},
    edit(item) {},
    detail(index) {
      this.$emit("detail", index);
    },
    apply(item) {
      this.$emit("apply", item);
    },
  },
};
</script>

<style lang="scss">
@import "@/static/css/pagesA/components/companyPubJobItem";
</style>
