<template>
  <view class="warp">
    <view class="inner">
      <view class="tips">
        <view class="title">
          填写管家<text v-if="type != '3' && type != '4'">擅长</text
          >{{ font }}有什么用？
        </view>
        <view class="cont">
          添加主营业务，方便千里马快速了解公司。平台也将根据你所填写的业务，更好的为你推荐符合要求的千里马。
        </view>
      </view>
      <view class="line">
        <view class="tit"> 已选{{ font }} </view>
        <view class="add" @click="goAdd">
          添加<text v-if="type != '3' && type != '4'">擅长</text>{{ font }}
        </view>
      </view>
      <view class="list">
        <view class="item" v-for="item in list" :key="item">
          <text>{{ item }}</text>
          <u-icon
            name="close"
            color="#4F8CF0"
            size="14"
            @click="delItem(item)"
          ></u-icon>
        </view>
      </view>
    </view>
    <view class="btn-warp">
      <view class="btn"> 保存 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      type: "1",
      list: [
        "人力资源服务",
        "技术服务",
        "技术推广",
        "技术开发",
        "技术咨询",
        "技术交流",
        "技术转让",
      ],
      font: "行业",
    };
  },
  onLoad: function (options) {
    // options 包含了传递过来的所有参数
    console.log(options);
    this.type = options.type;
    if (this.type == "2") {
      uni.setNavigationBarTitle({
        title: "擅长职业",
      });
      this.font = "职业";
    } else if (this.type == "3") {
      uni.setNavigationBarTitle({
        title: "服务人群",
      });
      this.font = "服务人群";
    } else if (this.type == "4") {
      uni.setNavigationBarTitle({
        title: "我的标签",
      });
      this.font = "标签";
    }
  },
  methods: {
    delItem(item) {
      this.list = this.list.filter((i) => i !== item);
    },
    goAdd() {
      uni.setStorageSync("myList", this.list);

      uni.navigateTo({
        url: "./addIndustries?type=" + this.type,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.inner {
  padding: 32rpx;

  .tips {
    padding: 40rpx;
    background: linear-gradient(180deg, #f2f8ff 0%, #ffffff 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 2rpx solid rgba(215, 233, 255, 1);
    margin-bottom: 32rpx;

    .title {
      font-size: 24rpx;
      color: #333333;
      margin-bottom: 24rpx;
    }

    .cont {
      font-size: 20rpx;
      color: #8d9aaa;
    }
  }

  .line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28rpx;

    .tit {
      font-size: 32rpx;
      color: #000000;
    }

    .add {
      font-size: 28rpx;
      color: #4f8cf0;
    }
  }

  .list {
    display: flex;
    flex-wrap: wrap;

    .item {
      display: flex;
      align-items: center;
      gap: 14rpx;
      padding: 10rpx 24rpx;
      background: #eef4ff;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-size: 24rpx;
      color: #4f8cf0;
      margin-right: 24rpx;
      margin-bottom: 24rpx;
    }
  }
}
.btn-warp {
  position: fixed;
  bottom: 0;
  width: 750rpx;
  height: 196rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;

  .btn {
    width: 686rpx;
    height: 80rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    line-height: 80rpx;
    margin-top: 24rpx;
  }
}
</style>
