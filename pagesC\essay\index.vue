<template>
	<view class="rulesbox">
		<view class="rules_ul" v-for="item in rulesList" :key="item.id">
			<text class="ul_title PR text-b">{{item.title}}</text>
			<view class="list_li PR" v-for="i in item.textList" :key="i.id" @click="routerJump(item.id,i.id)">
				<text class="PR text-b">{{i.name}}</text>
				<img src="../../static/new/右箭头@2x1.png" class="arrowImg" alt="" />
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				rulesList: [{
						id: 1,
						style: 'common',
						title: '基本协议',
						textList: [{
								id: 1,
								name: '招聘呗社区用户平台协议'
							},
							{
								id: 2,
								name: '招聘会社区视频隐私保护声明'
							},
							{
								id: 3,
								name: '招聘呗视频招聘用户服务协议'
							},
						]
					},
					{
						id: 2,
						style: 'more',
						title: '更多规则',
						textList: [{
								id: 1,
								name: '招聘呗社区直播协议'
							},
							{
								id: 2,
								name: '招聘呗社区管理条例'
							},
							{
								id: 3,
								name: '招聘呗社区规范'
							}
						]
					}
				]
			}
		},
		methods: {
			routerJump(id, i_id) {
				if (id === 1 && i_id === 1) {
					uni.navigateTo({
						url: '/pagesC/essay/hunting'
					})
				} else if (id === 1 && i_id === 2) {
					uni.navigateTo({
						url: '/pagesC/essay/rulesEssay'
					})
				} else if (id === 1 && i_id === 3) {
					uni.navigateTo({
						url: '/pagesC/essay/join'
					})

				} else if (id === 2 && i_id == 1) {
					uni.navigateTo({
						url: '/pagesC/essay/videoList'
					})
				} else if (id === 2 && i_id === 2) {
					uni.navigateTo({
						url: '/pagesC/essay/managerList'
					})
				} else if (id === 2 && i_id === 3) {
					uni.navigateTo({
						url: '/pagesC/essay/standed'
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped;>
	.PR {
		font-family: 'PingFang SC-Regular';
	}

	.text-b {
		color: #333333;
	}

	.rulesbox {
		display: flex;
		flex-direction: column;
		gap: 32rpx;

		.rules_ul {
			display: flex;
			flex-direction: column;
			gap: 32rpx;
			padding: 32rpx;

			.ul_title {
				font-size: 28rpx;
			}

			.list_li {
				display: flex;
				justify-content: space-between;
				align-items: center;
				gap: 32rpx;
				border-bottom: 1rpx solid #E6E6E6;
				padding-bottom: 24rpx;

				text {
					font-size: 24rpx;
				}

				image {
					width: 28rpx;
					height: 28rpx;
				}
			}
		}
	}
</style>