<template>
	<!-- 个人优势提交 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="form-container">
					<u-textarea v-model="params.content" placeholder="请输入个人优势" height="300" maxlength="300" count
						border="none"></u-textarea>
				</view>
			</view>

			<u-toast ref="toast"></u-toast>
		</scroll-view>

		<view class="btn-container">
			<view class="btn" @click="onSubmit">确定</view>
		</view>
	</view>
</template>

<script>
	import {
		addStrength,
		updateStrength
	} from '@/config';

	export default {
		data() {
			return {
				params: {
					strength_id: null,
					content: '',
				}
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onLoad(options) {
			if (!options.id) return;
			this.params.strength_id = options.id;
			this.params.content = options.content;
		},
		methods: {
			async onSubmit() {
				const params = {
					type: '1',
					...this.params,
					...this.userTypeParams,
				};
				const event = params.strength_id ? updateStrength : addStrength;
				const res = await event(params);
				if (res.status_code !== '200') return;
				this.$refs.toast.show({
					message: res.message,
					duration: 1000,
					complete: () => {
						uni.$u.route({
							type: 'back'
						});
					}
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;

		.btn-container {
			background-color: #FFFFFF;
			padding-block-start: 24rpx;
			padding-inline: 64rpx;
			padding-block-end: calc(24rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			border-start-start-radius: 16rpx;
			border-start-end-radius: 16rpx;
			box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);

			.btn {
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				border-radius: 16rpx;
				text-align: center;
				color: #FFFFFF;
				padding-block: 20rpx;
				font-size: 28rpx;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding: 32rpx;

				.form-container {
					background-color: #FFFFFF;
					padding-inline: 24rpx;
					padding-block: 16rpx;
					border-radius: 24rpx;
				}
			}
		}
	}
</style>