<template>
	<view class="item">
		<view class="up">
			<view class="info">
				<view class="cont">
					<view class="title">
						签署关键字
					</view>
					<view class="name">
						本人签名
					</view>
				</view>
				<view class="more" @click="more">
					<u-icon name="more-dot-fill"></u-icon>
				</view>
			</view>
			<view class="desc" v-if="tabIndex==1">
				<text class="name">摘要</text>
				<text class="txt">摘要内容</text>
			</view>
		</view>
		
		<view class="down">
			<view class="start">
				<text>关键字创建时间：</text>
				<text>2024/01/01 19:00:00</text>
			</view>
			<view class="start">
				<text>关键字更新时间：</text>
				<text>2024/01/01 19:00:00</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		name:'keywordItem',
		props:{
			tabIndex:{
				type:Number,
				default:1
			}
		},
		data(){
			return{
				
			}
		},
		methods:{
			more(){
				this.$emit('more',this.item)
			}
		}
	}
</script>

<style lang="less" scoped>
	.item{
		display: flex;
		flex-direction: column;
		padding: 24rpx 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;
		margin-bottom: 24rpx;
		.up{
			display: flex;
			flex-direction: column;
			padding-bottom: 24rpx;
			border-bottom: 1px solid #F5F5F7;
			.info{
				display: flex;
				align-items: center;
				.cont{
					display: flex;
					flex-direction: column;
					flex: 1;
					.title{
						font-weight: 400;
						font-size: 24rpx;
						color: #999999;
					}
					.name{
						font-weight: 500;
						font-size: 28rpx;
						color: #333333;
						margin-top: 24rpx;
					}
				}
				.more{
					transform: rotate(90deg);
				}
				
			}
			.desc{
				display: flex;
				justify-content: space-between;
				margin-top: 16rpx;
				.name{
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
				}
				.txt{
					font-weight: 400;
					font-size: 24rpx;
					color: #333333;
				}
			}
		}
		
		.down{
			display: flex;
			flex-direction: column;
			padding-top: 24rpx;
			.start{
				display: flex;
				justify-content: space-between;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				&:first-child{
					margin-bottom: 16rpx;
				}
			}
		}
	}
</style>