import {
    VERIFY
} from '@/env.js'
import {authChat} from "../config/common_api";

/**
 * 检查用户登录状态
 * @returns {boolean} 已登录返回true，未登录弹出登录提示框
 */
export const isLogin = () => {
    const res_userInfo = uni.getStorageSync('userInfo')

    if (res_userInfo == '') {
        uni.showModal({
            title: '提示',
            content: '请登录后进行操作',
            confirmText: '去登录',
            success: (res) => {
                if (res.confirm) {
                    uni.reLaunch({
                        url: '/pages/login/login'
                    })
                }
                return false
            }
        })
		return false;
    } else {
        return true
    }
}
//是否实名认证  info 基本信息  auth 是否实名 resume 是否完善简历 company_info 是否完善企业信息
export const isAuth = (method = ["info", "auth", "resume", "company_info"]) => {
    if (!VERIFY) {
        return true
    }
    const res_userInfo = uni.getStorageSync('userInfo');
    /*
    是否设置个人信息 member_info--set_status 1-是，2-否
    是否需要实名认证 member--certification_status 是否通过三要素认证：{"1":"通过","2":"待审核","3":"驳回","5":"审核中"}
    是否需要完善简历 resume_count = 0
    */
    var url = ""
    var prompt = ""
    var res = false
    var but = "去完善"
    var info = res_userInfo?.member_info?.set_status == 2
    var auth = res_userInfo?.member?.certification_status == '2'
    var resume = res_userInfo?.resume_count < 1
    var company_info = false
    if (res_userInfo != '') {
        company_info = res_userInfo.company.length == 0
    }
    /* 4.使用 try catch */
    try {
        method.forEach((item, index) => {

            if (item == 'login') {
                if (res_userInfo == '') {
                    prompt = "请登录后进行操作！"
                    but = "去登录"
                    url = "/pages/login/login"
                    res = true
                    throw ('终止循环'); // 抛出一个异常
                }
            }
            if (info && 'info' == item) {
                prompt = "请完善基本信息！"
                url = "/pagesA/personal/personalCenter"
                // if(uni.getStorageSync('roleType')=='member') {
                //     url = "/pagesA/personal/personalCenter"
                // } else {
                //     url = ""
                // }
                res = true
                throw ('终止循环'); // 抛出一个异常
            }
            if (auth && 'auth' == item) {
                prompt = "请进行实名认证！！"
                url = "/pages/my/cert"
                res = true
                throw ('终止循环'); // 抛出一个异常

            }
            if (resume && 'resume' == item) {
                prompt = "请完善简历！"
                url = "/pagesA/add/addResume"
                res = true
                throw ('终止循环'); // 抛出一个异常
            }
            if (company_info && 'company_info' == item) {
                prompt = "请完善企业信息！"
                url = "/pages/my/company_cert"
                res = true
                throw ('终止循环'); // 抛出一个异常
            }
        })
    } catch (e) {
        console.log('catch内容：' + prompt); // catch内容：终止循环
    }

    if (res) {
        uni.showModal({
            title: '提示',
            content: prompt,
            confirmText: but,
            success: (res) => {
                if (res.confirm) {
                    uni.navigateTo({
                        url: url
                    })
                } else {
                    uni.showModal({
                        title:'提示',
                        content:'是否退出登录',
                        confirmText:'退出',
                        success:(suc)=>{
                            if(suc.confirm) {
                                uni.clearStorageSync()
                                uni.reLaunch({
                                    url:'/pages/login/login'
                                })
                            }
                        }
                    })
                }
                return false
            }
        })
        return false
    }
    return true

}
//是否设置个人信息
export const isPersonal = () => {
    if (Array.isArray(userInfo().info.member_info)) {
        uni.showModal({
            title: '提示',
            content: '请设置个人信息',
            confirmText: '去认证',
            success: (res) => {
                if (res.confirm) {
                    uni.navigateTo({
                        url: '/pagesA/personalCenter/settings'
                    })
                }
                return false
            }
        })
    } else {
        if (!userInfo().info.member_info.nick_name) {
            uni.showModal({
                title: '提示',
                content: '请设置个人信息',
                confirmText: '去认证',
                success: (res) => {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: '/pagesA/personalCenter/settings'
                        })
                    }
                    return false
                }
            })
        }
    }
}
//是否设置默认简历
export const hasPubResume = () => {
    if (userInfo().info.member.public_resume_status == 2) {
        uni.showModal({
            title: '提示',
            content: '请设置主简历后进行操作',
            confirmText: '去设置',
            success: (res) => {
                if (res.confirm) {
                    uni.navigateTo({
                        url: '/pagesA/personalCenter/m-resume'
                    })
                }
                return false
            }
        })
    } else {
        return true
    }
}

// 检测用户设备是否开启定位功能
export const chooseAddress = async () => {
    let res = false
    try {
        await uni.authorize({scope: 'scope.userLocation'});
        res = await uni.chooseLocation();
    } catch (error) {
        uni.getSetting({
            success: res => {
                if (!res.authSetting['scope.userLocation']) {
                    uni.showModal({
                        title: '提示',
                        content: '检测到您没有打开位置权限，是否去设置打开？',
                        success: res1 => {
                            if (res1.confirm) uni.openSetting()
                        }
                    })
                }
            }
        })
    }
    return res
}

// 获取用户定位信息
export const getLocation = async () => {
    let res = false
    try {
        await uni.authorize({scope: 'scope.userLocation'});
        res = await uni.getFuzzyLocation({type: 'wgs84'})
        uni.setStorageSync('userLocation',res)
        console.log("获取用户定位",res);
    } catch (error) {
        uni.getSetting({
            success: res => {
                if (!res.authSetting['scope.userLocation']) {
                    uni.showModal({
                        title: '提示',
                        content: '检测到您没有打开位置权限，是否去设置打开？',
                        success: res1 => {
                            if (res1.confirm) uni.openSetting()
                        }
                    })
                }
            }
        })
    }
    return res
}

/**
 * 三级职位选择数据格式化
 * @param {Array} list - 原始职位数据 
 * @param {number} firstIndex - 一级分类索引
 * @param {number} secondIndex - 二级分类索引
 * @returns {Array} 根据索引返回对应的二级或三级职位列表
 */
export const getNextPositionList = (list, firstIndex, secondIndex) => {
    if (list.length === 0) return
    let secondList = []
    let thirdList = []
    const defaultList = [{id: 'all', class_name: '全部'}]
    secondList = list[firstIndex].children.length > 0 ? list[firstIndex].children : [...defaultList]
    if (typeof secondIndex !== 'undefined') {
        if (secondList[0].id !== 'all') {
            thirdList = secondList[secondIndex].children.length > 0 ? secondList[secondIndex].children : [...defaultList]
        } else {
            thirdList = [...defaultList]
        }
    }
    return typeof secondIndex === 'undefined' ? secondList : thirdList
}

/**
 * 跳转聊天页面前的认证检查
 * @param {string} type - 聊天类型
 * @param {string} id - 关联ID
 */
export const communicate = async (type, id) => {

    if (!isAuth(["login", "info"])) return

    let params = {
        type: type,
        id: id
    }
    const {
        status_code,
        message,
        data
    } = await authChat(params)
    if (status_code == 200) {
        uni.navigateTo({
            url: `/pages/message/message?personType=${data.personType}&selfType=${data.selfType}&company_id=${data.company_id}&member_id=${data.member_id}&name=${data.name}&avatar=${data.avatar}&chatId=${data.chat_id}&personTypeName=${data.personTypeName}&typeClass=${data.typeClass}`
        })
    }

}

/**
 * 选项列表索引查找
 * @param {Array} optList - 选项数组
 * @param {*} val - 要匹配的值
 * @returns {number} 匹配到的选项索引
 */
export const changeIndex = (optList, val) => {
    var opts = optList || {};
    var changeIndex = 0;

    val = val || 0;

    opts.forEach(function (item, index) {
        if (item.value == val) {
            changeIndex = index;
        }
    });
    return changeIndex;
}

/**
 * 通用数据转选项格式
 * @param {Array} data - 原始数据 
 * @returns {Array} 转换后的选项数组[{value,name}]
 */
export const data2opt = (data) => {
    var _opts = [];
    data.forEach(function (item, index) {
        _opts.push({
            value: item.id,
            name: item.name
        });
    });
    return _opts;
}


export const formatUploadShow = (data) => {
    var _uploadData = [];
    if (data.length > 0) {
        data.forEach(function (item, index) {
            _uploadData.push({
                id: item.id,
                url: item.path_url,
            })
        })
    }
    return _uploadData;
}

export const formatAddressShow = (data) => {
    var address = [];

    if (data.length < 1) {
        return address;
    }

    data.forEach(function (item, index) {
        address.push({
            text: item.name,
            value: item.id,
        })
    });

    return address;
}

