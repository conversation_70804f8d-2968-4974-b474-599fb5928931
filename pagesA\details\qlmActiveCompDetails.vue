<template>
	<view class="home-index">
		<qlm-active-comp-info :item="detail"></qlm-active-comp-info>
		<view class="list">
			<job-item v-for="item in 5" :type="type"></job-item>
		</view>
	</view>
</template>

<script>
	import QlmActiveCompInfo from '../components/qlmActiveCompInfo.vue'
	import JobItem from '../../components/jobItem.vue'
	export default {
		components: {
			QlmActiveCompInfo,
			JobItem
		},
		data() {
			return {
				type: 'active'
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding: 32rpx;
	}
</style>