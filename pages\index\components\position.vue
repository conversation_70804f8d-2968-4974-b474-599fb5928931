<template>
	<view class="container">
		<u-sticky>
			<view class="tabs-container">
				<u-tabs :list="tabsList" :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
					:inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }" @click="onTabsItemClick"></u-tabs>
			</view>
		</u-sticky>
		<view class="content">
			<view class="item" v-for="v in jobList" :key="v.job.job_id" @click="onDetail(v)">
				<view class="item-start">
					<view class="title-box">
						<image v-if="tabs == '1'" class="avater" src="/static/images/index/jyindex/fire_icon.png"></image>
						<view class="title_1">{{v.job.job_info_name}}</view>
						<view class="title_3">
							{{tabs == '1'?'全职':'兼职'}}
						</view>
						<view class="title_2">
							{{v.salary.salary_min}}
						</view>
					</view>
					<view class="time">{{tabs == '1'?v.job.job_introduction:'25岁 | 男女不限 | 10人'}}</view>
					<view class="tags">
						<view class="tag-inn">
							<view class="tag" v-for="k in ['世界500强','上市公司','游戏大厂']" :key="k.id">{{k}}</view>
						</view>
						<view v-if="tabs == '1'" class="bottom-info">
							<image class="small-icon"
								src="https://api-test.zhaopinbei.com/storage/uploads/images/vbMJCy8RKqGv0kzde7THmTgD80b7iY73YusWYJoW.png">
							</image>
							<text class="info-text">123</text>
						</view>
					</view>
					<view class="times" v-if="tabs == '2'">
						<text>面试时间：2025-03-28</text>
						<text>到岗时间：2025-04-28</text>
					</view>
					<view class="inn">
						<view class="left">
							<view class="age">
								学创北京｜A轮｜0-20人
							</view>
							<view class="add">
								<img src="/static/images/index/jyindex/address_icon.png" alt="" />
								<text>北京北京市昌平区1号院阳光小区</text>
								<view class="km">
									距12.1km
								</view>
							</view>
						</view>
						<view class="right" v-if="tabs == '1'">
							聊聊呗
						</view>
					</view>
					<u-line></u-line>
					<view class="item-end">
						<view class="btn-box" v-if="tabs == '2'">
							<view class="btn_1">
								立即报名
							</view>
							<view class="btn_2">
								聊聊呗
							</view>
						</view>
						<view class="end">
							<view class="name">
								<image src="/static/images/index/jyindex/baozhang_icon.png" mode=""></image>
								<text>平台保障</text>
							</view>
							<view class="name">
								<image src="/static/images/index/jyindex/zipin_icon.png" mode=""></image>
								<text>平台自聘</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getJobPublishList
	} from '@/config';

	export default {
		data() {
			return {
				tabsList: [{
						key: '1',
						name: '全职',
					},
					{
						key: '2',
						name: '兼职',
					},

				],
				tabs: '1',
				jobList: [],
				params: {
					limit: 20,
					page: 1,
					work_type_id: '2',
					job_type: '2'
				},
				isLoading: false,
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type]?.id || 61,
				};
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetJobPublishList();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetJobPublishList();
		},
		methods: {
			onDetail(v) {
				// uni.$u.route({
				// 	url: `/pagesA/details/memberJobDetails`,
				// 	params: {
				// 		id: v.job.job_id,
				// 	}
				// })
			},
			onTabsItemClick(res) {
				this.tabs = res.key;
			},
			onScrollGetList() {
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetJobPublishList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getJobPublishList(params);
				if (res.status_code !== '200') return;
				this.jobList = [...this.jobList, ...res.data.jobs_list?.data];
				this.isLoading = false;
			}
		}
	}
</script>


<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.tabs-container {
			// padding-inline: 16rpx;
			background-color: #f5f5f7;
			margin-left: -11px;
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			// padding-inline: 32rpx;

			.item {
				background-color: #ffffff;
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				justify-content: space-between;
				// flex-direction: column;
				gap: 24rpx;


				.avater {
					width: 48rpx;
					height: 48rpx;
				}

				.item-end {
					.btn-box {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 12rpx;

						view {
							width: 299rpx;
							height: 56rpx;
							background: #F1F6FE;
							border-radius: 16rpx 16rpx 16rpx 16rpx;
							font-size: 24rpx;
							color: #FFFFFF;
							text-align: center;
							line-height: 56rpx;

						}

						.btn_1 {
							color: #4F8CF0;
						}

						.btn_2 {
							background: linear-gradient(92deg, #4F8CF0 0%, #0061FF 100%);
						}
					}

					.end {
						display: flex;
						justify-content: space-between;
						align-items: center;
						gap: 28rpx;
					}

					.name {
						color: #666;
						font-size: 20rpx;
						display: flex;
						align-items: center;

						image {
							width: 48rpx;
							height: 48rpx;
							margin-right: 4rpx;
						}
					}
				}

				.item-start {
					width: 100%;
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.time {
						color: #666;
						font-size: 24rpx;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
					}

					.type-text {
						font-size: 28rpx;
						color: #041024;
					}

					.title-box {
						display: flex;
						align-items: center;
						justify-content: space-between;
						gap: 12rpx;
						font-size: 32rpx;
						color: #333333;

						.title-inner {
							display: flex;
							align-items: center;
							gap: 12rpx;

							.tit-inn {
								width: 100%;
								display: flex;
								align-items: center;
								justify-content: space-between;
								gap: 12rpx;

								.age {
									font-size: 24rpx;
									color: #666666;
								}
							}
						}

						.title_1 {
							// flex: 1;
							width: 160rpx;

							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.title_2 {
							color: #f98a14;
							white-space: nowrap;
							margin-left: 100rpx;
						}

						.title_3 {
							color: #4F8CF0;
							white-space: nowrap;
							margin-left: 100rpx;
						}
					}

					.tags {
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.tag-inn {
							display: flex;
							align-items: center;
							gap: 16rpx;
							overflow-x: auto;
							white-space: nowrap;
						}

						.bottom-info {
							display: flex;
							align-items: center;
							justify-content: flex-end;

							.small-icon {
								width: 32rpx;
								height: 32rpx;
							}

							.info-text {
								font-size: 20rpx;
								color: #666666;
							}
						}

						.tag {
							flex-shrink: 0;
							padding: 12rpx;
							border-radius: 8rpx;
							color: #666666;
							font-size: 22rpx;
							background-color: #F6F6F6;
						}
					}

					.times {
						display: flex;
						align-items: center;
						justify-content: space-between;
						font-size: 24rpx;
						color: #666666;
					}

					.inn {
						display: flex;
						align-items: center;
						justify-content: space-between;
						font-size: 24rpx;
						color: #666666;

						.left {
							display: flex;
							flex-direction: column;

							.add {
								display: flex;
								align-items: center;
								margin-top: 14rpx;

								text {
									width: 250rpx;
									overflow: hidden;
									display: -webkit-box;
									-webkit-box-orient: vertical;
									-webkit-line-clamp: 1;
								}

								img {
									width: 32rpx;
									height: 32rpx;
									margin-right: 8rpx;
								}

								.km {
									height: 36rpx;
									padding: 0 8rpx;
									line-height: 36rpx;
									background: #F6F6F6;
									border-radius: 50rpx 50rpx 50rpx 50rpx;
								}
							}
						}

						.right {
							width: 144rpx;
							height: 56rpx;
							background: linear-gradient(132deg, #4F8CF0 0%, #0061FF 100%);
							border-radius: 8rpx 8rpx 8rpx 8rpx;
							font-size: 24rpx;
							color: #FFFFFF;
							text-align: center;
							line-height: 56rpx;
						}
					}
				}
			}
		}
	}
</style>