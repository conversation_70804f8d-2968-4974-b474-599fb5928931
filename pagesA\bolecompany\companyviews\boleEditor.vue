<template>
	<view class="bloe-card" style="flex: 1;  display: flex; flex-direction: column; justify-content: space-between;">
		<view class="outer-container">
			<!-- 编辑公司介绍部分 -->
			<view style="width: 686rpx;margin: 0 auto;">
				<view class="info-card">
					<p class="font-bold">
						填写公司介绍有什么用？
					</p>
					<p class="font-blod-subtitle">
						详尽的公司信息呈现，能有效增强求职者信任，带来更多有效沟通！
					</p>
				</view>
			</view>

			<!-- 一句话介绍部分 -->
			<view class="input-section">
				<view class="Title">
					<p class="input-title">
						<span class="boleEditor-item__title">一句话介绍</span>
						<span class="boleEditor-item__Subtitle"> (非必填)</span>
					</p>
					<!-- <img :src="editor" alt="" class="title-img" /> -->
				</view>
				<view class="input-container">
					<textarea v-model="inputValue" class="input-container-input" placeholder="一句话介绍公司，请输入20字以内的文字"
						:maxlength="20" @input="handleInput" rows="6"
						placeholder-style="font-size:24rpx; margin-top:24rpx; width:646rpx"></textarea>
					<span class="count-tip" v-html="getCountTip(inputValue.length, 20, true)"></span>
				</view>
			</view>

			<!-- 公司简介部分 -->
			<view class="input-section">

				<view class="Title">
					<p class="input-title">
						<span class="boleEditor-item__title">公司简介</span>
					</p>
					<!-- <img :src="editor" alt="" class="title-img" /> -->
				</view>
				<view class="input-container">
					<textarea v-model="companyProfile" class="input-container-input" placeholder="业务模式、公司规模等"
						:maxlength="1000" @input="handleProfileInput" rows="6"
						placeholder-style="font-size:24rpx; margin-top:24rpx;"></textarea>
					<span class="count-tip" v-html="getCountTip(companyProfile.length, 1000, false)"></span>
				</view>

				<!-- 优质范例部分 -->
				<view class="example-section">
					<view class="example-card">
						<view class="custom-card">
							<span class="custom-card-Model">优质范例</span>
							<span class="custom-card-more" v-if="!showAll" @click="handleClick">
								展开更多
								<image :src="arrow" style="width: 24rpx;
																height: 26rpx; align-self: center; margin-left: 16rpx;"></image>
							</span>
						</view>
						<view class="scrollable-container" v-if="showAll">
							<!-- <p>范例仅供参考，请根据公司实际情况填写</p> -->
							<!-- 遍历范例列表 -->
							<view v-for="(example, index) in examples" :key="index" class="sub-card">
								<span class="subcard-span">范例 {{ index < 1? '一' : '二' }}</span>
								<p class="subcard-p">{{ example.content }}</p>
							</view>
							<view class="collapse-btn" @click="handleClick">
								收起全部
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 发展历程输入框部分 -->
			<view class="input-section">


				<view class="Title">
					<p class="input-title">
						<span class="boleEditor-item__title">发展历程</span>
						<span class="boleEditor-item__Subtitle"> (非必填)</span>
					</p>
					<!-- <img :src="editor" alt="" class="title-img" /> -->
				</view>

				<view class="input-container">
					<textarea v-model="developmentHistory" class="input-container-input" placeholder="奖项、荣誉、专利、作品"
						:maxlength="500" @input="handleDevelopmentHistoryInput" rows="6"
						placeholder-style="font-size:24rpx; margin-top:24rpx; width:646rpx"></textarea>
					<span class="count-tip" v-html="getCountTip(developmentHistory.length, 500, true)"></span>
				</view>

				<!-- 发展历程优质范例部分 -->
				<view class="example-section">
					<view class="example-card">
						<view class="custom-card">
							<span class="custom-card-Model">发展历程优质范例</span>
							<span class="custom-card-more" v-if="!developmentHistoryShowAll"
								@click="handleDevelopmentHistoryClick">
								展开更多
								<image :src="arrow" style="width: 24rpx;
																height: 26rpx; align-self: center; margin-left: 16rpx;"></image>
							</span>
						</view>
						<view class="scrollable-container" v-if="developmentHistoryShowAll">
							<!-- <p>范例仅供参考，请根据公司实际情况填写</p> -->
							<!-- 遍历范例列表 -->
							<view v-for="(example, index) in examples" :key="index" class="sub-card">
								<span class="subcard-span">范例 {{ index < 1? '一' : '二' }}</span>
								<p class="subcard-p">{{ example.content }}</p>
							</view>
							<view class="collapse-btn" @click="handleDevelopmentHistoryClick">
								收起全部
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 获得荣誉部分 -->
			<view class="input-section">

				<view class="Title">
					<p class="input-title">
						<span class="boleEditor-item__title">获得荣誉</span>
						<span class="boleEditor-item__Subtitle"> (非必填)</span>
					</p>
					<!-- <img :src="editor" alt="" class="title-img" /> -->
				</view>

				<view class="input-container">
					<textarea v-model="honors" class="input-container-input" placeholder="奖项、荣誉、专利、作品" :maxlength="500"
						@input="handleHonorsInput" rows="6"
						placeholder-style="font-size:24rpx; margin-top:24rpx; width:646rpx"></textarea>
					<span class="count-tip" v-html="getCountTip(honors.length, 500, true)"></span>
				</view>

				<!-- 获得荣誉优质范例部分 -->
				<view class="example-section">
					<view class="example-card">
						<view class="custom-card">
							<span class="custom-card-Model">获得荣誉优质范例</span>
							<span class="custom-card-more" v-if="!honorsShowAll" @click="handleHonorsClick">
								展开更多
								<image :src="arrow" style="width: 24rpx;
								height: 26rpx; align-self: center; margin-left: 16rpx;"></image>
							</span>
						</view>
						<view class="scrollable-container" v-if="honorsShowAll">
							<!-- <p>范例仅供参考，请根据公司实际情况填写</p> -->
							<!-- 遍历范例列表 -->
							<view v-for="(example, index) in examples" :key="index" class="sub-card">
								<span class="subcard-span">范例 {{ index < 1? '一' : '二' }}</span>
								<p class="subcard-p">{{ example.content }}</p>
							</view>
							<view class="collapse-btn" @click="handleHonorsClick">
								收起全部
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 企业文化输入框部分 -->
			<view class="input-section">

				<view class="Title">
					<p class="input-title">
						<span class="boleEditor-item__title">企业文化</span>
						<span class="boleEditor-item__Subtitle"> (非必填)</span>
					</p>
					<!-- <img :src="editor" alt="" class="title-img" /> -->
				</view>
				<view class="input-container">
					<textarea v-model="corporateCulture" class="input-container-input" placeholder="企业使命、愿景、战略、经营宗旨"
						:maxlength="500" @input="handleCorporateCultureInput" rows="6"
						placeholder-style="font-size:24rpx; margin-top:24rpx; width:646rpx"></textarea>
					<span class="count-tip" v-html="getCountTip(corporateCulture.length, 500, true)"></span>
				</view>

				<!-- 企业文化优质范例部分 -->
				<view class="example-section">
					<view class="example-card">
						<view class="custom-card">
							<span class="custom-card-Model">企业文化优质范例</span>
							<span class="custom-card-more" @click="handleCorporateCultureClick"
								v-if="!corporateCultureShowAll">
								展开更多
								<image :src="arrow" style="width: 24rpx;
								height: 26rpx; align-self: center; margin-left: 16rpx;"></image>
							</span>
						</view>
						<view class="scrollable-container" v-if="corporateCultureShowAll">
							<!-- <p>范例仅供参考，请根据公司实际情况填写</p> -->
							<!-- 遍历范例列表 -->
							<view v-for="(example, index) in examples" :key="index" class="sub-card">
								<span class="subcard-span">范例 {{ index < 1? '一' : '二' }}</span>
								<p class="subcard-p">{{ example.content }}</p>
							</view>
							<view class="collapse-btn" @click="handleCorporateCultureClick">
								收起全部
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="page-container">
			<view class="card-container">
				<view class="card">
					<button class="save-button" @click="change">保存</button>
				</view>
			</view>
		</view>
	</view>
	<!-- <carefulNotice v-show="carefulhide" /> -->
</template>

<script>
	// import carefulNotice from '@/views/cg-v2/Pop-up/carefulNotice.vue';
	export default {
		components: {
			// carefulNotice
		},
		data() {
			return {
				inputValue: '',
				companyProfile: '',
				developmentHistory: '',
				honors: '',
				corporateCulture: '',
				showAll: false,
				developmentHistoryShowAll: false,
				honorsShowAll: false,
				corporateCultureShowAll: false,
				carefulhide: false,
				examples: [{
						content: '公司现有两个主要自媒体品牌xx和xx，定位为财经自媒体、个人成长两大领域。创始成员均毕业于国内外Top10名校，有着丰富的自媒体经验，曾在国内一线互联网公司及财经媒体公司任高管。团队氛围轻松，扁平化管理，双休，五险一金。',
					},
					{
						content: '公司成立于xx年，是一家资深的互联网公司，从2018年开始专注做抖音短视频，华中地区专业短视频电商公司，专业运营抖音电商带货三年时间，2019年实操短视频带货单月佣金收入超过100万，2020年实现单月单账号直播GMV超过1000万，2021年打造多个百万，千万品牌带货直播间，2022年开启红火星球达人带货平台，期待优秀的你加入！',
					},
				],
				arrow:'https://api-test.zhaopinbei.com/storage/uploads/images/437h8NsrYviiLgTPxRPCpkqrP0wF7bZVxQZbJ22P.png',
				editor:'https://api-test.zhaopinbei.com/storage/uploads/images/txnOjETVWHOxxSMCbRHQAr7vLbrmYAlLTv0ekQq7.png'
			};
		},
		methods: {
			handleInput() {
				// 这里可以添加输入内容变化时的其他逻辑，比如限制长度后的截断处理等
				if (this.inputValue.length > 20) {
					this.inputValue = this.inputValue.substring(0, 20);
				}
			},
			getCountTip(currentLength, maxLength, isShowRemaining) {
				if (isShowRemaining) {
					const remaining = maxLength - currentLength;
					return `还可输入 ${remaining} 字`;
				} else {
					return `${currentLength} / ${maxLength}`;
				}
			},
			handleProfileInput() {
				if (this.companyProfile.length > 1000) {
					this.companyProfile = this.companyProfile.slice(0, 1000);
				}
			},
			handleDevelopmentHistoryInput() {
				if (this.developmentHistory.length > 500) {
					this.developmentHistory = this.developmentHistory.slice(0, 500);
				}
			},
			handleHonorsInput() {
				if (this.honors.length > 500) {
					this.honors = this.honors.slice(0, 500);
				}
			},
			handleCorporateCultureInput() {
				if (this.corporateCulture.length > 500) {
					this.corporateCulture = this.corporateCulture.slice(0, 500);
				}
			},
			handleClick() {
				this.showAll = !this.showAll;
			},
			handleDevelopmentHistoryClick() {
				this.developmentHistoryShowAll = !this.developmentHistoryShowAll;
			},
			handleHonorsClick() {
				this.honorsShowAll = !this.honorsShowAll;
			},
			handleCorporateCultureClick() {
				this.corporateCultureShowAll = !this.corporateCultureShowAll;
				console.log(111);
			},
			saveData() {
				// 这里假设没有 store 相关逻辑，若有需要可自行补充
				alert('保存成功');
			},
			debounce(fn, delay) {
				let timer = null;
				return function() {
					if (timer) {
						clearTimeout(timer);
					}
					timer = setTimeout(() => {
						fn.apply(this, arguments);
					}, delay);
				};
			},
			debouncedSaveData: function() {
				return this.debounce(this.saveData, 1000);
			},
			toggleCarefulHide() {
				this.carefulhide = !this.carefulhide;
			},
			getCountTip(count, maxLength, isOptional) {
				const color = isOptional ? 'rgba(79, 140, 240, 1)' : 'rgba(221, 78, 65, 1)';
				const countText = count === 0 ? `<span style="color: ${color}">0</span>` : count;
				return `${countText}/${maxLength}`;
			},
			goback() {
				let pages = getCurrentPages();
				if (pages.length > 1) {
					// 存在上一页，返回上一页
					uni.navigateBack({
						delta: 1
					});
				} else {
					// 不存在上一页，跳转到指定页面
					uni.navigateTo({
						url: '/pagesA/bolecompany/boleCompanyIntroduction' // 替换为你要跳转的页面路径
					});
				}
			}
		},
		mounted() {
			// 这里没有 store 相关获取数据逻辑，若有需要可自行补充
		}
	};
</script>

<style scoped>
	.bloe-card {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: 100vh;
		justify-content: space-between;
	}

	.outer-container {
		width: 750rpx;
		height: 1263rpx;
		overflow-y: auto;
		margin: 0 auto;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.outer-container::-webkit-scrollbar {
		display: none;
	}

	.outer-container {
		-ms-overflow-style: none;
	}

	.outer-container {
		scrollbar-width: none;
	}

	.info-card {
		width: 686rpx;
		display: flex;
		height: 166rpx;
		flex-direction: column;
		justify-content: center;
		margin: 20px auto;
		position: relative;
		background-image: linear-gradient(to bottom, rgba(242, 248, 255, 1), rgba(255, 255, 255, 1));
		border-radius: 16rpx;
		overflow: hidden;

	}

	.info-card::before {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 1px;
		left: 1px;
		content: '';
		border: 2rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	}

	.info-card::after {
		position: absolute;
		width: 100%;
		height: 100%;
		bottom: 1px;
		right: 1px;
		/* margin: 0px auto; */
		content: '';
		border: 2rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	}

	.font-bold {
		padding: 5px 0;
		font-size: 24rpx;
		line-height: 24.12rpx;
		margin-left: 40rpx;
		margin-left: 40rpx;
	}

	.font-blod-subtitle {
		color: rgba(141, 154, 170, 1);
		padding: 5px 0;
		font-size: 20rpx;
		line-height: 23.44rpx;
		width: 606rpx;
		margin-left: 40rpx;
	}

	.input-section {
		margin-bottom: 88rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.input-title {
		margin-bottom: 32rpx;
		width: 308rpx;
		display: flex;
	}

	.boleEditor-item__title {
		font-size: 28rpx;
		color: rgba(51, 51, 51, 1);
	}

	.boleEditor-item__Subtitle {
		font-size: 28rpx;
		color: rgba(141, 154, 170, 1);
		margin-left: 24rpx;
		margin-right: 24prx;
	}

	.input-container {
		position: relative;
		border: 1px solid rgba(230, 230, 230, 1);
		border-radius: 16rpx;
		width: 686rpx;
		/* height: 184rpx; */
	}

	.input-container-input {
		width: 686rpx;
		box-sizing: border-box;
		padding-top: 20rpx;
		padding-right: 80rpx;
		white-space: pre-wrap;
		word-wrap: break-word;
		line-height: 28.12rpx;
		line-height: 28.12rpx;
		padding-left: 24rpx;

	}

	.Title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 686rpx;
	}

	.title-img {
		width: 32rpx;
		height: 32rpx;
	}

	.text-title {
		font-size: 12px;
		color: rgba(153, 153, 153, 1);
	}

	.count-tip {
		position: absolute;
		right: 32rpx;
		bottom: 32rpx;
		color: #909399;
		z-index: 13;
		font-size: 22rpx;
	}

	.example-section {
		margin-top: 24rpx;
	}

	.example-card {
		background-color: rgba(242, 248, 255, 1);
		width: 686rpx;
	}

	.custom-card {
		width: 100%;
		height: 124rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.custom-card-Model {
		font-size: 24rpx;
		color: rgba(51, 51, 51, 1);
		margin-left: 16px;
	}

	.custom-card-more {
		font-size: 22rpx;
		color: rgba(141, 154, 170, 1);
		margin-right: 32rpx;
		display: flex;
		align-items: center;
	}

	.scrollable-container {
		height: 800rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.sub-card {
		height: 200rpx;
		padding: 20rpx;
		background-color: rgba(242, 248, 255, 1);
		margin-bottom: 20rpx;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}

	.collapse-btn {
		color: rgb(121 121 121);
		font-size: 30rpx;
		align-self: center;
		margin: 10px 0px;
		cursor: pointer;
	}

	.footer {
		display: flex;
		justify-content: space-between;
	}

	.footer-content {
		display: flex;
		justify-content: space-between;
		width: 100%;
	}

	.footer-tip {
		display: flex;
		align-self: center;
		height: 28px;
		cursor: pointer;
	}

	.footer-tip span {
		font-size: 20px;
		color: rgba(153, 153, 153, 1);
	}

	.save {
		/* 可根据需要添加按钮样式 */
	}

	.page-container {
		width: 750rpx;
		/* height: 196rpx; */
		background-color: rgba(255, 255, 255, 1);
		/* margin-top: 10px; */
		/* 页面背景色 */
	}

	.card {
		box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.05);
		/* margin-top: 20rpx; */
		display: flex;
		justify-content: space-around;
		width: 100%;
		height: 128rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 16rpx;
	}

	.prev-button {
		background-color: #ecf5ff;
		color: #3a7bd5;
		border: none;
		border-radius: 10rpx;
		padding: 5px 20px;
		width: 304rpx;
		height: 84rpx;
		font-size: 24rpx;
		margin-top: 24rpx;
	}

	.save-button {
		width: 570rpx;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		background-color: #1890ff;
		color: white;
		border-radius: 10rpx;
		padding: 10rpx 40rpx;
		height: 64rpx;
		font-size: 24rpx;
		margin-top: 24rpx;
		border: none;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.subcard-span {
		font-size: 24rpx;
		margin-bottom: 24rpx;
	}

	.subcard-p {
		font-size: 22rpx;
		color: rgba(102, 102, 102, 1);
	}
</style>

<style>
	.uni-steps__row {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center
	}

	.uni-steps__row-text-container {
		display: flex;
		flex-direction: row;
		align-items: flex-end;
		margin-bottom: 8px;
		width: 100%;
	}

	.uni-steps__row-title {
		font-size: 34rpx;
		line-height: 16px;
		text-align: center;
		font-size: 34rpx;
		/* align-items: end; */
	}

	.uni-steps__row-container {
		display: flex;
		flex-direction: row;
		position: absolute;
		width: 34%;
		/* height: 38rpx; */
		/* align-self: center; */
		margin: 0 auto;
		/* margiin-right: 35rpx; */
		right: 35%;
	}

	.uni-steps__row-line-item {
		display: inline-flex;
		flex-direction: row;
		flex: 1;
		height: 28rpx;
		line-height: 30rpx;
		align-items: center;
		justify-content: center;
	}

	.uni-steps__row-circle {
		width: 26rpx;
		height: 26rpx;
	}

	.uni-steps__row-line {
		flex: 1;
		height: 8rpx;
	}

	.uni-icons {
		font-size: 28px;
	}
</style>
