<template>
	<view class="container">
		<view class="content-container">
			<DragList :list="languageList" :itemHeight="120">
				<template slot-scope="{ item }">
					<view class="drag-item">
						<view class="text">{{ item.content }}</view>
						<image class="image" src="/static/new/sort.png"></image>
					</view>
				</template>
			</DragList>
		</view>
		<view class="fixed-container">
			<view class="btn">完成</view>
		</view>
	</view>
</template>

<script>
	import {
		getInterviewHelloWordList
	} from '@/config';
	import DragList from '@/components/gzz-drag/drag-list.vue';

	export default {
		components: {
			DragList,
		},
		data() {
			return {
				templatePopup: false,

				languageList: [],
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onShow() {
			this.onGetInterviewHelloWordList();
		},
		methods: {
			async onGetInterviewHelloWordList() {
				const res = await getInterviewHelloWordList({
					...this.params,
					...this.userTypeParams
				});
				if (res.status_code !== '200') return;
				this.languageList = res.data;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.container {
		height: calc(100vh - env(safe-area-inset-bottom));
		box-sizing: border-box;
		padding-inline: 32rpx;
		gap: 24rpx;
		display: flex;
		flex-direction: column;
		position: relative;
		overflow: visible;

		.fixed-container {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			z-index: 9999;
			box-sizing: border-box;
			background-color: #ffffff;
			padding-inline: 32rpx;
			padding-block-start: 24rpx;
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			border-start-start-radius: 24rpx;
			border-start-end-radius: 24rpx;
			box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
			gap: 24rpx;

			.btn {
				padding-block: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-image: linear-gradient(to right, #4f8cf0, #1e6dee);
				border-radius: 24rpx;
				color: #ffffff;
				font-size: 28rpx;
			}
		}

		.content-container {
			flex: 1;
			overflow-y: auto;

			.drag-item {
				padding: 24rpx;
				display: flex;
				align-items: center;
				text-align: start;
				gap: 24rpx;
				height: 120rpx;
				box-sizing: border-box;

				.text {
					color: #333333;
					font-size: 28rpx;
					overflow: hidden;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					flex: 1;
				}

				.image {
					width: 44rpx;
					height: 44rpx;
				}
			}
		}
	}
</style>
