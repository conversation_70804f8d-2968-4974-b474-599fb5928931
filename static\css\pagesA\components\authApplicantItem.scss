.item {
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    margin-bottom: 32rpx;
    background-color: #FFFFFF;
    border-radius: 24rpx;
    
    .item-up {
        display: flex;
        padding-bottom: 28rpx;
        
        &>image {
            width: 104rpx;
            height: 104rpx;
            border-radius: 16rpx;
        }
        
        .info {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1;
            padding-left: 24rpx;
            
            .user {
                display: flex;
                justify-content: space-between;
                align-items: center;
                
                .userInfo {
                    display: flex;
                    align-items: center;
                    
                    .name {
                        font-weight: 600;
                        font-size: 32rpx;
                        color: #333333;
                    }
                    
                    .cert {
                        display: flex;
                        align-items: center;
                        margin-left: 28rpx;
                        padding: 0 12rpx;
                        height: 40rpx;
                        
                        border-radius: 8rpx;
                        font-weight: 600;
                        font-size: 20rpx;
                        
                    }
                    
                    .ing{
                        color: #57D51C;
                        background: rgba(87, 213, 28, 0.1);
                    }
                    
                    .un{
                        background: rgba(249,173,20,0.1);
                        color: #F9AD14;
                    }
                }
            }
            
            
            
            .tags {
                display: flex;
                
                .tag {
                    display: flex;
                    align-items: center;
                    padding: 0 12rpx;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #999999;
                    // margin-right: 16rpx;
                    border-right: 1px solid #999999;
                    
                    &:first-child {
                        padding-left: 0;
                    }
                    
                    &:last-child {
                        border-right: none;
                    }
                }
            }
        }
        
    }
    
    
    
    .item-down {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 28rpx 0 0 0;
        // border-top: 1px solid #F5F5F5;
        
        .addr {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
        }
        
        .btn {
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #4F8CF0;
            font-weight: 600;
            padding: 0 40rpx;
            height: 64rpx;
            font-size: 24rpx;
            color: #FFFFFF;
            border-radius: 8rpx;
        }
    }
}