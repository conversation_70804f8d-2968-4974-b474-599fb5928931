page {
    background: #FFFFFF;
}

.register-one {
    // padding-bottom: 178rpx;
}

.inp {
    background: #FFFFFF;
    border-radius: 16rpx;
    
    .avatar {
        display: flex;
        align-items: center;
        
        .pic {
            padding: 0 30rpx 0 0;
            
            image {
                width: 108rpx;
                height: 108rpx;
            }
        }
    }
    
    .inp-item {
        display: flex;
        flex-direction: column;
        padding: 0 30rpx;
        flex: 1;
        
        // border-bottom: 1px solid #F5F5F7;
        .title {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 22rpx;
            color: #666666;
            margin: 16rpx 0 0 0;
            position: relative;
            
            .star {
                font-weight: 600;
                font-size: 22rpx;
                color: #FE4D4F;
                margin-left: 8rpx;
            }
            
            .plus {
                position: absolute;
                right: 0;
                top: 50%;
                margin-top: -16rpx;
                
                image {
                    width: 32rpx;
                    height: 32rpx;
                }
            }
        }
        
        .in {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #F5F5F7;
            min-height: 88rpx;
            font-size: 32rpx;
            
            ::v-deep uni-data-picker {
                width: 100%;
            }
            
            ::v-deep .arrow-area {
                transform: rotate(-135deg);
            }
            
            ::v-deep .input-arrow {
                width: 20rpx;
                height: 20rpx;
                border-left: 1px solid #606266;
                border-bottom: 1px solid #606266;
            }
            
            ::v-deep .input-value-border {
                border: none;
            }
            
            ::v-deep .input-value {
                padding: 0;
            }
            
            ::v-deep .placeholderClass {
                font-weight: 400;
                font-size: 32rpx;
            }
            
            ::v-deep picker {
                display: flex;
                flex-direction: column;
                flex: 1;
                height: 88rpx;
                
                .d-picker {
                    display: flex;
                    align-items: center;
                    // width: 60vw;
                    height: 88rpx;
                }
            }
        }
        
        .scroll-W {
            width: 100%;
            white-space: nowrap;
            
        }
        
        .nav-item {
            display: inline-block;
            text-align: center;
            background: #F5F5F7;
            padding: 0 16rpx;
            height: 50rpx;
            line-height: 50rpx;
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
            border-radius: 8rpx;
            margin-right: 40rpx;
            
            &:last-child {
                margin-right: 0;
            }
            
            .cont {
                display: flex;
                align-items: center;
                width: 100%;
                height: 100%;
                
                .del {
                    display: flex;
                    align-items: center;
                    
                    text {
                        margin-right: 8rpx;
                    }
                }
                
            }
        }
        
        .active {
            background: #4F8CF0;
            color: #FFFFFF;
        }
        
        .se {
            color: #999;
        }
        
        .lab {
            font-weight: 400;
            font-size: 22rpx;
            color: #999999;
        }
    }
}

.footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 10;
    border-radius: 16rpx 16rpx 0 0;
    padding: 24rpx 0;
    
    .next {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80rpx;
        width: 90%;
        font-size: 28rpx;
        font-weight: 600;
        border-radius: 44rpx;
        margin-bottom: 24rpx;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
    
    .gray {
        background-color: #cccccc;
        color: #FFFFFF;
    }
    
    .prev {
        background-color: #F5F5F7;
        color: #4F8CF0;
    }
    
    .sure {
        background-color: #4F8CF0;
        color: #FFFFFF;
    }
}

.workList {
    display: flex;
    flex-wrap: wrap;
    
    .workItem {
        display: flex;
        align-items: center;
        justify-content: center;
        align-items: center;
        height: 64rpx;
        padding: 0 22rpx;
        border-radius: 8rpx;
        background-color: #F5F5F7;
        margin-bottom: 16rpx;
        margin-right: 20rpx;
        font-size: 28rpx;
        font-weight: 600;
    }
    
    .active {
        color: #4F8CF0;
        border: 1px solid #4F8CF0
    }
}