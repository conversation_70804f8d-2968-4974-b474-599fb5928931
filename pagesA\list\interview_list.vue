<template>
	<view class="container">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="tabs">
					<u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
                        color: '#4F8CF0',
                        fontWeight: 'bold',
                        transform: 'scale(1.05)',
						fontSize:'32rpx'
                    }" :inactiveStyle="{
                        color: '#999999',
                        transform: 'scale(1)',
						fontSize:'24rpx'
                    }" :list="tabs" @click="changeTab"></u-tabs>
				</view>


				<view class="search-wrap">
					<u-search placeholder="请输入职位名称" bgColor="#FFFFFF" :showAction="true" v-model="keyword"
						@custom='searchClick'></u-search>
				</view>
				<view class="filters">
					<!-- <view class="filter">
                        <picker mode="date" :value="start_date" @change="bindDateChange">
                            <view class="d-picker">{{start_date?start_date:'面试开始时间'}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker mode="date" :value="end_date" @change="bindDateChange">
                            <view class="d-picker">{{start_date?start_date:'面试结束时间'}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker @change="changeStatus" :value="statusIndex" :range="statusList" range-key="name">
                            <view class="d-picker">{{statusList[statusIndex]['name']}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view> -->
					<view class="filter" v-for="(item,index) in Time" :key="index" @click="state=index">
						<picker mode="date" @change="bindDateChange">
							<view class="d-picker">{{item}}</view>
						</picker>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>
					<!-- 面试的筛选项，待面试，面试完成 -->
					<view class="filter" v-if="roleType=='member'" @click="filterInterview">
						<view class="d-picker">{{filterInterviewText}}</view>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>

				</view>
			</view>
		</u-sticky>

		<view class="list">
			<inter-view-item v-for="(item, index) in page.data" :key="index" :item="item" @open="openDetails"
				:itemIndex="index" @approve="openApprove" @punch="punch"></inter-view-item>
		</view>
		<Pages :status="page.status"></Pages>
		<!-- 面试详情弹窗 -->
		<u-popup :show="show" round="10" mode="center" @close="close" @open="open">
			<view>
				<view class="wrap">
					<view class="title">
						面试详情
					</view>
					<view class="items">
						<view class="item">
							<text class="lab">面试人：</text>
							<text class="name">{{ currentItem.contact_name }}</text>
						</view>
						<view class="item">
							<text class="lab">报名渠道：</text>
							<text
								class="name">{{ currentItem.model_type == 'job' ? '普通报名' : currentItem.model_type == 'job_active' ? '活动报名' : currentItem.model_type == 'task' ? '任务报名' : currentItem.model_type == 'project' ? '项目报名' : '普通报名' }}</text>
						</view>
						<view class="item" @click="callPhone(currentItem.contact_cellphone)">
							<text class="lab">联系电话：</text>
							<text class="name bottomLine">{{ currentItem.contact_cellphone }}</text>
						</view>
						<view class="item">
							<text class="lab">面试时间：</text>
							<text class="name">{{ currentItem.interviewed_at }}</text>
						</view>
						<view class="item">
							<text class="lab">面试地址：</text>
							<text class="name bottomLine" @click="openNavigation(currentItem.addresses[0])">
								{{ currentItem.addresses[0]['map_address'] }}</text>
						</view>
						<view class="item">
							<text class="lab">面试备注：</text>
							<text class="name">{{ currentItem.remark }}</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		getInterviewList,
		interviewSignup,
		interviewApprove,
	} from "../../config/api.js"
	import {
		interviewResult
	} from "../../config/headhunterList_api.js"
	import InterViewItem from "../components/interViewItem.vue"
	import Pages from "../../components/pages.vue";
	import {
		isAuth
	} from '@/common/common.js'
	export default {
		components: {
			Pages,
			InterViewItem,
		},
		computed: {},
		data() {
			return {
				keyword: '',
				page: {
					form: {
						page: 1,
						limit: 10,
						model_type: "",
						start_date: '',
						end_date: '',
						info: '', // 面试状态
					},
					status: 'nomore',
					more: false,
					data: [],
				},
				currentItem: {},
				show: false,
				tabIndex: 0,
				tabs: [{
						name: '全部',
					}, {
						name: '自主报名',
					},
					{
						name: '活动报名'
					},
					{
						name: '就业管家推荐'
					}
				],
				statusIndex: 0,
				statusList: [{
						value: 0,
						name: '面试状态'
					},
					{
						value: 1,
						name: '通过'
					},
					{
						value: 2,
						name: '备选'
					},
					{
						value: 3,
						name: '淘汰'
					}
				],
				roleType: '',
				options: [
					'通过',
					'备选',
					'淘汰',
				],
				filterInterviewText: '面试状态',

				Time: [
					'面试时间开始',
					'面试时间结束'
				],
				TimeRange: {
					begin: '',
					end: ''
				},
				state: ''
			}
		},
		onLoad() {
			this.getInterviewList()
			this.roleType = uni.getStorageSync('roleType')
		},
		//触底加载更多
		onReachBottom() {
			if (this.page.more) {
				this.page.status = 'loading';
				this.page.form.page++;
				this.getInterviewList()
			}
		},
		methods: {
			filterInterview(op) {
				// 底部选项选择待面试，面试完成
				uni.showActionSheet({
					itemList: ['全部', '待面试', '面试完成'], // 显示的选项
					success: (res) => {
						this.filterInterviewText = res.tapIndex == 0 ? '面试状态' : res.tapIndex == 1 ? '待面试' :
							'面试完成';
						this.page.form.info = res.tapIndex == 0 ? '' : res.tapIndex;
						this.initPage();
					},
				})
			},
			//选中时间
			bindDateChange(item) {
				if (this.state == 0) {
					this.TimeRange.begin = item.detail.value
					this.Time[0]=item.detail.value
				} else {
					this.TimeRange.end = item.detail.value
					this.Time[1]=item.detail.value
				}
				console.log(this.TimeRange);
				this.initPage();
			},
			// 拨打电话方法
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone,
				})
			},
			// 导航方法
			openNavigation(address) {
				wx.openLocation({
					latitude: parseFloat(address.lat), // 纬度，浮点数，范围为-90~90
					longitude: parseFloat(address.lng), // 经度，浮点数，范围为-180~180
					name: address.map_address || '面试地点', // 位置名
					scale: 18 // 地图缩放级别
				})
			},
			open() {
				this.show = true
			},
			close() {
				this.show = false
			},

			changeStatus(e) {
				this.statusIndex = e.detail.value
			},

			initPage() {
				this.page.form.page = 1;
				this.page.data = [];
				this.page.status = 'loadmore';
				this.getInterviewList()
			},
			changeTab(op) {
				this.tabIndex = op.index
				this.Time[0]='面试时间开始'
				this.Time[1]='面试时间结束'
				this.filterInterviewText='面试状态'
				this.page.form.info = ''
				switch (this.tabIndex) {
					case 1:
						this.page.form.model_type = "自主报名"; //临时测试
						break;
					case 2:
					                                //活动报名
						this.page.form.model_type = "job_fairs"; //临时测试
						this.page.data = [{
							interview_result: {
								result_status: 'eliminate'
							},
							model_type: 'job_fairs',
							status:1
						}]
						break;
					case 3:
					                                 //就业管家
						this.page.form.model_type = "job_active";
						this.initPage(); //临时
						break;
					default:
						this.page.form.model_type = ""; //临时
						this.initPage();
						break;
				}
				// 调用获取列表接口
				// this.initPage();
			},

			openDetails(item) {
				this.currentItem = item;
				this.show = true
			},

			//审核
			async openApprove(op) {
				let self = this;
				var _item = op.item;
				var _index = op.itemIndex;
				var _status = op.status;
				let _examine_data = op.examine_data;

				if (this.roleType == 'headhunters') {
					uni.showActionSheet({
						itemList: this.options, // 显示的选项
						success: async (res) => {
							let resultStatus = res.tapIndex == 0 ? 'pass' : res.tapIndex == 1 ?
								'alternate' : 'eliminate'

							interviewResult({
								interview_id: _item.id,
								result_status: pass,
							}).then(response => {
								uni.$u.toast(response.message);
								if (response.status_code == '200') {
									self.page.data.splice(_index, 1, response.data);
								}
							});
						},
						fail: (err) => {

							console.error(err); // 处理错误
						},
					});

					return true;
				}

				if (!isAuth(['auth'])) return
				// uni.showModal({
				// 	title: _status == 1 ? '同意' : '拒绝',
				// 	editable: _status == 1 ? false : true,
				// 	placeholderText: '请输入原因',
				// 	success: async function(res) {
				// 		console.log('resres', res.confirm)
				// 		if (res.confirm) {
				// 			const {
				// 				status_code,
				// 				data,
				// 				message
				// 			} = await interviewApprove({
				// 				interview_id: _item.id,
				// 				status: _status,
				// 				remark: _status == 1 ? '同意' : res.content
				// 			});
				// 			if (status_code == '200') {
				// 				uni.$u.toast(message || '成功')
				// 				self.page.data.splice(_index, 1, data);
				// 			}
				// 			if (status_code == '501') {
				// 				uni.$u.toast('请实名认证')
				// 				setTimeout(function() {
				// 					uni.navigateTo({
				// 						url: '/pages/my/cert'
				// 					})
				// 				}, 3000);

				// 			}

				// 		} else if (res.cancel) {}
				// 	}
				// })
				// const {
				// 	item,
				// 	status,
				// 	examine_data
				// } = op;
				// console.log('审核项:', item);
				// console.log('审核状态:', status);
				// console.log('审核理由:', examine_data);

				try {
					const {
						status_code,
						data,
						message
					} = await interviewApprove({
						interview_id: _item.id,
						result_status: _status,
						remark: _examine_data
					})
					if (status_code == '200') {
						uni.$u.toast(message || '成功');
						self.page.data.splice(_index, 1, data); // 更新列表数据
					} else if (status_code == '501') {
						uni.$u.toast('请实名认证');
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/my/cert'
							});
						}, 3000);
					}
				} catch (error) {
					console.error('审核失败:', error);
					uni.$u.toast('操作失败，请重试');
				}

			},


			//打卡
			async punch(opts) {
				var _item = opts.item;
				var _index = opts.itemIndex;

				let params = {
					interview_id: _item.id,
					lng: '116.40400',
					lat: '39.92800',
				}

				const {
					status_code,
					data,
					message
				} = await interviewSignup(params)

				uni.$u.toast(message);

				if (status_code == '200') {
					this.page.data.splice(_index, 1, data);
				}
				// let self = this;
				// uni.getLocation({
				// 	type: 'wgs84',
				// 	success: async function(res) {
				// 		let params = {
				// 			lng: res.longitude,
				// 			lat: res.latitude,
				// 			interview_id: item.id
				// 		}
				// 		const {
				// 			status_code,
				// 			data,
				// 			message
				// 		} = await interviewSignup(params)
				// 		if (status_code == 200) {
				// 			uni.$u.toast(message || '成功')
				// 			self.getInterviewList()
				// 		}
				// 	}
				// });
			},
			// 搜索
			searchClick() {
				this.page.form.name = this.keyword;
				this.initPage();
			},
			//面试列表
			async getInterviewList() {

				const {
					status_code,
					data
				} = await getInterviewList(this.page.form);

				if (status_code == '200') {
					console.log("res", data.data);
					this.page.data = this.page.data.concat(data.data);
					// 返回false代表没有下一页
					this.page.more = data.more;
					this.page.status = this.page.more ? "loadmore" : "nomore"
				}
			}
		}
	}
</script>
<style lang="scss" src="../../static/css/pagesA/list/interview_list.scss"></style>