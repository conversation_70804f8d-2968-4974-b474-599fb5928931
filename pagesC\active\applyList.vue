<template>
  <view
    class="home-index"
    :style="{ paddingBottom: tabIndex == 0 ? '120rpx' : '0' }"
  >
    <u-sticky bgColor="#F5F5F5">
      <view class="header">
        <view class="tabs">
          <view
            :class="['tab pg_1', tabIndex == 0 ? 'active' : '']"
            @click="changeTab(0)"
          >
            我申请的
          </view>
          <view
            :class="['tab pg_2', tabIndex == 1 ? 'active' : '']"
            @click="changeTab(1)"
          >
            我审批的
          </view>
        </view>
        <view class="search-wrap">
          <u-search
            placeholder="请输入名称或发布人"
            bgColor="#FFFFFF"
            :showAction="false"
            v-model="title"
            @search="getCompanyJobList"
          ></u-search>
        </view>
        <view class="filters">
          <view class="filter">
            <picker
              @change="changeStatus"
              :value="statusIndex"
              :range="statusList"
              range-key="name"
            >
              <view class="d-picker">{{
                statusList[statusIndex]["name"]
              }}</view>
            </picker>
            <image src="/static/images/index/down.png" mode=""></image>
          </view>
          <view class="filter">
            <picker
              @change="changeStatus1"
              :value="statusIndex1"
              :range="statusList1"
              range-key="name"
            >
              <view class="d-picker">{{
                statusList1[statusIndex1]["name"]
              }}</view>
            </picker>
            <image src="/static/images/index/down.png" mode=""></image>
          </view>
        </view>
      </view>
    </u-sticky>

    <view class="list">
      <block>
        <u-swipe-action>
          <u-swipe-action-item
            :options="options"
            v-for="(item, index) in 5"
            :name="index"
            :key="index"
            @click="handleSwipe(item, $event)"
          >
            <ApplyItem
              :item="item"
              :index="index"
              :sta="tabIndex"
              @expose="expose"
              @detail="goDetail"
              @apply="apply"
            ></ApplyItem>
          </u-swipe-action-item>
        </u-swipe-action>
      </block>
    </view>
    <!-- 展示审核 -->
    <view class="reject-modal" v-if="showRejectModal">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">未通过</text>
          <text class="modal-close" @click="showRejectModal = false">×</text>
        </view>

        <view class="modal-body">
          <view class="info-row">
            <view class="info-label">活动名称</view>
            <view class="info-value">{{ rejectInfo.activityName }}</view>
          </view>

          <view class="info-row">
            <view class="info-label">申请状态</view>
            <view class="info-value">{{ rejectInfo.status }}</view>
          </view>

          <view class="reason-section">
            <view class="reason-label">申核意见：</view>
            <view class="reason-content">{{ rejectInfo.reason }}</view>
          </view>
        </view>

        <view class="modal-footer">
          <text class="confirm-btn" @click="showRejectModal = false"
            >我知道了</text
          >
        </view>
      </view>
    </view>

    <!-- 审核 -->
    <view class="reject-modal" v-if="showRejectModal1">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">审核</text>
          <text class="modal-close" @click="showRejectModal1 = false">×</text>
        </view>

        <view class="modal-body">
          <view class="info-row">
            <view class="info-label">活动名称</view>
            <view class="info-value">{{ rejectInfo.activityName }}</view>
          </view>

          <view class="info-row">
            <view class="info-label">申请状态</view>
            <view class="info-value">{{ rejectInfo.status }}</view>
          </view>

          <view class="reason-section">
            <view class="reason-label">申核意见：</view>
            <view class="reason-content">
              <u--input
                placeholder="请填写审核意见"
                border="bottom"
                clearable
              ></u--input>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <text class="cancel-btn" @click="showRejectModal1 = false">驳回</text>
          <text class="confirm-btn" @click="showRejectModal1 = false"
            >通过</text
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  topCompanyJob,
  exposeCompanyJob,
  delCompanyJob,
  getCompanyJobList,
  jobCopyIndex,
} from "../../config/api.js";
import ApplyItem from "./components/applyItem.vue";

export default {
  components: {
    ApplyItem,
  },
  data() {
    return {
      title: "",
      page: 1,
      limit: 10,
      show: false,
      date: "",
      options: [
        {
          text: "删除",
          style: {
            backgroundColor: "#FE4D4F",
            borderRadius: "24rpx",
            bottom: "32rpx",
            height: "100%",
            width: "150rpx",
            marginLeft: "24rpx",
          },
        },
      ],
      tabIndex: 0,
      list: [],
      list_1: [],
      statusList: [
        {
          value: "0",
          name: "全部",
        },
        {
          value: "1",
          name: "特推招聘",
        },
        {
          value: "2",
          name: "宣讲会",
        },
        {
          value: "3",
          name: "招聘会",
        },
        {
          value: "4",
          name: "岗位预定",
        },
      ],
      statusIndex: 0,
      statusList1: [
        {
          value: "0",
          name: "全部",
        },
        {
          value: "1",
          name: "已通过",
        },
        {
          value: "2",
          name: "申请中",
        },
        {
          value: "3",
          name: "待申请",
        },
        {
          value: "4",
          name: "未通过",
        },
      ],
      statusIndex1: 0,
      // 添加弹框相关数据
      showRejectModal: false,
      showRejectModal1: false,
      rejectInfo: {
        activityName: "",
        status: "",
        reason: "",
      },
    };
  },

  onLoad() {
    this.date = uni.$u.timeFormat(this.start, "yyyy-mm-dd");
  },
  onShow() {
    this.getCompanyJobList();
  },

  methods: {
    // 抄送列表
    async jobCopyIndex() {
      let params = {
        limit: this.limit,
        page: this.page,
      };
      const res = await jobCopyIndex(params);
      this.list_1 = res.data.data;
      console.log("抄送列表", res);
    },
    open() {
      this.show = true;
    },
    close() {
      this.show = false;
    },
    openDetails(item) {
      this.show = true;
    },
    changeTab(index) {
      this.tabIndex = index;
      if (this.tabIndex == 0) {
        this.getCompanyJobList();
      } else {
        this.getCompanyJobList();
      }
    },
    changeEdu(e) {
      console.log(e);
      this.eduIndex = e.detail.value;
    },
    changeJobExperience(e) {
      this.jobExperienceIndex = e.detail.value;
    },

    changeStatus(e) {
      this.statusIndex = e.detail.value;
    },
    changeStatus1(e) {
      this.statusIndex1 = e.detail.value;
    },

    bindDateChange(e) {
      console.log(e);
      this.date = e.detail.value;
    },

    add() {
      uni.navigateTo({
        url: "/pagesC/active/addItem",
      });
    },

    //获取列表
    async getCompanyJobList() {
      let params = {
        page: this.page,
        limit: this.limit,
        title: this.title, //职位名称
        status: "", //审核状态：1-通过，2-待审核，3-驳回
        hot_status: "", //热门状态：1-开启，2-关闭，可不填，不填查全部职位
        list_status: this.statusList[this.statusIndex]["value"], //列表状态：默认：alll，全部： all	当前登录账号， 所有已发布的职位	招聘中 active	当前账号发布职位中， 状态为 已上架的职位	下架 off_shelf	当前账号发布职位中， 状态为 已下架的职位	草稿 draft	当前账号发布职位中， 提交状态为草稿的职位
      };
      const { status_code, data } = await getCompanyJobList(params);
      if (status_code == 200) {
        this.list = data.data;
      }
    },

    handleSwipe(item, e) {
      console.log(item, e);
      let self = this;
      //表示点击了删除按钮
      if (e.index == 0) {
        uni.showModal({
          title: "确定要删除该职位吗？",
          success: async (res) => {
            if (res.confirm) {
              console.log("用户点击确定");
              let params = {
                id: item.id,
              };
              const { status_code, data, message } = await delCompanyJob(
                params
              );
              if (status_code == 200) {
                self.page = 1;
                self.list = [];
                self.getCompanyJobList();
                return uni.$u.toast("成功");
              } else {
                return uni.$u.toast(message || "失败");
              }
            } else if (res.cancel) {
              console.log("用户点击取消");
            }
          },
        });
      }
    },

    //曝光
    async expose(item) {
      let self = this;
      uni.showModal({
        title: "确定要曝光该职位吗？",
        success: async (res) => {
          if (res.confirm) {
            console.log("用户点击确定");
            let params = {
              id: item.id,
            };
            const { status_code, data, message } = await exposeCompanyJob(
              params
            );
            if (status_code == 200) {
              self.page = 1;
              self.list = [];
              self.getCompanyJobList();
              return uni.$u.toast("成功");
            } else {
              return uni.$u.toast("失败" || message);
            }
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },

    //置顶
    async backTop(item) {
      let self = this;
      uni.showModal({
        title: "确定要置顶该职位吗？",
        success: async (res) => {
          if (res.confirm) {
            console.log("用户点击确定");
            let params = {
              id: item.id,
              day: 10,
            };
            const { status_code, data, message } = await topCompanyJob(params);
            if (status_code == 200) {
              self.page = 1;
              self.list = [];
              self.getCompanyJobList();
              return uni.$u.toast("成功");
            } else {
              return uni.$u.toast("失败" || message);
            }
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
    goDetail(index) {
      console.log(index);
      //   显示pop
      this.showRejectModal = true;
      if (index == 0) {
        this.rejectInfo = {
          activityName: "招聘会名称",
          status: "未通过",
          reason: "不合格",
        };
      } else {
        this.rejectInfo = {
          activityName: "招聘会名称",
          status: "已通过",
          reason: "合格",
        };
      }
    },
    apply() {
      this.rejectInfo = {
        activityName: "招聘会名称",
        status: "待审核",
      };
      this.showRejectModal1 = true;
    },
  },
};
</script>
<style>
page {
  background: #f5f5f7;
}
</style>
<style lang="less" scoped>
// .home-index{
// 	padding: 32rpx;
// }
.header {
  padding: 32rpx;

  .tabs {
    display: flex;
    justify-content: space-between;

    .tab {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 88rpx;
      display: flex;
      flex: 1;
      background: #ffffff;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;

      &:first-child {
        border-radius: 44rpx 0 0 44rpx;
      }

      &:last-child {
        margin-left: -40rpx;
        border-radius: 0 44rpx 44rpx 0;
      }
    }

    .pg_1 {
      clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
    }

    .pg_2 {
      clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
    }

    .active {
      color: #4f8cf0;
      font-weight: 600;
    }
  }

  .search-wrap {
    margin-top: 32rpx;
  }

  .filters {
    display: flex;
    align-items: center;
    margin-top: 32rpx;

    .filter {
      display: flex;
      align-items: center;
      height: 48rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
      padding: 0 16rpx;
      margin-right: 12rpx;
      border-radius: 8rpx;
      background: #ffffff;

      image {
        width: 24rpx;
        height: 24rpx;
      }
    }

    .search-wrap {
      flex: 1;
    }
  }
}

.list {
  padding: 0 32rpx 108rpx 32rpx;

  ::v-deep .u-swipe-action-item__right {
    bottom: 32rpx;
    border-radius: 24rpx;
  }

  ::v-deep .u-swipe-action-item__content {
    background: transparent;
  }
}

.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 196rpx;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;

  .next {
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #ffffff;
    height: 88rpx;
    width: 90%;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    border-radius: 16rpx;
  }

  .sure {
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
  }
}
.reject-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.modal-header {
  position: relative;
  padding: 30rpx 20rpx;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-body {
  padding: 0 20rpx;
}

.info-row {
  display: flex;
  border: 1px solid #f0f0f0;
}

.info-label {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  color: #666;
  font-size: 28rpx;
  border-right: 1px solid #f0f0f0;
  text-align: center;
}

.info-value {
  width: 70%;
  height: 80rpx;
  line-height: 80rpx;
  padding-left: 16rpx;
  color: #333;
  font-size: 28rpx;
}

.reason-section {
  padding: 24rpx 24rpx;
  border: 1px solid #f0f0f0;
  border-radius: 12rpx;
}

.reason-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.reason-content {
  font-size: 28rpx;
  color: #333;
}

.modal-footer {
  padding: 20rpx;
  display: flex;
  justify-content: flex-end;
}

.confirm-btn {
  color: #4f8cf0;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}
.cancel-btn {
  color: #777777;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}
</style>
