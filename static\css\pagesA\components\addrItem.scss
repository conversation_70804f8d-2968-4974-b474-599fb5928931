.item {
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    margin-bottom: 24rpx;
    background-color: #FFFFFF;
    border-radius: 24rpx;
    .name {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
    }
    .action {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 24rpx;
        .area {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
        }
        
        .mr {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            
            image {
                width: 28rpx;
                height: 28rpx;
                margin-left: 8rpx;
            }
        }
    }
}
