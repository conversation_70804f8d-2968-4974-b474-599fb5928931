<template>
	<view class="hourAndbenefitsBox">
		<view class="">
			<view class="hourFlex">
				<uni-section type="line" padding>
					<uni-steps :options="list1" active-icon="checkbox" :active="active" />
				</uni-section>
			</view>
			<wokehour v-if="Workerhide"></wokehour>
			<companybenefitsVue v-else-if="benefitshide"></companybenefitsVue>
		</view>
		<view class="page-container">
			<view class="card-container">
				<!-- 这里放置除按钮外卡片内的其他内容 -->
				<button class="save-button" @click="change">

					<text class="save">
						下一步
					</text>
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import wokehour from './wokehour.vue';
	import companybenefitsVue from './companybenefits.vue';
	export default {
		components: {
			wokehour,
			companybenefitsVue
		},
		data() {
			return {
				active: 0,
				list1: [{
					title: '工作时间'
				}, {
					title: '公司福利 1/20'
				}],
				Workerhide: false,
				benefitshide: false
			}
		},
		created() {
			this.Workerhide = true
		},
		methods: {
			// 调整步骤条状态
			change() {
				console.log(1111);
				// if (this.active < this.list1.length - 1) {
				// 	this.active += 1
				// 	this.benefitshide = true
				// }
				// else {
				// 	this.active = 0
				// }
				uni.navigateTo({
					url: '/pagesA/bolecompany/companyviews/companybenefits'
				})
			}
		}
	}
</script>

<style scoped>
	.hourAndbenefitsBox {
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		overflow: hidden;
	}

	.page-container {
		width: 750rpx;
		/* height: 196rpx; */
		background-color: rgba(0, 0, 0, 0.05);
		/* 页面背景色 */
	}

	.card-container {
		background-color: white;
		border-radius: 16rpx;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		/* 卡片阴影 */
		height: 128rpx;
	}

	.save-button {
		width: 686rpx;
		/* height: 96rpx; */
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 8rpx;
		font-size: 16px;
		/* 与卡片内其他内容间隔 */
	}



	.uni-steps__row-title {
		font-size: 24rpx;
		line-height: 32rpx;
		text-align: center;
	}

	.save {
		font-size: 28rpx;
	}

	/* .uni-steps__row-circle::deep {
		width: 26.24rpx;
		height: 26.24rpx;
		border-radius: 50%;
		background-color: #B7BDC6;
		margin: 0px 3px;
	}

	.uni-steps__row-check {
		margin: 0px 6px;
		width: 26.24rpx;
		height: 26.24rpx;
	} */
</style>

<style>
	.uni-section .uni-section-header__decoration.line {
		display: none;
	}

	.uni-steps__row {
		display: flex;
		flex-direction: column;
		position: relative;
		align-items: center
	}

	.uni-steps__row-text-container {
		display: flex;
		flex-direction: row;
		align-items: flex-end;
		margin-bottom: 16rpx;
		width: 100%;
	}

	.uni-steps__row-title {
		font-size: 34rpx !important;
		line-height: 32rpx;
		text-align: center;
		/* align-items: end; */
	}

	.uni-steps__row-container {
		display: flex;
		flex-direction: row;
		position: absolute;
		width: 34% !important;
		/* height: 38rpx; */
		/* align-self: center; */
		margin: 0 auto;
		/* margiin-right: 35rpx; */
		right: 35%;
	}

	.uni-steps__row-line-item {
		display: inline-flex;
		flex-direction: row;
		flex: 1;
		height: 28rpx;
		line-height: 30rpx;
		align-items: center;
		justify-content: center;
	}

	.uni-steps__row-circle {
		width: 26rpx !important;
		height: 26rpx !important;
	}

	.uni-steps__row-line {
		flex: 1;
		height: 8rpx;
	}

	.uni-icons {
		font-size: 36rpx !important;
	}

	.uni-steps__row-check {
		margin: 0rpx !important;
	}
</style>