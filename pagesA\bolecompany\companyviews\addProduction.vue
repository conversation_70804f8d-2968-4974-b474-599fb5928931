<template>
	<view class="product-form">
		<!-- 产品Logo上传区域 -->
		<view class="form-item-header">
			<label>产品Logo（上传或选择）</label>
			<view class="logo-upload">
				<image v-if="productLogo" :src="productLogo"></image>
				<view v-else class="upload-placeholder" @click="openImagePicker">+</view>
			</view>
		</view>
		<!-- 产品名称 -->
		<view class="form-item">
			<label>产品名称</label>
			<input type="text" v-model="productName" placeholder="填写产品或服务的名称"
				placeholder-style="padding:0px;font-size:28rpx; color:rgba(153, 153, 153, 1); margin-top:16rpx;" />
		</view>
		<!-- 产品介绍 -->
		<view class="form-item">
			<label>产品介绍</label>
			<view class="formtext">
				<textarea v-model="productIntroduction" placeholder="更多关键信息，突出特色与亮点"
					placeholder-style="padding:0px;font-size:28rpx; color:rgba(153, 153, 153, 1); margin-top:16rpx;"
					@click="addproductionInduction"></textarea>
				<img :src="right" alt="" class="formimg" />
			</view>
		</view>
		<!-- 产品slogan -->
		<view class="form-item">
			<label>产品slogan</label>
			<input type="text" v-model="productSlogan" placeholder="选填，产品用于宣传的标语或口号"
				placeholder-style="padding:0px;font-size:28rpx; color:rgba(153, 153, 153, 1); margin-top:16rpx;" />
		</view>
		<!-- 所在行业 -->
		<view class="form-item">
			<label>所在行业</label>
			<input type="text" v-model="industry" placeholder="选填，最多可上传5张"
				placeholder-style="padding:0px;font-size:28rpx; color:rgba(153, 153, 153, 1); margin-top:16rpx;" />
		</view>
		<!-- 照片添加区域 -->
		<view class="form-item">
			<view class="photo-upload">
				<view v-for="(photo, index) in productPhotos" :key="index" class="photo-item">
					<image :src="photo"></image>
					<view class="delete-icon" @click="deletePhoto(index)">×</view>
				</view>
				<view v-if="productPhotos.length < 5" class="upload-placeholder" @click="openImagePicker">
					<img :src="download" alt="" srcset="" style="width: 200rpx; height: 200rpx;" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	//import download from 'https://api-test.zhaopinbei.com/storage/uploads/images/sr6WScTn14O2fLIke7oou4gGSujngi6u1ufSO2fo.png'
	export default {
		data() {
			return {
				productLogo: null,
				productName: '',
				productIntroduction: '',
				productSlogan: '',
				industry: '',
				productPhotos: [],
				download:'https://api-test.zhaopinbei.com/storage/uploads/images/sr6WScTn14O2fLIke7oou4gGSujngi6u1ufSO2fo.png',
				right:'https://api-test.zhaopinbei.com/storage/uploads/images/r2ac8QppIj7V5E5n2U6aClsLLI54U4cAl7bOfy5d.png'
			};
		},
		methods: {
			// 打开图片选择器
			openImagePicker() {
				uni.chooseImage({
					count: this.productPhotos.length < 5 ? 5 - this.productPhotos.length : 0,
					success: (res) => {
						if (this.productLogo === null) {
							this.productLogo = res.tempFilePaths[0];
						} else {
							this.productPhotos = this.productPhotos.concat(res.tempFilePaths);
						}
					}
				});
			},
			// 删除照片
			deletePhoto(index) {
				this.productPhotos.splice(index, 1);
			},
			addproductionInduction() {
				uni.navigateTo({
					url: '/pagesA/bolecompany/companyviews/productionIntroduction'
				})
			}
		}
	};
</script>

<style scoped>
	.product-form {
		width: 750rpx;
		height: 958rpx;
		padding-left: 24rpx;
	}

	.form-item-header {
		margin-bottom: 32rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.form-item {
		margin-bottom: 32rpx;
		display: flex;
		flex-direction: column;
	}

	label {
		font-size: 24rpx;
		color: rgba(51, 51, 51, 1);
	}

	.logo-upload,
	.photo-upload {
		display: flex;
		flex-wrap: wrap;
		margin-right: 32rpx;
	}

	.upload-placeholder {
		width: 200rpx;
		height: 200rpx;
		border-radius: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 60rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		cursor: pointer;
		background: rgba(246, 246, 246, 1)
	}

	.image {
		width: 200rpx;
		height: 200rpx;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
	}

	.delete-icon {
		position: relative;
		top: -20rpx;
		right: -20rpx;
		background-color: #fff;
		border-radius: 50%;
		width: 40rpx;
		height: 40rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 24rpx;
		cursor: pointer;
	}

	.formimg {
		width: 32rpx;
		height: 32rpx;
		margin-right: 32rpx;
		margin-top: 16rpx;
	}

	.formtext {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.formtext textarea {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	input,
	textarea {
		width: 100%;
		border: none;
		border-radius: 10rpx;
		height: 64rpx;
	}

	/* textarea {
		height: 40rpx;
	} */
</style>
