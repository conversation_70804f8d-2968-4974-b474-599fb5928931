page {
    background-color: #f5f5f7;
}

.inp {
    padding: 32rpx;
    .inp-item {
        margin-bottom: 32rpx;
        .name {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            font-size: 32rpx;
            color: #000000;
            margin-bottom: 16rpx;
            image {
                width: 32rpx;
                height: 32rpx;
            }
			.privilege-container {
				display: flex;
				align-items: center;
				gap: 16rpx;
			}
			.privilege-tags {
				display: flex;
				flex-wrap: wrap;
				gap: 16rpx;
			}
        }
        .in {
            background-color: #FFFFFF;
            height: 88rpx;
            line-height: 88rpx;
        }
        
        ::v-deep input {
            background-color: #FFFFFF;
            height: 88rpx;
            border-radius: 24rpx;
            padding-left: 32rpx;
        }
        
        .list {
            display: flex;
            flex-wrap: w4;
            .item {
                display: flex;
                align-items: center;
                height: 64rpx;
                padding: 0 24rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: #666666;
                background-color: #FFFFFF;
                border-radius: 16rpx;
            }
        }
    }
}

.footer {
	display: flex;
	justify-content: center;
	align-items: center;
	position: fixed;
	left: 0;
	bottom: 0;
	height: 120rpx;
	width: 100%;
	background-color: #FFFFFF;
	z-index: 10;
	.btns {
		display: flex;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
		width: 90%;
		.logout {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 30%; // 注销按钮宽度减半
			height: 80rpx;
			border-radius: 16rpx;
			margin-right: 24rpx;
			background: linear-gradient(135deg, #F0544F 0%, #EE1E1E 100%);
		}
	.save {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%; // 保存按钮宽度变长
		height: 80rpx;
		border-radius: 16rpx;
		background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
	}
}

    // .next {
    // 	display: flex;
    // 	justify-content: center;
    // 	align-items: center;
    // 	height: 88rpx;
    // 	width: 90%;
    // 	border-radius: 16rpx;
    // }
    // .sure{
    // 	background: linear-gradient( 135deg, #4F8CF0 0%, #1E6DEE 100%);
    // 	color: #FFFFFF;
    // }
}

.inp {
    background: #FFFFFF;
    border-radius: 16rpx;
    
    .avatar {
        display: flex;
        align-items: center;
        
        .pic {
            padding: 0 30rpx 0 0;
            
            image {
                width: 108rpx;
                height: 108rpx;
            }
        }
    }
    
    .inp-item {
        display: flex;
        flex-direction: column;
        flex: 1;
		margin-bottom: 32rpx;
        
        // border-bottom: 1px solid #F5F5F7;
        .title {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 32rpx;
            color: #000;
            margin-bottom: 16rpx;
            position: relative;
            
            .star {
                font-weight: 600;
                font-size: 22rpx;
                color: #FE4D4F;
                margin-left: 8rpx;
            }
            
            .plus {
                position: absolute;
                right: 0;
                top: 50%;
                margin-top: -16rpx;
                
                image {
                    width: 32rpx;
                    height: 32rpx;
                }
            }
        }
        
        .in {
            display: flex;
            align-items: center;
            justify-content: space-between;
            // border-bottom: 1px solid #F5F5F7;
            min-height: 88rpx;
            font-size: 32rpx;
            
            ::v-deep uni-data-picker {
                width: 100%;
            }
            
            ::v-deep .arrow-area {
                transform: rotate(-135deg);
            }
            
            ::v-deep .input-arrow {
                width: 20rpx;
                height: 20rpx;
                border-left: 1px solid #606266;
                border-bottom: 1px solid #606266;
            }
            
            ::v-deep .input-value-border {
                border: none;
            }
            
            ::v-deep .input-value {
                padding: 0;
            }
            
            ::v-deep .placeholderClass {
                font-weight: 400;
                font-size: 32rpx;
            }
            
            ::v-deep picker {
                display: flex;
                flex-direction: column;
                flex: 1;
                height: 88rpx;
                
                .d-picker {
                    display: flex;
                    align-items: center;
                    // width: 60vw;
                    height: 88rpx;
                }
            }
        }
        
        .scroll-W {
            width: 100%;
            white-space: nowrap;
            
        }
        
        .nav-item {
            display: inline-block;
            text-align: center;
            background: #F5F5F7;
            padding: 0 16rpx;
            height: 50rpx;
            line-height: 50rpx;
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
            border-radius: 8rpx;
            margin-right: 40rpx;
            
            &:last-child {
                margin-right: 0;
            }
            
            .cont {
                display: flex;
                align-items: center;
                width: 100%;
                height: 100%;
                
                .del {
                    display: flex;
                    align-items: center;
                    
                    text {
                        margin-right: 8rpx;
                    }
                }
                
            }
        }
        
        .active {
            background: #4F8CF0;
            color: #FFFFFF;
        }
        
        .se {
            color: #999;
        }
        
        .lab {
            font-weight: 400;
            font-size: 22rpx;
            color: #999999;
        }
    }
}
