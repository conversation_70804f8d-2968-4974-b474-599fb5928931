<template>
	<view class="">
		<view class="list">
			<view :class="['item',tabIndex=='member'?'active':'']" @click="changeTab('member')">
				<!-- <image src="https://api-test.zhaopinbei.com/storage/uploads/images/kpRkZfnC9ObOQaGulntJl9wYcSo5x4BazkeVRDsQ.png" style="width: 210rpx;" mode="widthFix"></image> -->
				<image src="https://api-test.zhaopinbei.com/images/job.png" style="width: 234rpx;" mode=""></image>
				<view class="info">
					<view class="name">
						我要求职
					</view>
					<view class="desc">
						你的未来，从这里开始
					</view>
				</view>
			</view>

			<view :class="['item',tabIndex=='company'?'active':'']" @click="changeTab('company')">
				<image src="https://api-test.zhaopinbei.com/images/recuit.png" style="width: 244rpx;" mode="widthFix"></image>
				<!-- <image src="https://api-test.zhaopinbei.com/storage/uploads/images/ZbP7jRYnIXqOZgEo5MCawtAeH7Q45uYbymCwSAtI.png" style="width: 188rpx;" mode=""></image> -->
				<view class="info">
					<view class="name">
						我要招聘
					</view>
					<view class="desc">
						让招聘变得快捷，便捷！
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="btns">
				<view class="btn" @click="go">
					确认身份
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabIndex: ''
			}
		},
		methods: {
			changeTab(index) {
				this.tabIndex = index
			},
			go() {
				if (this.tabIndex == 'member') {
					uni.navigateTo({
						url: "/pagesA/tourist/select_tourist_two?type=" + this.tabIndex
					})
				} else {
					uni.navigateTo({
						url: "/pagesA/tourist/selectJob?type=" + this.tabIndex
					})
				}

			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.list {
		display: flex;
		flex-direction: column;
		padding: 32rpx;

		.item {
			display: flex;
			padding: 10rpx 32rpx;
			background-color: #FFFFFF;
			border-radius: 24rpx;
			margin-bottom: 24rpx;

			&>image {
				width: 210rpx;
				height: 220rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				flex: 1;
				justify-content: center;
				align-items: center;

				.name {
					font-weight: 600;
					font-size: 40rpx;
					color: #000000;
				}

				.desc {
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
					margin-top: 8rpx;
				}
			}
		}

		.active {
			border: 2px solid #4F8CF0;
		}
	}


	.footer {
		display: flex;
		position: fixed;
		justify-content: center;
		align-items: center;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 120rpx;

		.btns {
			display: flex;
			width: 90%;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 100rpx;
				width: 100%;
				font-weight: 600;
				font-size: 34rpx;
				color: #FFFFFF;
				border-radius: 16rpx;
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			}
		}
	}
</style>
