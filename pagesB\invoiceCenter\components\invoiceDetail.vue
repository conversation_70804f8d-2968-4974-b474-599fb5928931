<template>
  <view class="invoice-detail">
    <!-- Header section with invoice type and amount -->
    <view class="invoice-header">
      <view class="invoice-type-box">
        <text class="invoice-type">数电普票</text>
        <text class="invoice-status">已开出</text>
      </view>
      <text class="invoice-amount">2657元</text>
    </view>

    <!-- Company information -->
    <view class="company-info">
      <text class="company-name">学创联盟网络科技（北京）有限公司</text>
    </view>

    <!-- Invoice details -->
    <view class="invoice-info">
      <view class="info-item">
        <text class="info-label">发票号码：</text>
        <text class="info-value">25117000000659783704</text>
      </view>
      <view class="info-item">
        <text class="info-label">申请时间：</text>
        <text class="info-value">2025-05-09 17:06:27</text>
      </view>
      <view class="info-item">
        <text class="info-label">开出时间：</text>
        <text class="info-value">2025-05-09 17:06:27</text>
      </view>
    </view>

    <!-- Action buttons -->
    <view class="action-buttons">
      <view class="action-btn apply-btn" @click="applyExchange">申请换开</view>
      <view class="action-btn send-btn" @click="sendInvoiceDetail">发送订单明细</view>
    </view>

    <!-- Notification message -->
    <view class="notification-message">
      <text>订单明细已发送，请前往邮箱查看</text>
    </view>

    <!-- Customer service button -->
    <view class="customer-service-btn" @click="contactCustomerService">
      <text>咨询在线客服</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      invoiceData: {
        type: "数电普票",
        status: "已开出",
        amount: "2657",
        company: "学创联盟网络科技（北京）有限公司",
        invoiceNumber: "25117000000659783704",
        applyTime: "2025-05-09 17:06:27",
        issueTime: "2025-05-09 17:06:27"
      }
    };
  },
  methods: {
    // Apply for invoice exchange
    applyExchange() {
      uni.showToast({
        title: '申请换开功能开发中',
        icon: 'none'
      });
    },
    
    // Send invoice details
    sendInvoiceDetail() {
      uni.showToast({
        title: '订单明细已发送',
        icon: 'none'
      });
    },
    
    // Contact customer service
    contactCustomerService() {
      uni.showToast({
        title: '正在连接客服',
        icon: 'none'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f7;
}

.invoice-detail {
  min-height: 100vh;
  padding-bottom: 120rpx;
  position: relative;
}

/* Header section */
.invoice-header {
  background-color: #fff;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0 0 24rpx 24rpx;
}

.invoice-type-box {
  display: flex;
  align-items: center;
}

.invoice-type {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.invoice-status {
  font-size: 24rpx;
  color: #4F8CF0;
  background-color: rgba(79, 140, 240, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  margin-left: 16rpx;
}

.invoice-amount {
  font-size: 36rpx;
  font-weight: 600;
  color: #4F8CF0;
}

/* Company information */
.company-info {
  margin-top: 32rpx;
  background-color: #fff;
  padding: 32rpx;
  border-radius: 24rpx;
}

.company-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* Invoice details */
.invoice-info {
  margin-top: 32rpx;
  background-color: #fff;
  padding: 32rpx;
  border-radius: 24rpx;
}

.info-item {
  display: flex;
  margin-bottom: 24rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  width: 180rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

/* Action buttons */
.action-buttons {
  margin-top: 32rpx;
  display: flex;
  justify-content: space-between;
  padding: 0 32rpx;
}

.action-btn {
  width: 330rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16rpx;
  font-size: 28rpx;
}

.apply-btn {
  border: 2rpx solid #4F8CF0;
  color: #4F8CF0;
  background-color: #fff;
}

.send-btn {
  background-color: #4F8CF0;
  color: #fff;
}

/* Notification message */
.notification-message {
  margin-top: 32rpx;
  padding: 24rpx 32rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  text-align: center;
  color: #fff;
  font-size: 28rpx;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

/* Customer service button */
.customer-service-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
}
</style>
