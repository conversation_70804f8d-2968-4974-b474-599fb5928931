<template>
	<view class="home-index">
		<u-sticky>
			<view class="header">
				<view class="tabs">
					<view :class="['tab pg_1', tabIndex == 0 ? 'active' : '']" @click="changeTab(0)">
						在线简历
					</view>
					<view :class="['tab pg_2', tabIndex == 1 ? 'active' : '']" @click="changeTab(1)">
						附件简历
					</view>
				</view>
				<view class="open" v-if="list && list.length > 0">
					<view class="name">
						是否公开简历
					</view>
					<u-switch v-model="open_status" @change='changePublic' :activeValue='1' :inactiveValue='2'
						size="20"></u-switch>
				</view>

			</view>
		</u-sticky>

		<block v-if="list.length > 0">
			<view class="list">
				<online-resume-item v-for="(item, index) in list" :key="index" :item="item"
					@setMainResume='setMainResume' @more='more' :type='tabIndex'></online-resume-item>
			</view>

		</block>

		<block v-else>
			<u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png">
			</u-empty>
		</block>
		<view style="height: 196rpx;"></view>
		<view class="footer">
			<view class="next sure" @click="add" v-if="tabIndex == 0">
				创建简历
			</view>
			<view class="next sure" @click="getFile" v-if="tabIndex == 1">
				上传简历
			</view>
		</view>
		<u-action-sheet :actions="sheetList" :show="showSheet" round="12" @close="close" @select="select"
			cancelText="取消"></u-action-sheet>
	</view>
</template>

<script>
	import OnlineResumeItem from '../components/onlineResumeItem.vue'
	import {
		getResumeList,
		openResume,
		delResume,
		setMainResume,
		updateLoginInfo,
		resumeList,
		uploadApi,
		deleteResume
	} from "../../config/api.js"
	export default {
		components: {
			OnlineResumeItem
		},
		watch: {
			list: {
				handler(newValue, oldValue) {},
				deep: true,
				immediate: true
			}
		},
		data() {
			return {
				open_status: 2,
				limit: 10,
				page: 1,
				showSheet: false,
				tabIndex: 0,
				currentItem: {},
				currentIndex: {},
				list: [],
				sheetList: [
					// {
					// 	value: 'send',
					// 	name: '生成附件简历',
					// },
					//             {
					// 	value: 'send',
					// 	name: '发送至',
					//             },
					{
						value: 'preview',
						name: '预览'
					},
					{
						value: 'delete',
						name: '删除' //开启后文字不显示
					}
				],
			}
		},
		computed: {
			userInfo() {
				return this.$store.state.userInfo || uni.getStorageSync('userInfo')
			},
		},
		onLoad() {
			this.open_status = this.userInfo.member.open_status
			this.getResumeList()
		},
		methods: {
			add() {
				uni.navigateTo({
					// url: "/pagesA/add/addResume"
					url: '/pagesB/Jobsetting/online'
				})
				// uni.navigateTo({
				//     url: "/pagesA/personal/resumeTemplate"
				// })
			},
			// 微信聊天记录上传文件
			getFile() {
				uni.chooseMessageFile({
					count: 1,
					type: 'file',
					// extension: ['doc', 'docx', 'pdf', 'pptx', 'ppt', 'xls', 'xlsx'],
					success: tempFilePaths => {
						const file = tempFilePaths.tempFiles[0];
						uploadApi({
							file
						}).then(response => {
							const res = JSON.parse(response)
							if (res.code == 200) {
								uni.$u.toast('文件上传成功');
								this.page = 1
								this.list = []
								// 更新列表数据
								this.resumeList()
							} else {
								uni.$u.toast(res.message);
							}
						})
					},
					fail: (err) => {
						if (err.errMsg.includes('cancel')) return;
						uni.$u.toast('文件选择失败');
					}
				});
			},
			changeTab(index) {
				this.tabIndex = index
				this.page = 1
				this.list = []
				if (index == 0) {
					this.getResumeList()
				} else if (index == 1) {
					this.resumeList()
				}
			},
			// 附件简历列表
			resumeList() {
				let params = {
					user_id: uni.getStorageSync('userInfo').member_certification.id
				}
				resumeList(params).then(res => {
					this.list = res.data;
				})
			},
			//简历列表
			async getResumeList() {
				let params = {
					limit: this.limit,
					page: this.page,
					type: this.tabIndex == 0 ? 'resume' : 'draft' //简历类型：resume-简历，draft-草稿
				}
				const {
					status_code,
					data
				} = await getResumeList(params)
				if (status_code == 200) {
					this.list = data.data;
				}

			},
			changePublic(e) {
				console.log("改变简历状态：", e)
				this.open_status = e
				this.openResume()
			},

			//设置为主简历
			async setMainResume(obj) {
				console.log("设置为主简历：", obj)
				let params = {
					public_status: obj.value,
					id: obj.item.member_resume.id
				}
				const {
					status_code,
					data
				} = await setMainResume(params)

				if (status_code == 200) {
					console.log(22222)
					this.page = 1;
					this.list = [];
					this.getResumeList()
				}
			},

			//公开简历
			async openResume() {
				let params = {
					open_status: this.open_status
				}
				const {
					status_code,
					data
				} = await openResume(params)
				if (status_code == 200) {
					this.page = 1;
					this.getResumeList()

					let loginInfo = await updateLoginInfo()
					if (loginInfo.status_code == 200) {
						this.$store.commit('setUserInfo', loginInfo.data)
					}
				}
			},

			more(e) {
				this.showSheet = e.show;
				this.currentItem = e.item;
				this.currentIndex = e.index;
				console.log(e)

				console.log(this.currentItem)
			},
			close() {
				this.showSheet = false
				this.currentItem = {}
			},

			select(e) {
				let self = this;
				if (e.value == 'send' && self.tabIndex == 0) {
					uni.navigateTo({
						url: '/pagesA/list/messageList'
					})
				}
				// 预览附件简历
				if (e.value == 'preview' && self.tabIndex == 1) {
					// 文件预览
					const filePath = self.currentItem.path;
					uni.showLoading({
						title: '准备文件中...'
					});

					// 先下载文件到本地
					uni.downloadFile({
						url: filePath,
						success: (res) => {
							if (res.statusCode === 200) {
								// 打开文档预览
								uni.openDocument({
									filePath: res.tempFilePath,
									showMenu: true, // 显示右上角菜单支持分享
									fileType: self.currentItem.mime_type,
									success: () => uni.hideLoading(),
									fail: (err) => {
										uni.hideLoading();
										uni.showToast({
											title: '不支持该格式预览',
											icon: 'none'
										});
									}
								});
							}
						},
						fail: (err) => {
							uni.hideLoading();
							uni.showToast({
								title: '文件下载失败',
								icon: 'none'
							});
						}
					});
				}
				if (e.value == 'delete' && self.tabIndex == 0) {
					const id = self.currentItem.member_resume.id
					uni.showModal({
						title: '确定要删除该简历吗？',
						success: res => {
							if (res.confirm) {
								self.delResume(id)
							} else if (res.cancel) {
								console.log('用户点击取消');
								self.currentItem = {}
							}
						}
					})
				}

				if (e.value == 'delete' && self.tabIndex == 1) {
					const resume_id = self.currentItem.id
					uni.showModal({
						title: '确定要删除该简历吗？',
						success: res => {
							if (res.confirm) {
								deleteResume({
									resume_id
								}).then(() => {
									uni.$u.toast('删除成功')
									self.page = 1;
									self.list = [];
									self.resumeList()
								})


							} else if (res.cancel) {
								console.log('用户点击取消');
								self.currentItem = {}
							}
						}
					})

				}

				// 预览在线简历
				if (e.value == 'preview' && tabIndex == 0) {
					uni.navigateTo({
						url: '/pagesA/details/resumeDetails?id=' + self.currentItem.member_resume.id
					})
				}
			},

			//删除简历
			async delResume(id) {
				let params = {
					id
				}
				const {
					status_code,
					data
				} = await delResume(params)
				if (status_code == '200') {
					this.list.splice(this.currentIndex, 1);
					return uni.$u.toast('成功')
				}
			}
		}
	}
</script>

<style lang="scss">
	@import "../../static/css/pagesA/personal/myResume.scss";
</style>