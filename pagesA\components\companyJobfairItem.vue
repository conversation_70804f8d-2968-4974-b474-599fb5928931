<template>
    <view class="app" @click.stop="go">
        <view class="top">
            <image :src="item.job_active.image.path_url" mode="" class="imgLogo"></image>
            <view class="topText">
                <view class="title">{{ item.job_active.title }}</view>
                <view class="times">{{ item.job_active.start }}-{{ item.job_active.end }}</view>
                <view class="companyNum">已有{{ item.report_count }}家企业入职</view>
            </view>
        </view>
        <view class="upDate">
            <view>提交时间：{{ item.job_active.created_at }}</view>
            <!-- 状态动态变化 -->
            <!--:type==2?'greenCL':''-->
            <view v-if="item.job_active.active_status == 'started'" :class="['dateType','greenCL']">{{ item.job_active.active_status_name }}</view>
            <view v-if="item.job_active.active_status == 'end'" :class="['dateType','redCl']">{{ item.job_active.active_status_name }}</view>
            <view v-if="item.job_active.active_status == 'not-started'" :class="['dateType','yellowCL']">{{ item.job_active.active_status_name }}</view>
        </view>
        <!-- 待审核就整个隐藏掉这个bottom盒子v-if -->
        <view class="bottom" v-if="item.but.job_list == 1" @click.stop>
            <view class="listBtn" @click.stop="goListPage(item.job_active)" v-if="item.but.job_list == 1">职位列表</view>
            <view class="sendBtn" @click.stop="add(item.job_active)" v-if="item.but.job_add == 1">发布职位</view>
            <!-- 驳回后展示驳回原因 -->
            <!-- <view class="redFont">驳回原因</view> -->
        </view>
    </view>
</template>

<script>
    export default {
        name: "companyJobfairItem",
        props: {
            item: {
                type: Object,
                default: () => {}
            },
        },
        data() {
            return {
                type: 1
            };
        },
        computed: {
        	roleType() {
        		return this.$store.state.roleType || uni.getStorageSync("roleType")
        	}
        },
        methods: {
            // 从就业管家那边拿过来的进入详情
            go() {
                if (this.roleType == 'member') {
                    uni.navigateTo({
                        url: "/pagesA/details/qlmActiveDetails?id=" + this.item.job_active.id
                    })
                } else if (this.roleType == 'company' || this.roleType == 'headhunters') {
                    uni.navigateTo({
                        url: "/pagesA/details/blActiveDetails?id=" + this.item.job_active.id
                    })
                }
            },
            add(jobActive) {
            	uni.navigateTo({
            		url: '/pagesA/add/pubJobOne?type=job_active&job_active_id='+jobActive.id,
            	})
            },
            goListPage(jobActive) {
                uni.navigateTo({
                    url:'/pagesA/list/company_jobfair_jobList?job_active_id='+jobActive.id,
                })
            }
        }
    }
</script>

<style lang="less" scoped>
    view {
        box-sizing: border-box;
    }

    .app {
        width: 100%;
        // height: 398rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 32rpx;
        margin-bottom: 24rpx;
    }

    .imgLogo {
        width: 184rpx;
        height: 138rpx;
        background: #D9D9D9;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        margin-right: 16rpx;
    }

    .top {
        display: flex;
        align-items: center;
    }

    .topText {
        width: 404rpx;
    }

    .title {
        width: 100%;
        font-weight: bold;
        font-size: 28rpx;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 12rpx;
    }

    .times {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        margin-bottom: 12rpx;
    }

    .companyNum {
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
    }

    .upDate {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        margin-top: 36rpx;
    }

    .dateType {
        padding: 8rpx 16rpx;
        font-weight: 500;
        font-size: 24rpx;
		border-radius: 8rpx;
    }

    .yellowCL {
        background: rgba(249, 173, 20, 0.1);
        color: #F9AD14;
    }

    .greenCL {
        background: rgba(87, 213, 28, 0.1);
        color: #57D51C;
    }

    .redCl {
        background-color: #CCCCCC;
        color: white;
    }

    .bottom {
        margin-top: 24rpx;
        padding-top: 24rpx;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        border-top: 2rpx solid #F5F5F7;
    }

    .listBtn {
        width: 160rpx;
        height: 64rpx;
        background: rgba(79, 140, 240, 0.1);
        border-radius: 12rpx 12rpx 12rpx 12rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #4F8CF0;
        text-align: center;
        line-height: 64rpx;
        margin-right: 24rpx;
    }

    .sendBtn {
        width: 160rpx;
        height: 64rpx;
        background: #4F8CF0;
        border-radius: 12rpx 12rpx 12rpx 12rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #FFFFFF;
        text-align: center;
        line-height: 64rpx;
    }

    .redFont {
        font-weight: 600;
        font-size: 24rpx;
        color: #FE4D4F;
    }
</style>