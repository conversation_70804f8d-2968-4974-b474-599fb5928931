<template>
    <view class="item" @click="go">
        <block v-if="item.list_status=='active'">
            <view class="status ing">
                招聘中
            </view>
        </block>
        <block v-if="item.list_status=='draft'">
            <view class="status cg">
                草稿
            </view>
        </block>
        <block v-if="item.list_status=='off_shelf'">
            <view class="status yxj">
                已下架
            </view>
        </block>

        <view class="item-up">
            <view class="title">
                {{item.title}}
            </view>
            <view class="item-up-one">
                <view class="name">
                    {{item.job_class.name}}
                </view>
                <view class="money">
                    {{item.salary_info_str}}
                </view>
            </view>
            <view class="item-up-two">
                <view class="lab">
                    发布人
                </view>
                <view class="money">
                    {{item.send_user_member_certification.name}}
                </view>
            </view>
        </view>
        <block v-if="item.list_status=='active'">
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="item-down flexRow">
                <view class="btns">
                    <view class="btn zd" @click.stop="backTop(item)">
                        <image src="/static/images/plat/zd.png" mode=""></image>
                        置顶
                    </view>
                    <view :class="['btn',item.exposed_status==2?'agree':'refuse']" @click.stop="expose(item)">
                        <image src="/static/images/plat/bg.png" mode=""></image>
                        {{item.exposed_status==2?'曝光':'已曝光'}}
                    </view>
                </view>
            </view>
        </block> 
				<block v-if="item.list_status=='draft' && roleType == 'headhunters'">
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="item-down flexRow">
                <view class="btns">
                    <view class="btn zd" @click.stop="backTop(item)">
                        <image src="https://api-test.zhaopinbei.com/storage/uploads/images/YqgQKajQ46SxUZkUpVM5pOL8FYIBJ6TFvYv6c8Gy.png" mode=""></image>
                        编辑
                    </view>
                    <view :class="['btn',item.exposed_status==2?'agree':'refuse']" @click.stop="expose(item)">
                        <image src="https://api-test.zhaopinbei.com/storage/uploads/images/eJekyg1l5f6d891lZ1v2PXjJ24OWvHRMhpkJqWBC.png" mode=""></image>
                        发布
                    </view>
                </view>
            </view>
        </block>
    </view>
    
</template>

<script>
    export default {
        name: "companyPubJobItem",
        props: {
            item: {
                type: Object,
                default: () => {
                }
            },
        },


        data() {
            return {};
        },
				computed: {
				  roleType() {
				    console.log('当前用户的身份：', uni.getStorageSync('roleType'))
				    return this.$store.state.roleType || uni.getStorageSync('roleType')
				  },
				},

        methods: {
            go() {
                uni.navigateTo({
                    url: `/pagesA/details/companyJobDetails?type=${this.item.type}&id=${this.item.id}`
                })
            },
            expose(item) {
                console.log("曝光：", item)
                if (item.exposed_status != 2) return;
                this.$emit('expose', item)
            },
            backTop(item) {
                console.log("置顶：：", item)
                this.$emit('backTop', item)
            }
        }
    }
</script>

<style lang="scss">
    @import "../../static/css/pagesA/components/companyPubJobItem";
</style>
