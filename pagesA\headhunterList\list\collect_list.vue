<template>
	<view class="">
		<u-sticky bgColor="#FFFFFF">
			<view class="tabs">
				<u-tabs :current="tabIndex" lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" @click="changeTab"></u-tabs>
			</view>
			
		</u-sticky>

		<view class="list">
			<u-swipe-action>
				<u-swipe-action-item :options="options" v-for="(item,index) in list" :key="index">
					<block v-if="tabIndex==0">
            <collect-job-item :item="item"></collect-job-item>
					</block>
					<block v-if="tabIndex==1">
						<comp-item :item="item"></comp-item>
					</block>
					<block v-if="tabIndex==2">
						<qlm-job-item :item="item"></qlm-job-item>
					</block>
				</u-swipe-action-item>
			</u-swipe-action>
		</view>
	</view>
</template>


<script>
	import JobItem from "../../../components/jobItem.vue"
	import CompItem from "../../components/compItem.vue"
	import QlmJobItem from "../../../components/qlmJobItem.vue"
  import {getCollectCompanyList, getCollectJobList, getCollectCompanyMemberList} from "../../../config/api";
  import CollectJobItem from "../../components/collectJobItem.vue";
	export default {
		components: {
      CollectJobItem,
			JobItem,
			CompItem,
			QlmJobItem
		},
		data() {
			return {
        page: 1,
        limit: 10,
				isScroll: false,
        list:[],
				options: [{
					text: '删除',
					style: {
						backgroundColor: '#FE4D4F',
						borderRadius: '24rpx',
						bottom: '32rpx',
						height: '100%',
						width: '150rpx',
						marginLeft: '24rpx'
					}
				}],
				tabIndex: 0,
				tabs: [{
					name: '职位收藏',
				}, {
					name: '企业收藏',
				}, {
					name: '千里马收藏'
				}]
			}
		},
		onPageScroll(e) {
			this.isScroll = e.scrollTop > 0
		},
    onLoad() {
      this.getCollectJobList()
    },
		methods: {
      changeTab(e) {
        this.page = 1
        this.list = []
        this.tabIndex = e.index
        if (this.tabIndex == 0) {
          this.getCollectJobList()
        } else if (this.tabIndex == 1) {
          this.getCollectCompanyList()
        } else if (this.tabIndex == 2) {
          this.getCollectCompanyMemberList()
        }
      },
      //职位收藏列表
      async getCollectJobList() {
        let params = {
          page: this.page,
          limit: this.limit
        }
        const {
          status_code,
          message,
          data
        } = await getCollectJobList(params)
        if (status_code == 200) {
          this.list = data.data;
        }
      },
      //求职者收藏列表
      async getCollectCompanyMemberList() {
        let params = {
          page: this.page,
          limit: this.limit
        }
        const {
          status_code,
          message,
          data
        } = await getCollectCompanyMemberList(params)
        if (status_code == 200) {
          this.list = data.data;
        }
      },
      //企业收藏列表
      async getCollectCompanyList() {
        let params = {
          page: this.page,
          limit: this.limit
        }
        const {
          status_code,
          message,
          data
        } = await getCollectCompanyList(params)
        if (status_code == 200) {
          this.list = data.data;
        }
      }
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f5;
	}
</style>
<style lang="less" scoped>
	.tabs{
		margin-bottom: 32rpx;
	}
	.list {
		padding: 0 32rpx;

		::v-deep .u-swipe-action-item__right {
			bottom: 32rpx;
			border-radius: 24rpx;
		}

		::v-deep .u-swipe-action-item__content {
			background: transparent;
		}
	}
</style>