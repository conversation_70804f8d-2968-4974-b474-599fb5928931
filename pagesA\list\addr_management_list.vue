<template>
    <view class="main">
        <view class="list">
            <u-swipe-action>
                <u-swipe-action-item :options="options" v-for="(item,index) in page.data" :name="index" :key="item.id"
                                     @click="handleSwipe(item,$event,index)">
                    <addr-item :item="item" @setDefaultAddress="setDefaultAddress" :key="index" :itemIndex="index"
                               v-if="roleType == 'member'"></addr-item>
                    <headhunters-addr-item :item="item" @setDefaultAddress="setDefaultAddress" :key="index"
                                           v-else></headhunters-addr-item>
                </u-swipe-action-item>
            </u-swipe-action>
        </view>
        <Pages :status="page.status"></Pages>

        <view class="footer">
            <view class="next sure" @click="add">
                新建地址
            </view>
        </view>
    </view>
</template>

<script>
    import {
        getAddressList,
        delAddress,
        setDefaultAddress
    } from "../../config/api.js"
    import AddrItem from '../components/addrItem.vue'
    import HeadhuntersAddrItem from '../components/headhuntersAddrItem.vue'
    import Pages from "../../components/pages.vue";

    export default {
        components: {
            AddrItem,
            HeadhuntersAddrItem,
            Pages,
        },
        data() {
            return {
                page: {
                    statusType: {
                        true: 'loadmore',
                        false: 'nomore',
                    },
                    form: {
                        page: 1,
                        limit: 10,
                    },
                    more: false,
                    status: 'nomore',
                    data: [],
                },
                options: [{
                    text: '删除',
                    style: {
                        backgroundColor: '#FE4D4F',
                        borderRadius: '24rpx',
                        bottom: '32rpx',
                        height: '70%',
                        width: '150rpx',
                        marginLeft: '24rpx',
						marginBottom: '56rpx'
                    }
                }],

                list: []
            }
        },

        watch: {
            list: {
                handler(newValue, oldValue) {
                },
                immediate: true,
                deep: true
            }
        },

        onShow() {
            this.addItem().editItem();
        },
        onLoad() {
            this.getAddressList();
        },
        onReachBottom() {
            if (this.page.more) {
                this.page.status = 'loading';
                this.page.form.page++;
                this.getAddressList();
            }
        },
        computed: {
            roleType() {
                return this.$store.state.roleType || uni.getStorageSync('roleType')
            },

        },
        methods: {
            editItem() {

                var editAddress = uni.getStorageSync('editAddress');
                var addressItemIndex = uni.getStorageSync('addressItemIndex');
                uni.removeStorageSync('editAddress');


                if (editAddress && addressItemIndex >= 0) {
                    this.page.data.splice(addressItemIndex, 1, editAddress);
                }
                return this;
            },
            addItem() {
                var storeAddress = uni.getStorageSync('storeAddress');
                uni.removeStorageSync('storeAddress');
                if (storeAddress) {
                    this.page.data.unshift(storeAddress);
                }

                return this;
            },
            add() {
                uni.navigateTo({
                    url: "/pagesA/add/addAddress"
                })
            },
            initPage() {
                this.page.page = 1;
                this.page.data = [];
                this.page.status = 'loadmore';
            },
            //地址列表
            async getAddressList() {
                var _this = this;
                getAddressList(this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.statusType[_this.page.more] || 'nomore';
                    }
                });
            },

            //删除
            async handleSwipe(item, e,ind) {
                
                let self = this;
                //表示点击了删除按钮
                if (e.index == 0) {
                    uni.showModal({
                        title: '确定要删除该地址吗？',
                        success: async (res) => {
                            if (res.confirm) {
                                console.log('用户点击确定');
                                let params = {
                                    id: item.id
                                }
                                const {
                                    status_code,
                                    data,
                                    message
                                } = await delAddress(params)
                                if (status_code == 200) {
                                    self.page.data.splice(ind,1)
                                    return uni.$u.toast('成功')
                                } else {
                                    return uni.$u.toast(message || '失败')
                                }
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    })
                }
            },

            //设置默认地址
            async setDefaultAddress(op) {
                var _item = op.item;

                let params = {
                    id: _item.id
                }
                const {
                    status_code,
                    data,
                    message
                } = await setDefaultAddress(params)

                uni.$u.toast(message || '失败');

                if (status_code == '200') {
                    this.page.data = this.page.data.map(function (item) {
                        if (_item.id == item.id) {
                            item.is_default = 1;
                        } else {
                            item.is_default = 2;
                        }
                        return item;
                    });
                }
            }
        }
    }
</script>
<style lang="scss" src="../../static/css/pagesA/list/addr_management_list.scss"></style>