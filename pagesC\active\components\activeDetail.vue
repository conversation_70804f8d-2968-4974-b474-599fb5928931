<template>
  <view class="activity-detail">
    <!-- 活动标题卡片 -->
    <view class="card">
      <view class="title">郑大特推活动</view>
      <view class="subtitle">特推招聘</view>
      <view class="date">2025.01.01-2025.05.05</view>
    </view>

    <view class="card">
      <view class="section-title">活动简介</view>
      <view class="section-content">众多岗位，职等你来</view>
      <u-line></u-line>
      <view class="section-title">活动介绍</view>
      <view class="section-content"
        >岗位招聘岗位招聘岗位招聘岗位招聘岗位招聘岗位招聘岗位招聘岗位招聘岗位招聘</view
      >
      <u-line></u-line>
      <template v-if="id == 0">
        <view class="section-title">活动地址</view>
        <view class="address-txt">
          <img
            src="https://api-test.zhaopinbei.com/storage/uploads/images/rPvQxO1qxIVzBqs965YP2YJsqdM4VpjbFbiqWvyc.png"
            alt=""
          />
          <text>上饶信州区中科数创园4楼</text>
        </view>
        <view class="address"></view>
      </template>
      <template v-else>
        <view class="section-title">岗位展示</view>
        <view class="job-list">
          <view class="job-item" v-for="(item, index) in jobList" :key="index">
            <view class="job-title">{{ item.title }}</view>
            <view class="job-requirement">{{ item.requirement }}</view>
          </view>
        </view>
      </template>
      <u-line></u-line>
      <view class="section-title">其他要求</view>
      <view class="section-content">扫码报名</view>
      <view class="qr-code">
        <image
          src="/static/images/qrcode-placeholder.png"
          mode="aspectFit"
        ></image>
      </view>
    </view>
    <view style="height: 196rpx"></view>
    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next" @click="next"> 下载 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: null,
      jobList: [
        { title: "UI设计师 5-8K 3年工资经验 本科", requirement: "" },
        { title: "UI设计师 5-8K 3年工资经验 本科", requirement: "" },
        { title: "UI设计师 5-8K 3年工资经验 本科", requirement: "" },
        { title: "UI设计师 5-8K 3年工资经验 本科", requirement: "" },
        { title: "UI设计师 5-8K 3年工资经验 本科", requirement: "" },
        { title: "UI设计师 5-8K 3年工资经验 本科", requirement: "" },
      ],
    };
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      console.log("获取活动ID:", this.id);
    } else {
    }
  },
  methods: {
    downloadInfo() {
      uni.showToast({
        title: "开始下载",
        icon: "success",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-detail {
  padding: 20rpx;
  background-color: #f5f5f7;
  min-height: 100vh;

  .card {
    background-color: #ffffff;
    border-radius: 16rpx;
    padding: 32rpx 32rpx 32rpx 32rpx;
    margin-bottom: 20rpx;

    .title {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 10rpx;
    }

    .subtitle {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 10rpx;
    }

    .date {
      font-size: 28rpx;
      color: #999;
    }

    .section-title {
      font-size: 32rpx;
      color: #333;
      margin-bottom: 20rpx;
      margin-top: 24rpx;
    }

    .section-content {
      font-size: 28rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 24rpx;
    }

    .job-list {
      .job-item {
        padding: 20rpx 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .job-title {
          font-size: 28rpx;
          color: #333;
        }

        .job-requirement {
          font-size: 24rpx;
          color: #999;
          margin-top: 10rpx;
        }
      }
    }

    .address-txt {
      font-size: 28rpx;
      color: #666666;
      display: flex;
      gap: 18rpx;
      margin: 20rpx 0;
      img {
        width: 40rpx;
        height: 40rpx;
      }
    }
    .address {
      height: 280rpx;
      background: #ccc;
    }

    .qr-code {
      display: flex;
      margin-top: 30rpx;

      image {
        width: 162rpx;
        height: 162rpx;
        background: #d9d9d9;
        border-radius: 24rpx;
      }
    }
  }

  .footer {
    display: flex;
    justify-content: center;
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 196rpx;
    background-color: #ffffff;
    box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
    z-index: 10;
    border-radius: 24rpx 24rpx 0 0;

    .next {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 80rpx;
      width: 90%;
      border-radius: 16rpx;
      background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
      color: #ffffff;
      margin-top: 24rpx;
    }
  }
}
</style>
