<template>
    <view class="main">
        <view class="wrap">
            <view class="job-info">
                <view class="job-name">
                    <text class="job">{{details.title}}</text>
                    <text class="money">
                        {{details.salary_info_str}}
                    </text>
                </view>
                <view class="tags">
                    <view class="tag">
                        <image src="/static/images/index/addr.png" mode=""></image>
                        <text>{{details.addresses[0].city_name}}·{{details.addresses[0].district_name}}</text>
                    </view>
                    <view class="tag">
                        <image src="/static/images/index/package.png" mode=""></image>
                        <text>{{details.experience_name}}</text>
                    </view>
                    <view class="tag">
                        <image src="/static/images/index/doctor.png" mode=""></image>
                        <text>{{details.education_str}}</text>
                    </view>
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="pub-info">
                <image
                        :src="details.send_user_member_info.image['path_url']?details.send_user_member_info.image['path_url']:'https://api-test.zhaopinbei.com/storage/uploads/images/41R1LgOXldsXlyAjelHeNXsb0SuBPKE7ANcgobdG.png'"
                        mode=""></image>
                <view class="users">
                    <view class="username">
                        {{details.send_user_member_certification.name}}
                    </view>
                    <view class="address">
                        {{details.send_company.name?details.send_company.name:details.send_user_company.name}}
                    </view>
                </view>
            </view>
        </view>

        <view class="wrap">
            <view class="sub-wrap">
                <view class="sub-title">
                    职位内容
                </view>
                <view class="desc">
                    {{details.job_describe}}
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="sub-wrap">
                <view class="sub-title">
                    职位地址
                </view>
                <view class="pos">
                    <u-icon name="map-fill" size="28rpx"></u-icon>
                    <text @click="openNavigation(details.addresses[0])"
					>{{details.addresses[0].map_address}}</text>
                </view>
                <view class="map">
                    <map style="width: 100%; height: 300rpx;" :latitude="latitude" :longitude="longitude"
                         :markers="covers">
                    </map>
                </view>
            </view>
        </view>

        <view class="wrap">
            <view class="title">
                公司信息
            </view>
            <view class="compony-info">
                <image :src="details.company_info.logo.path_url" mode=""></image>
                <view class="info">
                    <view class="name">
                        {{details.company.name}}
                    </view>
                    <view class="tags">
                        <view class="tag">
                            {{ details.company_info.size_type_name }}
                        </view>
                        <view class="tag">
                            {{ details.company_info.financing_type_name }}
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- <view class="wrap" v-if="roleType=='member' && jobQuestionPage.data.length > 0" style="margin-bottom: 200rpx;">
            <view class="title">
                职位问答
            </view>
            <view class="answers">
                <view class="answer" v-for="(item,index) in jobQuestionPage.data" :key="index" :item="item">
                    <view class="ans">
                        <image src="/static/images/index/ans.png" mode=""></image>
                        <text>{{ item.content }}</text>
                    </view>

                    <view class="qus" v-for="(item,index) in item.childes" :key="index" :item="item">
                        <image src="/static/images/index/qus.png" mode=""></image>
                        <text>{{ item.content }}</text>
                    </view>

                </view>
            </view>
            <view class="more" v-if="jobQuestionPage.more" @click="jobQuestionMore">
                更多
                <image src="/static/images/index/down.png" mode=""></image>
            </view>
        </view> -->

        <view class="footer">
            <block v-if="roleType=='member'">
                <view :class="['favor',details.isFavor==1?'ysc':'']">
                    <u-icon name="star-fill" :color="details.isFavor==1?'#F9AD14':'#999999'"></u-icon>
                    收藏
                </view>
                <view class="btns">
                    <view :class="['btn',details.isCancel==1?'cancel':'']" @click="openComp">
                        {{details.isCancel==1?'取消报名':'立即报名'}}
                    </view>
                    <view class="btn agree">
                        聊聊呗
                    </view>
                </view>
            </block>
            <block>
                <view class="btns">
                    <view class="btn edit" @click="edit">
                        编辑职位
                    </view>
                    <view class="btn agree" v-if="details.but_open_status==1" @click="upDownCompanyJob('open')">
                        上架职位
                    </view>

                    <view class="btn down" v-if="details.but_off_status==1" @click="upDownCompanyJob('off')">
                        下架职位
                    </view>
                </view>
            </block>
        </view>


        <u-popup :show="showComp" :round="10" bgColor="#F5F5F5" mode="bottom" closeOnClickOverlay @close="closeComp"
                 @open="openComp">
            <view class="credit">
                <view class="title comp">
                    请选择发送简历
                </view>
                <view class="content">
                    <view class="btns">
                        <view :class="['btn',compIndex==1?'active':'']" @click="changeComp(1)">
                            <text class="">简历名称简历名称简历名称</text>
                        </view>
                        <view :class="['btn',compIndex==2?'active':'']" @click="changeComp(2)">
                            <text>简历名称简历名称简历名称</text>
                        </view>
                    </view>
                </view>

                <view class="agree" @click="goHome">
                    确定
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
    import Pages from "../../components/pages.vue";

    import {
        upDownCompanyJob,
        getCompanyJobDetails
    } from "../../config/api.js";

    import {
        getJobDetails
    } from "../../config/api";

    export default {
        components: {
            Pages,
        },
        data() {
            return {
                id: 0,
                compIndex: 1,
                showComp: false,
                latitude: 39.909,
                longitude: 116.39742,
                details: {
                    isFavor: 1,
                    isCancel: 1,
                },
                jobQuestionPage: {
                    form: {
                        page: 1,
                        limit: 10,
                        id: 0,
                    },
                    more: false,
                    data: [],
                },
                covers: [{
                    latitude: 39.909,
                    longitude: 116.39742,
                    iconPath: '../../../static/location.png'
                }],
                type: '',
            }
        },
        computed: {
            roleType() {
                return this.$store.state.roleType || uni.getStorageSync('roleType')
            }
        },
        onLoad(options) {
            this.id = options.id || 0;
            this.type = options.type;

        },
        onShow() {
            this.getCompanyJobDetails();
            if (this.roleType == 'member') {
                this.jobQuestionList();
            }
        },
        methods: {
            jobQuestionList() {
                this.jobQuestionPage.form.id = this.id;
                jobQuestionList(this.jobQuestionPage.form).then(response => {
                    if (response.status_code == '200') {
                        this.jobQuestionPage.data = this.jobQuestionPage.data.concat(response.data.data);
                        this.jobQuestionPage.more = response.data.more;
                    }
                });
            },
            jobQuestionMore() {
                if (this.jobQuestionPage.more) {
                    this.jobQuestionPage.form.page++;
                    this.jobQuestionList();
                }
            },
            edit() {
                uni.navigateTo({
                    url: `/pagesA/add/pubJobOne?type=${this.type}&id=${this.id}&copy=1`
                })
            },

			// 导航方法
			openNavigation(address) {
				// console.log('22223333', address)
				wx.openLocation({
					latitude: parseFloat(address.lat), // 纬度，浮点数，范围为-90~90
					longitude: parseFloat(address.lng), // 经度，浮点数，范围为-180~180
					name: address.map_address || '面试地点', // 位置名
					scale: 18 // 地图缩放级别
				})
			},

            openComp() {
                this.showComp = true
            },

            closeComp() {
                this.showComp = false
            },

            changeComp(index) {
                this.compIndex = index
            },

            async getCompanyJobDetails() {
                let params = {
                    id: this.id
                }
                const {
                    status_code,
                    data
                } = await getJobDetails(params)
                if (status_code == 200) {
                    this.details = data

                    this.covers[0]['latitude'] = this.details['addresses'][0]['lat']
                    this.covers[0]['longitude'] = this.details['addresses'][0]['lng']

                    this.latitude = this.details['addresses'][0]['lat']
                    this.longitude = this.details['addresses'][0]['lng']
                }
            },

            //上下架
            async upDownCompanyJob(type) {
                let self = this;
                uni.showModal({
                    title: type == 'off' ? '确定要下架该职位吗？' : '确定要上架该职位吗？',
                    success: async (res) => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            let params = {
                                id: self.id,
                                recruiting_status: type
                            }
                            const {
                                status_code,
                                data,
                                message
                            } = await upDownCompanyJob(params)
                            if (status_code == 200) {
                                self.getCompanyJobDetails()
                                return uni.$u.toast('成功')
                            } else {
                                return uni.$u.toast('失败' || message)
                            }
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                })
            }
        }
    }
</script>
<style>
    page {
        background: #f5f5f7;
    }
</style>
<style lang="scss">
    @import "../../static/css/pagesA/details/companyJobDetails";
</style>
