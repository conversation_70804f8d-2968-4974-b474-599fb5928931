<template>
    <view id="app">
        <view v-for="item in list" :key="item.authorize_company_id"
            :class="['personCla', { selected: selectedIds.includes(item.authorize_company_id) }]"
            @click="toggleSelection(item.authorize_company_id)">
            {{item.authorize_company.name}}
        </view>
        <Pages :status="status"></Pages>
        <u-modal :show="show" :title="title" @confirm="confirm" @close="close" :closeOnClickOverlay="true">
            <view class="slot-content">
                <textarea name="" id="" cols="30" rows="10" placeholder="请输入简介" v-model="remark"></textarea>
            </view>
        </u-modal>
        <view style="height: 196rpx;"></view>
        <view class="bottomBox">
            <view class="btn redBtn" @click="addBtn(1)">取消</view>
            <view class="btn greyBtn" @click="addBtn(2)">清空</view>
            <view class="btn blueBtn" @click="addBtn(3)">确定</view>
        </view>
    </view>
</template>

<script>
    import {
        pushCompanyStore
    } from '../../config/headhunterList_api.js'
    import {
        getHeadhuntersAuthEnterpriseList
    } from "../../config/api.js"
    import Pages from '../../components/pages.vue'
    export default {
        components: {
            Pages
        },
        data() {
            return {
                id: '',
                resume_id:'',
                page: 1,
                limit: 10,
                status: 'loadmore',
                more: false,
                list: [],
                selectedIds: [],
                show: false,
                title: '简介',
                remark:''
            };
        },
        onLoad(option) {
            this.id = option.id
            this.resume_id = option.resume_id
            this.getHeadhuntersAuthEnterpriseList()
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getHeadhuntersAuthEnterpriseList()
            } else {
                this.status = 'nomore'
            }
        },
        methods: {
            async getHeadhuntersAuthEnterpriseList() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    status:1,
                    cancel_status:2
                }
                const {
                    status_code,
                    data,
                    message
                } = await getHeadhuntersAuthEnterpriseList(params)
                if (status_code == 200) {
                    this.list = this.list.concat(data.data);
                    console.log("res", this.list)
                }
            },
            addBtn(num) {
                if (num == 1) {
                    uni.navigateBack()
                }
                if (num == 2) {
                    this.selectedIds = []
                }
                if (num == 3) {
                    this.show = true
                }
            },
            confirm() {
                if (this.selectedIds.length == 0) {
                    return uni.showToast({
                        title: '请选择企业',
                        icon: 'none'
                    });
                }
                if(this.remark) {
                    this.show = false
                    this.signUp()
                } else {
                    return uni.showToast({
                        title: '请输入简介',
                        icon: 'none'
                    });
                }
            },
            close() {
                this.show = false
            },
            async signUp() {
                
                // 后续逻辑，选完公司点击确定唤起输入简介，输入完简介点击确定走发送企业接口
                let params = {
                    member_id: this.id,
                    resume_id: this.resume_id,
                    company_id: this.selectedIds,
                    remakr:this.remark
                }

                const res = await pushCompanyStore(params)
                console.log("res",res)
                if (res.status_code == 200) {
                    uni.showToast({
                        title: '推送成功',
                        icon: 'none'
                    });
                }

            },
            toggleSelection(id) {
                const index = this.selectedIds.indexOf(id);
                if (index > -1) {
                    // 如果已经选中，则取消选择
                    this.selectedIds.splice(index, 1);
                } else {
                    // 如果未选中，则添加选择
                    this.selectedIds.push(id);
                }
            }
        }
    }
</script>
<style>
    page {
        background: #f5f5f7;
    }
</style>
<style scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .personCla {
        padding: 24rpx;
        border: 2rpx solid #ccc;
        margin-bottom: 12px;
        border-radius: 24rpx;
    }

    .selected {
        border-color: blue;
    }

    .bottomBox {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 196rpx;
        background: #FFFFFF;
        box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
        display: flex;
        padding: 24rpx 32rpx 92rpx 32rpx;
        justify-content: space-between;
    }

    .btn {
        width: 216rpx;
        height: 80rpx;
        text-align: center;
        line-height: 80rpx;
        color: #FFFFFF;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
    }

    .greyBtn {
        background: #ccc;
    }

    .redBtn {
        background: linear-gradient(135deg, #F0544F 0%, #EE1E1E 100%);
    }

    .blueBtn {
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
    }
</style>