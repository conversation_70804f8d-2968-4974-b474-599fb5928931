<template>
	<!-- 蓝牙权限 -->
	<view class="container">
		<u-navbar bgColor="transparent" placeholder :autoBack="true" />
		<scroll-view class="scroll-view" :scroll-y="true">
			<view class="scroll-container">
				<view class="title">蓝牙权限</view>
				<view class="sub-title">查看管理您的蓝牙权限，了解招聘呗如何收集、使用您的相机信息，以及如何保证您的相机信息安全</view>

				<view class="card-container">
					<view class="bluetooth-box">
						<view class="bluetooth-start">
							<view class="title">管理您的蓝牙权限设置</view>
							<text class="sub-title">去设置</text>
							<image class="image" src="/static/new/右箭头@2x1.png" alt="" />
						</view>
						<view class="bluetooth-end">开启蓝牙权限后，您会在招聘呗产品和服务中体验到诸多好处，包括使用二维码扫码登录、拍摄等</view>
					</view>
				</view>

				<view class="card-container">
					<view class="title">如何使用您的蓝牙功能</view>
					<view class="use-box">
						<view class="item">
							<image
								class="image"
								src="https://api-test.zhaopinbei.com/storage/uploads/images/N5ZHbTaNssyzmLhKvt9c9ApqSNglYj6ZyptaQYE7.png"></image>
							<text class="text">用于连接蓝牙设备进行音视频通话</text>
						</view>
					</view>
				</view>

				<view class="card-container">
					<view class="title">如何保护您的信息安全</view>
					<view class="security-box">
						<view class="item">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/xbc9Gxz1gYl7c2vjKzr5HbK3T7VUYSJg0ceCZwwT.png"></image>
							<view class="text-box">
								<text class="text-start">技术措施</text>
								<text class="text-end">我们已采取安全防护措施保护您的信息</text>
							</view>
						</view>
						<view class="item">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/JhDblqCoZk3HIKh6JaRzn0k9kV2A8WjZqoKfHn7I.png"></image>
							<view class="text-box">
								<text class="text-start">组织管理</text>
								<text class="text-end">我们从组织、制度、人员等多方面提升系统安全性</text>
							</view>
						</view>
						<view class="item">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/8SkSwWAgihxMJCLM4zaKo9g6eoVkqHW1kMO0gG19.png"></image>
							<view class="text-box">
								<text class="text-start">安全事件响应</text>
								<text class="text-end">我们已制定应急预案，会及时处理安全风险</text>
							</view>
						</view>
					</view>
				</view>

				<view class="bottom-text">
					<text>个人信息保护政策</text>
					<image class="image" src="/static/new/右箭头@2x2.png" alt="" />
				</view>
				<view class="bottom-text">
					<text>招聘呗蓝牙权限信息使用规则</text>
					<image class="image" src="/static/new/右箭头@2x2.png" alt="" />
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	},
};
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f7;
	background-image: url(https://api-test.zhaopinbei.com/storage/uploads/images/yj7QXQdszUerlJzQDa70978E2SzeGlRhfxoPvNIF.png);
	background-size: contain;
	background-repeat: no-repeat;
	display: flex;
	flex-direction: column;

	.bottom-text {
		color: #333333;
		font-size: 24rpx;
		display: flex;
		align-items: center;

		.image {
			width: 32rpx;
			height: 32rpx;
		}
	}

	.scroll-view {
		flex: 1;
		overflow-y: auto;

		.scroll-container {
			padding: 32rpx;
			display: flex;
			flex-direction: column;
			gap: 32rpx;
			padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

			.title {
				color: #333333;
				font-size: 32rpx;
			}

			.sub-title {
				color: #777777;
				font-size: 24rpx;
			}

			.card-container {
				padding: 32rpx;
				border-radius: 24rpx;
				background-color: #ffffff;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.bluetooth-box {
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.bluetooth-start {
						display: flex;
						align-items: center;

						.title {
							color: #333333;
							font-size: 28rpx;
							margin-inline-end: auto;
						}

						.sub-title {
							color: #999999;
							font-size: 24rpx;
						}

						.image {
							width: 32rpx;
							height: 32rpx;
						}
					}

					.bluetooth-end {
						color: #777777;
						font-size: 24rpx;
					}
				}

				.security-box {
					display: flex;
					flex-direction: column;
					gap: 30rpx;

					.item {
						display: flex;
						align-items: center;
						gap: 24rpx;

						.image {
							width: 104rpx;
							height: 104rpx;
						}

						.text-box {
							display: flex;
							flex-direction: column;
							gap: 20rpx;

							.text-start {
								color: #333333;
								font-size: 28rpx;
							}

							.text-end {
								color: #777777;
								font-size: 24rpx;
							}
						}
					}
				}

				.title {
					color: #333333;
					font-size: 28rpx;
				}

				.use-box {
					display: grid;
					grid-template-columns: repeat(1, 1fr);
					gap: 32rpx;

					.item {
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.text {
							color: #666666;
							font-size: 24rpx;
						}

						.image {
							width: 100%;
							height: 190rpx;
							border-radius: 24rpx;
						}
					}
				}
			}
		}
	}
}
</style>
