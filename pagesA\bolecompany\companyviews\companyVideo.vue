<template>
	<view class="container">
		<view style="width:686rpx;margin:0 auto;">
			<view class="info-card">
				<p class="font-bold">
					上传公司视频有什么用?
				</p>
				<p class="font-blod-subtitle">
					上传公司宣传视频，突出品牌优势，更吸引千里马。最多可上传5个视频
				</p>
			</view>
		</view>
		<!-- 添加媒体区域 -->
		<view class="media-upload-box">
			<view class="add-media" @click="openImagePicker">
				<view class="icon-plus"></view>
				<text class="add-media-text">添加视频</text>
			</view>
		</view>
		<!-- 注意事项区域 -->
		<view class="note" @click="showCareful">
			<text class="foottext">上传注意事项</text>
			<image :src="help" class="note-icon"></image>
		</view>
		<Careful v-if="carefulhide"></Careful>
	</view>
</template>

<script>
	/*import help from '../../static/images/companyDetail/help-fill.png'*/
	import Careful from '../companyNotice/carefulNotice.vue'

	export default {
		components: {
			Careful
		},

		data() {
			return {
				question: '上传公司宣传视频，突出品牌优势，更吸引千里马。最多可上传5个视频',
				hint: '',
				// 用于存储选择的图片等相关数据
				selectedImages: [],
				help:'https://api-test.zhaopinbei.com/storage/uploads/images/rYxkkwuyCNnqb4YUwWfJXzktOukmQ2dF2OuFvchb.png',
				carefulhide: false
			};
		},
		methods: {
			openImagePicker() {
				// 这里调用uniapp的图片选择器api，如uni.chooseImage
				uni.chooseImage({
					count: 5, // 假设最多选5张
					success: (res) => {
						this.selectedImages = res.tempFilePaths;
					}
				});
			},
			showCareful() {
				this.carefulhide = true
			}

		}
	};
</script>

<style scoped>
	.container {
		padding: 30rpx;
	}

	.info-card {
		width: 686rpx;
		display: flex;
		height: 166rpx;
		flex-direction: column;
		justify-content: center;
		margin: 40rpx auto;
		position: relative;
		background-image: linear-gradient(to bottom, rgba(242, 248, 255, 1), rgba(255, 255, 255, 1));
		overflow: hidden;
		border-radius: 32rpx;
	}

	.info-card::before {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 2rpx;
		left: 2rpx;
		content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 40rpx;
		z-index: 3;
	}

	.info-card::after {
		position: absolute;
		width: 100%;
		height: 100%;
		bottom: 4rpx;
		right: 2rpx;
		content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 40rpx;
		z-index: 3;
	}

	.font-bold {
		padding: 10rpx 0;
		font-size: 24rpx;
		line-height: 28.12rpx;
		margin-left: 40rpx;
		margin-left: 40rpx;
	}

	.font-blod-subtitle {
		color: rgba(141, 154, 170, 1);
		padding: 10rpx 0;
		font-size: 20rpx;
		line-height: 23.44rpx;
		width: 606rpx;
		margin-left: 40rpx;
	}

	.question-box {
		background-color: #f0f5ff;
		padding: 20rpx;
		border-radius: 16rpx;
		margin-bottom: 30rpx;
	}

	textarea {
		width: 100%;
		border: none;
		resize: none;
	}

	.hint-text {
		font-size: 24rpx;
		color: #999;
	}

	.media-upload-box {
		background-color: #f5f5f5;
		padding: 100rpx 0;
		text-align: center;
		margin-bottom: 30rpx;
		border-radius: 16rpx;
		width: 686rpx;
		height: 334rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.icon-plus {
		width: 32rpx;
		height: 32rpx;
		/* border: 2rpx dashed #ccc; */
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.icon-plus::after {
		content: "";
		width: 32rpx;
		height: 4rpx;
		background-color: #ccc;
		position: absolute;
	}

	.icon-plus::before {
		content: "";
		width: 4rpx;
		height: 32rpx;
		background-color: #ccc;
		position: absolute;
	}

	.add-media {
		cursor: pointer;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	.add-media-text {
		margin-left: 10rpx;
		font-size: 28rpx;
		color: #666;
	}

	.note {
		display: flex;
		align-items: center;
		font-size: 56rpx;
		color: #999;
		position: absolute;
		margin-top: 24rpx;
		/* margin-left: 32rpx; */
	}

	.note-icon {
		width: 28rpx;
		height: 28rpx;
		margin-left: 20rpx;
	}

	.foottext {
		color: rgba(153, 153, 153, 1);
		font-size: 24rpx;
		line-height: 28.12rpx;
	}
</style>
