<template>
    <view class="home-index">
        <view class="task-info">
            <view class="task">
                <view class="name">
                    {{detailList.task.title}}
                </view>
                <view class="per">
                    {{detailList.task.unit_total}}/每位
                </view>
            </view>
            <view class="desc">
                <view class="time">
                    {{detailList.task.start_at}}-{{detailList.task.end_at}}
                </view>
                <view
                    :class="['type',detailList.task.type=='job'?'blue':detailList.task.type=='custom'?'yellow':detailList.task.type=='job_active'?'green':'']">
                    {{detailList.task.type_name}}
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="info">
                <view class="username">
                    <image :src="detailList.member_info.image.thumbnail_path_url" mode="" class="userhead"></image>
                    <view class="name">
                        {{detailList.member_certification.name}}
                    </view>
                </view>
                <view class="phone">
                    {{detailList.member_certification.mobile_no}}
                </view>
            </view>
        </view>

        <view class="item-wrap">
            <view class="item" v-if="taskJobList.title" @click="goDetails">
                <view class="name gl">
                    关联职位
                </view>

                <view class="number ms">
                    {{taskJobList.title}}
                </view>
            </view>
            <u-line color="#F5F5F7" length="100%"></u-line>
            <view class="item">
                <view class="name">
                    任务所需人数
                </view>

                <view class="number">
                    {{detailList.task.count}}
                </view>
            </view>
        </view>


        <view class="wrap">
            <view class="title">
                任务简介
            </view>
            <view class="desc">
                {{detailList.task.intro}}
            </view>
        </view>

        <view class="footer">
            <!-- <view :class="['favor',details.isFavor==1?'ysc':'']">
				<u-icon name="star-fill" :color="details.isFavor==1?'#F9AD14':'#999999'"></u-icon>
				收藏
			</view> -->
            <view class="btn zx" @click="communicate('headhunters',detailList.company.id)">
                任务咨询
            </view>
            <!-- v-if="detailList.but.detailList==1" -->
            <view class="btn agree" @click="getTask(detailList.task.id)" v-if="detailList.but.report_status == 1">
                领取任务
            </view>
            <view class="btn greyIcon" v-else>
                暂时无法领取
            </view>
        </view>
    </view>
</template>

<script>
    import {
        taskSquareDetail,
        getTaskJobs
    } from '../../config/common_api.js'
    import {
        getTask
    } from '../../config/member_api.js'
    import {
        communicate,
        isAuth
    } from "../../common/common.js";
    export default {
        data() {
            return {
                detailList: {},
                taskJobList: ''
            }
        },
        onLoad(option) {
            this.taskSquareDetail(option.id)
            this.getTaskJobs(option.id)
        },
        methods: {
            // 分享功能
            onShareAppMessage(res) {
                const pages = getCurrentPages(); //获取当前页面的参数
                // pages[0].$page.fullPath//当前页面路径及页面参数
                if (res.from === 'button') { // 来自页面内分享按钮
                    console.log(res.target)
                }
                return {
                    title: this.detailList.task.title,
                    path: pages[pages.length - 1].$page.fullPath,
                }
            },
            communicate,
            async getTaskJobs(id) {
                let params = {
                    task_id: id
                }
                const res = await getTaskJobs(params)
                this.taskJobList = res.data[0]
                console.log("关联职位", res)
            },
            async getTask(id) {
                if (!isAuth(['info', 'auth'])) return
                let params = {
                    task_id: id
                }
                const res = await getTask(params)
                if (res.status_code == 200) {
                    uni.showToast({
                        title: '领取成功',
                        icon: 'none'
                    })
                } else {
                    uni.showToast({
                        title: res.message,
                        icon: 'none'
                    })
                }
                console.log('领取任务', res)
            },
            async taskSquareDetail(id) {
                let params = {
                    task_id: id * 1
                }
                const res = await taskSquareDetail(params)
                this.detailList = res.data
                console.log('res', res)
            },
            goDetails() {
                const pages = getCurrentPages(); //获取当前页面的参数
                const model_user_id = uni.getStorageSync('userInfo').login_user.id;
                // if (uni.getStorageSync('roleType') != 'member') return
                uni.navigateTo({
                    url: `/pagesA/details/memberJobDetails?id=${this.taskJobList.id}&model_user_id=${model_user_id}&model_id=${this.detailList.task.id}&model_type=task`
                })
            }
        },
    }
</script>
<style>
    page {
        background-color: #f5f5f7;
    }
</style>
<style lang="less" scoped>
    .home-index {
        padding: 32rpx 32rpx 170rpx 32rpx;
    }

    .task-info {
        display: flex;
        flex-direction: column;
        background-color: #FFFFFF;
        padding: 32rpx;
        border-radius: 24rpx;

        .task {
            display: flex;
            justify-content: space-between;

            .name {
                display: flex;
                flex: 1;
                font-weight: 500;
                font-size: 32rpx;
                color: #333333;
            }

            .per {
                font-weight: 600;
                font-size: 32rpx;
                color: #F98A14;
                margin-left: 16rpx
            }
        }

        .desc {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 16rpx 0;

            .time {
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
            }

            .type {
                font-weight: 600;
                font-size: 24rpx;
            }

            .yellow {
                color: #F9AD14;
            }

            .blue {
                color: #1690FF;
            }

            .green {
                color: #57D51C;
            }
        }

        .info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 32rpx;

            .username {
                display: flex;
                align-items: center;
                font-weight: 500;
                font-size: 28rpx;
                color: #333333;

                image {
                    width: 48rpx;
                    height: 48rpx;
                    margin-right: 32rpx;
                    border-radius: 50%;
                }
            }

            .phone {
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
            }
        }
    }


    .item-wrap {
        display: flex;
        flex-direction: column;
        margin: 32rpx 0;
        border-radius: 24rpx;
        background-color: #FFFFFF;

        .item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 104rpx;
            padding: 0 32rpx;



            .name {
                font-weight: 400;
                font-size: 28rpx;
                color: #333333;
            }

            .number {
                font-weight: 600;
                font-size: 28rpx;
                color: #4F8CF0;
            }

            .gl {
                font-weight: 500;
                font-size: 28rpx;
                color: #999999;
            }

            .ms {
                font-weight: 500;
                font-size: 28rpx;
                color: #333333;
            }
        }


    }



    .wrap {
        display: flex;
        flex-direction: column;
        background-color: #FFFFFF;
        padding: 32rpx;
        border-radius: 24rpx;

        .title {
            font-weight: 600;
            font-size: 28rpx;
            color: #333333;
            padding-bottom: 32rpx;
        }

        .desc {
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
        }
    }

    .footer {
        position: fixed;
        display: flex;
        justify-content: space-between;
        // align-items: center;
        height: 196rpx;
        width: 100%;
        left: 0;
        bottom: 0;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 28rpx;
        padding: 28rpx 42rpx;
        box-sizing: border-box;
        border-radius: 24rpx 24rpx 0 0;

        .favor {
            display: flex;
            align-items: center;
            color: #999999;
            margin-left: 32rpx;
        }

        .ysc {
            color: #F9AD14;
        }

        .btn {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 72rpx;
            width: 330rpx;
            height: 100rpx;
            // background:#F5F5F7;
            color: #333333;
            border-radius: 16rpx;

            // &:first-child {
            // 	margin-right: 20rpx;
            // }

        }

        .zx {
            border: 2rpx solid #4F8CF0;
            color: #4F8CF0;
            background: #FFFFFF;
            margin-right: 26rpx;
        }

        .agree {
            background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
            color: #FFFFFF;
        }

        .greyIcon {
            background: grey;
            color: #FFFFFF;
        }
    }
</style>