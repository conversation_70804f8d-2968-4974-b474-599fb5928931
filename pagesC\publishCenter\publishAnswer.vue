<template>
	<view class="warp">
		<view class="tit">
			简历怎么写才能让你hr眼前一亮呢？
		</view>
		<u--textarea v-model="value2" height="160" placeholder="填写回答，可帮助到1234人哦~" maxlength="2000" border="bottom"
			count></u--textarea>
		<view class="line">
			<view class="">
				<img src="/static/images/publishCenter/img-icon.png" alt="" />
				<text>图片</text>
			</view>
			<view class="">
				<img src="/static/images/publishCenter/font-icon.png" alt="" />
				<text>格式</text>
			</view>
		</view>
		<view class="tips">
			*发布即代表您已阅读并同意 <text class="inn">《招聘呗创作者平台用户协议》</text>
		</view>
		<view class="btn" @click="submit">
			保存
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value: '',
				value2: '',
				fileList1: [],
			}
		},
		methods: {
			submit() {
				uni.navigateTo({
					url: './publishSuccess'
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 32rpx;

		.tit {
			font-size: 32rpx;
			color: #333333;
			margin-bottom: 34rpx;
		}

		.line {
			display: flex;
			align-items: center;

			&>view {
				margin-right: 50rpx;
				display: flex;
				align-items: center;
			}

			img {
				width: 48rpx;
				height: 48rpx;
				margin-right: 12rpx;
			}
		}

		.upload-box {
			margin-top: 24rpx;
		}

		.huati {
			margin-top: 24rpx;
			background: #f0f0f0;
			width: 160rpx;
			height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 20rpx;
			font-size: 24rpx;

			img {
				width: 28rpx;
				height: 28rpx;
				margin-right: 6rpx;
			}

		}

		.tips {
			margin-top: 24rpx;
			font-size: 20rpx;
			color: #999;

			.inn {
				color: #4F8CF0;
			}
		}

		.btn {
			width: 686rpx;
			height: 80rpx;
			background: #4F8CF0;
			font-size: 28rpx;
			color: #FFFFFF;
			text-align: center;
			line-height: 80rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			position: fixed;
			bottom: 100rpx;
		}
	}
</style>