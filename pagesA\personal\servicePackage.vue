<template>
	<view class="home-index">
		<u-sticky>
			<view class="tabs">
				<view :class="['tab pg_1',tabIndex==0?'active':'']" @click="changeTab(0)">
					合同包
				</view>
				<view :class="['tab pg_2',tabIndex==1?'active':'']" @click="changeTab(1)">
					职位包
				</view>
			</view>
		</u-sticky>

		<view class="list">
			<view :class="['item',currentIndex==index?'active':'']" v-for="(item,index) in list" :key="index"
				@click="changePackage(index)">
				<view class="name">
					{{item.name}}
				</view>
				<view class="desc">
					{{item.price*item.count}}积分/{{item.count}}份
				</view>
				<view class="per">
					{{item.price}}积分/份
				</view>
			</view>
		</view>

		<view class="btn" @click="recharge">
			确认购买
		</view>
	</view>
</template>

<script>
	import {
		getServicePackageList,
		recharge
	} from "../../config/api.js"
	export default {
		data() {
			return {
				page: 1,
				limit: 20,
				tabIndex: 0,
				currentIndex: 0,
				list: []
			}
		},
		onLoad() {
			this.getServicePackageList()
		},
		methods: {
			changeTab(index) {
				this.tabIndex = index
				this.page = 1
				this.getServicePackageList()
			},

			changePackage(index) {
				this.currentIndex = index
			},

			//服务包购买
			async recharge() {
				let params = {
					id: this.list[this.currentIndex]['id'],
					code: '123456'
				}
				const {
					status_code,
					data,
					message
				} = await recharge(params)
				if (status_code == 200) {
					this.getServicePackageList()
					return uni.$u.toast('购买成功')
				}
			},

			async getServicePackageList() {
				let params = {
					page: this.page,
					limit: this.limit,
					type: this.tabIndex == 0 ? 'contract' : this.tabIndex == 1 ? 'job' :
						'' //contract-合同套餐，point-积分套餐，job-职位套餐，默认合同套餐
				}
				const {
					status_code,
					data,
					message
				} = await getServicePackageList(params)
				if (status_code == 200) {
					this.list = data
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding-bottom: 172rpx;
	}

	.tabs {
		display: flex;
		justify-content: space-between;
		padding: 32rpx;

		.tab {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			display: flex;
			flex: 1;
			background: #FFFFFF;
			font-weight: 400;
			font-size: 28rpx;
			color: #999999;

			&:first-child {
				border-radius: 44rpx 0 0 44rpx;
			}

			&:last-child {
				margin-left: -40rpx;
				border-radius: 0 44rpx 44rpx 0;
			}
		}

		.pg_1 {
			clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
		}

		.pg_2 {
			clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
		}

		.active {
			color: #FFFFFF;
			font-weight: 600;
			background-color: #4F8CF0;
		}
	}

	.list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0 32rpx;

		.item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			width: calc(48% - 48rpx);
			height: 212rpx;
			background-color: #FFFFFF;
			margin-bottom: 24rpx;
			padding: 0 24rpx;
			border-radius: 24rpx;

			.name {
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;
			}

			.desc {
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
				margin: 16rpx 0;
			}

			.per {
				font-weight: 600;
				font-size: 28rpx;
				color: #4F8CF0;
			}
		}

		.active {
			border: 1px solid #4F8CF0;
		}
	}

	.btn {
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 90%;
		height: 100rpx;
		bottom: 32rpx;
		left: 5%;
		background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
		border-radius: 16rpx;
		font-weight: 600;
		font-size: 34rpx;
		color: #FFFFFF;
	}
</style>