<template>
	<view class="container">
		<u-sticky>
			<view class="tabs-container">
				<u-tabs :list="tabsList" :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
						:inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }"></u-tabs>
			</view>
		</u-sticky>
		<view class="content">
			<view class="item" v-for="v in jobList" :key="v.job.job_id" @click="onDetail(v)">
				<view class="item-start">
					<view class="title-box">
						<view class="title-inner">
							<image class="avater" :src="v.company.company_logo.thumb_url"></image>
							<view class="">
								<view class="tit-inn">
									<view class="title_1">{{v.job.job_info_name}}</view>
									<view class="">
										ui设计师
									</view>
									<view class="title_2">
										{{v.salary.salary_min}}
									</view>
								</view>
								<view class="tit-inn">
									<view class="age">
										5年｜23岁 | 本科
									</view>
									<view class="title_3">
										实习/兼职
									</view>
								</view>
							</view>
						</view>
					</view>

					<view class="time">{{v.job.job_introduction}}</view>
					<u-line></u-line>
					<view class="item-end">
						<text class="name">求职状态: {{v.company.company_name}}</text>
						<view class="address">
							<view class="radi">
							</view>
							<view class="">
								在线
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getJobPublishList
	} from '@/config';

	export default {
		data() {
			return {
				tabsList: [{
					key: '1',
					name: '综合',
				},
					{
						key: '2',
						name: '职场人',
					},
					{
						key: '3',
						name: '实习岗',
					}, {
						key: '4',
						name: '应届生',
					},
				],
				jobList: [],
				params: {
					limit: 20,
					page: 1,
					work_type_id: '2',
					job_type: '2'
				},
				isLoading: false,
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type]?.id || 61,
				};
			}
		},
		created(){

		},
		onLoad(){

		},
		onShow(){

		},
		watch: {
			params: {
				handler(value) {
					this.onGetJobPublishList();
					this.$forceUpdate();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetJobPublishList();
			this.$forceUpdate();
		},
		methods: {
			onDetail(v) {
				/*uni.$u.route({
					url: `/pagesA/details/memberJobDetails`,
					params: {
						id: v.job.job_id,
					}
				})*/
			},
			onScrollGetList() {
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetJobPublishList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getJobPublishList(params);
				if (res.status_code !== '200') return;
				this.jobList = [...this.jobList, ...res.data.jobs_list?.data];
				this.more = res.data.more;
				this.isLoading = false;
				this.$forceUpdate();
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

	.tabs-container {
		padding-inline: 16rpx;
		background-color: #f5f5f7;
	}

	.content {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
	// padding-inline: 32rpx;

	.item {
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 32rpx;
		display: flex;
		justify-content: space-between;
	// flex-direction: column;
		gap: 24rpx;


	.avater {
		width: 104rpx;
		height: 104rpx;
		border-radius: 50%;
	}

	.item-end {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 28rpx;

	.name {
		color: #666;
		font-size: 24rpx;
	}



	.address {
		margin-inline-start: auto;
		color: #4F8CF0;
		font-size: 24rpx;
		display: flex;
		align-items: center;

	.radi {
		width: 8rpx;
		height: 8rpx;
		border-radius: 4rpx;
		background: #4F8CF0;
		margin-right: 12rpx;
	}
	}
	}

	.item-start {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 24rpx;

	.time {
		color: #666;
		font-size: 24rpx;
		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
	}

	.type-text {
		font-size: 28rpx;
		color: #041024;
	}

	.title-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 12rpx;
		font-size: 32rpx;
		color: #333333;

	.title-inner {
		display: flex;
		align-items: center;
		gap: 12rpx;

	.tit-inn {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 12rpx;

	.age {
		font-size: 24rpx;
		color: #666666;
	}
	}
	}

	.title_1 {
	// flex: 1;
		width: 160rpx;

		overflow: hidden;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
	}

	.title_2 {
		color: #f98a14;
		white-space: nowrap;
		margin-left: 100rpx;
	}

	.title_3 {
		color: #4F8CF0;
		white-space: nowrap;
		margin-left: 100rpx;
	}
	}
	}
	}
	}
	}
</style>
