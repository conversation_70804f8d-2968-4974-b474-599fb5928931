<template>
    <view>
        <u-popup :show="show" :round="10" bgColor="#F5F5F5" mode="bottom" closeOnClickOverlay @close="handleClose"
            @open="handleOpen">
            <view class="credit">
                <view class="title comp">请选择发送简历</view>
                <view class="content">
                    <view class="btns">
                        <view :class="['btn', resumeIndex === index ? 'active' : '']"
                            v-for="(item, index) in resumeList" :key="index" @click="changeResume(index)">
                            <text>{{ item.member_resume.resume_name }}</text>
                        </view>
                    </view>
                </view>
                <view class="agree" @click="confirmSelection">确定</view>
            </view>
        </u-popup>

    </view>
</template>

<script>
    export default {
        name:'resumePop',
        props: {
            show: {
                type: Boolean,
            },
            resumeList: {
                type: Array,
            }
        },
        data() {
            return {
                resumeIndex: null // 记录当前选中的简历索引
            };
        },
        methods: {
            handleClose() {
                this.$emit('close');
            },
            handleOpen() {
                // 重置选中的简历索引
                this.resumeIndex = null;
            },
            changeResume(index) {
                this.resumeIndex = index; // 更新选中的简历索引
            },
            confirmSelection() {
                if (this.resumeIndex !== null) {
                    // 传递选中的简历给父组件
                    this.$emit('confirm', this.resumeList[this.resumeIndex]);
                }
                this.handleClose();
            }
        }
    }
</script>


<style scoped lang="less">
    .credit {
        display: flex;
        flex-direction: column;
        padding: 40rpx 48rpx;

        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .logo-content {
                display: flex;

                image {
                    width: 48rpx;
                    height: 48rpx;
                    margin-right: 16rpx;
                }

                text {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 500;
                    font-size: 15px;
                    color: rgba(0, 0, 0, 0.9);
                }
            }

            .tip {
                image {
                    width: 48rpx;
                    height: 48rpx;
                }
            }
        }

        .comp {
            justify-content: center;
        }

        .content {
            display: flex;
            flex-direction: column;
            margin-top: 40rpx;

            .sub-title {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                color: #000000;
            }

            .desc {
                display: flex;
                font-weight: 400;
                margin-top: 16rpx;
                font-size: 24rpx;
                color: #999999;
            }

            .btns {
                display: flex;
                flex-direction: column;

                .btn {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    background: #FFFFFF;
                    height: 140rpx;
                    border-radius: 16rpx;
                    margin-top: 16rpx;

                    text {
                        font-weight: 400;
                        font-size: 32rpx;
                        color: rgba(0, 0, 0, 0.9);
                    }

                    .bind {
                        font-size: 24rpx;
                        color: #07C160;
                    }
                }

                .active {
                    color: #4F8CF0;
                    border: 1rpx solid #4F8CF0;
                }
            }
        }

        .other {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 60rpx 0;
            color: #576B95;
            font-weight: 500;
            font-size: 24rpx;
        }

        .agree {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 44rpx;
            height: 88rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #FFFFFF;
            background: #4F8CF0;
            margin-top: 32rpx;
        }
    }
</style>