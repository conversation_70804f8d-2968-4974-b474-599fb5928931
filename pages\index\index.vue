<template>
	<view class="container">
		<template v-if="roleType != 'headhunters'">
			<u-navbar placeholder bgColor="#BAD4FF">
				<template #left>
					<view class="nav-container">
						<!-- 根据千里马身份的不同，对地址进行切换 -->
						<text class="title" @click="isSchoolPicker = false">
							<text @click.stop="goAddress" v-if="switchValue">
								金水区
							</text>
							<text v-else>
								{{schoolText}}
							</text>
						</text>
					</view>
				</template>
			</u-navbar>
			<u-sticky>
				<view class="sticky-container">
					<view class="switch-container" v-if="roleType=='member'">
						<u-switch v-model="switchValue" @change="onSwitchChange"></u-switch>
						<text>{{switchValue ? '社会版' : '学生版'}}</text>
					</view>
					<view class="top-box">
						<template v-if="univeList.length">
							<view class="top-start">
								<view class="title">我的关注:</view>
								<view class="city-box">
									<view class="city-item" v-for="v in univeList" :key="v.id">
										<text>{{ v.college_name }}</text>
									</view>
								</view>
							</view>
						</template>
						<u-search placeholder="请输入市、县、乡 如：白马镇" bgColor="#FFFFFF" actionText="添加"></u-search>
						<view class="top-end">
							<view class="logo-box">
								<image class="image"
									src="https://api-test.zhaopinbei.com/storage/uploads/images/eGIVbjEBi1jEe2iEc6DjebYtw8SDLLTXB4Avd5xf.png">
								</image>
							</view>
							<template v-if="roleType === 'company'">
								<view class="btn" @click="onRecruit">免费发布招聘</view>
							</template>
						</view>
					</view>
				</view>
			</u-sticky>
			<scroll-view :scroll-y="true" class="scroll-y-view" @scrolltolower="onScrolltolower">
				<view class="scroll-y-container">
					<u-swiper class="swiper" :list="swiperList" height="132" indicator circular></u-swiper>
					<template v-if="roleType">
						<!-- 下拉框展示无显示 -->
						<view class="desired-box">
							<image class="avater"
								src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png">
							</image>
							<text>求职期望：郑州 | 设计 | 5-7K</text>
							<image class="image" src="/static/home/<USER>"></image>
						</view>
					</template>
					<view class="tips">今日动态</view>
					<scroll-view class="scroll-x-view" :scroll-x="true">
						<view class="scroll-x-container">
							<view class="item"
								v-for="v in typeList.filter(el => el.type.includes(roleType === 'company' && switchValue?'3': roleType === 'member' && switchValue? '2' : '1'))"
								:key="v.key" @click="onTypeItemClick(v)">
								<image class="image" :src="v.image"></image>
								<view>{{v.name}}</view>
							</view>
						</view>
					</scroll-view>
					<template v-if="typeKey === '1'">
						<Casual ref="casualRef" />
					</template>
					<template v-if="typeKey === '2'">
						<School ref="schoolRef" />
					</template>
					<template v-if="typeKey === '3'">
						<Enterprises ref="enterprisesRef" />
					</template>
					<template v-if="typeKey === '4'">
						<Butler ref="butlerRef" />
					</template>
					<template v-if="typeKey === '5'">
						<JobFair ref="jobFairRef" />
					</template>
					<template v-if="typeKey === '6'">
						<Job ref="jobRef" />
					</template>
					<template v-if="typeKey === '7'">
						<EnterprisesSocial ref="enterprisesSocialRef" />
					</template>
					<template v-if="typeKey === '8'">
						<ButlerSocial ref="butlerSocialRef" />
					</template>
					<template v-if="typeKey === '9'">
						<JobFairSocial ref="jobFairSocialRef" />
					</template>
					<template v-if="typeKey === '91'">
						<TalentList ref="talentRef" />
					</template>
					<template v-if="typeKey === '92'">
						<ButlerSocials ref="butlerSocialRefs" />
					</template>
					<template v-if="typeKey === '93'">
						<JobFairs ref="jobFairRefs" />
					</template>
					<template v-if="typeKey === '94'">
						<StudentList ref="studentRefs" />
					</template>
				</view>


				<u-picker :show="isSchoolPicker" closeOnClickOverlay :columns="[schoolList]"
					@confirm="onSchoolPickerConfirm" @cancel="onSchoolPickerClose"
					@close="onSchoolPickerClose"></u-picker>
			</scroll-view>

		</template>
		<template v-else>
			<u-navbar placeholder bgColor="#BAD4FF">
				<template #center>
					<view class="nav-default">逛一逛</view>
				</template>
				<template #left>
					<img class="nav-logo"
						src="https://api-test.zhaopinbei.com/storage/uploads/images/eGIVbjEBi1jEe2iEc6DjebYtw8SDLLTXB4Avd5xf.png"
						alt="" />
				</template>
			</u-navbar>
			<u-sticky>
				<view class="search">
					<view class="sear-box">
						<u-search bgColor="#FFFFFF" :showAction="false"></u-search>
					</view>
				</view>
				<view class="top-btm">
					<scroll-view :scroll-x="true" style="margin-top: 24rpx;">
						<view class="tabs">
							<view class="tabs-item" @click="changeTab(item)" :class="selTab === item.id?'sel-tab':''"
								v-for="(item,index) in tab" :key="item.id">
								<img :src="selTab === item.id?item.selectedIconPath:item.iconPath" alt="" />
								<view class="">
									{{item.title}}
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</u-sticky>
			<scroll-view :scroll-y="true" class="scroll-y-hea" @scrolltolower="onScrolltolowerHea">
				<view class="hea-inner">
					<TradingHall ref="hallRef" v-if="selTab === 1" />
					<Tlent ref="tlentRef" v-if="selTab === 2" />
					<EnterprisesHea ref="enterprisesRef" v-if="selTab === 3" />
					<JobFairHea ref="jobFairRef" v-if="selTab === 4" />
					<Position ref="positionRef" v-if="selTab === 5" />
					<StudentZone ref="studentZoneRef" v-if="selTab === 6" />
				</view>
			</scroll-view>
			<!-- <JyIndex></JyIndex> -->
		</template>
		<Tabbar />
	</view>
</template>

<script>
	import Tabbar from '@/components/tabbar.vue';
	import Casual from '@/components/indexComponents/casual.vue';
	import School from '@/components/indexComponents/school.vue';
	import Enterprises from '@/components/indexComponents/enterprises.vue';
	import Butler from '@/components/indexComponents/butler.vue';
	import JobFair from '@/components/indexComponents/jobFair.vue';
	import Job from '@/components/indexComponents/job.vue';
	import EnterprisesSocial from '@/components/indexComponents/enterprisesSocial.vue';
	import ButlerSocial from '@/components/indexComponents/butlerSocial.vue';
	import JobFairSocial from '@/components/indexComponents/jobFairSocial.vue';
	import TalentList from '@/components/indexComponents/talentList.vue'; //
	import ButlerSocials from '@/components/indexComponents/butlerSocials.vue'; //
	import JobFairs from '@/components/indexComponents/jobFairs.vue'; //
	import StudentList from '@/components/indexComponents/student.vue'; //
	// import JyIndex from './components/jy_index.vue'
	import TradingHall from "./components/tradingHall.vue"
	import Tlent from "./components/tlent.vue"
	import EnterprisesHea from './components/enterprises.vue';
	import JobFairHea from './components/jobFair.vue';
	import Position from './components/position.vue';
	import StudentZone from './components/studentZone.vue';
	import {
		getSchoolFocusList,
		getCarouselList,
		getCarouselList1,
	} from '@/config';
	import {
		isAuth,
		isLogin
	} from '@/common/common.js'
	import {
		getSysList,
		getCityList,
	} from "../../config/api.js"
	export default {
		components: {
			Tabbar,
			Casual,
			School,
			Enterprises,
			Butler,
			JobFair,
			Job,
			EnterprisesSocial,
			ButlerSocial,
			JobFairSocial,
			// JyIndex,
			TalentList,
			ButlerSocials,
			JobFairs,
			StudentList,
			TradingHall,
			Tlent,
			EnterprisesHea,
			JobFairHea,
			Position,
			StudentZone
		},
		data() {
			return {
				swiperList: [],
				typeKey: '',
				typeList: [{
						key: '1',
						type: ['1'],
						ref: 'casualRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/mAo34FV4FK1oFr9HKeaxjBsBvGTaDTzycJxHa1k5.png',
						name: '零工市场'
					},
					{
						key: '2',
						type: ['1'],
						ref: 'schoolRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/3oLyb7tCS1ExT8ltG8BaGYZnSWA4t3nkYVwq6yYO.png',
						name: '校招实习'
					},
					{
						key: '3',
						type: ['1'],
						ref: 'enterprisesRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/fap2fzpCHUCjYgvfh7CrCAdpETYpqCypEjru8nge.png',
						name: '看·企业'
					},
					{
						key: '4',
						type: ['1'],
						ref: 'butlerRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/d4AYRZ7wrYhAtyLnjOg0A1ZvUTdYh5itJ1vRDeQ0.png',
						name: '找·管家'
					},
					{
						key: '5',
						type: ['1'],
						ref: 'jobFairRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/QVA55vyjr4Czl7zuZ1EM2Fv5Pgs791qzBudEElxu.png',
						name: '寻·招聘会'
					},
					{
						key: '6',
						type: ['2'],
						ref: 'jobRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/3oLyb7tCS1ExT8ltG8BaGYZnSWA4t3nkYVwq6yYO.png',
						name: '搜·工作'
					},
					{
						key: '7',
						type: ['2'],
						ref: 'enterprisesSocialRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/fap2fzpCHUCjYgvfh7CrCAdpETYpqCypEjru8nge.png',
						name: '看·企业'
					},
					{
						key: '8',
						type: ['2'],
						ref: 'butlerSocialRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/d4AYRZ7wrYhAtyLnjOg0A1ZvUTdYh5itJ1vRDeQ0.png',
						name: '找·管家'
					},
					{
						key: '9',
						type: ['2'],
						ref: 'jobFairSocialRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/QVA55vyjr4Czl7zuZ1EM2Fv5Pgs791qzBudEElxu.png',
						name: '寻·招聘会'
					},
					{
						key: '91',
						type: ['3'],
						ref: 'talentRef',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/QVA55vyjr4Czl7zuZ1EM2Fv5Pgs791qzBudEElxu.png',
						name: '搜·人才'
					},
					{
						key: '92',
						type: ['3'],
						ref: 'butlerSocialRefs',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/d4AYRZ7wrYhAtyLnjOg0A1ZvUTdYh5itJ1vRDeQ0.png',
						name: '找·管家'
					}, {
						key: '93',
						type: ['3'],
						ref: 'jobFairRefs',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/QVA55vyjr4Czl7zuZ1EM2Fv5Pgs791qzBudEElxu.png',
						name: '寻·招聘会'
					}, {
						key: '94',
						type: ['3'],
						ref: 'studentRefs',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/QVA55vyjr4Czl7zuZ1EM2Fv5Pgs791qzBudEElxu.png',
						name: '大学生专区'
					}, {
						key: '95',
						type: ['1'],
						ref: 'schoolRefs',
						url: '/pagesC/schoolIntro/index',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/SCUztTse917lywAwZFaRbSIaiKkigvJAFHSeD9lI.png',
						name: '本校简介'
					},
					{
						key: '10',
						type: ['1', '2', '3'],
						url: '/pagesC/dynamic/index',
						image: 'https://api-test.zhaopinbei.com/storage/uploads/images/QVA55vyjr4Czl7zuZ1EM2Fv5Pgs791qzBudEElxu.png',
						name: '社区·动态'
					},
				],

				univeList: [],
				univeCurrentId: null,
				univeMoreLeft: 0,
				switchValue: false,

				isSchoolPicker: false,
				schoolText: '郑州大学',
				schoolList: [],
				tab: [{
					title: '交易大厅',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/vvedApgK55AwjJ2t6mENU6SbKFDQqtsK0xn4zOXn.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/QJl8nukldIuMqsJVDm9DlmAhih2CwnyqUNHggum9.png",
					ref: 'hallRef',
					id: 1
				}, {
					title: '搜·人才',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/rwlMzJc8QM31KGlF1KvOeaiRAAU6luG0hY30LlEl.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/rZXtWMP7sQgq5dUexhbsV0ucEIHeCsgTNHqA9gYh.png",
					ref: 'tlentRef',
					id: 2
				}, {
					title: '看·企业',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/RJB4qqMJKq4q5AZfHvHK5Uz1vxT3jAQhXkGzj7Mo.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/M5hWJZclWVysXb7d0rScTuVtQZ8dQWuBEuZIMG9u.png",
					ref: 'enterprisesRef',
					id: 3
				}, {
					title: '寻·招聘会',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/tAKZa6y87H2N2svln54YVIVTWtaWVgWdTlyVV7D3.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/9SxKr5uLbm6lfw7sSItIuogBZehVfnW7h7pCDwEX.png",
					ref: 'jobFairRef',
					id: 4
				}, {
					title: '搜·岗位',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/BgUsfFP5BpH485E2XrqPAi0d3G6Sr5Vf0qhhX4B6.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/KbbPzSpnlHT3YkowTxAYAlZyyZSl7hoL12db7QTR.png",
					ref: 'positionRef',
					id: 5
				}, {
					title: '大学生专区',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/5VXY2crH57v7dxY1qQHmVAOSlaDrjUYytPFeZD00.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/IH1WLbUhoKibyMhachW4pdqE5Cr3oSySgKUS80rI.png",
					ref: 'studentZoneRef',
					id: 6
				}, {
					title: '社区动态',
					iconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/5VXY2crH57v7dxY1qQHmVAOSlaDrjUYytPFeZD00.png",
					selectedIconPath: "https://api-test.zhaopinbei.com/storage/uploads/images/IH1WLbUhoKibyMhachW4pdqE5Cr3oSySgKUS80rI.png",
					ref: 'studentZoneRef',
					id: 7
				}, ],
				selTab: 1
			};
		},
		computed: {
			roleType() {
				return this.$store.state.roleType || uni.getStorageSync('roleType')
			},
		},
		onLoad() {

		},
		onShow() {
			if (this.roleType == 'company') { //这是从伯乐端进来，不展示学生版
				this.switchValue = true;
				this.typeKey = '91'; // 然后再改变回来，触发重新渲染
			} else if (this.roleType == 'member') { //这是千里马端进来，展示学生版
				this.typeKey = '1';
			}
		},
		created() {
			if (this.roleType) this.onGetSchoolFocusList();
			this.onGetCarouselList();
			this.getCityList();
			this.getSysData();
		},
		mounted() {

		},
		watch: {
			/*typeKey(newVal,oldVal){
				let _tis = this;
				_tis.typeKey = newVal;

			}*/
		},
		methods: {
			onSchoolPickerConfirm(event) {
				this.schoolText = event.value[0].text;
				this.onSchoolPickerClose();
			},
			onSchoolPickerClose() {
				this.isSchoolPicker = false;
			},
			onSwitchChange(bol) {
				if (this.roleType == 'company') { //这是从伯乐端进来，不展示学生版
					this.typeKey = '91'; // 然后再改变回来，触发重新渲染
				} else if (this.roleType == 'member') { //这是千里马端进来，展示学生版
					this.typeKey = bol ? '6' : '1';
					this.onGetSchoolFocusList();
				}
			},
			onScrolltolower() {
				const find = this.typeList.find(el => el.key === this.typeKey);
				this.$refs[find.ref].onScrollGetList();
			},
			onScrolltolowerHea() {
				this.$refs[find.ref].onScrollGetList();
			},
			onRecruit() {
				uni.$u.route({
					url: '/pagesA/add/pubJobOne',
				})
			},
			async onGetCarouselList() {
				let obj = {};
				obj.area_type = '1'; //先写死，之后城市接口接上后再换上真数据
				obj.area_id = '1511';
				const res = await getCarouselList1(obj);
				if (res.status_code !== '200') return;
				//this.swiperList = res.data.index_ad.map(el => el.image.path_url);
				this.swiperList.push(res.data.index_carousel.image.path_url);
				res.data.index_ad.forEach((item, index) => {
					this.swiperList.push(item.image.path_url);
				});
			},
			async onGetSchoolFocusList() {
				const res = await getSchoolFocusList();
				if (res.status_code !== '200') return;
				this.schoolList = res.data.map(el => {
					return {
						id: el.id,
						text: el.college_name
					}
				})
				this.univeList = res.data;
			},
			onTypeItemClick(v) {
				if (v.url) {
					if (!isLogin()) return
					if (!isAuth(['login'])) return
					uni.$u.route({
						url: v.url
					});
				} else this.typeKey = v.key;
				this.$forceUpdate();
			},
			//系统选项数据
			async getSysData() {
				const result = await getSysList()
				// this.sysData = result;
				this.$store.commit('setSysData', result);
			},
			//省市区数据
			async getCityList() {
				const result = await getCityList()
				if (result.status_code == 200) {
					this.$store.commit('setCityList', result.data)
				}
			},
			changeTab(item) {
				if (item.id == 7) {
					uni.navigateTo({
						url: '/pagesC/dynamic/index'
					})
					return
				}
				this.selTab = item.id
			},
			goAddress() {
				uni.navigateTo({
					url: '/pagesC/regionPage/index'
				})
			}
		},
	};
</script>
<style>
	.u-navbar--fixed {
		z-index: 999 !important;
	}
</style>
<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #f5f5f7;
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		padding-block-end: calc(108rpx + constant(safe-area-inset-bottom));
		padding-block-end: calc(108rpx + env(safe-area-inset-bottom));

		.nav-container {
			display: flex;
			align-items: center;
			gap: 12rpx;

			.title {
				font-size: 32rpx;
				color: #333333;
			}
		}

		.top-btm {
			margin-top: -24rpx;
			background: #F5F5F7;
			border-radius: 16rpx 16rpx 0rpx 0rpx;
			overflow: hidden;
			z-index: 999;
		}

		.search {
			height: 138rpx;
			background: #CDDFFF;
			z-index: 999;

			.sear-box {
				padding: 26rpx 32rpx;
			}
		}

		.hea-inner {
			padding-inline: 32rpx;
		}

		.nav-logo {
			width: 126rpx;
			height: 44rpx;
		}

		.nav-default {
			font-size: 34rpx;
			color: #000000;
		}

		.switch-container {
			display: flex;
			align-items: center;
			gap: 24rpx;
		}

		.sticky-container {
			padding-inline: 32rpx;
			padding-block: 24rpx;
			display: flex;
			flex-direction: column;
			gap: 24rpx;

			.top-box {
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.top-start {
					display: flex;
					align-items: center;
					color: #333333;
					font-size: 24rpx;
					gap: 16rpx;

					.title {
						white-space: nowrap;
					}

					.city-box {
						flex: 1;
						position: relative;
						display: flex;
						align-items: center;
						white-space: nowrap;
						overflow-x: auto;

						.city-item {
							margin-inline: 8rpx;
							display: inline-block;
						}
					}
				}

				.top-end {
					display: flex;
					align-items: center;
					justify-content: space-between;
					color: #333333;
					font-size: 28rpx;

					.logo-box {
						display: flex;
						align-items: center;
						gap: 24rpx;

						.image {
							width: 126rpx;
							height: 44rpx;
						}
					}

					.btn {
						font-size: 24rpx;
						color: #ffffff;
						background-color: #3d82f3;
						padding-inline: 16rpx;
						padding-block: 8rpx;
						border-radius: 8rpx;
					}
				}
			}
		}

		.tabs {
			display: flex;
			align-items: center;
			flex-direction: row;
			width: max-content;
			min-width: 100%;
			box-sizing: border-box;
			padding-bottom: 24rpx;
			padding-inline: 32rpx;

			.tabs-item {
				margin-right: 40rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				flex-shrink: 0;

				&>img {
					width: 88rpx;
					height: 88rpx;
				}

				&>view {
					font-size: 24rpx;
					color: #041024;
					margin-top: 2rpx;
					text-align: center;
				}

				&:last-child {
					margin-right: 0;
				}
			}

			.sel-tab {
				&>view {
					color: #4F8CF0;
				}
			}
		}


		.scroll-y-view {
			flex: 1;
			overflow-y: auto;

			.scroll-y-container {
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding-block-end: 32rpx;

				.tabs-container {
					padding-inline: 16rpx;
					background-color: #f5f5f7;
				}

				.scroll-x-view {
					width: 100vw;
					white-space: nowrap;
					overflow-x: auto;

					.scroll-x-container {
						min-width: 100vw;
						display: inline-flex;
						align-items: center;
						justify-content: space-between;
						gap: 48rpx;
						padding-inline: 32rpx;
						box-sizing: border-box;

						.item {
							display: flex;
							flex-direction: column;
							align-items: center;
							gap: 16rpx;
							color: #041024;
							font-size: 24rpx;
							white-space: nowrap;


							.image {
								width: 72rpx;
								height: 72rpx;
							}
						}
					}
				}

				.tips {
					color: #000000;
					font-size: 24rpx;
					padding-inline: 32rpx;
				}

				.desired-box {
					display: flex;
					align-items: center;
					gap: 24rpx;
					color: #333333;
					font-size: 32rpx;
					padding-inline: 32rpx;
					position: relative;

					.avater {
						width: 48rpx;
						height: 48rpx;
						border-radius: 999rpx;
					}

					.image {
						width: 32rpx;
						height: 32rpx;
						transition: all 0.1s linear;
					}
				}

				::v-deep .u-swiper {
					.u-swiper__wrapper__item {
						.u-swiper__wrapper__item__wrapper {
							box-sizing: border-box;
							padding-inline: 32rpx;

							.u-swiper__wrapper__item__wrapper__image {
								border-radius: 16rpx !important;
							}
						}
					}
				}
			}
		}

		.scroll-y-hea {
			margin-top: 24rpx;
			margin-bottom: 24rpx;
		}
	}
</style>
