<template>
	<view class="skill-selection-container">
		<view class="reference-skill-word">
			<text class="title">参考技能词</text>
			<text class="count">{{selectedTags.length}}/10</text>
		</view>
		<view class="skill-category" v-for="(category, categoryIndex) in skillCategories" :key="categoryIndex">
			<text class="category-title">{{category.title}}</text>
			<view class="category-content">
				<view class="skill-tag" v-for="(tag, tagIndex) in category.tags" :key="tagIndex"
					:class="{ 'tag-selected': selectedTags.includes(tag) }" @click="toggleTag(tag)">
					{{tag}}
				</view>
			</view>
		</view>
		<view class="selected-tags">
			<view class="tag-item" v-for="(selectedTag, selectedIndex) in selectedTags" :key="selectedIndex">
				<text class="tag-text">{{selectedTag}}</text>
				<text class="remove-tag" @click="removeTag(selectedTag)">×</text>
			</view>
		</view>
		<button class="confirm-button" @click="confirmSelection">确定</button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				skillCategories: [{
						title: 'UI设计师',
						tags: ['AE', 'PhotoShop', 'Spine', 'CAD', 'MAYA', 'Premiere', 'UI设计', '交互体验设计', '动效设计', 'UE设计',
							'设计师助理', '设计师', '美术设计师', '设计主管', '设计经理', '设计总监'
						]
					},
					{
						title: '大数据类',
						tags: []
					},
					{
						title: '开发编程类',
						tags: []
					},
					{
						title: '多媒体设计类',
						tags: []
					},
					{
						title: '工程制图类',
						tags: []
					},
					{
						title: '办公应用软件类',
						tags: []
					},
					{
						title: '语言类',
						tags: []
					},
					{
						title: '电子/硬件设计类',
						tags: []
					},
					{
						title: '财务管理类',
						tags: []
					},
					{
						title: '影视/后期',
						tags: []
					},
					{
						title: '兴趣/特长',
						tags: []
					}
				],
				selectedTags: []
			};
		},
		methods: {
			toggleTag(tag) {
				if (this.selectedTags.length < 10) {
					if (this.selectedTags.includes(tag)) {
						this.selectedTags = this.selectedTags.filter(t => t !== tag);
					} else {
						this.selectedTags.push(tag);
					}
				}
			},
			removeTag(tag) {
				this.selectedTags = this.selectedTags.filter(t => t !== tag);
			},
			confirmSelection() {
				// 这里可以添加确认选择后的逻辑，比如提交数据等
				console.log('确认选择的技能标签:', this.selectedTags);
			}
		}
	};
</script>

<style scoped>
	.skill-selection-container {
		padding: 15px;
		background-color: #f5f5f5;
	}

	.reference-skill-word {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15px;
	}

	.title {
		font-size: 20px;
		font-weight: bold;
	}

	.count {
		font-size: 16px;
		color: #666;
	}

	.skill-category {
		margin-bottom: 20px;
	}

	.category-title {
		font-size: 18px;
		font-weight: bold;
		margin-bottom: 10px;
	}

	.category-content {
		display: flex;
		flex-wrap: wrap;
	}

	.skill-tag {
		border: 1px solid #ccc;
		border-radius: 5px;
		padding: 8px 15px;
		margin-right: 10px;
		margin-bottom: 10px;
		cursor: pointer;
		background-color: #fff;
	}

	.tag-selected {
		background-color: #e6f2ff;
		border-color: #88b3ff;
	}

	.selected-tags {
		margin-bottom: 20px;
		display: flex;
		flex-wrap: wrap;
	}

	.tag-item {
		border: 1px solid #ccc;
		border-radius: 5px;
		padding: 8px 10px;
		margin-right: 10px;
		margin-bottom: 10px;
		display: flex;
		align-items: center;
		background-color: #fff;
	}

	.tag-text {
		margin-right: 5px;
	}

	.remove-tag {
		color: #ff4949;
		cursor: pointer;
	}

	.confirm-button {
		width: 100%;
		padding: 12px;
		background-color: #007aff;
		color: white;
		border: none;
		border-radius: 5px;
		font-size: 16px;
		cursor: pointer;
	}
</style>