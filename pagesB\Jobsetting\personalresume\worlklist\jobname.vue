<template>
	<view class="container">
		<view style="height: 194rpx;">
			<view class="title">选择岗位名称</view>
			<view class="search-box">
				<u--input placeholder="搜索岗位名称" prefixIcon="search" prefixIconStyle="font-size: 22px;color: #909399"
					style=""></u--input>
			</view>
		</view>
		<view class="job-list-container">
			<view class="joblist-left">
				<scroll-view scroll-y="true" class="scroll-Y">
					<view
						style="display: flex; flex-direction: column; justify-content: space-evenly; align-items: center; width: 260rpx;">
						<view v-for="(category, index) in jobList" :key="category.id" class="job-category"
							@click="toggleChecked(index)" :class="category.checked ? 'active':'job-category' ">
							<text class="category-name"
								:class="category.checked ? 'active-item':'category-name' ">{{category.name}}</text>
						</view>
					</view>
				</scroll-view>
			</view>
			<view class="joblist-right">
				<scroll-view scroll-y="true" class="scroll-Y">
					<view v-for="(category, index) in jobList" :key="category.id" class="category">
						<view style="display: flex;justify-content: space-evenly; align-items: center;">
							<view v-if="category.checked">
								<view v-for="(subList, subIndex) in category.list" :key="subList.id"
									style="display: flex; flex-wrap: wrap; flex-direction: column;">
									<view>{{subList.displayName}}</view>
									<view style="display: flex; flex-wrap: wrap; justify-content: space-between;">
										<view class="skill" v-for="(skill, skillIndex) in subList.jobcon"
											:key="skill.id">
											<label class="job-label">
												<text>
													{{skill.name}}
												</text>
											</label>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>


		<view class="footer">
			<button class="confirm-btn" @click="submitInfo">提交</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				jobList: [{
						"id": 1,
						"name": "互联网/AI",
						"checked": false,
						"list": [{
								"id": 2,
								"style": "Backend",
								"displayName": "后端开发2",
								"jobcon": [{
										"id": 3,
										"name": "Java",
										"checked": false
									},
									{
										"id": 4,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 5,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 6,
										"name": "Python",
										"checked": false
									},
									{
										"id": 7,
										"name": "C#",
										"checked": false
									},
									{
										"id": 8,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 9,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 10,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 11,
										"name": "Android",
										"checked": false
									},
									{
										"id": 12,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 13,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 14,
										"name": "Python",
										"checked": false
									},
									{
										"id": 15,
										"name": "C#",
										"checked": false
									},
									{
										"id": 16,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					},
					{
						"id": 17,
						"name": "电子/电气/通信",
						"checked": true,
						"list": [{
								"id": 18,
								"style": "Backend",
								"displayName": "后端开发3",
								"jobcon": [{
										"id": 19,
										"name": "Java",
										"checked": false
									},
									{
										"id": 20,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 21,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 22,
										"name": "Python",
										"checked": false
									},
									{
										"id": 23,
										"name": "C#",
										"checked": false
									},
									{
										"id": 24,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 25,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 26,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 27,
										"name": "Android",
										"checked": false
									},
									{
										"id": 28,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 29,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 30,
										"name": "Python",
										"checked": false
									},
									{
										"id": 31,
										"name": "C#",
										"checked": false
									},
									{
										"id": 32,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					},
					{
						"id": 33,
						"name": "运营/客服",
						"checked": false,
						"list": [{
								"id": 34,
								"style": "Backend",
								"displayName": "后端开发4",
								"jobcon": [{
										"id": 35,
										"name": "Java",
										"checked": false
									},
									{
										"id": 36,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 37,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 38,
										"name": "Python",
										"checked": false
									},
									{
										"id": 39,
										"name": "C#",
										"checked": false
									},
									{
										"id": 40,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 41,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 42,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 43,
										"name": "Android",
										"checked": false
									},
									{
										"id": 44,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 45,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 46,
										"name": "Python",
										"checked": false
									},
									{
										"id": 47,
										"name": "C#",
										"checked": false
									},
									{
										"id": 48,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					},
					{
						"id": 49,
						"name": "销售",
						"checked": false,
						"list": [{
								"id": 50,
								"style": "Backend",
								"displayName": "后端开发",
								"jobcon": [{
										"id": 51,
										"name": "Java",
										"checked": false
									},
									{
										"id": 52,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 53,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 54,
										"name": "Python",
										"checked": false
									},
									{
										"id": 55,
										"name": "C#",
										"checked": false
									},
									{
										"id": 56,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 57,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 58,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 59,
										"name": "Android",
										"checked": false
									},
									{
										"id": 60,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 61,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 62,
										"name": "Python",
										"checked": false
									},
									{
										"id": 63,
										"name": "C#",
										"checked": false
									},
									{
										"id": 64,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					},
					{
						"id": 65,
						"name": "人力/行政/法务",
						"checked": false,
						"list": [{
								"id": 66,
								"style": "Backend",
								"displayName": "后端开发",
								"jobcon": [{
										"id": 67,
										"name": "Java",
										"checked": false
									},
									{
										"id": 68,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 69,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 70,
										"name": "Python",
										"checked": false
									},
									{
										"id": 71,
										"name": "C#",
										"checked": false
									},
									{
										"id": 72,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 73,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 74,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 75,
										"name": "Android",
										"checked": false
									},
									{
										"id": 76,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 77,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 78,
										"name": "Python",
										"checked": false
									},
									{
										"id": 79,
										"name": "C#",
										"checked": false
									},
									{
										"id": 80,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					},
					{
						"id": 81,
						"name": "财务/审计/税务",
						"checked": false,
						"list": [{
								"id": 82,
								"style": "Backend",
								"displayName": "后端开发",
								"jobcon": [{
										"id": 83,
										"name": "Java",
										"checked": false
									},
									{
										"id": 84,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 85,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 86,
										"name": "Python",
										"checked": false
									},
									{
										"id": 87,
										"name": "C#",
										"checked": false
									},
									{
										"id": 88,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 89,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 90,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 91,
										"name": "Android",
										"checked": false
									},
									{
										"id": 92,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 93,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 94,
										"name": "Python",
										"checked": false
									},
									{
										"id": 95,
										"name": "C#",
										"checked": false
									},
									{
										"id": 96,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					},
					{
						"id": 97,
						"name": "生产制造",
						"checked": false,
						"list": [{
								"id": 98,
								"style": "Backend",
								"displayName": "后端开发",
								"jobcon": [{
										"id": 99,
										"name": "Java",
										"checked": false
									},
									{
										"id": 100,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 101,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 102,
										"name": "Python",
										"checked": false
									},
									{
										"id": 103,
										"name": "C#",
										"checked": false
									},
									{
										"id": 104,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 105,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 106,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 107,
										"name": "Android",
										"checked": false
									},
									{
										"id": 108,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 109,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 110,
										"name": "Python",
										"checked": false
									},
									{
										"id": 111,
										"name": "C#",
										"checked": false
									},
									{
										"id": 112,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					},
					{
						"id": 113,
						"name": "零售/生产服务",
						"list": [{
								"id": 114,
								"style": "Backend",
								"displayName": "后端开发",
								"jobcon": [{
										"id": 115,
										"name": "Java",
										"checked": false
									},
									{
										"id": 116,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 117,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 118,
										"name": "Python",
										"checked": false
									},
									{
										"id": 119,
										"name": "C#",
										"checked": false
									},
									{
										"id": 120,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 121,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 122,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 123,
										"name": "Android",
										"checked": false
									},
									{
										"id": 124,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 125,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 126,
										"name": "Python",
										"checked": false
									},
									{
										"id": 127,
										"name": "C#",
										"checked": false
									},
									{
										"id": 128,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					},
					{
						"id": 129,
						"name": "财务/审计/会计/",
						"checked": false,
						"list": [{
								"id": 130,
								"style": "Backend",
								"displayName": "后端开发",
								"jobcon": [{
										"id": 131,
										"name": "Java",
										"checked": false
									},
									{
										"id": 132,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 133,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 134,
										"name": "Python",
										"checked": false
									},
									{
										"id": 135,
										"name": "C#",
										"checked": false
									},
									{
										"id": 136,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 137,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 138,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 139,
										"name": "Android",
										"checked": false
									},
									{
										"id": 140,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 141,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 142,
										"name": "Python",
										"checked": false
									},
									{
										"id": 143,
										"name": "C#",
										"checked": false
									},
									{
										"id": 144,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					},
					{
						"id": 145,
						"name": "餐饮",
						"checked": false,
						"list": [{
								"id": 146,
								"style": "Backend",
								"displayName": "后端开发",
								"jobcon": [{
										"id": 147,
										"name": "Java",
										"checked": false
									},
									{
										"id": 148,
										"name": "C/C++",
										"checked": false
									},
									{
										"id": 149,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 150,
										"name": "Python",
										"checked": false
									},
									{
										"id": 151,
										"name": "C#",
										"checked": false
									},
									{
										"id": 152,
										"name": ".NET",
										"checked": false
									}
								]
							},
							{
								"id": 153,
								"style": "Frontend",
								"displayName": "前端/移动开发",
								"jobcon": [{
										"id": 154,
										"name": "前端开发工程师",
										"checked": false
									},
									{
										"id": 155,
										"name": "Android",
										"checked": false
									},
									{
										"id": 156,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 157,
										"name": "PHP",
										"checked": false
									},
									{
										"id": 158,
										"name": "Python",
										"checked": false
									},
									{
										"id": 159,
										"name": "C#",
										"checked": false
									},
									{
										"id": 160,
										"name": ".NET",
										"checked": false
									}
								]
							}
						]
					}
				],
				job: []
			}
		},
		methods: {

			toggleChecked(index) {
				this.jobList.forEach((item) => {
					item.checked = false;
				});
				this.jobList[index].checked = true;
			},
			toggle(type, item) {
				this.type = type
				// open 方法传入参数 等同在 uni-popup 组件上绑定 type属性
				this.$refs.popup.open(type)
				console.log(item);
				this.job.push(item)
				console.log(this.job);
			},
			close() {
				this.job = []
				this.$refs.popup.close();
			},
			handleClose() {
				this.job = [];
				this.showPopup = false;
			},
			submitInfo() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/hopePosition'
				})
			}
		}
	}
</script>

<style scoped>
	.container {
		height: 100vh;
		/* padding: 32rpx; */
		/* background-color: rgba(245, 245, 247, 1); */
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.title {
		font-size: 32rpx;
		margin-left: 32rpx;
		margin-top: 32rpx;
	}

	.scroll-Y {
		height: 100%;
	}

	.search-box {
		height: 64rpx;
	}

	.job-list-container {
		padding: 10px;
		display: flex;
		flex: 1;
		justify-content: space-around;
	}

	.joblist-left {
		display: flex;
		flex-direction: column;
		background: rgba(245, 245, 245, 1);
		/* justify-content: space-evenly; */
	}

	.joblist-right {
		width: 426rpx;
		/* padding: 0rpx 32rpx; */
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.job-category {
		height: 88rpx;
		/* margin-bottom: 15px; */
		display: flex;
		justify-content: start;
		align-items: center;
		width: 234rpx;
		padding-left: 24rpx;
	}

	.category-name {
		font-size: 28rpx;
		color: rgba(119, 119, 119, 1);
		margin-bottom: 5px;
		overflow: hidden;
		/* 隐藏溢出的文本 */
		white-space: nowrap;
		/* 保持文本在一行内显示 */
		text-overflow: ellipsis;

	}

	.active {
		color: rgba(79, 140, 240, 1);
		;
	}

	.job-item {
		width: 141rpx;
		margin-bottom: 10px;
		background-color: #f5f5f5;
		border-radius: 8px;
		padding: 16rpx 32rpx;
		display: flex;
		justify-content: space-around;
	}

	.job-label {
		/* width: 108rpx; */
		height: 40rpx;
		/* justify-content: space-between; */
		display: flex;
		align-items: center;
	}

	.job-label text {
		font-size: 24rpx;
		color: rgba(51, 51, 51, 1);
	}


	.job-context {
		margin-left: 10px;
		font-size: 14px;
		line-height: 1.4;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.context-box {
		width: 362rpx;
		height: 68rpx;
		display: flex;
		justify-content: space-between;
		align-items: end;
	}

	.context-box text {
		width: 230rpx;
		font-size: 28rpx;
		display: -webkit-box;
		/*弹性伸缩盒子模型显示*/
		-webkit-box-orient: vertical;
		/*排列方式*/
		-webkit-line-clamp: 2;
		/*显示文本行数(这里控制多少行隐藏)*/
		overflow: hidden;
		/*溢出隐藏*/
	}

	.context-box img {
		width: 32rpx;
		height: 32rpx;
		align-self: self-end;
	}

	::v-deep .u-input {
		width: 686rpx;
		height: 64rpx;
		background-color: rgba(243, 243, 243, 1);
		border-radius: 126rpx;
		padding: 0rpx !important;
		margin: 32rpx auto;
	}

	.active {
		background-color: rgba(255, 255, 255, 1);
		position: relative;
		/* 点击后改变的背景颜色 */
	}

	.active-item {
		color: rgba(79, 140, 240, 1);
	}

	.active:before {
		content: '';
		width: 6rpx;
		height: 88rpx;
		background-color: rgba(79, 140, 240, 1);
		position: absolute;
		left: 0rpx;
	}

	.popup-content {
		background-color: #fff;
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
		box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
		padding: 15px;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}

	.popup-title {
		font-size: 18px;
		font-weight: 500;
	}

	.popup-close {
		width: 20px;
		height: 20px;
	}

	.popup-body {
		line-height: 1.5;
		font-size: 28rpx;
	}

	.header {
		width: 686rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.skill {
		display: flex;
		justify-content: center;
		align-items: center;
		/* padding: 16rpx 32rpx; */
		width: 202rpx;
		height: 66rpx;
		background-color: rgba(245, 245, 245, 1);
		border-radius: 12rpx;
		color: rgba(51, 51, 51, 1);
		margin: 8rpx 0rpx;
	}
</style>