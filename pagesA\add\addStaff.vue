<template>
	<view class="">
		<view class="inp">
			<view class="inp-item">
				<view class="name">
					员工名称
				</view>
				<u--input placeholder="请输入员工姓名" border="none" fontSize="28rpx" v-model="formData.name"></u--input>
			</view>

			<view class="inp-item">
				<view class="name">
					手机号
				</view>
				<u--input placeholder="请输入手机号" border="none" fontSize="28rpx" v-model="formData.cellphone"></u--input>
			</view>

			<view class="inp-item" @click="showGenderPicker">
				<view class="name">
					性别
				</view>
				<u--input placeholder="请选择性别" border="none" fontSize="28rpx" v-model="formData.gender"
					disabled></u--input>
			</view>

			<view class="inp-item">
				<view class="name">
					年龄
				</view>
				<u--input placeholder="请输入员工年龄" border="none" fontSize="28rpx" v-model="formData.age"></u--input>
			</view>

			<!-- 员工权限 -->
			<view class="inp-item">
			    <view class="title">
			        员工权限
			        <view class="plus">
			            <image src="/static/images/index/plus.png" mode="" @click="addLabel"></image>
			        </view>
			    </view>
			    <view class="in">
			        <scroll-view class="scroll-W" :scroll-x="true">
			            <block v-if="jobLabelList.length==0">
			                <view style="color: #c0c4cc; margin-left: 32rpx; font-size: 28rpx;">
			                    添加员工标签
			                </view>
			            </block>
			            <block v-else>
			                <view class="nav-item" v-for="(item,index) in jobLabelList" :key="index">
			                    <view class="cont">
			                        <text>{{ item }}</text>
			                        <view class="del">
			                            <u-icon name="close" size="28rpx" @click="delLabel(item)"></u-icon>
			                        </view>
			                    </view>
			                </view>
			            </block>
			        </scroll-view>
			    </view>
			</view>
		</view>
		<!-- 底部按钮 -->
		<view class="footer">
			<view class="btns">
				<!-- <view class="logout" @click="logout">
					注销
				</view> -->
				<view class="save" @click="but">
					保存
				</view>
			</view>
		</view>

		<!-- 性别选择器 -->
		<u-picker :show="showPicker" :columns="genderColumns" keyName="label" @confirm="onGenderConfirm"
			@cancel="showPicker = false"></u-picker>
		<!-- 标签弹框 -->
		<u-modal :show="showLabel" showCancelButton cancelColor="#FE4D4F" @cancel="cancel" @confirm="sureLabel"
		         title="添加标签">
		    <view class="slot-content">
		        <view class="inp">
		            <u--input placeholder="请输入标签内容" border="surround" v-model="labelValue"></u--input>
		        </view>
		    </view>
		</u-modal>
	</view>
</template>

<script>
	import {
		staffStore
	} from "../../config/company_api";

	export default {
		data() {
			return {
				formData: {
					cellphone: "", // 登录用的手机号
					name: '', // 员工名称
					idcard: '', // 身份证号
					gender: '', // 性别
					age: '', // 年龄
					privileges: [], // 员工权限列表
				},
				genderColumns: [
					[{
							label: '男',
							value: '男'
						},
						{
							label: '女',
							value: '女'
						},
						{
							label: '其他',
							value: '其他'
						}
					]
				],
				showPicker: false, // 控制性别选择器的显示与隐藏
				showPrivilegeModal: false, // 控制添加权限弹窗的显示与隐藏
				newPrivilege: '', // 新增权限的名称
				jobLabelList: [],
				showLabel: false,
				labelValue: '', // 标签输入框的值
				pubJobOne: { // 用于存储标签数据
					label: []
				}
			}
		},
		methods: {
			auth() {
				uni.showModal({
					title: '该功能尚未开放',
					showCancel: false,
					success: function(res) {
						if (res.confirm) {
							console.log('用户点击确定');
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			async but() {
				//this.formData.privileges = this.jobLabelList.join(',');
				this.formData.privileges = this.jobLabelList;
				await staffStore(this.formData).then(response => {
					uni.$u.toast(response.message);

					if (response.status_code == '200') {
						uni.setStorageSync('storeUser', response.data);
						setTimeout(() => {
							uni.navigateBack()
						}, 1000);
					} else {
						uni.$u.toast(response.message);
					}
				})
			},
			// 显示性别选择器
			showGenderPicker() {
				this.showPicker = true;
			},
			// 确认选择性别
			onGenderConfirm(e) {
				this.formData.gender = e.value[0].label;
				this.showPicker = false;
			},
			// 注销按钮点击事件
			logout() {
				uni.showModal({
					title: '提示',
					content: '确定要注销当前员工吗？',
					success: (res) => {
						if (res.confirm) {
							console.log('用户点击确定注销');
							uni.$u.toast('员工已注销');
						} else if (res.cancel) {
							console.log('用户取消注销');
						}
					}
				});
			},
			addLabel() {
			    this.showLabel = true;
			},

			delLabel(item) {
			    let index = this.jobLabelList.findIndex(v => item == v)
			    this.jobLabelList.splice(index, 1)
			},

			sureLabel() {
			    if (!this.labelValue) return uni.$u.toast('请输入员工权限')
			    this.jobLabelList.push(
			        this.labelValue
			    )
			    this.pubJobOne.label = this.jobLabelList
			    this.showLabel = false
			},
		}
	}
</script>
<style lang="scss" src="../../static/css/pagesA/add/addStaff.scss"></style>
