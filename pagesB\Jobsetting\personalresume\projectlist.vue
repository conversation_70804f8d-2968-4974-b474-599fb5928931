<template>
	<view class="container">
		<view class="content">
			<view class="form-item" @click="routerName">
				<label>项目名称</label>
				<u--input placeholder="请输入项目名称" :value="dataform.companyName" suffixIcon="arrow-right"
					suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
			</view>
			<view class="form-item" @click="routerjoblist">
				<label>项目角色</label>
				<view class="selected-con">
					<view class="picker-view" v-if="dataform.projectRole != ''">
						{{ dataform.projectRole }}
					</view>
					<view class="picker-view" v-else>
						请选择你在项目中担任的角色
					</view>
					<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
				</view>
			</view>
			<view class="form-item">
				<label>项目时间</label>

				<view class="time-picker">
					<view class="selected-con">
						<picker class="form-input" mode="date" :value="dataform.startTime" :start="startDate"
							:end="endDate" @change="bindDateChange">
							<view class="form-input-placeholder" v-if="dataform.startTime===''">请选择开始时间</view>
							<view class="form-input-placeholder" v-else>{{dataform.startTime}}</view>
						</picker>
						<text>-</text>
						<picker class="form-input" mode="date" :value="dataform.endTime" :start="dataform.startTime"
							@change="bindDateChange2">
							<view class="form-input-placeholder" v-if="dataform.endTime===''">请选择结束时间</view>
							<view class="form-input-placeholder" v-else>{{dataform.endTime}}</view>
						</picker>
					</view>
				</view>
			</view>


			<view class="form-item" @click="routerdescribe">
				<view style="display: flex; justify-content: space-between; align-items: center;">
					<label>项目描述</label>
					<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
				</view>
				<view class="selected-con">
					<view class="picker-view" v-if="dataform.workDescription != ''">
						{{ dataform.workDescription }}
					</view>
					<view class="picker-view" style="display: flex;flex-direction: column;" v-else>
						描述该项目、像招聘者展示您的项目经验
						<text>例如：</text>
						<text> 1.项目概述... </text>
						<text> 2.人员分工... </text>
						<text>3.我的分工...</text>
					</view>

				</view>

			</view>

			<view class="form-item" @click="routerwork">
				<label>项目业绩（选填）</label>
				<view class="selected-con">
					<view class="picker-view" v-if="dataform.projectAchievement != ''">
						{{ dataform.projectAchievement }}
					</view>
					<u--input type="text" placeholder="描述该项目，像招聘者展示您的项目经验" :value="dataform.projectAchievement"
						suffixIcon="arrow-right" suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
				</view>
			</view>
			<view class="form-item">
				<label>项目链接</label>
				<u--input type="text" placeholder="例如：github.com/eril" :value="dataform.projectLink"
					suffixIcon="arrow-right" suffixIconStyle="color:rgba(51, 51, 51, 1)"
					v-model="dataform.projectLink"></u--input>
			</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" @click="submitForm">完成</button>
		</view>

	</view>
	</view>
</template>
</view>
<script>
	export default {
		data() {
			return {
				dataform: {
					projectName: '',
					projectRole: '',
					roleOptions: ['UI设计师', '前端开发', '后端开发'], // 项目角色选项
					roleIndex: 0,
					showRoleSelector: false,
					startTime: '',
					endTime: '',
					workDescription: '',
					projectAchievement: '',
					projectLink: '',
					showLinkSelector: false
				}
			};
		},
		computed: {
			startDate() {
				return this.getDate('start');
			},
			endDate() {
				return this.getDate('end');
			}
		},
		methods: {
			submitForm() {
				// 这里可以添加表单提交逻辑，例如调用接口发送数据等
			},

			routerName() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/project/projectname'
				})
			},
			routerwork() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/project/projectresult'
				})
			},
			routerjoblist() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/project/projectPart'
				})
			},
			routerdescribe() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/project/projectDescribe'
				})
			},
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			// 选择开始时间
			bindDateChange(e) {
				this.dataform.startTime = e.detail.value
			},
			// 选择结束时间
			bindDateChange2(e) {
				this.dataform.endTime = e.target.value;
			},
			// 按照时间查找
			findDate() {
				if (this.dataform.startTime === '' || this.dataform.endTime === '') {
					uni.showModal({
						title: '提示',
						content: `请选择起始时间和结束时间后，再点击查询`,
						showCancel: false
					});
				} else {
					getTask(this.dataform).then(res => {
						this.tasklistArr = JSON.parse(JSON.stringify(res.data.data));
					})
				}

			},
			// 清除时间
			cleardataform() {
				this.dataform.startTime = "";
				this.dataform.endTime = "";
				getTask(this.dataform).then(res => {
					this.tasklistArr = JSON.parse(JSON.stringify(res.data.data));
				})
			},

		}
	};
</script>

<style scoped>
	.container {
		padding: 0rpx 20rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		background-color: rgba(245, 245, 247, 1);
	}

	.content {
		flex: 1;
		width: 622rpx;
		padding: 32rpx;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.form-item {
		margin-bottom: 20rpx;
		width: 622rpx;
		border-bottom: 1rpx solid rgba(230, 230, 230, 1);
		padding-bottom: 32rpx;
	}

	.label {
		display: block;
		margin-bottom: 5rpx;
		height: 114rpx;
	}


	/* .input {
		width: 100%;
		padding: 10rpx;
		border: 1rpx solid #ccc;
		border-radius: 5rpx;
	} */

	::v-deep .u-input {
		border: none;
		padding: 0rpx !important;
		width: 622rpx;
	}

	.picker-view {
		width: 100%;
		/* padding: 10rpx; */
		/* border: 1rpx solid #ccc; */
		color: rgba(204, 204, 204, 1);
		margin-top: 16rpx;
		border-radius: 5rpx;
		font-size: 28rpx;
	}

	.selected-con {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 622rpx;
		/* height: 40rpx; */
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.time-picker {
		display: flex;
		align-items: center;
	}

	.time-picker text {
		margin: 0 10px;
	}

	.form-input-placeholder {
		color: #999;
	}
</style>