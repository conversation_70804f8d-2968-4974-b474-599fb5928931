<template>
  <!-- 查看收藏记录 -->
  <view class="container">
    <u-sticky>
      <view class="tabs-container">
        <u-tabs
          :current="tabsIndex"
          :list="tabsList"
          :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.05)' }"
          :inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }"
          @click="onTabsItemClick"
        ></u-tabs>
      </view>
    </u-sticky>
    <scroll-view :scroll-y="true" class="scroll-view">
      <view class="scroll-container">
        <template v-if="tabsIndex == 0">
          <template v-for="_ in 20">
            <view class="time">6月10日</view>
            <view class="item">
              <view class="item-start">
                <image class="image" src="/static/new/火苗@2x.png"></image>
                <text class="text_1">Web 前端</text>
                <text class="text_2">实习/兼职</text>
                <text class="text_3">6-9K</text>
              </view>
              <view class="item-center">
                <view class="title">前端</view>
                <view class="tag-box">
                  <view class="tag">世界500强</view>
                  <view class="tag">上市公司</view>
                  <view class="tag">游戏大厂</view>
                  <view class="find-box">
                    <image
                      class="image"
                      src="/static/new/浏览量****************"
                    ></image>
                    <text class="number">1024</text>
                  </view>
                </view>
                <view class="info-box">
                  <view class="detail">学创北京｜A轮｜0-20人</view>
                  <view class="location-box">
                    <image class="image" src="/static/new/定位@2x.png"></image>
                    <text class="position">北京北京市昌平区1号</text>
                    <view class="distance">距12.1km</view>
                  </view>
                </view>
              </view>
              <u-line></u-line>
              <view class="item-end">
                <image
                  class="image"
                  src="https://api-test.zhaopinbei.com/storage/uploads/images/fAm3AgZSBHCsSb9A56QAAlxS9lcv24ezx9Lwzg51.png"
                >
                </image>
                <text class="text margin-text">平台保障</text>
                <image
                  class="image"
                  src="https://api-test.zhaopinbei.com/storage/uploads/images/OKHqUrYjgtO8nsbNaKp4h73DbHQHzIzZBczc5ScK.png"
                >
                </image>
                <text class="text">平台自聘</text>
              </view>
            </view>
          </template>
        </template>
        <template v-if="tabsIndex == 1">
          <template v-for="_ in 20">
            <view class="time">6月10日</view>
            <view class="item">
              <view class="item-start">
                <text class="text_1">Web 前端</text>
                <text class="text_3">1000/人</text>
              </view>
              <view class="item-start">
                <text class="text_4">职位任务</text>
                <text class="text_5">1/30</text>
              </view>
              <u-line></u-line>
              <view class="item-center">发布人：张瑞显</view>
              <view class="item2-end">
                <view class="line">
                  <img src="" alt="" />
                  <text> 邯山区麒局串吧餐饮店 </text>
                </view>
                <view class="item2-btn"> 已结束 </view>
              </view>
            </view>
          </template>
        </template>
        <template v-if="tabsIndex == 2">
          <view
            class="item-type-1-warp"
            v-for="v in jobActiveList"
            :key="v.job_active.id"
          >
            <view class="time">6月10日</view>
            <view class="item-type-1" @click="onDetail(v.job_active.id)">
              <view class="item-start">
                <view class="title">{{ v.job_active.title }}</view>
                <view class="sub-title">500/天</view>
              </view>
              <view class="item-center">
                {{ v.job_active.intro }}
              </view>
              <view class="item-end">
                <view>上岗时间：{{ v.job_active.start }}</view>
                <view>报名截止时间：{{ v.job_active.end }}</view>
              </view>
            </view>
          </view>
        </template>
        <template v-if="tabsIndex == 3">
          <view class="time">6月10日</view>
          <view class="item-type-2">
            <view class="item-start">
              <image
                class="image"
                src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"
                mode=""
              ></image>
              <view class="start-info">
                <text class="title">xxx公司宣讲会</text>
                <text class="time">讲师：王哈哈</text>
                <text class="time">提供岗位：22个</text>
                <text class="time">河南大学礼堂</text>
                <view class="tag">进行中</view>
              </view>
            </view>
          </view>
        </template>
        <template v-if="tabsIndex == 4">
          <view class="time">6月10日</view>
          <view class="item-type-2">
            <view class="item-start">
              <image
                class="image"
                src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"
                mode=""
              ></image>
              <view class="start-info">
                <text class="title">xxx公司宣讲会</text>
                <text class="time">2022年12月12日</text>
                <text class="time">已有200家企业入驻</text>
                <text class="time">地址：郑州市金水区万正商务大厦</text>
                <view class="tag">进行中</view>
              </view>
            </view>
            <view class="item-end">
              <text class="time">·52367人参加</text>
              <text class="time">9月15日 08:00:00开始</text>
            </view>
          </view>
        </template>
        <template v-if="tabsIndex == 5">
          <view class="time">6月10日</view>
          <view class="item-type-2">
            <view class="item-start">
              <image
                class="image"
                src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"
                mode=""
              ></image>
              <view class="start-info">
                <text class="title">xxx公司宣讲会</text>
                <text class="time">2022年12月12日</text>
                <text class="time">已有200家企业入驻</text>
                <view class="tag">进行中</view>
              </view>
            </view>
            <view class="item-end">
              <text class="time">·52367人参加</text>
              <text class="time">9月15日 08:00:00开始</text>
            </view>
          </view>
        </template>
        <template v-if="tabsIndex == 6">
          <view class="time">6月10日</view>
          <view class="img-list">
            <img class="ims" src="" v-for="v in 4" alt="" />
          </view>
        </template>
        <template v-if="tabsIndex == 7">
          <view class="time">6月10日</view>
          <view class="item-type-2">
            <view class="item-start">
              <image
                class="file-img"
                src="https://api-test.zhaopinbei.com/storage/uploads/images/mbrC52puNrB598j3KqmLe32aYoT7B03jgkcT7jVN.png"
                mode=""
              ></image>
              <view class="start-info">
                <text class="title">文件名称</text>
                <text class="time">修改时间：2024/09/26 15:47:57</text>
              </view>
            </view>
          </view>
        </template>
        <template v-if="tabsIndex == 8">
          <view class="time">6月10日</view>
          <view class="item-type-2">
            <view class="item-start">
              <view class="start-info">
                <text class="title"
                  >你好你好你好你好你好你好你好你好你好你好你你好你好你好</text
                >
                <text class="time">2024.11.22</text>
              </view>
            </view>
          </view>
        </template>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { getJobActiveList } from "@/config";
export default {
  data() {
    return {
      tabsList: [
        {
          key: "1",
          name: "职位",
        },
        {
          key: "2",
          name: "任务",
        },
        {
          key: "3",
          name: "特推招聘",
        },
        {
          key: "4",
          name: "宣讲会",
        },
        {
          key: "5",
          name: "招聘会",
        },
        {
          key: "6",
          name: "岗位预定",
        },
        {
          key: "7",
          name: "图片/视频",
        },
        {
          key: "8",
          name: "文件",
        },
        {
          key: "9",
          name: "聊天记录",
        },
      ],
      tabsIndex: 0,
      tabsValue: "1",
      jobActiveList: [],
      params: {
        limit: 20,
        page: 1,
        type: "1",
      },
      isLoading: false,
      more: true,
    };
  },
  computed: {
    userTypeParams() {
      const user = uni.getStorageSync("userInfo");
      if (!user) return {};
      return {
        member_id: user[user.role_type]?.id || 61,
      };
    },
  },
  onLoad(options) {
    // 获取传入的id参数
    if (options.id) {
      const id = options.id;

      // 当id为1时，移除tabsList的前两项
      if (id === "1" || id === 1) {
        this.tabsList = this.tabsList.slice(2); // 只保留第三项"活动详情"
        this.tabsIndex = 0; // 重置当前选中的tab索引
        this.tabsValue = this.tabsList[0].key; // 重置当前选中的tab值
      }

      // 保存id到data中，以便其他方法使用
      this.activeId = id;

      // 加载活动详情数据
      this.getActiveDetails(id);
    }
  },
  mounted() {
    this.onGetJobActiveList();
  },
  methods: {
    onTabsItemClick(v) {
      this.tabsValue = v.key;
      this.tabsIndex = v.index;
    },
    onInitJobActiveList() {
      this.params.page = 1;
      this.jobActiveList = [];
    },
    onScrollGetList() {
      if (!this.more) return;
      if (this.isLoading) return;
      this.isLoading = true;
      this.params.page++;
    },
    async onGetJobActiveList() {
      const params = {
        ...this.params,
        ...this.userTypeParams,
      };
      const res = await getJobActiveList(params);
      if (res.status_code !== "200") return;
      this.jobActiveList = [...this.jobActiveList, ...res.data.data];
      this.more = res.data.more;
      this.isLoading = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  height: 100vh;
  background-color: #f5f5f7;
  display: flex;
  flex-direction: column;

  .tabs-container {
    background-color: #ffffff;
  }

  .scroll-view {
    flex: 1;
    overflow-y: auto;

    .scroll-container {
      display: flex;
      flex-direction: column;
      padding-inline: 32rpx;
      padding-block-start: 32rpx;
      gap: 32rpx;
      padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
      padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

      .time {
        color: #666666;
        font-size: 24rpx;
      }

      .item {
        background-color: #ffffff;
        border-radius: 24rpx;
        padding: 32rpx;
        display: flex;
        flex-direction: column;
        gap: 24rpx;

        .item-end {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 4rpx;

          .image {
            width: 48rpx;
            height: 48rpx;
          }

          .text {
            color: #666666;
            font-size: 20rpx;
          }

          .margin-text {
            margin-inline-end: auto;
          }
        }

        .item2-end {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 4rpx;

          .line {
            display: flex;
            align-items: center;
          }

          img {
            width: 48rpx;
            height: 48rpx;
            background: #d9d9d9;
            border-radius: 8rpx;
            margin-right: 12rpx;
          }

          text {
            font-size: 24rpx;
            color: #999999;
          }

          .item2-btn {
            width: 96rpx;
            height: 46rpx;
            background: #cccccc;
            border-radius: 8rpx;
            font-size: 24rpx;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .item-center {
          display: flex;
          flex-direction: column;
          gap: 24rpx;

          .info-box {
            display: flex;
            flex-direction: column;
            gap: 12rpx;

            .detail {
              color: #666666;
              font-size: 24rpx;
            }

            .location-box {
              display: flex;
              align-items: center;
              gap: 12rpx;

              .image {
                width: 32rpx;
                height: 32rpx;
              }

              .position {
                color: #666666;
                font-size: 24rpx;
              }

              .distance {
                color: #666666;
                font-size: 20rpx;
                padding-inline: 8rpx;
                padding-block: 4rpx;
                border-radius: 999rpx;
                background-color: #f6f6f6;
              }
            }
          }

          .title {
            color: #333333;
            font-size: 28rpx;
          }

          .tag-box {
            display: flex;
            align-items: center;
            gap: 16rpx;

            .tag {
              background-color: #f6f6f6;
              color: #666666;
              padding: 12rpx;
              border-radius: 8rpx;
              font-size: 22rpx;
            }

            .find-box {
              display: flex;
              align-items: center;
              margin-inline-start: auto;

              .image {
                width: 32rpx;
                height: 32rpx;
              }

              .number {
                color: #666666;
                font-size: 20rpx;
              }
            }
          }
        }

        .item-start {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 12rpx;

          .image {
            width: 48rpx;
            height: 48rpx;
          }

          .text_1 {
            color: #041024;
            font-size: 32rpx;
          }

          .text_2 {
            color: #4f8cf0;
            font-size: 32rpx;
            margin-inline: auto;
          }

          .text_3 {
            color: #f98a14;
            font-size: 40rpx;
          }

          .text_4 {
            font-size: 24rpx;
            color: #4f8cf0;
          }

          .text_5 {
            font-size: 24rpx;
            color: #f98a14;
          }
        }
      }
    }
  }

  .item-type-2 {
    display: flex;
    flex-direction: column;
    align-items: center;

    .item-end {
      width: 80%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 24rpx;
      background: linear-gradient(90deg, #f5fcfc 0%, #fcfbfa 100%);
      border-end-start-radius: 16rpx;
      border-end-end-radius: 16rpx;
      font-size: 24rpx;

      .time {
        color: #333333;
      }

      .detail {
        color: #4f8cf0;
      }
    }

    .item-start {
      width: 100%;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      gap: 24rpx;
      background-color: #ffffff;
      border-radius: 16rpx;
      padding: 32rpx;

      .start-info {
        display: flex;
        flex-direction: column;
        gap: 16rpx;
        align-items: flex-start;

        .title {
          color: #333333;
          font-size: 32rpx;
        }

        .time {
          color: #999999;
          font-size: 28rpx;
        }

        .tag {
          padding-block: 8rpx;
          padding-inline: 16rpx;
          background: rgba(87, 213, 28, 0.1);
          border-radius: 8rpx;
          color: #57d51c;
          font-size: 24rpx;
        }
      }

      .image {
        width: 196rpx;
        height: 196rpx;
      }

      .file-img {
        width: 96rpx;
        height: 96rpx;
      }
    }
  }

  .item-type-1-warp {
    .btm {
      display: flex;
      align-items: center;
      font-size: 24rpx;

      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
      }
    }
  }

  .item-type-1 {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    background-color: #ffffff;
    border-radius: 24rpx;
    padding: 32rpx;
    margin: 24rpx 0;

    .item-end {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #333333;
      font-size: 24rpx;
    }

    .item-center {
      color: #666666;
      font-size: 28rpx;
    }

    .item-start {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 32rpx;

      .title {
        color: #333333;
      }

      .sub-title {
        color: #f98a14;
      }
    }
  }

  .img-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10rpx;

    .ims {
      width: 160rpx;
      height: 160rpx;
      background: #d9d9d9;
      border-radius: 8rpx;
    }
  }
}
</style>
