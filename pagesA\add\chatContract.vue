<template>
    <view id="app">
        <view class="content">
            <view class="child">
                <view class="til">合同主题 <text class="redIcon">*</text> </view>
                <input type="text" placeholder="请输入合同主题" v-model="form.title" />
            </view>

            <view class="child">
                <view class="til">合同名称 <text class="redIcon">*</text> </view>
                <input type="text" placeholder="请输入合同名称" v-model="form.name" />
            </view>

            <view class="child" @click="select1()">
                <view class="til">签署时是否需要上传附件 <text class="redIcon">*</text> </view>
                <view :class="['childText',form.is_annex?'childTextIng':'']">
                    {{!form.is_annex ? '是否需要上传附件' : form.is_annex==1? '需要' : '不需要'}}
                </view>
            </view>

            <view class="child" @click="select2()">
                <view class="til">是否需要双方签署 <text class="redIcon">*</text> </view>
                <view :class="['childText',form.signer_need?'childTextIng':'']">
                    {{!form.signer_need ? '是否需要双方签署' : form.signer_need=='all'? '双方签署' : '签署人签署'}}
                </view>
            </view>

            <view class="child">
                <view class="til">签署截止日期 <text class="redIcon">*</text> </view>
                <picker mode="date" :value="form.end_date" @change="changeEndDate">
                    <view :class="['childText',form.end_date?'childTextIng':'']">
                        {{form.end_date?form.end_date:"请选择开始时间"}}
                    </view>
                </picker>
            </view>

            <view class="child" @click.stop="getFile(1)">
                <view class="til">合同文件 <text class="redIcon">*</text> </view>
                <view class="childText" v-if="!fileType">请选择合同文件</view>
                <view class="childTextIng" v-for="(item,index) in fileList" v-if="fileType">
                    {{item.org_name}}
                </view>
            </view>

            <view class="child" @click="getFile(2)">
                <view class="til">协议文件 <text class="redIcon">*</text> </view>
                <view class="childText" v-if="!signerFileType">请选择协议文件</view>
                <view class="childTextIng" v-for="(item,index) in signer_fileList" v-if="signerFileType">
                    {{item.org_name}}
                </view>
            </view>

            <view class="child" @click="getFile(3)">
                <view class="til">其他文件 <text class="redIcon">*</text> </view>
                <view class="childText" v-if="!otherFileType">请选择其他文件</view>
                <view class="childTextIng" v-for="(item,index) in other_fileList" v-if="otherFileType">
                    {{item.org_name}}
                </view>
            </view>

            <view class="child" @click="selectKeyWord(1)">
                <view class="til">本人签署关键字 <text class="redIcon">*</text> </view>
                <input type="text" placeholder="请选择本人签署关键字" v-model="form.member.key_word" disabled />
            </view>

            <view class="child" @click="selectKeyWord(2)">
                <view class="til">签署人签署关键字 <text class="redIcon">*</text> </view>
                <input type="text" placeholder="请选择签署人签署关键字" v-model="form.signer.key_word" disabled/>
            </view>

            <view class="child" @click="selectType()">
                <view class="til">签名人类型 <text class="redIcon">*</text> </view>
                <view :class="['childText',form.signer.user.signer_user_type?'childTextIng':'']">
                    {{!form.signer.user.signer_user_type ? '请选择签名人类型' : form.signer.user.signer_user_type=='member'? '个人' : '企业'}}
                </view>
            </view>

            <view class="child">
                <view class="til">手机号码 <text class="redIcon">*</text> </view>
                <input type="number" placeholder="请输入手机号" v-model="form.signer.user.cellphone" />
            </view>

            <view class="child">
                <view class="til">姓名 <text class="redIcon">*</text> </view>
                <input type="text" placeholder="请输入姓名" v-model="form.signer.user.name" />
            </view>

            <view class="child">
                <view class="til">身份证号码 <text class="redIcon">*</text> </view>
                <input type="idcard" placeholder="请输入身份证号码" v-model="form.signer.user.id_no" />
            </view>
            <view v-if="form.signer.user.signer_user_type == 'user'">
              <view class="child">
                <view class="til">公司名称 <text class="redIcon">*</text> </view>
                <input type="text" placeholder="请输入公司名称" v-model="form.signer.user.company_name" />
              </view>

              <view class="child">
                <view class="til">法定代表人手机号码 <text class="redIcon">*</text> </view>
                <input type="number" placeholder="请输入法定代表人手机号码" v-model="form.signer.user.company_legal_cellphone" />
              </view>

              <view class="child">
                <view class="til">法定代表人姓名 <text class="redIcon">*</text> </view>
                <input type="text" placeholder="请输入法定代表人姓名" v-model="form.signer.user.company_legal_name" />
              </view>

              <view class="child">
                <view class="til">组织机构代码 <text class="redIcon">*</text> </view>
                <input type="text" placeholder="请输入组织机构代码" v-model="form.signer.user.company_org_code" />
              </view>

              <view class="child">
                <view class="til">法定代表人身份证号码 <text class="redIcon">*</text> </view>
                <input type="idcard" placeholder="请输入法定代表人身份证号码" v-model="form.signer.user.company_legal_id_no" />
              </view>
            </view>


            <view class="child">
                <view class="til">备注 <text class="redIcon">*</text> </view>
                <input type="text" placeholder="请输入备注" v-model="form.remark" />
            </view>

        </view>
        <view class="bottomBtn_text" @click="contract">邀请签署</view>
        <view style="height: 100rpx;"></view>
    </view>
</template>

<script>
    import {
        uploadImg,
    } from "../../config/api.js"
    export default {
        data() {
            return {
                
                member_id: '',
                chat_id: '',
                name: '',
                avatar: '',
                user_id: '',
                form: {
                    title: '',
                    name: '',
                    is_annex: '', // 签署时是否需要上传附件
                    signer_need: '', // 使用需要双方签署：all-双方签署，signer-签署人签署
                    end_date: '', // 签署截止日期时间
                    file: [], // 合同文件
                    signer_file: [], // 协议文件
                    other_file: [], // 其他文件
                    member: {
                        key_word: '', // 本人签署关键字
                    },
                    signer: {
                        signer: '', // 签署人信息
                        key_word: '', // 签署人签署关键字
                        user: {
                            signer_user_type: '', // 签名人类型：member个人，user企业
                            cellphone: '', // 手机号码
                            name: '', // 姓名
                            id_no: '', // 身份证号码
                            company_name: '', // 公司名称
                            company_legal_cellphone: '', // 法定代表人手机号码
                            company_legal_name: '', // 法定代表人姓名
                            company_org_code: '', // 组织结构代码
                            company_legal_id_no: '', // 法定代表人身份证号码

                        }
                    },
                    remark: '',
                   
                },
                fileList:[],
                fileType:false,
                signer_fileList:[],
                signerFileType:false,
                other_fileList:[],
                otherFileType:false,
            }
        },
        onLoad(options) {
            this.member_id = options.member_id
            this.chat_id = options.chatId
            this.name = options.name
            this.avatar = options.avatar
            this.user_id = options.user_id
        },
        onShow() {
            if (uni.getStorageSync('selectedKeyword')) {
                this.form.member.key_word = uni.getStorageSync('selectedKeyword').key_word
            }
            if (uni.getStorageSync('selectedKeywordTwo')) {
                this.form.signer.key_word = uni.getStorageSync('selectedKeywordTwo').key_word
            }
            
        },
        methods: {
            contract() {
                console.log("获取form", this.form)
                this.form.member_id = this.member_id
                uni.removeStorageSync('selectedKeyword')
                uni.removeStorageSync('selectedKeywordTwo')
                uni.setStorageSync('contract', this.form)
                uni.navigateBack()
                // uni.navigateTo({
                //     url: `/pages/message/message?type=contract&name=${this.name}&avatar=${this.avatar}&chatId=${this.chat_id}&member_id=${this.member_id}&user_id=${this.user_id}`,
                // })
            },
            // 签署时是否需要上传附件
            select1() {
                uni.showActionSheet({
                    itemList: ['需要', '不需要'], // 选项列表
                    success: (res) => {
                        // 签署时是否需要上传附件，1-需要，2-不需要
                        this.form.is_annex = res.tapIndex + 1;
                    },
                    fail: (res) => {
                        console.log(res.errMsg);
                    }
                });
            },
            // 双方签署
            select2() {
                uni.showActionSheet({
                    itemList: ['双方签署', '签署人签署'], // 选项列表
                    success: (res) => {
                        // 使用需要双方签署：all-双方签署，signer-签署人签署
                        this.form.signer_need = res.tapIndex == 0 ? 'all' : 'signer';
                    },
                    fail: (res) => {
                        console.log(res.errMsg);
                    }
                });
            },
            // 签署截止日期
            changeEndDate(e) {
                this.form.end_date = e.detail.value;
            },
            // 签名人类型
            selectType() {
                uni.showActionSheet({
                    itemList: ['个人', '企业'], // 选项列表
                    success: (res) => {
                        // 签名人类型:member-个人，user-企业
                        this.form.signer.user.signer_user_type = res.tapIndex == 0 ? 'member' : 'user';
                    },
                    fail: (res) => {
                        console.log(res.errMsg);
                    }
                });
            },
            // 选择关键字
            selectKeyWord(type) {
                uni.navigateTo({
                    url: '/pagesA/add/chatKeyWords?type='+type
                })
            },
            getFile(num) {
                wx.chooseMessageFile({
                    count: 1,
                    type: 'file',
                    // extension: ['doc', 'docx', 'pdf', 'pptx', 'ppt', 'xls', 'xlsx'],
                    success: (tempFilePaths) => {
                        const path = tempFilePaths.tempFiles[0].path;
                        const name = tempFilePaths.tempFiles[0].name;
                        const tempArr = tempFilePaths.tempFiles[0].name.split('.');
                        const fileSystemManager = wx.getFileSystemManager();
                        fileSystemManager.readFile({
                            filePath: path,
                            success: async (res) => {
                                const base64 = wx.arrayBufferToBase64(res.data);
                                let imageParams = {
                                    ext: tempArr[1],
                                    content: base64,
                                    org_name: name
                                }
                                const result = await uploadImg(imageParams)
                                if (num == 1) {
                                    this.form.file.push(result.data.id)
                                    this.fileList.push(result.data)
                                    this.fileType = true;
                                }
                                if (num == 2) {
                                    this.form.signer_file.push(result.data.id)
                                    this.signer_fileList.push(result.data)
                                    this.signerFileType = true;
                                }
                                if (num == 3) {
                                    this.form.other_file.push(result.data.id)
                                    this.other_fileList.push(result.data)
                                    this.otherFileType = true
                                }
                            },
                        })
                    }
                });
            },



        }
    }
</script>
<style>
    page {
        background: #f5f5f7;
    }
</style>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .content {
        width: 100%;
        padding: 32rpx;
        background: #fff;
        border-radius: 24rpx;
    }

    .child {
        padding-bottom: 24rpx;
        margin-bottom: 24rpx;
        border-bottom: 2rpx solid #F5F5F7;
    }

    .til {
        font-weight: 400;
        font-size: 22rpx;
        color: #666666;
        margin-bottom: 16rpx;
    }

    .redIcon {
        color: #FE4D4F;
        padding-left: 8rpx;
    }

    input {
        font-weight: 400;
        font-size: 32rpx;
    }

    .childText {
        font-weight: 400;
        font-size: 32rpx;
        color: #999;
    }

    .childTextIng {
        color: #000;
    }



    .bottomBtn_text {
        width: 100%;
        height: 80rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #FFFFFF;
        margin-top: 24rpx;
    }
</style>