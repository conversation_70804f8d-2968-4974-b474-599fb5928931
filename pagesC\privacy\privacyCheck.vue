<template>
	<view class="warp">
		<view class="top">
			<view class="arrow">
				<u-icon name="arrow-left" color="#fff" size="20" @click="back"></u-icon>
			</view>
			<view class="top-title">
				隐私检查
			</view>
			<view class="top-title1">
				检查你的个人隐私信息，了解你的个人信息使用状态，并记录你当前个人信息的使用情况。
			</view>
		</view>
		<view class="inner">
			<view class="box" v-for="(item,index) in list" :key="index" @click="goPage(item.id)">
				<img class="left" :src="item.img" alt="" />
				<view class="right">
					<view class="title">
						<view class="">
							{{item.title}}
						</view>
						<view class="num-txt">
							<view class="">
								已交换 <text class="num">{{item.num}}</text>次
							</view>
							<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAMZJREFUWEft1jEOAiEQBdCZregtLCz0KB7EZG04gdfYnhAo5CYWHsIrWNhvQYjZ3ur/SUgM9OQ/PoSMSuelnfNlAEYDUAMppX1r7eKcu8/z/GEeMgSIMd5EZBGR1zRNZ+/9G0VAgBDCQVWfqnpiERBgO23O+VhrfbAIGGCFoAAWCBrAIkwADMIMgCJMAQjivwDI32DWABK+XZkJAA03ATDhNIANpwAW4TDAKhwGdB9ISim7dV2v3UYydPz6tc/kH2BAAzAa+AIR6qkhBYG27AAAAABJRU5ErkJggg==" alt="" />
						</view>
					</view>
					<view class="tip">
						{{item.tips}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					id: 1,
					title: '手机号',
					tips: '近3个月，你已和3个伯乐交换了手机号，对方可以通过此手机号与你联系',
					img: 'data:image/png;base64,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',
					num: 3
				}, {
					id: 2,
					title: '微信号',
					tips: '近3个月，你已和6个伯乐交换了微信，对方可以通过此微信与你联系',
					img: 'data:image/png;base64,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',
					num: 6
				}, {
					id: 3,
					title: '附件简历',
					tips: '近3个月，你已和66个伯乐发送了附件简历（其中包含电话、微信、邮箱等信息），对方可能通过上述信息与你直接联系',
					img: 'data:image/png;base64,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',
					num: 88
				}]
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			// 跳转时间
			goPage(val) {
				switch (val) {
					case 1:
						uni.navigateTo({
							url: './privacyCheck_phone'
						})
						break;
					case 2:
						uni.navigateTo({
							url: './privacyCheck_wx'
						})
						break;
					case 3:
						uni.navigateTo({
							url: './privacyCheck_word'
						})
						break;
				}
			}
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.warp {
		width: 100vh;
		background: #F5F5F7;
		position: relative;
		top: 0;

		.top {
			height: 464rpx;
			width: 686rpx;
			z-index: -1;
			background: linear-gradient(360deg, #F5F5F7 0%, #9DD3FF 18%, #4F75F0 100%);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			// filter: blur(3.5999999046325684px);
			position: absolute;
			top: 0;
			padding: 0 32rpx;

			.arrow {
				margin-top: 116rpx;
			}

			.top-title {
				font-size: 32rpx;
				margin-top: 52rpx;
				color: #FFFFFF;
			}

			.top-title1 {
				margin-top: 28rpx;
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}

		.inner {
			width: 686rpx;
			position: absolute;
			top: 380rpx;
			left: 32rpx;

			.box {
				width: 638rpx;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				padding: 32rpx 24rpx;
				margin-bottom: 40rpx;
				background: #FFFFFF;
				display: flex;
				align-items: center;

				.left {
					width: 136rpx;
					height: 136rpx;
					margin-right: 24rpx;
				}

				.right {
					width: 462rpx;

					.title {
						font-size: 28rpx;
						color: #333333;
						margin-bottom: 16rpx;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.num-txt {
							display: flex;
							align-items: center;
							overflow: hidden;

							.num {
								color: #4F8CF0;
							}

							img {
								width: 32rpx;
								height: 32rpx;
							}
						}
					}

					.tip {
						font-size: 24rpx;
						color: #999999;
						margin-bottom: 24rpx;
					}
				}
			}
		}
	}
</style>
