<template>
	<view class="warp">
		<view class="title">
			拉黑伯乐
		</view>
		<view class="txt">
			添加屏蔽公司后，你和这些公司的伯乐，都不会被相互推荐，你的查看行为也不会告知对方，你将不会收到对方的任何消息。
		</view>
		<view class="">
			<view class="inner" v-for="item in list">
				<view class="left">
					<img :src="item.ava" alt="" />
					<view class="msg">
						<view class="top">
							{{item.name}}
						</view>
						<view class="">
							{{item.pos}}
						</view>
					</view>
				</view>
				<view class="btn" @click="relieve(item)">
					解除
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					name: '王哈哈',
					pos: '学创联盟·人事',
					ava: 'https://api-test.zhaopinbei.com/storage/uploads/images/7Md0Ku73yZXCUOENs3uRGMcZPkuGoUNdMIpFvY2b.png'
				}, {
					name: '王1',
					pos: '学创联盟·人事',
					ava: 'https://api-test.zhaopinbei.com/storage/uploads/images/7Md0Ku73yZXCUOENs3uRGMcZPkuGoUNdMIpFvY2b.png'
				}, {
					name: '王2',
					pos: '学创联盟·人事',
					ava: 'https://api-test.zhaopinbei.com/storage/uploads/images/7Md0Ku73yZXCUOENs3uRGMcZPkuGoUNdMIpFvY2b.png'
				}]
			}
		},
		methods: {
			// 解除
			relieve(item) {
				uni.showModal({
					title: '解除拉黑',
					content: `解除拉黑后，将正常接收“${item.name}”发来的消息，是否要解除拉黑`,
					success: function(res) {
						if (res.confirm) {

						} else {

						}
					}
				});
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 0 32rpx;
		height: 100vh;
		background: #fff;

		.title {
			font-size: 32rpx;
			color: #333333;
			margin: 32rpx 0 48rpx 0;
		}

		.txt {
			font-size: 24rpx;
			color: #666666;
			margin-bottom: 40rpx;
		}

		.inner {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 20rpx;

			.left {
				display: flex;
				align-items: center;

				img {
					width: 88rpx;
					height: 88rpx;
					border-radius: 8rpx 8rpx 8rpx 8rpx;
					margin-right: 28rpx;
				}

				.msg {
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 8rpx;

					.top {
						font-size: 28rpx;
						color: #333333;
					}
				}
			}

			.btn {
				width: 96rpx;
				height: 58rpx;
				border-radius: 52rpx 52rpx 52rpx 52rpx;
				border: 2rpx solid #4F8CF0;
				font-size: 24rpx;
				color: #4F8CF0;
				text-align: center;
				line-height: 58rpx;
			}
		}
	}
</style>
