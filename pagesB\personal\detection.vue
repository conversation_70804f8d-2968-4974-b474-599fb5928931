<template>
	<!-- 安全检测 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="title">输入短信验证码</view>
				<view class="sub-title">验证已绑定的尾号为0333的手机号</view>

				<yi-code class="code-container" type="line" maxlength="4"></yi-code>

				<view class="send">重新发送</view>
				<view class="btn" @click="onRoute('detection_issue')">下一步</view>
				<view class="desc">使用语音验证码</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		methods: {
			onRoute(url) {
				uni.$u.route({
					url: `/pagesB/personal/${url}`
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #FFFFFF;
		display: flex;
		flex-direction: column;

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
				padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

				.desc {
					color: #4F8CF0;
					font-size: 24rpx;
				}

				.btn {
					background-color: #4F8CF0;
					color: #FFFFFF;
					font-size: 28rpx;
					border-radius: 12rpx;
					padding-block: 20rpx;
					text-align: center;
				}

				.code-container {
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.title {
					color: #333333;
					font-size: 32rpx;
				}

				.sub-title {
					color: #666666;
					font-size: 24rpx;
				}

				.send {
					color: #999999;
					font-size: 24rpx;
					text-align: center;
				}
			}
		}
	}
</style>