<template>
  <view class="cardBox">
    <image
      src="/static/images/project/listMore.png"
      mode=""
      class="listMore"
      @click="showActionSheet"
    ></image>
    <view class="cardBox_topLeft">签署关键字</view>
    <view class="cardBox_peopleName">{{ item.key_word }}</view>
    <view class="abstractBox">
      <view class="abstractBox_title">摘要</view>
      <view class="abstractBox_text">{{ item.summary }}</view>
    </view>
    <view class="hrCla"></view>
    <view class="cardBox_times marginBox">
      <view>关键字创建时间：</view>
      <view>{{ item.created_at }}</view>
    </view>
    <view class="cardBox_times">
      <view>关键字更新时间：</view>
      <view>{{ item.updated_at }}</view>
    </view>
  </view>
</template>

<script>
import { keyWordDestroy } from "../../config/api.js";
export default {
  name: "keywords",
  props: {
    tabsIndex: Number,
    item: Object,
  },
  data() {
    return {
      options: ["编辑", "删除"], // 选项列表
      keyword: "",
    };
  },
  methods: {
    showActionSheet() {
      uni.showActionSheet({
        itemList: this.options, // 显示的选项
        success: async (res) => {
          if (res.tapIndex == 1) {
            let params = {
              id: this.item.id,
            };
            const res = await keyWordDestroy(params);
            if (res.status_code == 200) {
              uni.showToast({
                title: "删除成功",
                icon: "none",
              });
              this.$parent.tabClick(1); // 调用父组件的方法
            }
          } else {
            uni.navigateTo({
              url: `/pagesA/contract/contractInput?type=keywords&id=${this.item.id}`,
            });
          }
        },
        fail: (err) => {
          console.error(err); // 处理错误
        },
      });
    },
  },
};
</script>

<style scoped lang="less">
view {
  box-sizing: border-box;
}

.cardBox {
  width: 100%;
  // height: 338rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 24rpx 32rpx;
  margin-top: 24rpx;
  position: relative;
}

.listMore {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  top: 50rpx;
  right: 32rpx;
}

.cardBox_topLeft {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 24rpx;
}

.cardBox_peopleName {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
}

.abstractBox {
  display: flex;
  justify-content: space-between;
  align-items: top;
}

.abstractBox_title {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
}

.abstractBox_text {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
}

.hrCla {
  width: 100%;
  height: 2rpx;
  background: #f5f5f7;
  margin: 24rpx 0;
}

.cardBox_times {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.marginBox {
  margin-bottom: 16rpx;
}

.bottomBtn {
  width: 100%;
  height: 196rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 24rpx 32rpx;
}

.bottomBtn_text {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  text-align: center;
  line-height: 80rpx;
  font-weight: 600;
  font-size: 28rpx;
  color: #ffffff;
}
</style>
