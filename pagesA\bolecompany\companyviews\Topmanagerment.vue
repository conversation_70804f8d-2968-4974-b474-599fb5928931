<template>
	<view class="container">
		<view class=""
			style="display: flex; flex-direction: column; justify-content: space-between; flex: 1; padding: 0rpx 20rpx;">
			<!-- 问题及描述部分 -->
			<view style="width: 686rpx;
					   align-self: center;">
				<view class="info-card">
					<p class="font-bold">
						填写高管介绍有什么用?
					</p>
					<p class="font-blod-subtitle">
						介绍公司可提供的员工培养和晋升制度，良好的职业成长空间对人才更有吸引力
					</p>
				</view>
			</view>
			<!-- 插画及提示部分 -->
			<view class="illustration-section">
				<image class="illustration" :src="oftenNOImg" />
				<view class="tip">
					<!-- <img :src="oftenNOImg" alt="" srcset="" /> -->
				</view>
			</view>
			<Careful v-if="carefulhide"></Careful>
			<!-- 上传注意事项及按钮部分 -->
			<view class="footer-left" @click="toggleCarefulHide">
				<text class="footer-text">上传注意事项</text>
				<image class="footer-icon" :src="help"></image>
			</view>
		</view>
		<view class="footer">
			<view class="footview">
				<view class="footer-card">
					<button class="save" @click="addTop">添加高管</button>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	//import oftenNOImg from 'https://api-test.zhaopinbei.com/storage/uploads/images/MNhvpHVeteyfSj9bRhhcJvVLx0pG3JsGQZtmMssD.png'
	/* import help from 'https://api-test.zhaopinbei.com/storage/uploads/images/rYxkkwuyCNnqb4YUwWfJXzktOukmQ2dF2OuFvchb.png' */
	import Careful from '../companyNotice/carefulNotice.vue'
	export default {
		components: {

			Careful

		},
		data() {
			return {
				oftenNOImg: 'https://api-test.zhaopinbei.com/storage/uploads/images/MNhvpHVeteyfSj9bRhhcJvVLx0pG3JsGQZtmMssD.png',
				help: 'https://api-test.zhaopinbei.com/storage/uploads/images/rYxkkwuyCNnqb4YUwWfJXzktOukmQ2dF2OuFvchb.png',
				carefulhide: false
			}
		},
		methods: {
			addTop() {
				uni.navigateTo({
					url: '/pagesA/bolecompany/companyviews/addTopmanager'
				})
			},
			toggleCarefulHide() {
				console.log(1111111);
				this.carefulhide = !this.carefulhide;
				console.log(this.carefulhide);
			}
		}
	}
</script>

<style>
	.container {
		/* padding: 20rpx; */
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		overflow: hidden;
	}

	.info-card {
		width: 686rpx;
		display: flex;
		height: 166rpx;
		flex-direction: column;
		justify-content: center;
		margin: 40rpx auto;
		position: relative;
		background-image: linear-gradient(to bottom, rgba(242, 248, 255, 1), rgba(255, 255, 255, 1));
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 16rpx;
		overflow: hidden;

	}

	.info-card::before {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 2rpx;
		left: 2rpx;
		content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 3;
	}

	.info-card::after {
		position: absolute;
		width: 100%;
		height: 100%;
		bottom: 2rpx;
		right: 2rpx;
		/* margin: 0px auto; */
		content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 3;
	}

	.font-bold {
		padding: 5px 0;
		font-size: 24rpx;
		line-height: 14.06px;
		margin-left: 40rpx;
	}

	.font-blod-subtitle {
		color: rgba(141, 154, 170, 1);
		padding: 10rpx 0;
		font-size: 20rpx;
		line-height: 23.48rpx;
		width: 606rpx;
		margin-left: 40rpx;
	}

	.illustration-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 40rpx;
	}

	.illustration {
		width: 300rpx;
		height: 300rpx;
		margin-bottom: 20rpx;
	}

	.tip {
		font-size: 28rpx;
		color: #6699ff;
	}

	.bottom-section {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.upload-note {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.note-text {
		font-size: 24rpx;
		color: #999;
		margin-right: 10rpx;
	}

	.note-icon {
		font-size: 24rpx;
		color: #999;
	}

	.footer {
		height: 108rpx;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 1.1);
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 20rpx;
	}

	.footer-left {
		display: flex;
		align-items: center;
		height: 34rpx;
	}

	.footer-text {
		color: rgb(121 121 121);
		/* height: 28px; */
		font-size: 24rpx;
	}

	.footer-icon {
		width: 28rpx;
		height: 28rpx;
		margin-left: 24rpx;
	}

	.save {
		width: 570rpx;
		height: 80rpx;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 28rpx;
		/* margin-top: 42px; */
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
