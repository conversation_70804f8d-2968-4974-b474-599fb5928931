<template>
	<view class="register-one">
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						企业名称<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入企业名称" fontSize="32rpx" :placeholderStyle="placeholderStyle" clearable
							border="none" v-model="form.company.name"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						企业编号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入企业编号" fontSize="32rpx" :placeholderStyle="placeholderStyle" clearable
							border="none" v-model="form.company.credit_id"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						委托期限
					</view>
					<view class="in se">
						<picker mode="date" :value="form.end_at" @change="bindDateChange">
							<view class="d-picker">{{form.end_at?form.end_at:"请选择结束时间"}}</view>
						</picker>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						备注<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入备注" fontSize="32rpx" :placeholderStyle="placeholderStyle" clearable
							border="none" v-model="form.remark"></u--input>
					</view>
				</view>

				<view class="pic">
					<view class="title">
						<text>附件</text> 请上传委托方带有公章或法人签字的委托证明
					</view>

					<view class="pic-list">
						<view class="pic-item" v-if="file_img">
							<image :src="file_img" mode=""></image>
						</view>
						<view class="pic-item add" @click="uploadFile">
							<image src="/static/images/my/add.png" mode=""></image>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="next sure" @click="entrustAuth">
				提交
			</view>
		</view>
	</view>
</template>

<script>
	import {
		entrustAuth,
		uploadImg
	} from "../../config/api.js"
	export default {
		data() {
			return {
				placeholderStyle: {
					'fontSize': '32rpx'
				},
				file_img: '',
				form: {
					company: {
						name: "",
						credit_id: ""
					},
					end_at: "",
					remark: "",
					contract_file: []
				}
			}
		},
		onLoad(options) {
		},
		methods: {
			bindDateChange(e) {
				this.form.end_at = uni.$u.timeFormat(e.detail.value, 'yyyy-mm-dd');
			},

			async entrustAuth() {
				let params = {
					...this.form
				}

				const {
					status_code,
					data,
					message
				} = await entrustAuth(params)
				if (status_code == 200) {
					uni.navigateBack()
					return uni.$u.toast(message || '成功')
				} else {
					return uni.$u.toast(message || '失败')
				}
			},

			//上传附件
			uploadFile() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								if (result.status_code == 200) {
									self.file_img = result.data.url
									self.form.contract_file = [result.data.id]
								}
							}
						})
					}
				});
			},
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
	}
</style>
<style lang="less" scoped>
	.register-one {
		display: flex;
		flex-direction: column;
		padding-bottom: 178rpx;
	}

	.wrap {
		padding: 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			margin-bottom: 32rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}



				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}

					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}

				.fb {
					font-size: 32rpx;
				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}

			.pic {
				display: flex;
				flex-direction: column;
				margin-bottom: 32rpx;
				padding: 0 32rpx 32rpx 32rpx;

				.title {
					display: flex;
					align-items: center;

					&>text {
						font-weight: 600;
						font-size: 32rpx;
						color: #333333;
						margin-right: 16rpx;
					}

					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
					margin: 16rpx 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.pic-list {
					display: flex;
					flex-wrap: wrap;

					.pic-item {
						margin-right: 24rpx;
						background-color: #F5F5F7;
						border-radius: 24rpx;
						width: 200rpx;
						height: 180rpx;

						&:last-child {
							margin-right: 0;
						}

						image {
							width: 100%;
							height: 100%;
							border-radius: 24rpx;
						}
					}

					.add {
						display: flex;
						justify-content: center;
						align-items: center;

						&>image {
							width: 40rpx;
							height: 40rpx;
						}
					}
				}
			}
		}
	}

	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 24rpx;
		height: 64rpx;
		width: 260rpx;
		// margin-left: 32rpx;
		background: #4F8CF0;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
		border-radius: 8rpx;
	}

	.footer {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 600;
			font-size: 28rpx;
			height: 80rpx;
			width: 90%;
			border-radius: 16rpx;
		}

		.gray {
			background-color: #cccccc;
			color: #FFFFFF;
		}

		.sure {
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	}
</style>
