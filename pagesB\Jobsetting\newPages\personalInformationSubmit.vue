<template>
	<!-- 个人信息提交 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="form-container">
					<u-form labelWidth="120">
						<u-form-item label="我的千里马身份" borderBottom @click="identityShowPicker = true">
							<u-input v-model="identityText" inputAlign="right" placeholder="请选择千里马身份" readonly
								border="none"></u-input>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
						<u-form-item label="姓名" borderBottom>
							<u-input v-model="params.name" inputAlign="right" placeholder="请输入姓名"
								border="none"></u-input>
						</u-form-item>
						<u-form-item label="性别" borderBottom>
							<u-radio-group v-model="params.sex">
								<view class="sex-container">
									<u-radio label="男" name="1"></u-radio>
									<u-radio label="女" name="2"></u-radio>
								</view>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="当前求职类型" borderBottom>
							<u-checkbox-group v-model="params.valueCall">
								<view class="type-container">
									<u-checkbox v-for="v in checkboxColumns" :label="v.text" :name="v.key"></u-checkbox>
								</view>
							</u-checkbox-group>
						</u-form-item>
						<u-form-item label="当前求职状态" borderBottom @click="stateShowPicker = true">
							<u-input v-model="stateText" inputAlign="right" placeholder="请选择当前求职状态" readonly
								border="none"></u-input>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
						<u-form-item label="出生年月" borderBottom @click="timeShowPicker = true">
							<u-input v-model="params.date_of_birth" inputAlign="right" placeholder="请选择出生年月" readonly
								border="none"></u-input>
							<u-icon slot="right" name="arrow-right"></u-icon>
						</u-form-item>
						<u-form-item label="手机号" borderBottom>
							<u-input v-model="params.phone" inputAlign="right" placeholder="请输入手机号"
								border="none"></u-input>
						</u-form-item>
						<u-form-item label="微信号" borderBottom>
							<u-input v-model="params.vx_id" inputAlign="right" placeholder="请输入微信号"
								border="none"></u-input>
						</u-form-item>
						<u-form-item label="邮箱" rightIcon="arrow-right">
							<u-input v-model="params.mailbox" inputAlign="right" placeholder="请输入邮箱"
								border="none"></u-input>
						</u-form-item>
					</u-form>
				</view>
			</view>

			<u-picker :show="identityShowPicker" title="请选择千里马身份" :columns="[[
				{key: '1', text: '学生'},
				{key: '2', text: '职场人'},
			]]" closeOnClickOverlay @confirm="onIdentityPickerConfirm" @cancel="onIdentityPickerClose"
				@close="onIdentityPickerClose"></u-picker>

			<u-picker :show="stateShowPicker" title="当前求职状态" :columns="[pickerColumns]" closeOnClickOverlay
				@confirm="onStatePickerConfirm" @cancel="onStatePickerClose" @close="onStatePickerClose"></u-picker>

			<u-datetime-picker ref="timePicker" closeOnClickOverlay :show="timeShowPicker" v-model="timeTextModel"
				mode="year-month" @confirm="onTimePickerConfirm" @cancel="onTimePickerClose"
				@close="onTimePickerClose"></u-datetime-picker>

			<u-toast ref="toast"></u-toast>
		</scroll-view>

		<view class="btn-container">
			<view class="btn" @click="onSubmit">提交</view>
		</view>
	</view>
</template>

<script>
	import {
		submitMember
	} from '@/config';

	const studentCheckbox = [{
			key: '1',
			text: '实习'
		},
		{
			key: '2',
			text: '兼职'
		},
		{
			key: '3',
			text: '全职'
		},
	];
	const societyCheckbox = [{
			key: '2',
			text: '兼职'
		},
		{
			key: '3',
			text: '全职'
		},
	];
	const studentPicker = [{
		key: '1',
		text: '离校 · 随时到岗'
	}, {
		key: '2',
		text: '在校 · 月内到岗'
	}, {
		key: '3',
		text: '在校 · 考虑机会'
	}, {
		key: '4',
		text: '在校 · 暂不考虑'
	}];

	const societyPicker = [{
		key: '1',
		text: '离职 · 随时到岗'
	}, {
		key: '2',
		text: '在职 · 月内到岗'
	}, {
		key: '3',
		text: '在职 · 考虑机会'
	}, {
		key: '4',
		text: '在职 · 暂不考虑'
	}];

	export default {
		data() {
			return {
				identityText: null,
				identityShowPicker: false,

				stateText: null,
				stateShowPicker: false,

				timeTextModel: Number(new Date()),
				timeShowPicker: false,


				params: {
					identity: null,
					name: null,
					valueCall: [],
					student_status: null,
					society_status: null,
					date_of_birth: null,
					sex: '1',
					phone: null,
					vx_id: null,
					mailbox: null
				}
			};
		},
		computed: {
			checkboxColumns() {
				return this.params.identity === '1' ? studentCheckbox : societyCheckbox;
			},
			pickerColumns() {
				return this.params.identity === '1' ? studentPicker : societyPicker;
			},
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onReady() {
			this.$refs.timePicker.setFormatter(this.formatter);
		},
		onLoad(options) {
			console.log(options);
		},
		methods: {
			formatTimestampToYearMonth(timestamp) {
				const date = new Date(timestamp);
				const year = date.getFullYear();
				const month = (date.getMonth() + 1).toString().padStart(2, '0');
				return `${year}-${month}`;
			},
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			onIdentityPickerConfirm(event) {
				this.params.identity = event.value[0].key;
				this.identityText = event.value[0].text;
				this.params.student_status = null;
				this.params.society_status = null;
				this.stateText = null;
				this.onIdentityPickerClose();
			},
			onIdentityPickerClose() {
				this.identityShowPicker = false;
			},
			onStatePickerConfirm(event) {
				this.params[this.params.identity === '1' ? 'student_status' : 'society_status'] = event.value[0].key;
				this.stateText = event.value[0].text;
				this.onStatePickerClose();
			},
			onStatePickerClose() {
				this.stateShowPicker = false;
			},
			onTimePickerConfirm(event) {
				this.params.date_of_birth = this.formatTimestampToYearMonth(event.value);
				this.onTimePickerClose();
			},
			onTimePickerClose() {
				this.timeShowPicker = false;
			},

			async onSubmit() {
				const params = {
					...this.params,
					...this.userTypeParams
				};
				const res = await submitMember(params);
				if (res.status_code !== '200') return;
				this.$refs.toast.show({
					message: '添加成功',
					complete: () => {
						uni.$u.route({
							type: 'back'
						});
					}
				})
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;

		.btn-container {
			background-color: #FFFFFF;
			padding-block-start: 24rpx;
			padding-inline: 64rpx;
			padding-block-end: calc(24rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			border-start-start-radius: 16rpx;
			border-start-end-radius: 16rpx;
			box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);

			.btn {
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				border-radius: 16rpx;
				text-align: center;
				color: #FFFFFF;
				padding-block: 20rpx;
				font-size: 28rpx;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding: 32rpx;

				.form-container {
					background-color: #FFFFFF;
					padding-inline: 24rpx;
					border-radius: 24rpx;
					padding-block: 16rpx;

					.sex-container {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: flex-end;
						gap: 48rpx;
					}

					.type-container {
						width: 100%;
						display: flex;
						align-items: center;
						justify-content: flex-end;
						gap: 32rpx;
					}
				}
			}
		}
	}
</style>