<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="title">活动地址</view>
    </view>

    <view class="search-box">
      <u--input
        v-model="keyword"
        :show-action="false"
        placeholder="请输入活动地址"
        height="80rpx"
        maxlength="46"
        shape="square"
        :clearabled="false"
        border="bottom"
        bgColor="#fff"
        searchIconSize="0"
        @change="changeInput"
      ></u--input>
      <text class="count">{{ keyword.length }}/46</text>
    </view>

    <!-- 公司列表 -->
    <view class="company-list">
      <view
        class="company-item"
        v-for="(item, index) in list"
        :key="index"
        @click="selectAddress(item, index)"
      >
        <view class="radio-wrapper">
          <u-radio
            :name="index"
            :checked="selectedIndex === index"
            shape="circle"
            activeColor="#4F8CF0"
          ></u-radio>
        </view>
        <view class="company-info">
          <view class="company-name">
            {{ item.name }}
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next" @click="next"> 确认 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      keyword: "",
      selectedIndex: -1,
      list: [
        { name: "郑州市金水区万正商务大厦8层814", type: "1" },
        { name: "郑州大学礼堂", type: "2" },
        { name: "郑州大学礼堂", type: "3" },
      ],
    };
  },

  onLoad() {},

  methods: {
    changeInput(e) {
      console.log(e);
    },
    selectAddress(item, index) {
      this.selectedIndex = index;
    },
    next() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
  padding: 32rpx;
}

.header {
  margin-bottom: 32rpx;

  .title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 24rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

.search-box {
  position: relative;
  margin-bottom: 32rpx;

  .count {
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24rpx;
    color: #999;
  }
}

.company-list {
  .company-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx 0rpx;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;
    .radio-wrapper {
      margin-right: 20rpx;
    }
    .company-logo {
      width: 136rpx;
      height: 136rpx;
      background: #d9d9d9;
      border-radius: 8rpx;
      margin-right: 24rpx;
    }

    .company-info {
      flex: 1;

      .company-name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 4rpx;
        display: flex;
        align-items: center;

        .tag {
          font-size: 20rpx;
          color: #fff;
          background-color: #4f8cf0;
          padding: 4rpx 12rpx;
          border-radius: 4rpx;
          margin-left: 16rpx;
        }
      }

      .company-type {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 12rpx;
      }
      .tags {
        width: 176rpx;
        height: 50rpx;
        background: #f4f4f4;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-size: 24rpx;
        color: #777777;
        text-align: center;
        line-height: 50rpx;
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  border-radius: 24rpx 24rpx 0 0;

  .next {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    width: 90%;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
    margin-top: 24rpx;
  }
}
</style>
