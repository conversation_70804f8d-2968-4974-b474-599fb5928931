<template>
	<view class="container">
		<u-sticky>
			<view class="tabs-container">
				<u-tabs :current="tabsIndex" :list="tabsList" :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
					:inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }" @click="onTabsItemClick"></u-tabs>
			</view>
		</u-sticky>
		<view class="content">
			<template v-if="tabsItem == '1'">
				<view class="item_template1" v-for="v in jobList" :key="v.job.job_id" @click="onDetail(v)">
					<view class="item-start">
						<view class="title-box">
							<view class="title_1">{{v.job.job_info_name}}</view>
							<view class="title_3">
								{{tabs == '1'?'全职':'兼职'}}
							</view>
							<view class="title_2">
								{{v.salary.salary_min}}
							</view>
						</view>
						<view class="time">{{tabs == '1'?v.job.job_introduction:'25岁 | 男女不限 | 10人'}}</view>
						<view class="tags">
							<view class="tag" v-for="k in ['世界500强','上市公司','游戏大厂']" :key="k.id">{{k}}</view>
						</view>
						<view class="times" v-if="tabs == '2'">
							<text>面试时间：2025-03-28</text>
							<text>到岗时间：2025-04-28</text>
						</view>
						<view class="inn">
							<view class="left">
								<view class="age">
									学创北京｜A轮｜0-20人
								</view>
								<view class="add">
									<img src="/static/images/index/jyindex/address_icon.png" alt="" />
									<text>北京北京市昌平区1号院阳光小区</text>
									<view class="km">
										距12.1km
									</view>
								</view>
							</view>
						</view>
						<u-line></u-line>
						<view class="item-end">
							<view class="btn-box">
								<view class="btn_1">
									立即报名
								</view>
								<view class="btn_2">
									聊聊呗
								</view>
							</view>
							<view class="end">
								<view class="name">
									<image src="/static/images/index/jyindex/baozhang_icon.png" mode=""></image>
									<text>平台保障</text>
								</view>
								<view class="name">
									<image src="/static/images/index/jyindex/zipin_icon.png" mode=""></image>
									<text>平台自聘</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</template>
			<template v-if="tabsItem == '2'">
				<view class="item-type-1-warp" v-for="v in jobActiveList" :key="v.job_active.id">
					<view class="title">{{v.job_active.title}}</view>
					<view class="item-type-1" @click="onDetail(v.job_active.id)">
						<view class="item-start">
							<view class="title">{{v.job_active.title}}</view>
							<view class="sub-title">500/天</view>
						</view>
						<view class="item-center">
							{{v.job_active.intro}}
						</view>
						<view class="item-end">
							<view>上岗时间：{{v.job_active.start}}</view>
							<view>报名截止时间：{{v.job_active.end}}</view>
						</view>
					</view>
					<view class="btm"><img src="/static/images/index/jyindex/job_icon.png"
							alt="" /><text>本次活动提供岗位：22个，参与的高校及乡镇街道共2个</text></view>
				</view>
			</template>
			<template v-if="tabsItem == '3'">
				<view class="item-type-2">
					<view class="item-start">
						<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode=""></image>
						<view class="start-info">
							<text class="title">xxx公司宣讲会</text>
							<text class="time">讲师：王哈哈</text>
							<text class="time">提供岗位：22个</text>
							<text class="time">河南大学礼堂</text>
							<view class="tag">进行中</view>
						</view>
					</view>
					<view class="item-end">
						<text class="time">·52367人参加</text>
						<text class="time">2025.09.02 08:00:00开始</text>
						<!-- <text class="detail">查看详情</text> -->
					</view>
				</view>
			</template>
			<template v-if="tabsItem == '4'">
				<view class="item-type-2">
					<view class="item-start">
						<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png" mode=""></image>
						<view class="start-info">
							<text class="title">xxx公司宣讲会</text>
							<text class="time">2022年12月12日</text>
							<text class="time">已有200家企业入驻</text>
							<text class="time">地址：郑州市金水区万正商务大厦</text>
							<view class="tag">进行中</view>
						</view>
					</view>
					<view class="item-end">
						<text class="time">·52367人参加</text>
						<text class="time">9月15日 08:00:00开始</text>
					</view>
				</view>
			</template>
			<template v-if="tabsItem == '5'">
				<view class="item-type-2">
					<view class="item-start">
						<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png" mode=""></image>
						<view class="start-info">
							<text class="title">xxx公司宣讲会</text>
							<text class="time">2022年12月12日</text>
							<text class="time">已有200家企业入驻</text>
							<view class="tag">进行中</view>
						</view>
					</view>
					<view class="item-end">
						<text class="time">·52367人参加</text>
						<text class="time">9月15日 08:00:00开始</text>
					</view>
				</view>
			</template>
			<template v-if="tabsItem == '6'">
				<view class="item_template2" v-for="v in headunterList" :key="v.id" @click="onDetail(v.id)">
					<view class="item-start">
						<image class="image" :src="v.member_info.image.path_url"></image>
						<view class="info">
							<view class="info-start">
								<view class="name-box">
									<text class="name">{{v.name}}</text>
									<view class="identity"><img
											src="https://api-test.zhaopinbei.com/storage/uploads/images/6HUdgbieZV8fIGjOkQFKOdeIXL9b5HKuUFihXlCF.png"
											alt="" /><text>{{v.member.certification_status_name}}</text></view>
								</view>
								<view class="tip-ide">
									主推: 电子/互联网/IT服务、大数据
								</view>
								<view class="detal-box" v-if="v.remark">{{v.remark}}</view>
								<view class="tags">
									<view class="tag">5年经验</view>
								</view>
							</view>
							<view class="info-end">聊聊呗</view>
						</view>
					</view>
					<u-line></u-line>
					<view class="item-end">
						<view class="evaluation">
							<text class="name">专业能力</text>
							<text class="number">4.4</text>
						</view>
						<view class="evaluation">
							<text class="name">专业能力</text>
							<text class="number">4.4</text>
						</view>
						<view class="evaluation">
							<text class="name">专业能力</text>
							<text class="number">4.4</text>
						</view>
					</view>
				</view>
			</template>
			<template v-if="tabsItem == '7'">
				<view class="item_template2" style="background:#fff" v-for="v in headunterList" :key="v.id"
					@click="onDetail(v.id)">
					<view class="item-start">
						<image class="image" :src="v.member_info.image.path_url"></image>
						<view class="info">
							<view class="info-start">
								<view class="name-box">
									<text class="name">{{v.name}}</text>
									<!-- <view class="identity">{{v.member.certification_status_name}}</view> -->
								</view>
								<view class="detal-box">高效百科 | 招聘信息</view>
								<view class="tags">
									<view class="tag2">北京</view>
								</view>
							</view>
							<view class="info-end-btn" @click="goSchool">立即查看</view>
						</view>
					</view>

				</view>
			</template>
		</view>
	</view>
</template>

<script>
	import {
		getJobPublishList,
		getJobActiveList,
		getHeadunterUserList
	} from '@/config';

	export default {
		data() {
			return {
				tabsIndex: 0,
				tabsList: [{
						key: '1',
						name: '学生简历',
					},
					{
						key: '2',
						name: '特推招聘',
					},
					{
						key: '3',
						name: '宣讲会',
					}, {
						key: '4',
						name: '招聘会',
					}, {
						key: '5',
						name: '岗位预定',
					}, {
						key: '6',
						name: '就业管家',
					}, {
						key: '7',
						name: '学校',
					},
				],
				headunterList: [],
				headunterParams: {
					limit: 20,
					page: 1
				},
				tabsItem: '1',
				jobList: [],
				jobActiveList: [],
				params: {
					limit: 20,
					page: 1,
					work_type_id: '2',
					job_type: '2'
				},
				activeParams: {
					limit: 20,
					page: 1,
					type: '1',
				},
				isLoading: false,
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type]?.id || 61,
				};
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetJobPublishList();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetJobPublishList();
			this.onGetJobActiveList();
			this.onGetHeadunterUserList();
		},
		methods: {
			onDetail(v) {
				// uni.$u.route({
				// 	url: `/pagesA/details/memberJobDetails`,
				// 	params: {
				// 		id: v.job.job_id,
				// 	}
				// })
			},
			onTabsItemClick(res) {
				this.tabsItem = res.key;
			},
			onScrollGetList() {
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetJobPublishList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getJobPublishList(params);
				if (res.status_code !== '200') return;
				this.jobList = [...this.jobList, ...res.data.jobs_list?.data];
				this.isLoading = false;
			},
			async onGetJobActiveList() {
				const params = {
					...this.activeParams,
					...this.userTypeParams
				}
				const res = await getJobActiveList(params);
				if (res.status_code !== '200') return;
				this.jobActiveList = [...this.jobActiveList, ...res.data.data];
				this.more = res.data.more;
				this.isLoading = false;
			},
			async onGetHeadunterUserList() {
				const params = {
					...this.headunterParams,
					// ...this.userTypeParams
					member_id: 300,
				}
				const res = await getHeadunterUserList(params);
				if (res.status_code !== '200') return;
				this.headunterList = [...this.headunterList, ...res.data.data];
				this.more = res.data.more;
				this.isLoading = false;
			},
			goSchool() {
				uni.navigateTo({
					url: '/pagesC/schoolIntro/index'
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.tabs-container {
			// padding-inline: 16rpx;
			background-color: #f5f5f7;
			margin-left: -11px;
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;


			// padding-inline: 32rpx;
			.item_template1 {
				background-color: #ffffff;
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				justify-content: space-between;
				// flex-direction: column;
				gap: 24rpx;


				.avater {
					width: 48rpx;
					height: 48rpx;
				}

				.item-end {
					.btn-box {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 12rpx;

						view {
							width: 299rpx;
							height: 56rpx;
							background: #F1F6FE;
							border-radius: 16rpx 16rpx 16rpx 16rpx;
							font-size: 24rpx;
							color: #FFFFFF;
							text-align: center;
							line-height: 56rpx;

						}

						.btn_1 {
							color: #4F8CF0;
						}

						.btn_2 {
							background: linear-gradient(92deg, #4F8CF0 0%, #0061FF 100%);
						}
					}

					.end {
						display: flex;
						justify-content: space-between;
						align-items: center;
						gap: 28rpx;
					}

					.name {
						color: #666;
						font-size: 20rpx;
						display: flex;
						align-items: center;

						image {
							width: 48rpx;
							height: 48rpx;
							margin-right: 4rpx;
						}
					}
				}

				.item-start {
					width: 100%;
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.time {
						color: #666;
						font-size: 24rpx;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
					}

					.type-text {
						font-size: 28rpx;
						color: #041024;
					}

					.title-box {
						display: flex;
						align-items: center;
						justify-content: space-between;
						gap: 12rpx;
						font-size: 32rpx;
						color: #333333;

						.title-inner {
							display: flex;
							align-items: center;
							gap: 12rpx;

							.tit-inn {
								width: 100%;
								display: flex;
								align-items: center;
								justify-content: space-between;
								gap: 12rpx;

								.age {
									font-size: 24rpx;
									color: #666666;
								}
							}
						}

						.title_1 {
							// flex: 1;
							width: 160rpx;

							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.title_2 {
							color: #f98a14;
							white-space: nowrap;
							margin-left: 100rpx;
						}

						.title_3 {
							color: #4F8CF0;
							white-space: nowrap;
							margin-left: 100rpx;
						}
					}

					.tags {
						flex: 1;
						display: flex;
						align-items: center;
						gap: 16rpx;
						overflow-x: auto;
						white-space: nowrap;

						.tag {
							flex-shrink: 0;
							padding: 12rpx;
							border-radius: 8rpx;
							color: #666666;
							font-size: 22rpx;
							background-color: #F6F6F6;
						}
					}

					.times {
						display: flex;
						align-items: center;
						justify-content: space-between;
						font-size: 24rpx;
						color: #666666;
					}

					.inn {
						display: flex;
						align-items: center;
						justify-content: space-between;
						font-size: 24rpx;
						color: #666666;

						.left {
							display: flex;
							flex-direction: column;

							.add {
								display: flex;
								align-items: center;
								margin-top: 14rpx;

								text {
									width: 250rpx;
									overflow: hidden;
									display: -webkit-box;
									-webkit-box-orient: vertical;
									-webkit-line-clamp: 1;
								}

								img {
									width: 32rpx;
									height: 32rpx;
									margin-right: 8rpx;
								}

								.km {
									height: 36rpx;
									padding: 0 8rpx;
									line-height: 36rpx;
									background: #F6F6F6;
									border-radius: 50rpx 50rpx 50rpx 50rpx;
								}
							}
						}

						.right {
							width: 144rpx;
							height: 56rpx;
							background: linear-gradient(132deg, #4F8CF0 0%, #0061FF 100%);
							border-radius: 8rpx 8rpx 8rpx 8rpx;
							font-size: 24rpx;
							color: #FFFFFF;
							text-align: center;
							line-height: 56rpx;
						}
					}
				}
			}

			.item_template2 {
				padding: 32rpx;
				background: linear-gradient(151deg, #4F8CF0 -50%, #FFFFFF 40%);
				border-radius: 24rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-end {
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 24rpx;

					.evaluation {
						display: flex;
						align-items: center;
						gap: 8rpx;
						font-size: 24rpx;

						.name {
							color: #666666;
						}

						.number {
							color: #DD4E41;
						}
					}
				}

				.item-start {
					display: flex;
					align-items: flex-start;
					gap: 20rpx;

					.image {
						width: 96rpx;
						height: 96rpx;
						border-radius: 999rpx;
					}


					.info {
						flex: 1;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.info-end {
							background-color: #4F8CF0;
							color: #FFFFFF;
							font-size: 24rpx;
							padding-block: 8rpx;
							padding-inline: 12rpx;
							border-radius: 8rpx;
						}

						.info-end-btn {
							border: 2rpx solid #4F8CF0;
							color: #4F8CF0;
							font-size: 24rpx;
							padding-block: 8rpx;
							padding-inline: 12rpx;
							border-radius: 8rpx;
						}

						.info-start {
							flex: 1;
							display: flex;
							flex-direction: column;
							gap: 16rpx;

							.tags {
								display: flex;
								align-items: center;
								gap: 16rpx;

								.tag {
									background: rgba(188, 213, 255, 0.2);
									border-radius: 8rpx;
									color: #7286A6;
									font-size: 24rpx;
									padding-block: 4rpx;
									padding-inline: 12rpx;
								}
								.tag2 {
									background: #F5F5F5;
									border-radius: 8rpx;
									color: #666666;
									font-size: 20rpx;
									padding-block: 4rpx;
									padding-inline: 12rpx;
								}
							}

							.tip-ide {
								font-size: 24rpx;
								color: #999999;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 1;
							}

							.detal-box {
								flex: 1;
								color: #999999;
								font-size: 24rpx;
								overflow: hidden;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 1;
							}

							.name-box {
								display: flex;
								align-items: center;
								gap: 32rpx;

								.name {
									color: #333333;
									font-size: 28rpx;
								}

								.identity {
									color: #4787F0;
									font-size: 20rpx;
									padding-block: 6rpx;
									padding-inline: 8rpx;
									background: rgba(79, 140, 240, 0.2);
									border-radius: 8rpx;
									display: flex;
									align-items: center;
									gap: 4rpx;

									img {
										width: 32rpx;
										height: 32rpx;
									}
								}
							}
						}
					}
				}
			}

			.item-type-2 {
				display: flex;
				flex-direction: column;
				align-items: center;

				.item-end {
					width: 80%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 24rpx;
					background: linear-gradient(90deg, #F5FCFC 0%, #FCFBFA 100%);
					border-end-start-radius: 16rpx;
					border-end-end-radius: 16rpx;
					font-size: 24rpx;

					.time {
						color: #333333;
					}

					.detail {
						color: #4F8CF0;
					}
				}

				.item-start {
					width: 100%;
					box-sizing: border-box;
					display: flex;
					align-items: center;
					gap: 24rpx;
					background-color: #FFFFFF;
					border-radius: 16rpx;
					padding: 32rpx;

					.start-info {
						display: flex;
						flex-direction: column;
						gap: 16rpx;
						align-items: flex-start;

						.title {
							color: #333333;
							font-size: 32rpx;
						}

						.time {
							color: #999999;
							font-size: 28rpx;
						}

						.tag {
							padding-block: 8rpx;
							padding-inline: 16rpx;
							background: rgba(87, 213, 28, 0.1);
							border-radius: 8rpx;
							color: #57D51C;
							font-size: 24rpx;
						}
					}

					.image {
						width: 196rpx;
						height: 196rpx;
					}
				}
			}

			.item-type-1-warp {
				.btm {
					display: flex;
					align-items: center;
					font-size: 24rpx;

					image {
						width: 32rpx;
						height: 32rpx;
						margin-right: 16rpx;
					}
				}
			}

			.item-type-1 {
				display: flex;
				flex-direction: column;
				gap: 24rpx;
				background-color: #FFFFFF;
				border-radius: 24rpx;
				padding: 32rpx;
				margin: 24rpx 0;

				.item-end {
					display: flex;
					align-items: center;
					justify-content: space-between;
					color: #333333;
					font-size: 24rpx;
				}

				.item-center {
					color: #666666;
					font-size: 28rpx;
				}

				.item-start {
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 32rpx;

					.title {
						color: #333333;
					}

					.sub-title {
						color: #F98A14;
					}
				}
			}

			.item {
				background-color: #ffffff;
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				justify-content: space-between;
				// flex-direction: column;
				gap: 24rpx;

				.right {
					image {
						width: 176rpx;
						height: 176rpx;
						border-radius: 24rpx 24rpx 24rpx 24rpx;
					}
				}

				.avater {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
				}

				.item-end {
					display: flex;
					align-items: center;
					gap: 28rpx;


					.name {
						color: #333333;
						font-size: 24rpx;
					}

					.address {
						margin-inline-start: auto;
						color: #999999;
						font-size: 24rpx;
					}
				}

				.item-start {
					width: 400rpx;
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.time {
						color: #999999;
						font-size: 24rpx;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
					}

					.type-text {
						font-size: 28rpx;
						color: #041024;
					}

					.title-box {
						display: flex;
						align-items: center;
						// justify-content: space-between;
						gap: 12rpx;
						font-size: 24rpx;
						color: #666666;

						.title_1 {
							// flex: 1;
							width: 160rpx;
							color: #777777;
							font-size: 24rpx;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.title_2 {
							color: #f98a14;
							font-size: 36rpx;
							white-space: nowrap;
						}
					}
				}
			}
		}
	}
</style>
