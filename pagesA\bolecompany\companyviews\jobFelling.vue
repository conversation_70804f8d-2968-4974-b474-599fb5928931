<template>
	<view class="container">
		<view class="container-con">
			<!-- 标题输入框 -->
			<view class="titleBox">
				<view class="title-input">
					<input type="text" placeholder="添加标题更规范呢（选填）" />
				</view>
				<!-- 问题部分 -->
				<view class="question-section">
					<text class="question-hash"></text>
					<input type="text" class="question-plc" placeholder="#入职时的面试流程怎么样？" />
				</view>
				<!-- 添加照片区域 -->
				<view class="add-photo">
				</view>
				<view class="photo-placeholder" @click="openImagePicker">
					<text class="icon-plus">+</text>
					<text class="photo-text">添加照片</text>
				</view>
			</view>
			<!-- 话题标签区域 -->
			<view class="topic-tags">
				<scroll-view class="scroll-view_H" scroll-x="true" @scroll="scroll">
					<view class="flex-tags">
						<view class="tag" v-for="(tag, index) in tags" :key="index">
							#{{tag}}
							<text class="tag-hash"></text>
							<!-- <text class="tag-text"></text> -->
						</view>
					</view>
				</scroll-view>
				<view class="eyeInput">
					<img :src="eye" class="eye" alt="" srcset="" />
					<input type="text" placeholder="看看别人怎么写" class="eye-plc" placeholder-style="font-size:28rpx;"
						v-model="newTag" />
					<text class="word-count">{{wordCount}}/1600</text>
					<!-- <scroll-view class="scroll-view_H" scroll-x="true" @scroll="scroll">
					</scroll-view> -->
				</view>
				<!-- <button @click="addTag">添加</button> -->
			</view>
		</view>
		<view class="footer">
			<!-- 保存按钮 -->
			<view class="footview">
				<view class="footer-card">
					<button class="add-button" @click="saveData">保存</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/*import eye from '../../static/images/companyDetail/眼睛icon.png'*/
	export default {
		data() {
			return {
				tags: ['全部话题', '吃住行方便吗?', '试用期几个月?', '试用期几个月?'],
				newTag: '',
				wordCount: 0,
				eye:'https://api-test.zhaopinbei.com/storage/uploads/images/DtDwOsy3R4axhGAnyw2PgXbActV6TG19ceJO4XdE.png'
			};
		},
		methods: {
			openImagePicker() {
				// 这里调用uniapp的图片选择器api，例如uni.chooseImage
				uni.chooseImage({
					count: 1, // 可选择图片数量
					success: (res) => {
						console.log('选择的图片路径', res.tempFilePaths);
					},
				});
			},
			addTag() {
				if (this.newTag.trim() !== '') {
					this.tags.push(this.newTag);
					this.newTag = '';
				}
			},
			saveData() {
				// 这里添加保存数据到服务器等逻辑，例如调用uni.request
				console.log('执行保存操作');
				uni.showToast({
					title: '保存成功',
					icon: 'none'
				});
			},
		},
	};
</script>

<style>
	.container {

		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.container-con {
		padding: 0px 32rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		flex: 1;
	}

	.title-input input {
		width: 100%;
		height: 44rpx;
		/* border-bottom: 1rpx solid #ccc; */
		color: rgba(153, 153, 153, 1);
		font-size: 32rpx;
	}

	.question-section {
		display: flex;
		align-items: center;
	}

	.question-hash {
		margin-right: 10rpx;
		color: #999;
	}

	.question-section input {
		width: 100%;
		height: 40rpx;
	}

	.add-photo {
		margin-top: 40rpx;

	}

	.question-plc::placeholder {
		margin-top: 20rpx;
		font-size: 28rpx;
	}

	.photo-placeholder {
		width: 200rpx;
		height: 200rpx;
		border: 1rpx dashed #ccc;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		background: rgba(244, 244, 244, 1);
	}

	.icon-plus {
		font-size: 60rpx;
		color: #ccc;
	}

	.photo-text {
		color: #999;
		font-size: 24rpx;
	}

	.topic-tags {
		margin-top: 40rpx;
		display: flex;
		flex-wrap: wrap;
		align-items: center;


	}

	.scroll-view_H {
		width: 100vw;
		white-space: nowrap;
	}


	.tag {
		background-color: rgba(79, 140, 240, 1);
		color: white;
		border-radius: 54rpx;
		padding: 12rpx 24rpx;
		margin-right: 24rpx;
		margin-bottom: 24rpx;
		display: flex;
		align-items: center;
		height: 58rpx;
		font-size: 24rpx;

	}

	.tag-hash {
		margin-right: 5rpx;
	}

	.topic-tags input {
		width: 100%;
		height: 40rpx;
		/* min-height: 102px; */
		overflow-wrap: break-word;
		white-space: normal;
	}




	.word-count {
		color: #999;
		margin-left: auto;
		font-size: 28rpx;
	}

	.save-button {
		width: 100%;
		height: 80rpx;
		background-color: #1890ff;
		color: white;
		border: none;
		border-radius: 5rpx;
		margin-top: 40rpx;
	}

	.question-plc {
		color: rgba(4, 16, 36, 1) !important;
		font-size: 28rpx;
		line-height: 16.41px !important;
	}

	input[type="text"] {
		width: 100%;
		/* 根据实际需求调整宽度 */
		height: auto;
		/* 高度自适应，以便容纳换行后的内容 */
		min-height: 102rpx;
		/* 设置最小高度，保证初始有一定高度显示 */
		overflow-wrap: break-word;
		/* 允许在单词内换行 */
		white-space: normal;
		/* 取消强制不换行，恢复正常换行规则 */
	}

	.eyeInput {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		height: 20px;
	}

	.eye {
		width: 40rpx;
		height: 40rpx;
	}

	.add-button {
		width: 570rpx;
		height: 80rpx;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 28rpx;
	}

	.footer {
		/* height: 196rpx; */
		display: flex;
		justify-content: center;
	}

	.footer-card {
		margin-top: 32rpx;
		background-color: white;
		border-radius: 16rpx;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 1.1);
		/* 卡片阴影 */
		padding: 24rpx;
		/* height: 196rpx; */
		/* width: 100vw; */
		/* margin-left: -28px; */
	}

	.eye-plc {
		/* margin-top: 19px !important; */
		min-height: 40rpx !important;
		/* margin-bottom: 39px; */
		/* margin-top: 19px; */
	}

	.eye-plc::placeholder {
		font-size: 28rpx;
		/* line-height: 32rpx; */
		/* height: 40rpx; */
	}

	.footview {
		width: 100vw;
		/* margin-left: -10px; */
		position: relative;
	}

	.flex-tags {
		display: flex;
		overflow-x: auto;
	}
</style>
