<template>
	<view class="companyBox">
		<view class="cardCompany">
			<view class="card-container">
				<view class="card">
					<view class="card-content">
						<view class="card-title">填写公司基本信息有什么用？</view>
						<view class="card-desc">详尽的公司信息呈现，能有效增强求职者信任，带来更多有效沟通。展示卡片的具体内容。</view>
					</view>
				</view>
			</view>
			<view class="card-list">
				<view class="card-list-item" v-for="(item, index) in list" :key="index" @click="routerCompany(index)">
					<view style="margin-left: 4.2%; font-size: 28rpx;color: rgba(51, 51, 51, 1);">{{ item.title }}
					</view>
					<view v-if="item.status === 'completed'" class="card-imgblock">
						<span>
							{{ item.progress }}/{{ item.total }}
						</span>
						<image :src="rightIdentifier" class="cart-listimg" mode="" style="width: 28rpx; height: 28rpx;">
						</image>
					</view>
					<view v-else class="card-imgblock">
						<span>
							{{ item.status }}
						</span>
						<image :src="rightIdentifier" class="cart-listimg" mode="" style="width: 28rpx; height: 28rpx;">
						</image>
					</view>
				</view>
			</view>
			<view class="card-notice" @click="showNotice">
				<text style="font-size: 24rpx;">
					请认真阅读并遵守《
					<text style="color: rgba(79, 140, 240, 1); font-size: 24rpx;">公司信息填写须知</text>
					》
				</text>
			</view>
		</view>
		<Notice v-show="isModalVisible" :visible="isModalVisible" @close="isModalVisible = false"
			@confirm="handleConfirm"></Notice>
	</view>
</template>

<script>
	import Notice from './companyNotice/Notice.vue'
	export default {
		components: {
			Notice
		},
		data() {
			return {

				rightIdentifier:'https://api-test.zhaopinbei.com/storage/uploads/images/r2ac8QppIj7V5E5n2U6aClsLLI54U4cAl7bOfy5d.png',
				list: [{
						title: '公司基本信息',
						status: 'completed',
						progress: '6',
						total: '6',
						hasEditIcon: true
					},
					{
						title: '公司福利',
						status: 'completed',
						progress: '2',
						total: '2',
						hasEditIcon: true
					},
					{
						title: '公司介绍',
						status: '未完善',
						hasEditIcon: true
					},
					{
						title: '主营业务',
						status: 'completed',
						progress: '7',
						total: '10',
						hasEditIcon: true
					},
					{
						title: '公司相册',
						status: '未完善',
						hasEditIcon: true
					},
					{
						title: '人才发展',
						status: '',
						hasEditIcon: true
					},
					{
						title: '在职感受',
						status: '未完善',
						hasEditIcon: true
					},
					{
						title: '公司视频',
						status: '',
						hasEditIcon: true
					},
					{
						title: '产品介绍',
						status: '未完善',
						hasEditIcon: true
					},
					{
						title: '高管介绍',
						status: '未完善',
						hasEditIcon: true
					}
				],
				carefulnoticehide: false,
				isModalVisible: false
			};

		},
		onLoad() {

		},
		methods: {
			showNotice() {
				// this.carefulnoticehide = !this.carefulnoticehide
				this.isModalVisible = true;
				// console.log(this.carefulnoticehide);
			},
			handleConfirm() {
				// 这里可以添加确认后的逻辑，比如提示用户已确认等
				this.isModalVisible = false;
			},
			routerCompany(id) {
				console.log(id);
				// selectedId.value = id;
				switch (id) {
					case 0:
						uni.navigateTo({
							url: "/pagesA/bolecompany/companyviews/boleIntroduction"
						})
						break;
					case 1:
						uni.navigateTo({
							url: "/pagesA/bolecompany/companyviews/companyHoursAndBenefits"
						})
						break;
					case 2:
						uni.navigateTo({
							url: "/pagesA/bolecompany/companyviews/boleEditor"
						})
						break;
					case 3:
						uni.navigateTo({
							url: '/pagesA/bolecompany/companyviews/business'
						})
						break;
					case 4:
						uni.navigateTo({
							url: '/pagesA/bolecompany/companyviews/companyPhoto'
						})
						break;
					case 5:
						uni.navigateTo({
							url: '/pagesA/bolecompany/companyviews/talentDevelopment'
						})
						break;
					case 6:
						uni.navigateTo({
							url: "/pagesA/bolecompany/companyviews/jobFelling"
						})
						break;
					case 7:
						uni.navigateTo({
							url: '/pagesA/bolecompany/companyviews/companyVideo'
						})
						break;

					case 8:
						uni.navigateTo({
							url: "/pagesA/bolecompany/companyviews/production"
						})
						break;
					case 9:
						uni.navigateTo({
							url: '/pagesA/bolecompany/companyviews/Topmanagerment'
						})
						break;
					default:
						break;
				}
				console.log(id);
			}
		},
	}
</script>

<style>
	page {
		background-color: transparent;
	}

	.companyBox {
		overflow: hidden;
		height: 100vh;
	}

	.cardCompany {
		width: 100%;
		height: 100%;
		background-color: rgba(245, 245, 247, 1);
	}

	.card-container {
		width: 100%;
		padding: 16px;
		display: flex;
		justfy-content: center;
		align-item: center;
		padding: 0px;
	}

	.card {
		width: 686rpx;
		height: 194rpx;
		position: relative;
		margin: 2% auto;
		background-color: white;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
		box-shadow: none;
		background: linear-gradient(to bottom, rgba(242, 248, 255, 1), rgba(255, 255, 255, 1));
	}

	/* .card::before {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 1px;
		left: 1px;
		content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	} */

	/* 	.card::after {
		position: absolute;
		width: 100%;
		height: 100%;
		bottom: 1px;
		right: 1px;
		content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	} */

	.card-content {
		padding: 20rpx;
	}

	.card-title {
		width: 100%;
		height: 34rpx;
		font-size: 24rpx;
		font-weight: bold;
		color: rgba(51, 51, 51, 1);
		margin-top: 20rpx;
		margin-bottom: 12rpx;
		line: height 28.12rpx;
	}

	.card-desc {
		width: 100%;
		height: 28rpx;
		font-size: 20rpx;
		color: #8D9AAA;
		line: height 11.74rpx;
	}

	.card-list {
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: none;
		border-radius: 8px 8px 8px 8px;
		overflow: hidden;
		width: 91%;
		height: 70%;
		margin: 0 auto;
	}

	.card-list-item {
		display: flex;
		width: 100%;
		height: 11%;
		justify-content: space-between;
		align-items: center;
		background-color: rgba(255, 255, 255, 1);
	}

	.card-imgblock {
		width: 17.5%;
		height: 2%;
		font-size: 24rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		align-self: center;
		color: rgba(153, 153, 153, 1);
		margin-right: 4.2%;
	}

	.card-notice {
		width: 100%;
		height: 2.5%;
		font-size: 24rpx;
		color: rgba(119, 119, 119, 1);
		margin-left: 5%;
		margin-top: 1.7%;
	}
</style>
