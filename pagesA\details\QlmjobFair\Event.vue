<template>
	<view class="box">
		<view class="activity-detail" style="background-color: rgba(255, 255, 255, 1);">
			<view class="header">
				<text class="title">活动名称介绍</text>
				<view v-if="isRunning" class="status-tag">
					<text class="status-text">进行中</text>
				</view>
			</view>
			<view class="create-time">
				<span>
					创建时间：
				</span>
				<span>2025 年 4 月 1 日</span>
			</view>

			<view class="company-info">
				<img src="" alt="" class="company-info-img" />
				<text>****公司已加入本次招聘会，欢迎各位同学踊跃参加！</text>
			</view>
			<view class="section">
				<text class="section-title">活动简述</text>
				<text class="section-content">
					xxx 学校（以下简称“软通动力”）是中国领先的软件与信息技术服务商，致力于成为具有全球影响力的数字技术服务领导企业，企业数字化转型可信赖合作伙伴。2005
					年，公司成立于北京，立足中国，服务全球市场。目前，在全球 40 余个城市设有近百个分支机构和超过 20 个全球交付中心，员工 90000 余人。
				</text>
			</view>
			<!-- 类似结构添加活动时间、覆盖范围等模块 -->
			<view class="section">
				<text class="section-title">活动时间</text>
				<view class="time-range">
					<text class="firstTime">2025 年 4 月 1 日 08:00:00</text>
					<text>----></text>
					<text class="LastTime">2025 年 1 月 5 日 18:00:00</text>
				</view>
			</view>
			<view class="section">
				<text class="section-title">活动覆盖范围</text>
				<text class="section-content">
					河南学校、河南学校、河南学校、河南学校等 23 个学校
				</text>
			</view>
			<!-- 继续添加活动组织单位、协助单位等模块 -->
			<view class="section">
				<text class="section-title">活动组织单位</text>
				<text class="section-content">
					某某公司
				</text>
			</view>
			<view class="section">
				<text class="section-title">协助单位</text>
				<text class="section-content">
					某某公司、某某公司、某某公司
				</text>
			</view>
			<view class="section">
				<text class="section-title">活动执行管家名单</text>
				<text class="section-content">
					王哈哈、王哈哈、王哈哈、王哈哈、王哈哈
				</text>
			</view>
			<view class="sectionBox" @click="jionShow">

			</view>
			<view class="footer">
				<text class="contact-info">活动咨询、投诉电话 11111111</text>
				<text class="chat-link">聊聊呗</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isRunning: true // 假设活动进行中，实际可从接口获取状态
			};
		},
		methods: {
			jionShow() {
				uni.redirectTo({
					url: '/pagesA/details/QlmjobFairTalk/jionList'
				})
			}
		}
	};
</script>

<style scoped>
	.box {
		background: rgba(245, 245, 247, 1);
		padding-top: 32rpx;
	}

	.activity-detail {
		padding: 30rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 24rpx 24rpx 0rpx 0rpx;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
	}

	.status-tag {
		background-color: #4caf50;
		color: white;
		padding: 10rpx 20rpx;
		border-radius: 10rpx;
	}

	.status-text {
		font-size: 28rpx;
	}

	.create-time {
		font-size: 22rpx;
		color: rgba(153, 153, 153, 1);
		width: 293rpx;
		height: 30rpx;
		margin-bottom: 30rpx;
	}

	.company-info {
		font-size: 24rpx;
		margin-bottom: 30rpx;
		color: rgba(51, 51, 51, 1);
		background: rgba(245, 245, 247, 1);
		border-radius: 8rpx;
		width: 686rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
		justify-content: space-evenly;
	}

	.company-info-img {
		width: 31.6rpx;
		height: 24rpx;
	}

	.section {
		margin-bottom: 40rpx;
		display: flex;
		flex-direction: column;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.section-content {
		font-size: 28rpx;
		line-height: 1.5;
		color: rgba(102, 102, 102, 1);
	}

	.time-range {
		display: flex;
		align-items: center;
		font-size: 22rpx;
		justify-content: space-evenly;
	}

	.firstTime {
		color: rgba(71, 189, 96, 1);
		background-color: rgba(245, 245, 247, 1);
		border-radius: 8rpx;
	}

	.LastTime {
		color: rgba(254, 77, 79, 1);
		background-color: rgba(245, 245, 247, 1);
		border-radius: 8rpx;
	}

	.sectionBox {
		width: 134rpx;
		height: 134rpx;
		background-color: rgba(217, 217, 217, 1);
		margin-bottom: 32rpx;
	}

	.footer {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		color: rgba(51, 51, 51, 1);
	}

	.contact-info {
		flex: 1;
	}
</style>