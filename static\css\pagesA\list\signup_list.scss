page {
    background: #f5f5f7;
}

.header {
    .tabs {
        background: #FFFFFF;
    }
    
    .filters {
        display: flex;
        align-items: center;
        padding: 22rpx 32rpx;
        
        .filter {
            display: flex;
            align-items: center;
            height: 48rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding: 0 12rpx;
            margin-right: 12rpx;
            border-radius: 8rpx;
            
            image {
                width: 24rpx;
                height: 24rpx;
            }
        }
        
        .search-wrap {
            flex: 1;
        }
    }
}

.list {
    padding: 0 32rpx;
}

.filterss {
        display: flex;
        margin-bottom: 32rpx;
		margin-left: 32rpx;
        
        .filter {
            display: flex;
            align-items: center;
            height: 48rpx;
            background-color: #FFFFFF;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding: 0 12rpx;
            margin-right: 12rpx;
            border-radius: 8rpx;
            
            image {
                width: 24rpx;
                height: 24rpx;
            }
        }
    }

.no-data-container {
    display: flex;
    flex-direction: column;
    justify-content: center; /* 垂直居中 */
    align-items: center; /* 水平居中 */
    height: 100%; /* 使容器占满整个视口高度 */
	margin-top: 100rpx;
	image {
		width: 60%;
		height: 350rpx;
	}
}

.button {
    background-color: #4f8cf0;
    padding: 8rpx 12rpx;
    border-radius: 10rpx;
    width: 30%;
    color: white;
    text-align: center; /* 文字居中 */
    margin-top: 20rpx; /* 根据需要调整按钮与图片之间的间距 */
}


.custom-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}

.dialog-content {
    padding: 40rpx;
    background-color: #fff;
    border-radius: 20rpx;
    width: 600rpx;
    text-align: center;
}

.dialog-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 40rpx;
    color: #333;
}

.dialog-buttons {
    display: flex;
    justify-content: space-between;
}

.dialog-buttons button {
    flex: 1;
    margin: 0 20rpx;
    // padding: 12rpx 0;
	width: 300rpx;
    background-color: #4f8cf0;
    color: white;
    border-radius: 10rpx;
    text-align: center;
    font-size: 28rpx;
}

.dialog-buttons button:active {
    background-color: #3a6bb0;
}