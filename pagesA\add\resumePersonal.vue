<template>
	<view class="">
		<view class="wrap">
			<view class="inp">
				<view class="avatar">
					<view class="inp-item">
						<view class="title">
							头像<text class="star">*</text>
						</view>
						<view class="in lab">
							请选择系统默认头像
						</view>
					</view>
					<view class="pic">
						<image src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode=""></image>
					</view>
				</view>
				<u-line color="#F5F5F5" width="100%"></u-line>
				<view class="inp-item">
					<view class="title">
						昵称<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入内容" clearable placeholderClass="placeholderClass" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>
				<u-line color="#F5F5F5" width="100%"></u-line>
				<view class="inp-item">
					<view class="title">
						民族<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入内容" clearable placeholderClass="placeholderClass" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>
				<u-line color="#F5F5F5" width="100%"></u-line>
				<view class="inp-item">
					<view class="title">
						性别<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeSex" :value="sexIndex" :range="sex">
							<view class="d-picker" :style="{color:sexIndex==0?'#c0c4cc':'#303133'}">{{sex[sexIndex]}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				<u-line color="#F5F5F5" width="100%"></u-line>
				<view class="inp-item">
					<view class="title">
						年龄<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入内容" clearable placeholderClass="placeholderClass" border="none" v-model="value" @change="change"></u--input>
					</view>
				</view>
				<u-line color="#F5F5F5" width="100%"></u-line>
				<view class="inp-item">
					<view class="title">
						最高学历<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeEdu" :value="eduIndex" :range="eduList" range-key="name">
							<view class="d-picker" :style="{color:eduIndex==0?'#c0c4cc':'#303133'}">{{eduList[eduIndex]['name']}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				<u-line color="#F5F5F5" width="100%"></u-line>
				<view class="inp-item">
					<view class="title">
						工作状态<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeWorkStatus" :value="workStatusIndex" :range="workStatusList" range-key="name">
							<view class="d-picker" :style="{color:workStatusIndex==0?'#c0c4cc':'#303133'}">{{workStatusList[workStatusIndex]['name']}}</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<view class="btn">
			保存
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return{
				sex: ['男', '女'],
				sexIndex: 0,
				eduList: [{
					name: '请选择',
					value: 0
				}, {
					name: '初中及以下',
					value: 1
				}, {
					name: '高中',
					value: 2
				}, {
					name: '大专',
					value: 3
				}, {
					name: '本科',
					value: 4
				}, {
					name: '研究生及以上',
					value: 5
				}],
				eduIndex:0,
				workStatusList:[
					{
						name: '请选择',
						value: 0
					}, {
						name: '在职',
						value: 1
					}, {
						name: '离职',
						value: 2
					}
				],
				workStatusIndex:0
			}
		},
		methods:{
			changeEdu(e){
				this.eduIndex = e.detail.value
			},
			changeSex(e){
				this.sexIndex = e.detail.value
			},
			changeWorkStatus(e){
				this.workStatusIndex = e.detail.value
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.wrap {
		padding: 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			padding: 0 30rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					// padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				// padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					// border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					// ::v-deep picker {
					// 	display: flex;
					// 	flex-direction: column;
					// 	flex: 1;
					// 	height: 88rpx;

					// 	.d-picker {
					// 		display: flex;
					// 		align-items: center;
					// 		// width: 60vw;
					// 		height: 88rpx;
					// 	}
					// }

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}
					::v-deep picker{
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;
						.d-picker{
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}



				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.btn {
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 90%;
		height: 100rpx;
		bottom: 32rpx;
		left: 5%;
		background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
		border-radius: 16rpx;
		font-weight: 600;
		font-size: 34rpx;
		color: #FFFFFF;
	}
</style>
