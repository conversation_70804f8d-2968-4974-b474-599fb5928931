<template>
	<view class="warp">
		<view class="top">
			<view class="arrow">
				<u-icon name="arrow-left" color="#fff" size="20" @click="back"></u-icon>
			</view>
			<view class="top-title">
				隐私保护
			</view>
			<view class="top-title1">
				我们深知个人信息对您的重要性，我们将按法律法规要求，采取严格的安全保护措施，全力保护您的隐私安全
			</view>
		</view>
		<view class="inner">
			<view class="box" v-for="(item,index) in list" :key="index">
				<view class="title">
					{{item.title}}
				</view>
				<view class="tip">
					{{item.tips}}
				</view>
				<view class="box-top">

				</view>
				<view class="cont" v-for="it in item.contents">
					<view class="req">

					</view>
					<view class="cont-inn">
						{{it}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					title: '完善的屏蔽机制',
					tips: '通过完善的屏蔽机制来保护你的隐私安全',
					contents: ['子公司识别智能屏蔽机制', '关联公司识别屏蔽机制', '同企业邮箱关联公司识别屏蔽机制', '同企业邮箱关联公司识别屏蔽机制']
				}, {
					title: '智能识别，自动屏蔽',
					tips: '会在以下一些情况下自动屏蔽当前公司',
					contents: ['首次注册时自动屏蔽', '添加/编辑工作经历时自动屏蔽', '关联公司入驻时自动屏蔽', '手动屏蔽时，自动补全关联屏蔽']
				}, {
					title: '在有未屏蔽风险时提醒你',
					tips: '在有发现还未识别屏蔽公司时会提醒你',
					contents: ['长时间未使用招聘呗，再次使用时，提醒你', '加入企业后，重新开始求职时，提醒你']
				}, {
					title: '如何保证你的隐私安全',
					tips: '在屏蔽公司后，我们会在以下情况下保护你的隐私安全',
					contents: ['屏蔽后，不会出现在对方的推荐列表上', '屏蔽后，对方不能看到你的在线简历', '屏蔽后，你查看对方公司的职位时，不会出现在对方的「看过我」列表中',
						'屏蔽后，你将无法和屏蔽企业下的伯乐发起沟通，也无法收到来自屏蔽企业下的伯乐的任何消息'
					]
				}]
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.warp {
		width: 100vh;
		background: #F5F5F7;
		position: relative;
		top: 0;

		.top {
			height: 464rpx;
			width: 686rpx;
			z-index: -1;
			background: linear-gradient(360deg, #F5F5F7 0%, #9DD3FF 18%, #4F75F0 100%);
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			// filter: blur(3.5999999046325684px);
			position: absolute;
			top: 0;
			padding: 0 32rpx;

			.arrow {
				margin-top: 116rpx;
			}

			.top-title {
				font-size: 32rpx;
				margin-top: 52rpx;
				color: #FFFFFF;
			}

			.top-title1 {
				margin-top: 28rpx;
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}

		.inner {
			width: 686rpx;
			position: absolute;
			top: 380rpx;
			left: 32rpx;

			.box {
				width: 638rpx;
				border-radius: 24rpx 24rpx 24rpx 24rpx;
				padding: 32rpx 24rpx;
				margin-bottom: 40rpx;
				background: #FFFFFF;

				.title {
					font-size: 28rpx;
					color: #333333;
					margin-bottom: 16rpx;
				}

				.tip {
					font-size: 24rpx;
					color: #999999;
					margin-bottom: 24rpx;
				}

				.box-top {
					width: 638rpx;
					height: 152rpx;
					background: #D9D9D9;
					border-radius: 24rpx 24rpx 0 0;
					margin-bottom: 24rpx;
				}

				.cont {
					display: flex;
					align-items: center;
					margin-bottom: 16rpx;

					.req {
						width: 10rpx;
						height: 10rpx;
						background: #99C0FF;
						border-radius: 5rpx;
						margin-right: 16rpx;
					}

					.cont-inn {
						font-size: 24rpx;
						color: #666666;
					}
				}
			}
		}
	}
</style>