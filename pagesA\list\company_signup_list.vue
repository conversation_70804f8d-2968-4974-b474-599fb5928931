<template>
  <view class="">
    <u-sticky bgColor="#F5F5F5">
      <view class="header">
        <view class="tabs">
          <u-tabs
            lineWidth="20"
            lineColor="#4F8CF0"
            :activeStyle="{
              color: '#4F8CF0',
              fontWeight: 'bold',
              transform: 'scale(1.05)',
            }"
            :inactiveStyle="{
              color: '#999999',
              transform: 'scale(1)',
            }"
            :list="tabs"
            :current="tabIndex"
            @click="changeTab"
          ></u-tabs>
        </view>
        <view class="search-wrap">
          <u-search
            placeholder="请输入职位名称或千里马姓名"
            bgColor="#FFFFFF"
            :showAction="false"
            v-model="keyword"
          ></u-search>
        </view>
        <view class="filters">
          <view class="" style="display: flex;align-items: center;">
            <view class="filter">
              <picker
                @change="changeStatus"
                :value="statusIndex"
                :range="statusList"
                range-key="name"
              >
                <view class="d-picker">{{
                  statusList[statusIndex]["name"]
                }}</view>
              </picker>
              <image src="/static/images/index/down.png" mode=""></image>
            </view>
            <view class="filter" v-if="tabIndex != 0">
              <picker
                @change="changeStatus1"
                :value="statusIndex1"
                :range="statusList1"
                range-key="name"
              >
                <view class="d-picker">{{
                  statusList1[statusIndex1]["name"]
                }}</view>
              </picker>
              <image src="/static/images/index/down.png" mode=""></image>
            </view>
          </view>
          <view class="filter1" @click="showHua = true"> 通知话术 </view>
        </view>
        <!-- <view class="filters">
                    <view class="filter">
                        期望薪资
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>

                </view> -->
      </view>
    </u-sticky>
    <view class="list">
      <companySignupItemC
        v-for="(item, index) in 5"
        :key="index"
        :item="item"
        :index="index"
        @apply="apply"
        @look="look"
      ></companySignupItemC>
    </view>
    <u-popup
      :show="showReason"
      :round="10"
      bgColor="#F5F5F5"
      mode="center"
      closeOnClickOverlay
      @close="closeReason"
      @open="openReason"
    >
      <view class="credit" style="width: 70vw">
        <view class="title comp"> 拒绝原因 </view>
        <view class="content">
          <u--textarea v-model="reason" placeholder="请输入内容"></u--textarea>
        </view>

        <view class="agree" @click="sureCancel"> 确定 </view>
      </view>
    </u-popup>
    <u-popup
      :show="show"
      mode="center"
      closeOnClickOverlay
      @close="show = false"
      :round="12"
    >
      <view class="jianli" style="width: 80vw">
        <view class="resume-header">
          <text class="resume-title">简历</text>
          <text class="close-icon" @click="show = false">×</text>
        </view>
        <view class="jianli_box">
          <view class="user-info">
            <image
              class="avatar"
              src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"
            ></image>
            <view class="user-details">
              <text class="user-name">王哈哈</text>
              <text class="user-basic-info"
                >22岁 | 24应届生 | 本科 | 离校-随时到岗</text
              >
              <text class="user-job-info">设计（行业） | 5-7K | 郑州</text>
            </view>
          </view>

          <view class="section">
            <view class="section-header">
              <view class="blue-bar"></view>
              <text class="section-title">个人优势</text>
            </view>
            <text class="section-content"
              >个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势个人优势</text
            >
          </view>

          <view class="section">
            <view class="section-header">
              <view class="blue-bar"></view>
              <text class="section-title">期望职位</text>
            </view>
            <text class="section-content">全职职位</text>
            <text class="section-content">设计（行业） | 5-7K | 郑州</text>
          </view>

          <view class="section">
            <view class="section-header">
              <view class="blue-bar"></view>
              <text class="section-title">工作/实习经历</text>
            </view>
            <view class="experience">
              <text class="company-name">北京学创联盟（北京）有限公司</text>
              <text class="job-title">UI设计师</text>
              <text class="job-description"
                >内容：内容内容内容内容内容内容</text
              >
            </view>
          </view>

          <view class="section">
            <view class="section-header">
              <view class="blue-bar"></view>
              <text class="section-title">项目经历</text>
            </view>
            <view class="experience">
              <text class="company-name">北京学创联盟（北京）有限公司</text>
              <text class="job-title">UI设计师</text>
              <text class="job-description"
                >内容：内容内容内容内容内容内容</text
              >
            </view>
          </view>

          <view class="section">
            <view class="section-header">
              <view class="blue-bar"></view>
              <text class="section-title">教育经历</text>
            </view>
            <view class="education">
              <view class="school-info">
                <image
                  class="school-avatar"
                  src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"
                ></image>
                <view class="school-details">
                  <view class="school-row">
                    <text class="school-name">郑州大学</text>
                    <text class="school-year">2021-2025</text>
                  </view>
                  <text class="major">会计 本科</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="footer">
          <button class="audit-btn" @click="confirmApply">审核</button>
        </view>
      </view>
    </u-popup>
    <!-- 审核 -->
    <u-popup
      :show="showRejectModal1"
      mode="center"
      closeOnClickOverlay
      @close="showRejectModal1 = false"
      :round="12"
    >
      <view class="modal-content" style="width: 80vw">
        <view class="modal-header">
          <text class="modal-title">审核</text>
          <text class="modal-close" @click="showRejectModal1 = false">×</text>
        </view>

        <view class="reason-section">
          <view class="reason-content">
            <u--textarea placeholder="请填写审核意见" clearable></u--textarea>
          </view>
        </view>
        <view class="radio">
          <u-radio-group placement="row" v-model="radioValue">
            <u-radio shape="square" label="再审"> </u-radio>
            <u-radio shape="square" label="通过"> </u-radio>
            <u-radio shape="square" label="驳回"> </u-radio>
          </u-radio-group>
        </view>
      </view>

      <view class="modal-footer">
        <text class="confirm-btn" @click="showRejectModal1 = false"
          >我知道了</text
        >
      </view>
    </u-popup>
    <!-- 话术 -->
    <u-popup
      :show="showHua"
      mode="center"
      closeOnClickOverlay
      @close="showHua = false"
      :round="12"
    >
      <view class="modal-content" style="width: 80vw">
        <view class="modal-header">
          <text class="modal-title">通知话术</text>
          <text class="modal-close" @click="showRejectModal1 = false">×</text>
        </view>
        <view class="reason-section">
          <view class="item" v-for="item in huashuList" :key="item">
            <view class="radoi-icon"> </view>
            <view class="hua_txt">
              {{ item }}
            </view>
            <img
              src="https://api-test.zhaopinbei.com/storage/uploads/images/Wz3F5mIhpCnNUDtiTGcfKYoxn8EZ1ozOtRbdj5bU.png"
              alt=""
            />
            <img
              src="https://api-test.zhaopinbei.com/storage/uploads/images/MGMq4BMDov0HxRPfln7BlxfTipoFMR2Q9yYV8j5Q.png"
              alt=""
            />
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <text class="cancel-btn" @click="showHua = false">取消</text>
        <text class="confirm-btn1" @click="showHua = false">发送</text>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getCompanySignupList, cancelCompanySignup } from "../../config/api.js";

import { headhunterJobActiveReportList } from "../../config/headhunterList_api";

import CompanySignupItemC from "../components/companySignupItemC.vue";

export default {
  components: {
    CompanySignupItemC,
  },

  data() {
    return {
      reason: "",
      showReason: false,
      currentItem: {},
      page: 1,
      limit: 10,
      status: "loadmore",
      more: false,
      tabIndex: 0,
      tabs: [
        {
          name: "全部",
        },
        {
          name: "网上招聘",
        },
        {
          name: "特推招聘",
        },
        {
          name: "宣讲会",
        },
        {
          name: "招聘会",
        },
        {
          name: "岗位预定",
        },
        {
          name: "就业管家推荐",
        },
      ],
      list: [],
      radioValue: "再审",
      statusList: [
        {
          value: "0",
          name: "全部",
        },
        {
          value: "1",
          name: "待审核",
        },
        {
          value: "2",
          name: "再审",
        },
        {
          value: "3",
          name: "反馈同意",
        },
        {
          value: "4",
          name: "反馈不同意",
        },
      ],
      statusIndex: 0,
      statusList1: [
        {
          value: "0",
          name: "全部",
        },
      ],
      statusIndex1: 0,
      show: false,
      showRejectModal1: false,
      showHua: false,
      huashuList: [
        "感谢您投递[职位]岗位。经过评估，您的经历与当前岗位需求暂未完全匹配，祝您您早日找到满意的工作！",
        "恭喜您通过[职位]的初筛！现邀请您于[时间]到[地点]/通过[线下平台]参加面试。请确认是否可如期参加，或回复告知方便的时间。",
        "感谢您参与[职位]的面试。您的[具体优点，如“专业能力/沟通技巧”]给我们留下深刻印象，但经过综合评估，我们决定暂不推进后续流程。未来若有更匹配的岗位将第一时间联系您。再次感谢您的时间与信任！",
      ],
    };
  },
  onLoad() {
    this.getList();
  },
  //触底加载更多
  onReachBottom() {
    if (this.more) {
      this.status = "loading";
      this.page++;
      this.getList();
    } else {
      this.status = "nomore";
    }
  },
  methods: {
    inviteInterview(item) {
      this.currentItem = item;
      console.log(this.currentItem);
      uni.navigateTo({
        url:
          "/pagesA/add/inviteInterview?report_id=" +
          item.id +
          "&job_id=" +
          item.job_id +
          "&member_id=" +
          item.member_id,
      });
    },
    changeStatus(e) {
      this.statusIndex = e.detail.value;
    },
    changeStatus1(e) {
      this.statusIndex1 = e.detail.value;
    },
    openReason() {
      this.showReason = true;
    },

    closeReason() {
      this.reason = "";
      this.showReason = false;
    },
    cancelSignup(item) {
      this.showReason = true;
      this.currentItem = item;
    },
    changeTab(op) {
      this.page = 1;
      this.list = [];
      this.tabIndex = op.index;
      this.getList();
    },

    getList() {
      // this.page = 1;
      // this.list = [];
      // 需要增加参数进行切换work_type_id?所对应的123？

      console.log("TAB", this.tabIndex);
      this.getCompanySignupList();

      //   switch (this.tabIndex) {
      //     case 0:
      //       this.getCompanySignupList();
      //       break;
      //     case 1:
      //       this.headhunterJobActiveReportList();
      //       break;
      //   }
    },
    async sureCancel() {
      let params = {
        report_id: this.currentItem.id,
        cancel_reason: this.reason,
      };
      const { status_code, data, message } = await cancelCompanySignup(params);
      this.closeReason();
      uni.$u.toast(message);
      if (status_code == 200) {
        this.getCompanySignupList();
      }
    },
    async getCompanySignupList() {
      let params = {
        page: this.page,
        limit: this.limit,
        report_status: "",
      };
      const { status_code, data, message } = await getCompanySignupList(params);
      if (status_code == 200) {
        this.list = this.list.concat(data.data);
        // 返回false代表没有下一页
        this.more = data.more;
        this.status = this.more ? "loadmore" : "nomore";
        console.log("记录：", this.list);
      }
    },
    async headhunterJobActiveReportList() {
      let params = {
        page: this.page,
        limit: this.limit,
        report_status: "",
      };
      const { status_code, data, message } =
        await headhunterJobActiveReportList(params);
      if (status_code == 200) {
        this.list = this.list.concat(data.data);
        // 返回false代表没有下一页
        this.more = data.more;
        this.status = this.more ? "loadmore" : "nomore";
        console.log("记录：", this.list);
      }
    },
    look() {
      this.show = true;
    },
    apply() {
      this.showRejectModal1 = true;
    },
    confirmApply() {
      this.show = false;
      this.showRejectModal1 = true;
    },
  },
};
</script>
<style>
page {
  background: #f5f5f7;
}
</style>
<style lang="less" scoped>
.header {
  margin-bottom: 24rpx;
  .tabs {
    background: #ffffff;
    border-radius: 0 0 24rpx 24rpx;
  }

  .search-wrap {
    padding: 0 32rpx;
    margin: 22rpx 0;
  }

  .filters {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 32rpx;
    padding: 0 32rpx;
    padding-bottom: 24rpx;
    .filter {
      display: flex;
      align-items: center;
      height: 48rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #333333;
      padding: 0 16rpx;
      margin-right: 12rpx;
      border-radius: 8rpx;
      background: #ffffff;

      image {
        width: 24rpx;
        height: 24rpx;
      }
    }
    .filter1 {
      display: flex;
      align-items: center;
      height: 48rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #fff;
      padding: 0 16rpx;
      border-radius: 8rpx;
      background: #4f8cf0;
    }

    .search-wrap {
      flex: 1;
    }
  }
}

.list {
  padding: 0 32rpx;
}

.credit {
  display: flex;
  flex-direction: column;
  padding: 40rpx 48rpx;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .tip {
      image {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

  .comp {
    justify-content: center;
  }

  .content {
    display: flex;
    flex-direction: column;
    margin-top: 40rpx;

    .sub-title {
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 28rpx;
      color: #000000;
    }
  }

  .agree {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 44rpx;
    height: 88rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #ffffff;
    background: #4f8cf0;
    margin-top: 32rpx;
  }
}
.jianli {
  background-color: #fff;
  border-radius: 12px;
  padding: 30rpx 30rpx 0 30rpx;

  .resume-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20rpx;

    .resume-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }

    .close-icon {
      font-size: 40rpx;
      color: #999;
    }
  }
  .jianli_box {
    max-height: 50vh;
    overflow-y: auto;
  }

  .user-info {
    display: flex;
    padding: 20rpx 0;

    .avatar {
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      margin-right: 24rpx;
    }

    .user-details {
      display: flex;
      flex-direction: column;

      .user-name {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 10rpx;
      }

      .user-basic-info,
      .user-job-info {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 6rpx;
      }
    }
  }

  .section {
    margin: 20rpx 0;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .blue-bar {
        width: 6rpx;
        height: 30rpx;
        background-color: #4f8cf0;
        margin-right: 16rpx;
        border-radius: 6rpx;
      }

      .section-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
      }
    }

    .section-content {
      font-size: 24rpx;
      color: #333;
      line-height: 1.5;
      display: block;
      margin-bottom: 10rpx;
    }

    .experience {
      margin-bottom: 20rpx;

      .company-name {
        font-size: 24rpx;
        color: #333;
        display: block;
        margin-bottom: 6rpx;
      }

      .job-title {
        font-size: 24rpx;
        color: #333;
        display: block;
        margin-bottom: 6rpx;
      }

      .job-description {
        font-size: 24rpx;
        color: #333;
        display: block;
      }
    }

    .education {
      .school-info {
        display: flex;
        align-items: center;

        .school-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
        }

        .school-details {
          .school-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 32rpx;
            width: 100%;

            .school-name {
              font-size: 28rpx;
              color: #333;
            }

            .school-year {
              font-size: 24rpx;
              color: #999;
            }
          }

          .major {
            font-size: 24rpx;
            color: #333;
          }
        }
      }
    }
  }

  .footer {
    float: right;
    margin-top: 30rpx;

    .audit-btn {
      width: 128rpx;
      height: 68rpx;
      background-color: #4f8cf0;
      color: #fff;
      font-size: 32rpx;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.reject-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.modal-header {
  position: relative;
  padding: 30rpx 20rpx;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
}

.modal-body {
  padding: 0 20rpx;
}

.info-row {
  display: flex;
  border: 1px solid #f0f0f0;
}

.info-label {
  width: 30%;
  height: 80rpx;
  line-height: 80rpx;
  color: #666;
  font-size: 28rpx;
  border-right: 1px solid #f0f0f0;
  text-align: center;
}

.info-value {
  width: 70%;
  height: 80rpx;
  line-height: 80rpx;
  padding-left: 16rpx;
  color: #333;
  font-size: 28rpx;
}

.reason-section {
  padding: 24rpx 24rpx;
  border-radius: 12px;
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20rpx;
    margin-bottom: 54rpx;
    .radoi-icon {
      width: 12rpx;
      height: 12rpx;
      background: #d9d9d9;
      border-radius: 6rpx;
    }
    .hua_txt {
      font-size: 28rpx;
      color: #333333;
      width: 370rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
    }
    img {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.reason-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.reason-content {
  font-size: 28rpx;
  color: #333;
}
.radio {
  padding: 0 24rpx;
}
.modal-footer {
  padding: 0rpx 20rpx;
  display: flex;
  justify-content: flex-end;
}

.confirm-btn {
  color: #4f8cf0;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}
.confirm-btn1 {
  width: 128rpx;
  height: 68rpx;
  background: #4f8cf0;
  border-radius: 16rpx;
  color: #fff;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cancel-btn {
  width: 128rpx;
  height: 68rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  border: 2rpx solid #999999;
  color: #999999;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}
::v-deep .u-radio-group {
  gap: 40rpx;
}
</style>
