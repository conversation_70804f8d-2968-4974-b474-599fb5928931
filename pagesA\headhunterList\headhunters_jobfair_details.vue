<template>
	<view class="home-index">
		<view class="swiper">
			<image src="https://api-test.zhaopinbei.com/images/swiper1.png" mode=""></image>
		</view>

		<view class="wrap">
			<view class="active-info">
				<view class="title">
					<view class="name">活动名称活动名称活动名称活</view>
					<view class="status ing">进行中</view>
				</view>
				<view class="time">创建时间：2022年10月23日</view>
				<view class="notice">
					<u-notice-bar bgColor="#F5F5F7" color="#333333" :text="text1"></u-notice-bar>
				</view>
			</view>

			<view class="sub-wrap">
				<view class="title">活动信息</view>
				<view class="desc">
					活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动信息活动限定140字·
				</view>
			</view>

			<view class="sub-wrap">
				<view class="title">活动时间</view>
				<view class="times">
					<view class="time">2022年12月31日 08:00:00</view>
					<view class="line">----</view>
					<view class="time">2022年12月31日 08:00:00</view>
				</view>
			</view>

			<view class="sub-wrap">
				<view class="title">活动地点</view>
				<view class="pos">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png" mode=""></image>
					上饶信州区中科数创园4楼
				</view>
				<view class="map">
					<map style="width: 100%; height: 300rpx" :latitude="latitude" :longitude="longitude" :markers="covers"></map>
				</view>
			</view>
		</view>

		<view class="footer">
			<view class="btns">
				<view class="btn zx">咨询主办方</view>
				<!-- <view class="btn agree" @click="signup">
					立即报名
				</view> -->
				<view class="btn agree" @click="pubJob">发布职位</view>
				<!-- <view class="btn over">
					已结束
				</view> -->
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			text1: '*****公司已加入本次招聘会，欢迎各位同学踊跃参加！',
			latitude: 39.909,
			longitude: 116.39742,
			covers: [
				{
					latitude: 39.909,
					longitude: 116.39742,
					iconPath: '../../../static/location.png',
				},
				{
					latitude: 39.9,
					longitude: 116.39,
					iconPath: '../../../static/location.png',
				},
			],
		};
	},
	methods: {
		//立即报名
		signup() {
			uni.navigateTo({
				url: '/pagesA/add/activeSignup',
			});
		},
		//报名以后可以发布职位
		pubJob() {
			uni.navigateTo({
				url: '/pagesA/add/pubJobOne',
			});
		},
	},
};
</script>

<style lang="less" scoped>
.home-index {
	padding: 0 0 170rpx 0;
}

.swiper {
	image {
		width: 100%;
		height: 376rpx;
	}
}

.wrap {
	position: relative;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
	padding: 24rpx 32rpx;
	border-radius: 24rpx;
	margin-top: -60rpx;
	z-index: 100;

	.active-info {
		display: flex;
		flex-direction: column;

		.title {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.name {
				font-weight: 600;
				font-size: 40rpx;
				color: #333333;
			}

			.status {
				font-weight: 600;
				font-size: 28rpx;
			}

			.ing {
				color: #57d51c;
			}

			.wks {
				color: #f9ad14;
			}
		}

		.time {
			font-weight: 400;
			font-size: 22rpx;
			color: #999999;
			margin: 16rpx 0;
		}
	}

	.sub-wrap {
		display: flex;
		flex-direction: column;
		margin-top: 32rpx;

		.title {
			font-weight: 600;
			font-size: 32rpx;
			color: #333333;
		}

		.desc {
			font-weight: 400;
			font-size: 28rpx;
			color: #666666;
			margin-top: 16rpx;
		}

		.times {
			display: flex;
			align-items: center;
			margin-top: 16rpx;

			.time {
				display: flex;
				flex: 1;
				align-items: center;
				justify-content: center;
				height: 50rpx;
				background: #f1f6fe;
				font-weight: 500;
				font-size: 24rpx;
				color: #4f8cf0;
				border-radius: 8rpx;
			}

			.line {
				color: #4f8cf0;
			}
		}

		.pos {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 28rpx;
			color: #666666;
			margin: 16rpx 0;

			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 16rpx;
			}
		}
	}
}

.footer {
	position: fixed;
	display: flex;
	justify-content: center;
	align-items: center;
	height: 140rpx;
	width: 100%;
	left: 0;
	bottom: 0;
	background: #ffffff;
	font-weight: 600;
	font-size: 28rpx;
	z-index: 1000;

	.btns {
		display: flex;
		width: 90%;

		.btn {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 1;
			height: 80rpx;
			background: #f5f5f7;
			color: #333333;
			border-radius: 16rpx;

			&:first-child {
				margin-right: 20rpx;
			}
		}

		.agree {
			background: #4f8cf0;
			color: #ffffff;
		}

		.zx {
			color: #4f8cf0;
			background: rgba(79, 140, 240, 0.1);
		}

		.over {
			background: #cccccc;
			color: #ffffff;
		}
	}
}
</style>
