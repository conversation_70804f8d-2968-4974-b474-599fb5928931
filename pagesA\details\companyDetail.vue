<template>
	<view class="content" :style="{
			backgroundImage: 'url(https://api-test.zhaopinbei.com/images/compBg1.png)',
			backgroundSize: 'cover',
			backgroundPosition: 'center',
			paddingTop: (titletop + titleheight) * 2 + 'rpx !important',
			paddingBottom: titleheight * 2 + 'rpx !important',
		}">
		<!-- 头部 -->
		<image class="back" :style="{ top: titletop + titleheight + 30 + 'rpx' }"
			src="https://api-test.zhaopinbei.com/storage/uploads/images/AiaBYluONHS4nW6LN71MddmwfH7ZeVlcTQxwRNoC.png" @click="back()"></image>
		<view class="topCon">
			<view class="onetent">
				<view class="name">
					<image :src="details.company_info.logo.path_url" alt="" />
					<text style="width: 90%;">
						{{ details.name }}
					</text>
				</view>
				<!-- <u-icon name="star-fill" color="#FFFFFF"></u-icon> -->
				<view v-if="roleType != 'company'">
					<view class="like" v-if="details.but.collect_status == 1" @click="collectEnterprise">
						<image
							src="https://api-test.zhaopinbei.com/storage/uploads/images/uqfD4Nhvcv44j1jjCYstmhdrcLEl8Zmc40HH7ygH.png"
							alt="" />
						关注
					</view>
					<view class="like" v-else @click="cancelCollectEnterprise">
						<image
							src="https://api-test.zhaopinbei.com/storage/uploads/images/uqfD4Nhvcv44j1jjCYstmhdrcLEl8Zmc40HH7ygH.png"
							alt="" />
						已关注
					</view>
				</view>
			</view>
			<view class="twotent">
				{{ details.company_info.financing_type_name }}
				| {{ details.company_info.size_type_name }} | {{ details.short_name }}
			</view>
			<view class="threetent">
				<!-- certification_status -->
				<view class="">
					<view class="tent" v-if="item.certification_status == 1">
						<image
							src="https://api-test.zhaopinbei.com/storage/uploads/images/MkUtY3RRbrtJf4AZsLAWfLgbeT9fKgiLlTsBwFl2.png"
							alt="" />
						已认证
					</view>
					<view class="tent" v-else>
						<image
							src="https://api-test.zhaopinbei.com/storage/uploads/images/MkUtY3RRbrtJf4AZsLAWfLgbeT9fKgiLlTsBwFl2.png"
							alt="" />
						已认证
					</view>
				</view>
				<view class="tent">
					<image
						src="https://api-test.zhaopinbei.com/storage/uploads/images/mHLgqsecgxy8ri75OJchV6CvGoxmcXkflCk7YLfg.png"
						alt="" />
					入住时长：1年内
				</view>
			</view>
		</view>

		<!-- 企业福利 -->
		<view class="compMoney">
			<view class="title">公司福利</view>
			<view class="tags">
				<block>
					<view class="tags" v-for="(item, index) in details.tags" :key="index">
						<view class="tag">
							{{ item.title }}
						</view>
					</view>
				</block>
			</view>
		</view>

		<!-- 公司介绍 -->
		<view class="intro">
			<view class="title">公司介绍</view>
			<view class="text-content" :class="{ expanded: isExpanded }">
				{{ details.intro || '暂无介绍' }}
			</view>
			<!-- 展开按钮 -->
			<!-- v-if="showExpandButton" -->
			<view class="expand-button" @click="toggleExpand" v-if="showExpandButton">
				<text>{{ isExpanded ? '收起' : '展开' }}</text>
				<!-- <image :src="isExpanded ? '/static/up.png' : '/static/down.png'" mode="widthFix" class="icon"></image> -->
			</view>
		</view>
		<!-- 公司相册 -->
		<view class="picBox">
			<view class="title">公司相册</view>
			<!-- 设置scroll-view的高度，并移除white-space -->
			<scroll-view class="scroll-view_H" scroll-x="true">
				<!-- 添加flex容器，并设置flex-shrink -->
				<view class="scroll-content">
					<view class="bg-g" v-for="i in 10" :key="i">
						<!-- 添加默认图片或占位图 -->
						<image src="https://picsum.photos/584/268" mode="aspectFill"></image>
					</view>
				</view>
			</scroll-view>
		</view>

		<view class="picBox">
			<view class="title">公司视频</view>
			<scroll-view class="scroll-view_H" scroll-x="true" :style="{ height: scrollHeight + 'rpx' }">
				<view class="scroll-content">
					<view class="video-item" style="height: 268rpx;" v-for="(item, index) in videoList" :key="index">
						<video :src="item.src" controls object-fit="cover" class="video"
							@loadedmetadata="handleVideoLoaded" />
					</view>
				</view>
			</scroll-view>
		</view>
		<!-- 企业文化 -->
		<view class="culture PM">
			<text>企业文化</text>
			<view class="main">
				<view class="maintext">
					<text>企业使命：1111111111111111111111111111111111111111111111111111111111</text>
					<text>愿景：11111111111111111111111111111111111111111111111111111111111111</text>
					<text>战略：11111111111111111111111111111111111111111111111111111111111111111</text>
				</view>
				<view class="more">
					展开
					<uni-icons type="down" size="16" color="#CCCCCC"></uni-icons>
				</view>
			</view>
		</view>
		<!-- 主营业务 -->
		<view class="business">
			<text class="PM">主营业务</text>
			<view class="tag-view">
				<uni-tag v-for="item in businessList" :key="item.id" :inverted="true" :text="item.val" />
			</view>
		</view>
		<!-- 产品介绍 -->
		<view class="product">
			<text class="PM title">产品介绍</text>
			<scroll-view class="scroll-view_H" scroll-x="true">
				<view class="pro-ul">
					<view class="li" v-for="i in 6">
						<view class="img-box">
							<img src="" alt="" />
						</view>
						<view class="li-text">
							<text class="pro-title PM">网站应用</text>
							<text>APP、网站、游戏等产品介绍</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<!-- 人才发展 -->
		<view class="business" style="margin-bottom: 20rpx;">
			<text class="PM">人才发展</text>

			<view class="tag-view">
				<uni-tag v-for="item in talentList" :key="item.id" :inverted="true" :text="item.val" />
			</view>

		</view>
		<!-- 高管介绍 -->
		<view class="business">
			<text class="PM">人才发展</text>
			<view class="tag-view ">
				<uni-tag v-for="item in managerList" :key="item.id" :inverted="true" :text="item.val" />
			</view>
		</view>
		<!-- 公司地址 -->
		<view class="address">
			<view class="title">公司地址</view>
			<!-- <u-line color="#F5F5F7" length="100%"></u-line> -->

			<address-map :addresses="addresses"></address-map>
			<!-- <view class="Add">
				<view class="leftAdd">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/Iw0PgtDrcOPuvzdoB4j2PXGt0lrxaye9tFMxkYJY.png" alt="" />
					万正商务大厦
				</view>
				<view class="rightAdd">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/Iw0PgtDrcOPuvzdoB4j2PXGt0lrxaye9tFMxkYJY.png" alt="" />
					导航
				</view>
			</view> -->
		</view>



		<!-- 人事列表 -->
		<view class="hrList">
			<view class="title">人事列表</view>
			<scroll-view scroll-x class="list">
				<view class="oneHr" v-for="(item, index) in hrList" :key="index">
					<image :src="item.avatar" mode="aspectFill"></image>
					<view class="hrCon">
						<view class="name">{{ item.name }}</view>
						<view class="job">{{ item.job }}</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 公司职位 -->
		<view class="" style="padding: 24rpx">
			<view class="jobList">
				<view class="title">在招职位</view>
				<view class="title-right" @click="toCom()">
					<text>更多职位</text>
					<u-icon name="arrow-right" size="28rpx"></u-icon>
				</view>
			</view>
			<view class="threeList" v-for="(i, index) in jobList" :key="index">
				<view class="oneItem">
					<view class="top">
						<view class="">{{ i.title }}</view>
						<view style="color: #f98a14">{{ i.salary_info_str }}</view>
					</view>
					<view class="tags">
						<block>
							<view class="tags" v-for="(item, index) in i.tags" :key="index">
								<view class="tag">
									{{ item.title }}
								</view>
							</view>
						</block>
					</view>
					<view class="jobIntro">
						{{ i.intro }}
					</view>
				</view>
			</view>
		</view>

		<!-- 公司评论 -->
		<view class="comment">
			<view class="title">在职感受</view>
			<view>
				<!-- <view class="leftCom">
					<view class="grade">4.0</view>
					<view class="star">
						<u-icon name="star-fill" color="#F9AD14"></u-icon>
						<u-icon name="star-fill" color="#F9AD14"></u-icon>
						<u-icon name="star-fill" color="#F9AD14"></u-icon>
						<u-icon name="star-fill" color="#F9AD14"></u-icon>
					</view>
					<view class="goComment">
						<image src="https://api-test.zhaopinbei.com/storage/uploads/images/2k75aEDUwsmRouVtYBzfeI4WashqC6WfZjFbrNWw.png" alt="" />
						去评论
					</view>
				</view> -->
				<view class="rightCom">
					<view class="Allcom ">
						<image
							src="https://api-test.zhaopinbei.com/storage/uploads/images/lBJpgI0XRMLo17xqaHmHxvr17futeNLhBJUtLdVe.png"
							alt="" />
						<view class="all-li">
							匿名用户
							<text class="PR desc">招聘经理·在职6年</text>
						</view>
					</view>
					<view style="color: white; font-size: 26rpx; margin-bottom: 16rpx">
						公司氛围好 员工也很热情 老板身上有很多闪光点值得我们去学习
					</view>
					<view style="color: white; font-size: 28rpx; color: #777777">09.13发布</view>
				</view>
				<view class="rightCom">
					<view class="Allcom">
						<image
							src="https://api-test.zhaopinbei.com/storage/uploads/images/lBJpgI0XRMLo17xqaHmHxvr17futeNLhBJUtLdVe.png"
							alt="" />
						<view class="all-li">
							匿名用户
							<text class="PR desc">招聘经理·在职6年</text>
						</view>
					</view>
					<view style="color: white; font-size: 26rpx; margin-bottom: 16rpx">
						公司氛围好 员工也很热情 老板身上有很多闪光点值得我们去学习
					</view>
					<view style="color: white; font-size: 28rpx; color: #777777">09.13发布</view>
				</view>
			</view>
		</view>
		<view style="text-align: center; color: #777777">查看全部评论 · 10条</view>

		<!-- 我来说两句 -->
		<view class="myCom">
			<view class="title">我来说两句</view>
			<view class="coming">
				<view class="leftCom">还没人评论这家公司，快来抢沙发吧！</view>
				<view class="rightCom" @click="isText = !isText">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/2k75aEDUwsmRouVtYBzfeI4WashqC6WfZjFbrNWw.png" alt="" />
					评论
				</view>
			</view>
		</view>
		<view class="textBox" style="color: aliceblue;" v-show="isText">
			<input type="text" v-model="con_text" placeholder="请输入评论内容"
				style="background-color: #FFFFFF; color: black; border-radius: 24rpx; padding: 24rpx 0rpx; padding-left: 24rpx;" />
		</view>

		<!-- <view class="info">
			<view class="title">
				工商信息
			</view>
			<view class="info-item">
				<view class="label">公司全称</view>
				<view class="content">{{details.name}}</view>
			</view>
			<view class="info-item">
				<view class="label">法人代表</view>
				<view class="content">史红阳</view>
			</view>
			<view class="info-item">
				<view class="label">成立时间</view>
				<view class="content">2017-07-11</view>
			</view>
			<view class="info-item">
				<view class="label">注册资本</view>
				<view class="content">3000万人民币</view>
			</view>
			<view class="info-item">
				<view class="label">统一社会信用代码</view>
				<view class="content">91110108MA00G2C30U</view>
			</view>
			<view class="info-item">
				<view class="label">注册地址</view>
				<view class="content">{{details.register_address.address_info}}</view>
			</view>
			<view class="info-item">
				<view class="label">数据来源</view>
				<view class="content">数据来源</view>
			</view>
		</view> -->
	</view>
</template>

<script>
	import JobItem from '../../components/jobItem.vue';
	import {
		cancelCollectEnterprise,
		collectEnterprise,
		getComJobList,
		getEnterpriseDetails
	} from '../../config/api.js';
	import AddressMap from '../../public_label/addressMap.vue';
	export default {
		components: {
			AddressMap,
			JobItem,
		},
		data() {
			return {
				hrList: [{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '王哈哈',
						job: '人事经理'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '李笑笑',
						job: '招聘专员'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '张伟',
						job: 'HRBP'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '刘芳',
						job: '培训主管'
					},
					{
						avatar: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png',
						name: '陈晨',
						job: '薪酬经理'
					},
					// 更多数据...
				],
				videoList: [{
						src: "https://stream7.iqilu.com/10339/upload_transcode/202002/18/20200218114723HDu3hhxqIT.mp4"
					},
					{
						src: "https://prod-streaming-video-msn-com.akamaized.net/0b927d99-e38a-4f51-8d1a-598fd4d6ee97/3493c85c-f35a-488f-9a8f-633e747fb141.mp4"
					},
				],
				talentList: [{
						id: 1,
						val: '定期普调'
					},
					{
						id: 2,
						val: '定期绩效调薪'
					},
					{
						id: 3,
						val: '晋级涨薪'
					},
					{
						id: 4,
						val: '项目奖金'
					},
					{
						id: 5,
						val: '人才补贴'
					},
				],
				managerList: [{
						id: 1,
						val: '定期普调'
					},
					{
						id: 2,
						val: '定期绩效调薪'
					},
					{
						id: 3,
						val: '晋级涨薪'
					},
					{
						id: 4,
						val: '项目奖金'
					},
					{
						id: 5,
						val: '人才补贴'
					},
				],
				businessList: [{
						id: 1,
						val: '咖啡'
					},
					{
						id: 2,
						val: '冰激凌'
					},
					{
						id: 3,
						val: '冰激凌'
					}
				],
				scrollHeight: 300,
				isExpanded: false,
				showExpandButton: false,
				industry_classesName: '',
				roleType: '',
				id: '',
				details: {},
				jobList: [],
				page: 1,
				limit: 3,
				addresses: [],
				latitude: '',
				longitude: '',
				covers: [{
					id: 1,
					latitude: '',
					longitude: '',
				}, ],
				titleheight: 0,
				titletop: 0,
				iconTop: 0,
				isText: false,
				con_text: '',
			};
		},
		onLoad(options) {
			this.id = options.id;
			this.getEnterpriseDetails();
			this.getjobList();
			this.roleType = uni.getStorageSync('roleType');
			this.getHeight();
		},
		mounted() {
			this.checkTextOverflow();
		},
		methods: {
			getHeight() {
				let res = uni.getMenuButtonBoundingClientRect();
				this.titletop = res.top;
				this.titleheight = res.height;
				// console.log(res);
				// console.log("titletop的值", this.titletop);
				// console.log("titleheight的值", this.titleheight);
				// console.log("一半的titleheight的值", this.titleheight / 2);
			},
			// 切换展开状态
			toggleExpand() {
				this.isExpanded = !this.isExpanded;
			},
			// 检查文字是否超出三行
			checkTextOverflow() {
				const query = uni.createSelectorQuery().in(this);
				query
					.select('.text-content')
					.boundingClientRect(res => {
						if (res.height > 3 * 20) {
							this.showExpandButton = true;
						}
					})
					.exec();
			},

			async getEnterpriseDetails() {
				let params = {
					id: this.id,
				};
				const {
					status_code,
					message,
					data
				} = await getEnterpriseDetails(params);
				if (status_code == 200) {
					this.details = data;
					console.log('地址', this.details);
					this.latitude = this.details.register_address.lat;
					this.longitude = this.details.register_address.lng;

					let address = {
						map_address: this.details.register_address.address_info,
						lat: this.details.register_address.lat,
						lng: this.details.register_address.lng,
					};
					this.addresses.push(address);
					if (this.details.industry_classes.length > 0) {
						this.industry_classesName = this.details.industry_classes[0]['name'];
					}

					this.covers[0]['latitude'] = this.details.register_address.lat;
					this.covers[0]['longitude'] = this.details.register_address.lng;
				}
			},

			// index: getComJobList,
			// indexs: getRecomendList
			async getjobList() {
				let params = {
					company_id: this.id,
					showType: 1,
					page: this.page,
					limit: this.limit,
				};
				const {
					status_code,
					data,
					message
				} = await getComJobList(params);
				if (status_code == 200) {
					this.jobList = data.data;
					console.log('列表', this.jobList);
				}
			},

			toCom() {
				uni.navigateTo({
					url: '/pagesA/list/company_job_list?id=' + this.id,
				});
			},

			async collectEnterprise() {
				let params = {
					id: this.id,
				};
				const {
					status_code,
					data,
					message
				} = await collectEnterprise(params);
				if (status_code == 200) {
					this.getEnterpriseDetails();
					return uni.$u.toast(message || '成功');
				}
			},

			async cancelCollectEnterprise() {
				let params = {
					id: this.id,
				};
				const {
					status_code,
					data,
					message
				} = await cancelCollectEnterprise(params);
				if (status_code == 200) {
					this.getEnterpriseDetails();
					return uni.$u.toast(message || '成功');
				}
			},

			goDetail() {
				uni.navigateTo({
					url: '/pagesA/details/companyDetail',
				});
			},
			back() {
				uni.navigateBack({
					delta: 1,
				});
			},
			// 监听视频加载完成，获取视频实际尺寸并更新容器高度
			handleVideoLoaded(e) {
				const video = e.target;
				const ratio = video.videoHeight / video.videoWidth;
				this.scrollHeight = 584 * ratio;
			},
		},
	};
</script>
<style lang="scss" scoped>
	.all-li {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		gap: 8rpx;
	}

	.desc {
		color: #FFFFFF;
		font-size: 24rpx;
	}

	.title {
		font-size: 32rpx;
	}

	.PM {
		font-family: 'PingFang SC-Medium';
	}

	.PR {
		font-family: 'PingFang SC-Regular';
	}

	.content {
		box-sizing: border-box;
		width: 100%;
		overflow: hidden;
		position: relative;
	}

	.back {
		width: 32rpx;
		height: 32rpx;
		position: absolute;
		// top: 50rpx;
		position: fixed;
		left: 24rpx;
	}

	.scroll-view_H {
		width: 100%;
		white-space: nowrap;

		.scroll-content {
			display: flex;
			gap: 16rpx;
			padding-right: 24rpx; // 右侧留出间距
		}
	}

	.picBox {
		padding: 24rpx;

		.bg-g {
			width: 584rpx;
			height: 268rpx;
			border-radius: 12rpx;
			background-color: #D9D9D9;
			flex-shrink: 0; // 防止元素被压缩

			image {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
				object-fit: cover; // 确保图片填满容器
			}
		}
	}

	.scroll-content {
		display: inline-flex;
		gap: 16rpx;
	}

	.video-item {
		flex-shrink: 0;
		width: 584rpx; // 保持宽度固定，高度通过动态样式控制
		border-radius: 12rpx;
		overflow: hidden;

		video {
			width: 100%;
			height: 100%;
		}
	}

	.culture {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		padding: 24rpx;
		color: #FFFFFF;

		.main {
			display: flex;
			justify-content: space-between;

			.maintext {
				font-size: 24rpx;
				display: flex;
				flex-direction: column;
				gap: 16rpx;

				text {
					white-space: nowrap;
					/* 强制文本在一行内显示 */
					overflow: hidden;
					/* 溢出内容隐藏 */
					text-overflow: ellipsis;
					/* 溢出部分显示省略号 */
					max-width: 500rpx;
					/* 确保不超出容器宽度 */
				}
			}

			.more {
				align-self: flex-end;
				font-size: 20rpx;
				color: #CCCCCC;
				display: flex;
				align-items: center;
			}
		}
	}

	.business {
		padding: 24rpx;
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		color: #FFFFFF;

		.tag-view {
			display: flex;
			flex-wrap: nowrap;
			gap: 24rpx;
			overflow-x: auto;
			padding-bottom: 10rpx;
		}
	}

	::v-deep .uni-tag {
		background: transparent !important;
		white-space: nowrap; // 禁止文本换行
	}

	::v-deep .uni-tag--inverted {
		color: #FFFFFF !important;
		// background: transparent !important;
	}

	.product {
		display: flex;
		flex-direction: column;
		gap: 24rpx;
		padding: 24rpx;
		color: #FFFFFF;

		.pro-ul {
			display: flex;
			gap: 40rpx;

			.li {
				width: 380rpx;
				display: flex;
				// align-items: center;
				gap: 24rpx;

				.img-box {
					width: 96rpx;
					height: 96rpx;
					background-color: #CCCCCC;
					border-radius: 8rpx;
				}

				.li-text {
					display: flex;
					flex-direction: column;
					gap: 8rpx;
					color: #FFFFFF;
					font-size: 20rpx;

					.pro-title {
						font-size: 28rpx;
					}

					text {
						white-space: nowrap; // 禁止文本换行
						overflow: hidden; // 溢出内容隐藏
						text-overflow: ellipsis; // 溢出部分显示省略号
					}
				}
			}
		}
	}

	.textBox {
		padding: 24rpx;
	}
</style>
<style lang="scss" scoped>
	@import '../../static/css/pagesA/details/companyDetail.scss';
</style>
