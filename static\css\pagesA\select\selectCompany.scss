page {
    background-color: #F5F5F7;
}

view {
    box-sizing: border-box;
}

#app {
    width: 100%;
    padding: 32rpx;
}

.content {
    width: 100%;
    padding: 32rpx;
    background: #FFFFFF;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    border: 2rpx solid transparent;
}

.selected {
    border: 2rpx solid #1E6DEE; /* 选中状态下边框样式 */
}

.btn {
    width: 100%;
    height: 196rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 24rpx 32rpx 92rpx 32rpx;
    
}

.btnCla {
    width: 100%;
    height: 80rpx;
    background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-weight: 600;
    font-size: 28rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 80rpx;
}
