<template>
    <view id="app">

        <view class="content">
            <view class="content_title">{{details.title}}</view>
            <view class="hrBox"></view>
            <view class="content_user">
                <view class="content_userLeft">
                    <image :src="details.member_info.image.thumbnail_path_url" mode="" class="userHead"></image>
                    <view>{{details.member_certification.name}}</view>
                </view>
                <view>{{details.updated_at}}</view>
            </view>
        </view>


        <view class="content">
            <view class="content_title">项目详情</view>
            <view class="content_text">
                {{details.content}}
            </view>
        </view>


        <view class="bottomBox" v-if="userId==details.user_id">
            <view class="bottomBtn" @click="destory">关闭协同</view>
        </view>
        <view class="bottomBox" v-else>
            <view class="bottomBtn" @click="add">加入协同</view>
        </view>

    </view>
</template>

<script>
    import {
        cooperateDetails,cooperateDestroy
    } from '../../config/headhunterList_api.js'
    import {
        addFriendStore,coordinationShow
    } from '../../config/common_api.js'
    export default {
        data() {
            return {
                details: '',
                detailsId:'',
                userId: '',
            }
        },
        onLoad(option) {
            this.detailsId = option.id
            this.cooperateDetails(option.id)
            this.userId = uni.getStorageSync('userInfo').login_user.id
        },
        methods: {
            async cooperateDetails(id) {
                let params = {
                    id
                }

                const res = await coordinationShow(params)
                this.details = res.data
                console.log("res", this.details)
            },
            destory() {
                // 弹窗输入
                uni.showModal({
                    title: '是否关闭此协同', // 标题
                    success: async (res) => {
                        if (res.confirm) {
                            let params = {
                                id: this.detailsId
                            }
                            // 后续处理
                            const result = await cooperateDestroy(params)
                            console.log("res", result)
                            if (result.status_code == 200) {
                                uni.$u.toast('已关闭')
                            } else {
                                uni.$u.toast(result.message)
                            }
                
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                })
            },
            add(id) {
                if (this.details.user_id == this.userId) {
                    return uni.$u.toast('无法加入自己发起的协同')
                }
                // 弹窗输入
                uni.showModal({
                    title: '是否加入协同', // 标题
                    editable: true, // 开启输入框
                    placeholderText: '请输入加入原因', // 输入框提示语
                    success: async (res) => {
                        if (res.confirm) {
                            let params = {
                                user_id: id,
                                note: res.content
                            }
                            // 后续处理
                            const result = await addFriendStore(params)
                            console.log("res", result)
                            if (result.status_code == 200) {
                                uni.$u.toast('已发起申请')
                            } else {
                                uni.$u.toast(result.message)
                            }

                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                })
            },
        }
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .content {
        width: 100%;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 24rpx 32rpx;
        margin-bottom: 24rpx;
    }

    .content_title {
        font-weight: bold;
        font-size: 32rpx;
        color: #333333;
    }

    .hrBox {
        width: 622rpx;
        height: 2rpx;
        background: #F5F5F7;
        margin: 24rpx 0;
    }

    .content_user {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 500;
        font-size: 24rpx;
        color: #333333;
    }

    .content_userLeft {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .userHead {
        width: 48rpx;
        height: 48rpx;
        margin-right: 16rpx;
        border-radius: 50%;
    }

    .content_text {
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        line-height: 40rpx;
        margin-top: 24rpx;
    }

    .bottomBox {
        width: 100%;
        height: 196rpx;
        padding: 24rpx 22rpx 92rpx 42rpx;
        background: #FFFFFF;
        position: fixed;
        bottom: 0;
        left: 0;
        border-radius: 24rpx 24rpx 0 0;
    }

    .bottomBtn {
        width: 100%;
        height: 80rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #FFFFFF;
    }
</style>