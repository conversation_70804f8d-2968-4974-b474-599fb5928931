<template>
	<view id="app">
		<view class="addLabelCla">
			<view class="addLabelCla_til">就业管家标签<text class="imop">*</text></view>
			<view class="labelBox">
				<view class="labelBox_childs" @click.stop="showInputDialog">
					<view class="labelBox_none" v-if="labels.length==0" @click.stop="showInputDialog">请添加就业管家标签</view>
					<view class="labelBox_child" v-for="(label, index) in labels" :key="index"
						@click.stop="removeLabel(index)">{{label}} <text class="downIcon">X</text> </view>
				</view>
				<image src="/static/images/project/rightIcon.png" mode="" class="rightIcon">
				</image>
			</view>
		</view>

		<view class="addLabelCla">
			<view class="addLabelCla_til">就业管家简介<text class="imop">*</text></view>
			<textarea class="contentText" placeholder="请输入就业管家简介" v-model="introduction"></textarea>
		</view>

		<view class="saveBtnBox" @click="saveButlerDetails">
			<view class="saveBtnCla">
				保存
			</view>
		</view>

	</view>
</template>

<script>
	import {
		saveButlerDetails,getButlerDetails
	} from '../../config/api'
	export default {
		data() {
			return {
				labels: [], // 默认标签
				introduction: '', // 简介信息
			};
		},
		onLoad() {
		this.getButlerDetails()
		},
		methods: {
			async getButlerDetails() {
			  const {
			    status_code,
			    data
			  } = await getButlerDetails()
			  if (status_code == 200) {
					  console.log('this.list',data)
					  this.labels = data.label;
					  this.introduction = data.introduction
			  }
			},
			async saveButlerDetails(id) {
				if (this.labels.length == 0) {
					return uni.showToast({
						icon: 'none',
						title: '请添加标签',
						duration: 2000
					})
				}
				if (!this.introduction) {
					return uni.showToast({
						icon: 'none',
						title: '请输入简介',
						duration: 2000
					})
				}
				let params = {
					label: this.labels,
					introduction: this.introduction
				}
				const {
					status_code,
					data
				} = await saveButlerDetails(params)
				if (status_code == 200) {
					uni.showToast({
						icon: 'none',
						title: '保存成功',
						duration: 2000,
						success: () => {
							setTimeout(() => {
								uni.switchTab({
									url: '/pages/my/my'
								})
							}, 2000);
						}
					})

				}
			},
			showInputDialog() {
				const self = this; // 保存上下文
				uni.showModal({
					title: '添加标签',
					content: '',
					placeholderText: '请输入标签内容',
					editable: true,
					cancelColor: "#FE4D4F",
					confirmColor: '#4F8CF0',
					success(res) {
						if (res.confirm) {
							const newLabel = res.content.trim(); // 获取输入的内容
							if (newLabel) {
								self.labels.push(newLabel); // 添加新标签
							} else {
								uni.showToast({
									title: '标签不能为空！',
									icon: 'none'
								});
							}
						}
					}
				});
			},
			removeLabel(index) {
				const self = this; // 保存上下文
				uni.showModal({
					title: '确认删除',
					content: '您确定要删除此标签吗？',
					success(res) {
						if (res.confirm) {
							self.labels.splice(index, 1); // 确认删除
						}
					}
				});
			}
		}
	};
</script>

<style scoped>
	view {
		box-sizing: border-box;
	}

	#app {
		width: 100%;
		padding: 30rpx 32rpx;
		min-height: 100vh;
		height: auto;
		background-color: #F5F5F7;
	}

	.addLabelCla {
		width: 100%;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 32rpx;
		margin-bottom: 32rpx;
	}

	.addLabelCla_til {
		font-weight: 400;
		font-size: 22rpx;
		color: #666666;
		margin-bottom: 16rpx;
	}

	.imop {
		font-weight: 600;
		font-size: 22rpx;
		color: #FE4D4F;
		padding-left: 8rpx;
	}

	.labelBox {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.labelBox_childs {
		flex: 1;
		display: flex;
		flex-wrap: wrap;
		gap: 24rpx;
	}

	.rightIcon {
		width: 32rpx;
		height: 32rpx;
	}

	.labelBox_child {
		padding: 8rpx 16rpx;
		background: #F5F5F7;
		border-radius: 8rpx 8rpx 8rpx 8rpx;

		font-weight: 500;
		font-size: 24rpx;
		color: #333333;
	}

	.downIcon {
		padding-left: 14rpx;
	}

	.contentText {
		width: 100%;
		min-height: 306rpx;
		height: auto;
		font-weight: 400;
		font-size: 32rpx;
		color: #333333;
		line-height: 38rpx;
	}

	.saveBtnBox {
		position: fixed;
		bottom: 106rpx;
		left: 0;
		width: 100%;
		height: 88rpx;
		padding: 0 32rpx;
	}

	.saveBtnCla {
		width: 100%;
		height: 100%;
		font-weight: 600;
		font-size: 34rpx;
		color: #FFFFFF;
		line-height: 88rpx;
		text-align: center;
		background: #4F8CF0;
		border-radius: 24rpx;
	}

	.labelBox_none {
		font-weight: 400;
		font-size: 32rpx;
		color: #999999;
	}
</style>