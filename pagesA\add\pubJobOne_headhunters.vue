<template>
  <view
    class="register-one"
    :style="{ paddingBottom: step == 0 ? '178rpx' : '266rpx' }"
  >
    <u-steps :current="step" dot="true">
      <u-steps-item title="第一步"></u-steps-item>
      <u-steps-item title="第二步"></u-steps-item>
    </u-steps>
    <view class="inp">
      <view
        class="inp-item"
        @click="goToCompanyList"
        v-if="roleType == 'headhunters'"
      >
        <view class="title">
          关联公司
          <text class="star">*</text>
        </view>
        <view class="in se">
          <view class="d-picker" :style="{ color: '#303133' }">
            <view v-if="companyAuthorizeValue.length > 0">
              <view
                class="d-picker"
                style="color: #303133"
                v-for="(item, index) in companyAuthorizeValue"
                :key="index"
                :item="item"
              >
                {{ item.authorize_company.name }}
              </view>
            </view>
            <view v-else style="color:#c0c4cc"> 请选择关联公司 </view>
          </view>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <!-- <view class="inp-item" v-if="roleType=='headhunters'" @click="goToCompanyList">
				<view class="title">
					抄送企业
					<text class="star">*</text>
				</view>
				<view class="in se">
					<view v-if="companyAuthorizeValue.length > 0">
						<view class="d-picker" style="color:#303133" v-for="(item,index) in companyAuthorizeValue" :key="index"
							:item="item">
							{{ item.authorize_company.name }}
						</view>
					</view>
					<view class="d-picker" style="color:#c0c4cc" v-else>
						请选择抄送企业
					</view>
					<u-icon name="arrow-right"></u-icon>
				</view>
			</view> -->

      <view class="inp-item">
        <view class="title">
          岗位名称
          <text class="star">*</text>
        </view>
        <view class="in">
          <u--input
            placeholder="请输入岗位名称"
            placeholderClass="placeholderClass"
            clearable
            border="none"
            v-model="pubJobOne.title"
          ></u--input>
        </view>
      </view>

      <view class="inp-item" v-if="roleType == 'headhunters'">
        <view class="title">
          招聘类型
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            @change="changeType"
            :value="typeIndex"
            :range="typeList"
            range-key="name"
          >
            <view
              class="d-picker"
              :style="{ color: typeIndex == 0 ? '#c0c4cc' : '#303133' }"
            >
              {{ typeIndex ? typeList[typeIndex]["name"] : "请选择招聘类型" }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <!-- <view class="inp-item">
				<view class="title">
					学历要求
					<text class="star">*</text>
				</view>
				<view class="in se">
					<picker @change="changeEdu" :value="eduIndex" :range="education" range-key="label">
						<view class="d-picker" :style="{color:eduIndex==0?'#c0c4cc':'#303133'}">
							{{ education[eduIndex]['label'] }}
						</view>
					</picker>
					<u-icon name="arrow-right"></u-icon>
				</view>
			</view> -->

      <view class="inp-item">
        <view class="title">
          职位行业
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            @change="changeIndustry"
            :value="industryIndex"
            :range="industry_classess"
            range-key="name"
          >
            <view
              class="d-picker"
              :style="{ color: industryIndex == 0 ? '#c0c4cc' : '#303133' }"
            >
              {{
                industryIndex
                  ? industry_classess[industryIndex]["name"]
                  : "请选择职位行业"
              }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>

      <!-- <view class="inp-item">
				<view class="title">
					职位岗位
					<text class="star">*</text>
				</view>
				<view class="in se"> -->
      <!-- <picker @change="changeClass" :value="classIndex" :range="classList" range-key="name">
            <view class="d-picker" :style="{color:classIndex==0?'#c0c4cc':'#303133'}">
              {{classList[classIndex]['name']}}
            </view>
          </picker> <u-icon name="arrow-right"></u-icon> -->
      <!-- <uni-data-picker :map="mapClass" placeholder="请选择岗位" popup-title="请选择岗位" :localdata="classList"
						v-model="pubJobOne.class_id" @change="onchangeclass">
					</uni-data-picker>
				</view>
			</view> -->

      <!-- <view class="inp-item" @click="openWork">
				<view class="title">
					就业方式
					<text class="star">*</text>
				</view>
				<view class="in">
					<block v-if="selectedWork.length==0">
						<view class="" style="color: #c0c4cc;">
							请选择就业方式
						</view>
					</block>
					<block v-else>
						<view class="types">
							<view class="nav-item" v-for="(item,index) in selectedWork" :key="index">
								<view class="cont">
									<text>{{ item.name }}</text>
								</view>
							</view>
						</view>

					</block>
					<u-icon name="arrow-right"></u-icon>
				</view>
			</view> -->

      <!-- <view class="inp-item">
				<view class="title">
					联系人
					<text class="star">*</text>
				</view>
				<view class="in">
					<u--input placeholder="请输入联系人" placeholderClass="placeholderClass" clearable border="none"
						v-model="pubJobOne.contact"></u--input>
				</view>
			</view> -->

      <!-- <view class="inp-item">
				<view class="title">
					联系电话
					<text class="star">*</text>
				</view>
				<view class="in">
					<u--input placeholder="请输入联系电话" placeholderClass="placeholderClass" clearable border="none"
						v-model="pubJobOne.contact_phone"></u--input>
				</view>
			</view> -->

      <view class="inp-item">
        <view class="title">
          公司福利
          <text class="star">*</text>
        </view>
        <view class="in se">
          <picker
            @change="changeWelfare"
            :value="WelfareIndex"
            :range="welfarelist"
            range-key="title"
          >
            <view
              class="d-picker"
              :style="{ color: WelfareIndex == 0 ? '#c0c4cc' : '#303133' }"
            >
              {{
                WelfareIndex
                  ? welfarelist[WelfareIndex]["title"]
                  : "请选择公司福利"
              }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>

      <view class="inp-item">
        <view class="title">
          岗位标签
          <text class="star">*</text>
          <!-- <view class="plus">
						<image src="/static/images/index/plus.png" mode="" @click="addLabel"></image>
					</view> -->
        </view>
        <view class="in">
          <u--input
            placeholder="请输入岗位标签"
            placeholderClass="placeholderClass"
            clearable
            border="none"
            v-model="pubJobOne.label"
          ></u--input>
        </view>
        <!-- <view class="in">
					<scroll-view class="scroll-W" :scroll-x="true">
						<block v-if="jobLabelList.length==0">
							<view style="color: #c0c4cc;">
								添加岗位标签
							</view>
						</block>
						<block v-else>
							<view class="nav-item" v-for="(item,index) in jobLabelList" :key="index">
								<view class="cont">
									<text>{{ item }}</text>
									<view class="del">
										<u-icon name="close" size="28rpx" @click="delLabel(item)"></u-icon>
									</view>
								</view>
							</view>
						</block>
					</scroll-view>
				</view> -->
      </view>
      <view class="inp-item">
        <view class="title">
          岗位简介
          <text class="star">*</text>
        </view>
        <view class="in">
          <u--input
            placeholder="请输入岗位简介"
            placeholderClass="placeholderClass"
            clearable
            border="none"
            v-model="pubJobOne.intro"
          ></u--input>
        </view>
      </view>
      <view class="inp-item">
        <view class="title">
          岗位描述
          <text class="star">*</text>
        </view>
        <view class="in">
          <u--input
            placeholder="请输入岗位描述"
            placeholderClass="placeholderClass"
            clearable
            border="none"
            v-model="pubJobOne.job_describe"
          ></u--input>
        </view>
      </view>
    </view>

    <view class="footers">
      <view class="next sure" @click="next"> 下一步 </view>
    </view>

    <!-- 标签弹框 -->
    <u-modal
      :show="showLabel"
      showCancelButton
      cancelColor="#FE4D4F"
      @cancel="cancel"
      @confirm="sureLabel"
      title="添加标签"
    >
      <view class="slot-content">
        <view class="inp">
          <u--input
            placeholder="请输入标签内容"
            border="surround"
            v-model="labelValue"
          ></u--input>
        </view>
      </view>
    </u-modal>

    <!-- 行业弹框 -->
    <!-- <work-pop ref="workRef" :list="workTypes" :selected="selectedWork" @confirm="sureWork"></work-pop> -->
    <!-- 提交时候是否有记录，没有的话直接从confirm1里拿 -->
    <u-modal
      :show="show"
      title="选择就业方式"
      cancelText="取消"
      showCancelButton
      @cancel="cancel1"
      @confirm="confirm1"
    >
      <view class="slot-content">
        <view class="workList">
          <view
            v-for="(item, index) in workTypes"
            :key="index"
            @click="handleTab(item)"
            :class="[
              'workItem',
              { active: selectedItem.some((v) => item.id == v.id) },
            ]"
          >
            {{ item.name || item.title }}
          </view>
        </view>
      </view>
    </u-modal>
  </view>
</template>

<script>
import {
  getWorkType,
  getCompanyJobClassList,
  getWelfareList,
  getIndustryList,
  getJobDetails,
  getHeadhuntersAuthEnterpriseList,
} from "../../config/api.js";
import WorkPop from "../../components/workPop.vue";

export default {
  components: {
    WorkPop,
  },
  data() {
    return {
      id: "",
      step: 0,
      mapClass: {
        text: "name",
        value: "id",
      },

      compIndex: 0,
      compList: [
        {
          value: 0,
          id: "",
          name: "请选择",
        },
      ],

      classList: [],
      classIndex: 0,

      eduIndex: 0,
      typeIndex: 0,
      WelfareIndex: 0,

      industryIndex: "",
      industry_classess: [],

      selectedWork: [],
      selectedItem: [],
      workTypes: [],

      welfarelist: [],
      welfareIds: [],

      labelValue: "",
      showLabel: false,
      jobLabelList: [],
      companyAuthorizeData: [],
      pubJobOne: {
        copy_company_id: [],
        job_active_id: 0,
        type: "job",
        company_id: "",
        title: "",
        education: "",
        industry_id: [],
        work_type_id: [],
        class_id: "",
        contact: "",
        contact_phone: "",
        intro: "",
        tag_id: [],
        label: [],
        job_describe: "",
        typeJob: "",
      },
      show: false,
      copy: "",
      typeList: [
        {
          name: "社招全职",
          value: "社招全职",
        },
        {
          name: "应届生",
          value: "应届生",
        },
        {
          name: "实习生",
          value: "实习生",
        },
        {
          name: "兼职",
          value: "兼职",
        },
      ],
    };
  },
  computed: {
    roleType() {
      return this.$store.state.roleType || uni.getStorageSync("roleType");
    },

    //系统数据
    sysData() {
      return this.$store.state.sysData || uni.getStorageSync("sysData");
    },

    education() {
      return this.sysData.education;
    },
    companyAuthorize() {
      console.log("companyAuthorize", this.$store.state.companyAuthorize);
      return this.$store.state.companyAuthorize || [];
    },
    companyAuthorizeValue() {
      console.log("companyAuthorize", this.$store.state.companyAuthorize);
      return this.companyAuthorize;
    },
  },

  onLoad(options) {
    this.getWorkType(); //行业
    this.getWelfareList(); //福利
    this.getIndustryList();
    this.getCompanyJobClassList();

    if (uni.getStorageSync("roleType") == "headhunters") {
      this.getHeadhuntersAuthEnterpriseList();
    }

    if (options.type) {
      this.pubJobOne.type = options.type;
    }
    if (options.copy) {
      this.copy = options.copy;
    }
    this.pubJobOne.job_active_id = options.job_active_id || 0;

    if (options.id) {
      this.id = options.id;
      this.getJobDetails();
    }
  },

  methods: {
    goToCompanyList() {
      uni.navigateTo({
        url: "/pagesA/add/pubJobCompany/index",
      });
    },
    async getCompanyJobClassList() {
      const result = await getCompanyJobClassList();
      if (result.status_code == 200) {
        console.log(result);
        this.classList = result.data;
      }
    },
    async getWorkType() {
      const result = await getWorkType();
      if (result.status_code == 200) {
        this.workTypes = result.data;
      }
    },
    async getHeadhuntersAuthEnterpriseList() {
      let params = {
        status: 1,
        cancel_status: 2,
      };
      const result = await getHeadhuntersAuthEnterpriseList(params);
      if (result.status_code == 200) {
        result.data.forEach((item, index) => {
          this.compList.push({
            id: item.authorize_company.id,
            name: item.authorize_company.name,
          });
        });
      }
    },

    onchangeclass(e) {
      let data = e.detail.value;
      this.pubJobOne.class_id = data[data.length - 1]["value"];
    },

    next() {
      this.pubJobOne.copy_company_id = [];
      this.companyAuthorizeValue.forEach((item, index) => {
        this.pubJobOne.copy_company_id.push(item.authorize_company.id);
      });
      this.pubJobOne.education = this.education[this.eduIndex]["value"];
      this.pubJobOne.industry_id[0] =
        this.industry_classess[this.industryIndex]["id"];
      this.pubJobOne.class_id = this.classList[this.classIndex]["id"];
      this.pubJobOne.typeJob = this.typeList[this.typeIndex]["name"];
      this.pubJobOne.work_type_id = this.selectedWork.map((item) => item.id);

      this.$store.commit("setPubJobOne", this.pubJobOne);

      if (!this.copy) {
        uni.navigateTo({
          url: "/pagesA/add/pubJobTwo_headhunters?id=" + this.id,
        });
      } else {
        uni.navigateTo({
          url:
            "/pagesA/add/pubJobTwo_headhunters?id=" +
            this.id +
            "&copy=" +
            this.copy,
        });
      }
    },

    changeClass(e) {
      this.classIndex = e.detail.value;
      this.pubJobOne.class_id = this.classList[this.classIndex]["id"];
    },

    changeComp(e) {
      console.log(e);
      this.compIndex = e.detail.value;
      let id = this.compList[e.detail.value].id;
      this.pubJobOne.company_id = id;
      console.log(id);
    },

    changeEdu(e) {
      this.eduIndex = e.detail.value;
      this.pubJobOne.education = this.education[this.eduIndex]["value"];
    },
    changeType(e) {
      this.typeIndex = e.detail.value;
      this.pubJobOne.type = this.education[this.typeIndex]["value"];
    },

    changeIndustry(e) {
      this.industryIndex = e.detail.value;
      this.pubJobOne.industry_id[0] =
        this.industry_classess[this.industryIndex]["id"];
    },

    changeWelfare(e) {
      // let isExsit = this.welfareIds.some(v => item.id == v)
      // if (isExsit) {
      // 	let index = this.welfareIds.findIndex(v => item.id == v)
      // 	this.welfareIds.splice(index, 1)
      // } else {
      // 	this.welfareIds.push(item.id)
      // }
      // this.pubJobOne.tag_id = this.welfareIds
      console.log(e);
      this.WelfareIndex = e.detail.value;
      this.pubJobOne.tag_id = this.welfarelist[this.WelfareIndex]["title"];
    },

    openWork() {
      this.show = true;
      // this.selectedItem = this.selectedWork
      // this.$refs.workRef.open();
    },

    // sureWork(selected) {
    //   this.selectedWork = selected
    //   this.pubJobOne.work_type_id = this.selectedWork.map(item => item.id)
    // },

    cancel() {
      this.labelValue = "";
      this.showLabel = false;
    },
    cancel1() {
      this.show = false;
    },
    confirm1() {
      this.selectedWork = this.selectedItem;
      this.show = false;
    },
    handleTab(item) {
      const index = this.selectedItem.indexOf(item);
      if (index === -1) {
        // 如果没有选中，添加到选中列表
        this.selectedItem.push(item);
      } else {
        // 如果已经选中，移除选中
        this.selectedItem.splice(index, 1);
      }
    },

    addLabel() {
      this.showLabel = true;
    },

    delLabel(item) {
      let index = this.jobLabelList.findIndex((v) => item == v);
      this.jobLabelList.splice(index, 1);
    },

    sureLabel() {
      if (!this.labelValue) return uni.$u.toast("请输入职位标签");
      this.jobLabelList.push(this.labelValue);
      this.pubJobOne.label = this.jobLabelList;
      this.showLabel = false;
    },

    async getIndustryList() {
      const { stauts_code, data } = await getIndustryList();
      this.industry_classess = data;
      console.log("行业类型：", this.industry_classess);
    },

    async getWelfareList() {
      const { stauts_code, data } = await getWelfareList();
      this.welfarelist = data;
    },

    async getJobDetails() {
      let params = {
        id: this.id,
      };
      const { status_code, data } = await getJobDetails(params);
      if (status_code == 200) {
        this.details = data;
        console.log(this.details.class_id, "类型di");
        this.pubJobOne.title = data.title;
        this.eduIndex = this.education.findIndex(
          (item) => item.value == this.details.education
        );
        // this.classIndex = this.classList.findIndex(item => item.value == this.details.class_id)
        this.selectedWork = this.details.work_types;
        this.pubJobOne.work_type_id = this.selectedWork.map((item) => item.id);
        this.pubJobOne.contact = this.details.contact;
        this.pubJobOne.contact_phone = this.details.contact_phone;
        this.pubJobOne.intro = this.details.intro;
        this.welfareIds = this.details.tags.map((item) => item.id);
        this.pubJobOne.tag_id = this.welfareIds;
        this.jobLabelList = this.details.label;
        this.pubJobOne.label = this.jobLabelList;
        this.pubJobOne.job_describe = this.details.job_describe;

        // 		this.covers[0]['latitude'] = this.details['addresses'][0]['lat']
        // 		this.covers[0]['longitude'] = this.details['addresses'][0]['lng']

        // 		this.latitude = this.details['addresses'][0]['lat']
        // 		this.longitude = this.details['addresses'][0]['lng']
      }
    },
  },
};
</script>
<style lang="scss">
@import "../../static/css/pagesA/add/pubJobOne";
</style>
<style lang="less" scoped>
.footers {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 196rpx;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;

  .next {
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #ffffff;
    height: 88rpx;
    width: 90%;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    border-radius: 16rpx;
  }

  .sure {
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
  }
}
</style>
