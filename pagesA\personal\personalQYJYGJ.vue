<template>
	<view class="home-index">
		<view class="wrap">
			<view class="inp">
				<view class="avatar">
					<view class="inp-item">
						<view class="title">
							头像<text class="star">*</text>
						</view>
						<view class="in lab">
							请选择系统默认头像
						</view>
					</view>
					<view class="pic" @click="uploadAvatar">
						<image :src="avatar && avatar.length>0?avatar[0]['path_url']:'https://api-test.zhaopinbei.com/storage/uploads/images/DjpYAiYx76keAZZls68bXyVPGHjYpURJJxA9YHcU.png'"
							mode=""></image>
					</view>
				</view>
				<u-line color="#F5F5F5" width="100%"></u-line>
				<view class="inp-item">
					<view class="title">
						姓名<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入昵称" clearable placeholderClass="placeholderClass" border="none"
							v-model="details.nick_name"></u--input>
					</view>
				</view>
				<u-line color="#F5F5F5" width="100%"></u-line>

				<view class="inp-item">
					<view class="title">
						性别<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeSex" :value="sexIndex" :range="sex" range-key="name">
							<view class="d-picker" style="">{{sex[sexIndex]['name']}}
							</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				<u-line color="#F5F5F5" width="100%"></u-line>
				<view class="inp-item">
					<view class="title">
						年龄<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入年龄" clearable placeholderClass="placeholderClass" border="none"
							v-model="details.age"></u--input>
					</view>
				</view>
                <u-line color="#F5F5F5" width="100%"></u-line>
                <view class="inp-item">
                	<view class="title">
                		手机号<text class="star">*</text>
                	</view>
                	<view class="in">
                		<u--input placeholder="请输入手机号" clearable placeholderClass="placeholderClass" border="none"
                			v-model="details.age"></u--input>
                	</view>
                </view>
			</view>
		</view>

		<view class="btn" @click="editUserInfo">
			保存
		</view>
	</view>
</template>

<script>
	import {
		updateLoginInfo,
		uploadImg,
		getUserDetails,
		editUserInfo,
		getWorkType,
        cert,
	} from "../../config/api.js"
	export default {
		data() {
			return {
				formList: [{
					id: 1,
					school: '',
					start: '',
					end: ''
				}],
				details: {},
				avatar: [],
				sexIndex: 0,
				sex: [{
						name: '男',
						value: 1
					},
					{
						name: '女',
						value: 2
					}, {
						name: '保密',
						value: 3
					}
				],

				eduIndex: 0,
				workStatusList: [{
						value: 1,
						name: '离职'
					},
					{
						value: 2,
						name: '在职'
					},
					{
						value: 3,
						name: '考虑'
					}
				],
				workStatusIndex: 0,
				workTypes: [], // 工作类型数据
				selectedItems: [], // 存储选中的项
				show: false,
                tips:'',
                certForm: {
                	id_no: '',
                	mobile_no: '',
                	name: '',
                	code: '',
                	idCard_img: []
                },
                certification_status:'',
                resume_count:0,
			}
		},
		computed: {
			sysData() {
				return this.$store.state.sysData || uni.getStorageSync('sysData')
			},
			eduList() {
				return this.sysData.education
			},

		},
		onLoad() {
			this.getWorkType()
			this.getUserDetails()
		},
        onShow() {
            this.certification_status = uni.getStorageSync('userInfo').member.certification_status;
            this.resume_count = uni.getStorageSync('userInfo').resume_count;
        },
		methods: {
            codeChange(text) {
            	this.tips = text;
            },
            getCode() {
            	if (this.$refs.uCode.canGetCode) {
            		// 模拟向后端请求验证码
            		uni.showLoading({
            			title: '正在获取验证码'
            		})
            		setTimeout(() => {
            			uni.hideLoading();
            			// 这里此提示会被this.start()方法中的提示覆盖
            			uni.$u.toast('验证码已发送');
            			// 通知验证码组件内部开始倒计时
            			this.$refs.uCode.start();
            		}, 2000);
            	} else {
            		uni.$u.toast('倒计时结束后再发送');
            	}
            },

            deleteJobList(index) {
            	this.formList.splice(index, 1);
            },
			isSelected(item) {
				return this.selectedItems.includes(item);
			},
			handleTab(item) {
				const index = this.selectedItems.indexOf(item);
				if (index === -1) {
					// 如果没有选中，添加到选中列表
					this.selectedItems.push(item);
				} else {
					// 如果已经选中，移除选中
					this.selectedItems.splice(index, 1);
				}
			},
			cancel() {
				this.show = false
			},
			confirm() {
				this.show = false
				this.details.work_type_id = this.selectedItems.map(item => item.id);
			},
			changeEdu(e) {
				this.eduIndex = e.detail.value
				this.details.education_type = this.eduList[this.eduIndex]['value']
			},
			changeSex(e) {
				this.sexIndex = e.detail.value
				this.details.sex = this.sex[this.sexIndex]['value']
			},
			changeWorkStatus(e) {
				this.workStatusIndex = e.detail.value
				this.details.job_status = this.workStatusList[this.workStatusIndex]['value']
			},

			bindStartDateChange(item, e) {
				let id = item.id
				let index = this.formList.findIndex(v => v.id == id)
				this.formList[index]['start'] = uni.$u.timeFormat(e.detail.value, 'yyyy-mm-dd');
			},
			bindEndDateChange(item, e) {
				let id = item.id
				let index = this.formList.findIndex(v => v.id == id)
				this.formList[index]['end'] = uni.$u.timeFormat(e.detail.value, 'yyyy-mm-dd');
			},

			add() {
				this.formList.push({
					id: this.formList.length + 1,
					school: '',
					start: '',
					end: ''
				})
			},
			async getWorkType() {
				const result = await getWorkType()
				if (result.status_code == 200) {
					this.workTypes = result.data;
				}
			},
			async getUserDetails() {
				const result = await getUserDetails()
				this.details = result.data
				this.avatar = [result.data.image];
				let sindex = this.sex.findIndex(item => item.value == result.data.sex)
				this.sexIndex = sindex < 0 ? 0 : sindex
				let windex = this.workStatusList.findIndex(item => item.value == result.data.job_status)
				this.workStatusIndex = windex < 0 ? 0 : windex
				let eindex = this.eduList.findIndex(item => item.value == result.data.education_type)
				this.eduIndex = eindex < 0 ? 0 : eindex


				this.selectedIds = this.details.work_types.map(item => item.id);
				// 将选中的项设置为工作类型中 ID 在 selectedIds 列表中的项目
				this.selectedItems = this.workTypes.filter(item => this.selectedIds.includes(item.id));
				console.log("111111", this.selectedItems)
				this.formList = this.details.education_log ? this.details.education_log : this.formList
			},
            // async saveBaseInfo() {
            // 	if (!this.certForm.id_no || !this.certForm.name || !this.certForm.mobile_no || !this.certForm.code)
            // 		return uni.$u.toast('请填写完整实名信息')
            // 	let params = {
            // 		...this.certForm
            // 	}
            // 	let result = await cert(params)
            // 	if (result.status_code == 200) {

            // 		// this.step = 1
            // 		let loginInfo = await updateLoginInfo()
            // 		if (loginInfo.status_code == 200) {
            // 			this.$store.commit('setUserInfo', loginInfo.data)
            // 			// uni.navigateTo({
            // 			// 	url: '/pagesA/add/addResume'
            // 			// })
            // 			return uni.$u.toast('保存信息成功')

            // 		}
            // 	}
            // },
			async editUserInfo() {
                if(!this.avatar) {
                   return uni.$u.toast('请选择头像')
                }
                if(!this.details.nick_name) {
                   return uni.$u.toast('请输入昵称')
                }
                if(!this.details.nation) {
                   return uni.$u.toast('请输入民族')
                }
                if(!this.details.age) {
                   return uni.$u.toast('请输入年龄')
                }
                // 如果实名中有内容，优先调用实名
                if (this.certForm.id_no || this.certForm.name || this.certForm.mobile_no || this.certForm.code&&this.certification_status==2) {
                    if (!this.certForm.id_no || !this.certForm.name || !this.certForm.mobile_no || !this.certForm.code)
                    	return uni.$u.toast('请填写完整实名信息')
                    let params = {
                    	...this.certForm
                    }
                    let result = await cert(params)
                    if (result.status_code == 200) {

                    	// this.step = 1
                    	let loginInfo = await updateLoginInfo()
                    	if (loginInfo.status_code == 200) {
                    		this.$store.commit('setUserInfo', loginInfo.data)
                            this.certification_status = 1;
                    		// uni.navigateTo({
                    		// 	url: '/pagesA/add/addResume'
                    		// })
                    		// return uni.$u.toast('保存信息成功')

                    	}
                    }
                }
				let params = {
					nick_name: this.details.nick_name,
					nation: this.details.nation,
					sex: this.sex[this.sexIndex]['value'],
					age: this.details.age,
					education_type: this.eduList[this.eduIndex]['value'],
					job_status: this.workStatusList[this.workStatusIndex]['value'],
					image: this.avatar,
					education_log: this.formList,
					work_type_id: this.details.work_type_id
				}
				console.log(params, "参数")
				const result = await editUserInfo(params)
				if (result.status_code == 200) {
					this.getUserDetails()
					uni.$u.toast('基本信息修改成功')
					let loginInfo = await updateLoginInfo()
					if (loginInfo.status_code == 200) {
						this.$store.commit('setUserInfo', loginInfo.data)
                        if(this.resume_count<1) {
                            uni.navigateTo({
                            	url: '/pagesA/add/addResume'
                            })
                        }
					}
				}
			},

			//上传荣誉证书
			uploadAvatar() {
				let self = this

				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						// $dialog.loading('上传中')
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {

								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								console.log("图片信息：", result.data)
								if (result.status_code == 200) {
									self.avatar = []
									self.avatar.push({
										id: result.data.id,
										path_url: result.data.url,
										pdf_url: "",
										thumbnail_path_url: result.data.thumbnail_url
									})
								}
							}
						})
					}
				});
			},
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding-bottom: 160rpx;
	}

	.wrap {
		padding: 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			padding: 0 30rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					// padding: 0 30rpx 0 0;
					background-color: #F5F5F7;
					border-radius: 50%;

					image {
						width: 108rpx;
						height: 108rpx;
						border-radius: 50%;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				// padding: 0 30rpx;
				flex: 1;

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.ins {
					display: flex;
					align-items: center;
					// justify-content: space-between;
					// border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					view {
						margin-right: 20rpx;
					}
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					// border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					// ::v-deep picker {
					// 	display: flex;
					// 	flex-direction: column;
					// 	flex: 1;
					// 	height: 88rpx;

					// 	.d-picker {
					// 		display: flex;
					// 		align-items: center;
					// 		// width: 60vw;
					// 		height: 88rpx;
					// 	}
					// }

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}

					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}



				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.add {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 24rpx;
		height: 64rpx;
		width: 260rpx;
		margin-left: 32rpx;
		background: #4F8CF0;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
		border-radius: 8rpx;
	}

	.btn {
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 90%;
		height: 100rpx;
		z-index: 100;
		bottom: 32rpx;
		left: 5%;
		background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
		border-radius: 16rpx;
		font-weight: 600;
		font-size: 34rpx;
		color: #FFFFFF;
	}

	.workList {
		display: flex;
		flex-wrap: wrap;

		.workItem {
			display: flex;
			align-items: center;
			justify-content: center;
			align-items: center;
			height: 64rpx;
			padding: 0 22rpx;
			border-radius: 8rpx;
			background-color: #F5F5F7;
			margin-bottom: 16rpx;
			margin-right: 20rpx;
			font-size: 28rpx;
			font-weight: 600;
		}

		.active {
			color: #4F8CF0;
			border: 1px solid #4F8CF0
		}
	}

    .delBtnCla {
    	width: 100%;
    	// height: 100rpx;
    	text-align: right;
    	box-sizing: border-box;
    	margin-top: 24rpx;
    }

    .delCla {
    	width: 100rpx;
    	// height: 100rpx;
    	background: red;
    	color: #FFF;
    	padding: 10rpx 20rpx;
    	border-radius: 24rpx;
    	font-size: 24rpx;
    	// line-height: 100rpx;
    }
</style>
