<template>
	<view class="warp">
		<view class="row" v-for="item in rowList" :key="item.id">
			<view class="top">
				<view class="tit-warp">
					<view class="tit">
						{{item.title}}
					</view>
					<u-icon name="error-circle" color="#999999" size="14"></u-icon>
				</view>
				<u-switch v-model="item.checked" active-color="#4F8CF0" @change="changeSwitch(item)"></u-switch>
			</view>
			<view class="btm">
				{{item.txt}}
			</view>
		</view>
		<view class="phone" v-if="showPhone">
			<view class="p-row m-b">
				<view class="left-txt">
					号码保护
				</view>
				<view class="right-txt">
					隐藏手机号
				</view>
			</view>
			<view class="p-row">
				<view class="left-txt">
					每天最多联系次数
				</view>
				<view class="right-txt">
					3次
				</view>
			</view>
		</view>
		<view class="phone" v-if="showPhone">
			<view class="p-row">
				<view class="left-txt">
					联系时间
				</view>
				<view class="right-txt" @click="showTime">
					<view class="u-m-r-8">
						{{timeValue[0]}}-{{timeValue[1]}}
					</view>
					<u-icon name="arrow-right" color="#999" size="16"></u-icon>
				</view>
			</view>
		</view>
		<u-picker :show="showPicker" ref="uPicker" :columns="columns" @confirm="confirm"></u-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				rowList: [{
						title: '对就业管家隐藏',
						txt: '开启后，就业管家将无法主动查看你的在线简历或通过虚拟电话号码直接联系你',
						checked: false,
						id: 1
					},
					{
						title: '不看就业管家职位',
						txt: '开启后，将看不到系统推荐就业管家职位',
						checked: false,
						id: 2
					},
					{
						title: '允许就业管家电话联系我',
						txt: '开启后，就业管家可通过虚拟电话号码直接联系你，但平台不会提供你的真实电话号码',
						checked: false,
						id: 3
					},
				],
				showPhone: false,
				showPicker: false,
				columns: [
					[],
					[]
				],
				timeValue: ['08:00', '18:00']
			}
		},
		created() {
			this.getTime()
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			getTime() {
				let startTime = new Date("2000-01-01T00:00:00");
				let endTime = new Date("2000-01-02T00:00:00");
				let timeArray = [];
				let currentTime = new Date(startTime);

				// 生成时间序列
				while (currentTime < endTime) {
					let hours = String(currentTime.getHours()).padStart(2, '0');
					let minutes = String(currentTime.getMinutes()).padStart(2, '0');
					timeArray.push(`${hours}:${minutes}`);
					currentTime.setTime(currentTime.getTime() + 5 * 60 * 1000); // 增加5分钟
				}

				// 将时间序列添加到columns的每项中
				this.columns = [
					[...timeArray],
					[...timeArray]
				];
				console.log(this.columns);
			},
			changeSwitch(item) {
				console.log(item);
				switch (item.id) {
					case 1:
						// 切换为true
						if (item.checked) {
							uni.showModal({
								title: '对就业管家隐藏',
								content: '隐藏后，你将不会被推荐给就业管家。除非你主动发起聊天，否则就业管家无法查看你的简历，也无法与你沟通或通过虚拟电话号码直接联系你。',
								success: function(res) {
									if (res.confirm) {
										uni.navigateTo({
											url: './viewPermisson_butler_raeson'
										})
									}else {
										item.checked = false
									}
								}
							});
							// 为true时将电话设置false，并且不显示电话
							this.rowList[2].checked = false
							this.showPhone = false
						} else {
							uni.showToast({
								icon: 'none',
								title: '已取消对就业管家隐藏',
							})
						}
						break;
					case 2:
						if (item.checked) {
							uni.navigateTo({
								url: './viewPermisson_butler_raeson_zhiwei'
							})
						} else {
							uni.showToast({
								icon: 'none',
								title: '已取消不看就业管家职位',
							})
						}
						break;
					case 3:
						if (item.checked) {
							if (this.rowList[0].checked) {
								uni.showToast({
									icon: 'none',
									title: '需要关闭“对就业管家隐藏”后才可开启此功能',
								})
								item.checked = false
							} else {
								this.showPhone = true
							}
						} else {
							this.showPhone = false
						}
						break;
				}
			},
			showTime() {
				this.showPicker = true
			},
			confirm(e) {
				console.log(e);
				this.timeValue = e.value
				this.showPicker = false
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 0 32rpx;
		height: 100vh;
		background: #fff;

		.row {
			padding: 32rpx 0;
			border-bottom: 1rpx solid #E6E6E6;

			.top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 14rpx;

				.tit-warp {
					display: flex;
					align-items: center;

					.tit {
						font-size: 28rpx;
						color: #333333;
						margin-right: 22rpx;
					}
				}

			}

			.btm {
				font-size: 24rpx;
				color: #999999;
			}
		}

		.phone {
			padding: 32rpx;
			font-size: 24rpx;
			color: #666666;
			background: #F5F5F5;
			border-radius: 16rpx;
			margin-top: 32rpx;

			.p-row {
				display: flex;
				justify-content: space-between;

				.right-txt {
					color: #333333;
					display: flex;
					align-items: center;
				}
			}

			.m-b {
				margin-bottom: 24rpx;
			}
		}
	}
</style>