<template>
	<view class="report-container">
		<scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltolower="lower" @scroll="scroll">
			<view class="report-title">请选择举报原因:</view>
			<view class="report-item" v-for="(item, index) in reportReasons" :key="index"
				@click="selectReportReason(item)">
				<view class="title">
					<view class="item-title">{{ item.title }}</view>
					<view class="arrow-icon">
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				<view class="item-desc">{{ item.desc }}</view>
			</view>
			<view class="report-record" @click="viewReportRecord">
				<uni-icons type="warning" size="20"></uni-icons>
				举报记录
			</view>
		</scroll-view>
		<uni-popup ref="popup" type="bottom" @close="dialogClose">
			<view class="test-popup">
				<view class="title-popup">
					<view class="text">
						具体情况说明
					</view>
					<u-icon name="close" @click="dialogClose" style="width: 32rpx; height: 32rpx;"></u-icon>
				</view>
				<view class="radio-popup">
					<radio-group @change="chang" class="radio-group">
						<view v-for="(item, index) in radioGroup" :key="index" class="radio-box">

							<text>{{ item.label }}</text>
							<radio value="" :checked="item.isChecked" />
						</view>
					</radio-group>
				</view>
				<u-button type="primary" text="确定" style="height: 80rpx; margin-bottom: 24rpx;"></u-button>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '../../uni_modules/uni-popup/components/uni-popup/uni-popup.vue'
	export default {
		components: {
			uniPopup
		},
		data() {
			return {
				reportReasons: [{
						title: '违法',
						desc: '在招聘过程中存在违法/违规言行或涉及政治等敏感',
					},
					{
						title: '违反劳动法',
						desc: '在招聘过程中存在违反劳动法的情况',
					},
					{
						title: '向求职者收费',
						desc: '涉及以各种名义直接或变相收取求职者费用',
					}, {
						title: '涉嫌诈骗',
						desc: '在招聘过程中诱导求职者投资或诈骗求职者',
					}, {
						title: '职位虚假',
						desc: '实际沟通的岗位情况与招聘者发布的职位信息不符',
					}, {
						title: '招聘者身份虚假',
						desc: '招聘者所招聘岗位或沟通言行中存在色情低俗情况',
					}, {
						title: '色情低俗',
						desc: '在招聘过程中存在违反劳动法的情况',
					}, {
						title: '人身攻击/歧视',
						desc: '招聘过程中存在辱骂、歧视、骚扰等情况',
					}, {
						title: '求职体验问题',
						desc: '招聘者及其公司存在影响求职体验的情况',
					},
				],
				selectedReason: null,
				scrollTop: 0,
				old: {
					scrollTop: 0
				},
				selectedValue: '',
				radioGroup: [

					{
						label: '政治敏感',
						isChecked: false
					},
					{
						label: '封建迷信',
						isChecked: false
					},
					{
						label: '人身伤害',
						isChecked: false
					},
					{
						label: '扣押证件',
						isChecked: false
					},
					{
						label: '涉及侵权行为',
						isChecked: false
					},
					{
						label: '涉及买卖平台账号',
						isChecked: false
					},
					{
						label: '索取信息',
						isChecked: false

					}
				]
			};
		},
		methods: {
			selectReportReason(reason) {
				this.selectedReason = reason;
				// 这里可以添加后续逻辑，比如跳转到新页面填写详细举报信息等
				console.log('选择的举报原因:', this.selectedReason);
				this.$refs.popup.open('bottom') //底部弹出
			},
			viewReportRecord() {
				// 这里添加查看举报记录的逻辑，比如跳转到举报记录页面
				console.log('查看举报记录');
				uni.navigateTo({
					url: '/pagesB/interviewDetails/complantList'
				})
			},
			lower: function(e) {
				console.log(e)
			},
			scroll: function(e) {
				console.log(e)
				this.old.scrollTop = e.detail.scrollTop
			},
			dialogClose() {
				this.$refs.popup.close()
			},
			chang(e) {
				this.activeRadio = e.detail.value;
				console.log(this.activeRadio);
			}

		}
	};
</script>

<style scoped>
	.report-container {
		padding: 15rpx;
		background-color: #fff;
	}

	.scroll-Y {
		height: 1100rpx;
	}

	.report-title {
		font-size: 24rpx;
		color: rgba(102, 102, 102, 1);
		margin-bottom: 24rpx;
	}

	.report-item {
		padding: 15rpx 0;
		border-bottom: 1rpx solid #ebebeb;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-evenly;
		height: 118rpx;
	}

	.title {
		display: flex;
		width: 686rpx;
		justify-content: space-between;
	}

	.item-title {
		font-size: 28rpx;
		display: flex;
		justify-content: space-between;
		color: #333;
	}

	.item-desc {
		width: 686rpx;
		font-size: 24rpx;
		color: #999;
		margin-top: 5rpx;
	}

	.arrow-icon {
		font-size: 30rpx;
		color: #999;
	}

	.report-record {
		position: fixed;
		bottom: 80rpx;
		width: 204rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 76rpx;
		background-color: rgba(255, 255, 255, 1);
		border-radius: 48rpx;
		right: 0rpx;
		box-shadow: 0 0 14px rgba(0, 0, 0, 0.12);
	}

	.test-popup {
		background: white; //默认全屏时灰色，只需要把你需要的部分改成白色
		border-radius: 20px; //设置弧度的大小
		height: 800rpx;
		width: 750rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
	}

	.title-popup {
		width: 686rpx;
		height: 44rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 32rpx;
	}

	.text {
		font-size: 32rpx;
	}

	.radio-popup {
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
		align-self: center;
		height: 650rpx;
	}

	.radio-group {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		width: 686rpx;
	}

	.radio-box {
		flex: 1;
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 72rpx;
	}
</style>