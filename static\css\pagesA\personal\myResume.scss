page {
    background-color: #F5F5F7;
}

.home-index {
    padding: 32rpx;
}

.header {
    background-color: #F5F5F7;
}

.tabs {
    display: flex;
    justify-content: space-between;
    
    // padding: 32rpx;
    .tab {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 88rpx;
        display: flex;
        flex: 1;
        background: #FFFFFF;
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        
        &:first-child {
            border-radius: 44rpx 0 0 44rpx;
        }
        
        &:last-child {
            margin-left: -40rpx;
            border-radius: 0 44rpx 44rpx 0;
        }
    }
    
    .pg_1 {
        clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
    }
    
    .pg_2 {
        clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
    }
    
    .active {
        color: #FFFFFF;
        background-color: #4F8CF0;
        font-weight: 600;
    }
}

.open {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 44rpx;
    height: 88rpx;
    background-color: #FFFFFF;
    margin: 32rpx 0;
    padding: 0 32rpx;
    
    .name {
        font-weight: 600;
        font-size: 28rpx;
        color: #333333;
    }
}

.footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    bottom: 0;
    height: 120rpx;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 10;
    
    .next {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 88rpx;
        width: 90%;
        font-size: 28rpx;
        font-weight: 600;
        border-radius: 16rpx;
    }
    
    .sure {
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        color: #FFFFFF;
    }
}