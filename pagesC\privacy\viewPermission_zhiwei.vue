<template>
	<view class="warp">
		<view class="row" v-for="item in rowList" :key="item.id">
			<view class="top">
				<view class="tit-warp">
					<view class="tit">
						{{item.title}}
					</view>
					<u-icon name="error-circle" color="#999999" size="14"></u-icon>
				</view>
				<u-switch v-model="item.checked" active-color="#4F8CF0" @change="changeSwitch(item)"></u-switch>
			</view>
			<view class="btm">
				{{item.txt}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				rowList: [{
					title: '不看外地职位',
					txt: '开启后，在千里马推荐列表，不再展示工作城市在外地的职位',
					checked: false,
					id: 1
				}],
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			changeSwitch(item) {
				console.log(item);
				switch (item.id) {
					case 1:
						// 切换为true
						if (item.checked) {
							uni.showModal({
								title: '不看外地职位',
								content: '不看外地职位，您将不会再推荐页看到外地职位',
								success: function(res) {
									if (res.confirm) {

									} else {
										item.checked = false
									}
								}
							});
						} else {
							
						}
						break;

				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 0 32rpx;
		height: 100vh;
		background: #fff;

		.row {
			padding: 32rpx 0;
			border-bottom: 1rpx solid #E6E6E6;

			.top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 14rpx;

				.tit-warp {
					display: flex;
					align-items: center;

					.tit {
						font-size: 28rpx;
						color: #333333;
						margin-right: 22rpx;
					}
				}

			}

			.btm {
				font-size: 24rpx;
				color: #999999;
			}
		}
	}
</style>