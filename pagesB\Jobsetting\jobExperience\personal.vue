<template>
	<!-- 个人信息 -->
	<view class="container">
		<view class="context">
			<radio-group @change="chang" class="radio-group">
				<view class="recruitment-item" v-for="(item, index) in recruitmentList" :key="index">
					<scroll-view class="scroll-view_H" scroll-x="true">
						<view class="item-recrutment">
							<view class="item-box">
								<radio value="" :checked="item.isChecked" style="width: 32rpx; height: 32rpx;" />
								<!-- <img src="/staic/jobsetting/radio.png" alt="" style="width: 36rpx; height: 36rpx;" /> -->
								<view class="personal-info">
									<view class="personal">
										<view class="name-and-status">
											<text class="name">{{item.name}}</text>
											<text class="status">
												<text v-if="item.Working" style="color: rgba(87, 213, 28, 1);">●</text>
												<text v-else style="color: rgba(213, 20, 2, 1.0);">●</text>
												<text v-if="item.Working">在职</text>
												<text v-else>离职</text>
											</text>
										</view>
										<text
											style="font-size: 24rpx; color: rgba(153, 153, 153, 1);">{{item.sex}}·{{item.Date}}·{{item.Race}}·{{item.Education}}</text>
										<view class="basic-info">
											<view class="basic-perinfo">
												<view class="phone">
													<img src="/static/jobsetting/Phone.png" alt="" />
													{{item.Phone}}
												</view>
												<view class="phone">
													<img src="/static/jobsetting/WX.png" alt="" />
													{{item.wx}}
												</view>
											</view>
										</view>
									</view>
									<view class="icons">
										<view class="personal-info-avatar" @click="routerPersonal"></view>
										<image :src="editor" @click="editItem(index)"
											style="width: 32rpx; height: 32rpx; align-self: flex-end;">
										</image>
									</view>
								</view>
							</view>
							<view class="delete-btn" v-if="item.showDelete" @click="deleteItem(index)">
								<text style="width: 56rpx; font-size: 28rpx;">删除</text>
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="add" @click="add(e)">
					添加
				</view>
			</radio-group>
		</view>
		<view class="footer">
			<view class="confirm-btn" @click="confirmAction">确定</view>
		</view>
	</view>
</template>

<script>
	import editor from '../../../static/jobsetting/editor-setting.png'
	export default {
		data() {
			return {
				recruitmentList: [{
					name: '那凉了啊',
					status: true,
					sex: '男',
					Date: 24,
					Race: '汉族',
					Education: '本科',
					Phone: '17639222235',
					wx: '17639222235',
					avatar: '',
					ischecked: false,
					Working: true,
					showDelete: true
				}, {
					name: '那凉了啊',
					status: true,
					sex: '男',
					Date: 24,
					Race: '汉族',
					Education: '本科',
					Phone: '17639222235',
					wx: '17639222235',
					avatar: '',
					ischecked: false,
					Working: false,
					showDelete: true
				}, ],
				activeRadio: '',
				editor
			};
		},
		methods: {
			editItem(index) {
				// 编辑逻辑，比如弹出编辑框等
				console.log('编辑第', index, '项');
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/personaldetials'
				})
			},
			deleteItem(index) {
				this.recruitmentList.splice(index, 1);
			},
			confirmAction() {
				// 确定操作逻辑
				console.log('执行确定操作');
			},
			chang(e) {
				this.activeRadio = e.detail.value;
				console.log(this.activeRadio);
			}
		}
	};
</script>

<style scoped>
	.container {
		/* padding: 20rpx; */
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		background-color: rgba(245, 245, 247, 1);
	}

	.context {
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}


	.recruitment-item {
		width: 100%;
		margin-bottom: 42rpx;
	}

	.item-recrutment {
		display: flex;
		background-color: #fff;
		border-radius: 24rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
		align-items: center;
		width: 642rpx;
		height: 220rpx;
	}

	.scroll-view_H {
		/* white-space: nowrap; */
		width: 100%;
	}

	.item-box {
		width: 686rpx;
		height: 220rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
	}

	.status {
		font-size: 28rpx;
		color: #666;
		margin-left: 16rpx;
	}

	.personal-info {
		margin-bottom: 20rpx;
		width: 686rpx;
		height: 156rpx;
		margin-left: 24rpx;
		/* padding-bottom: 32rpx; */
		/* border-bottom: 1rpx solid rgba(230, 230, 230, 1); */
		display: flex;
		/* justify-content: space-between; */
	}

	.personal {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.personal-info-avatar {
		width: 90rpx;
		height: 90rpx;
		border-radius: 100%;
		background-color: rgba(217, 217, 217, 1);
	}

	.icons {
		display: flex;
		justify-content: space-between;
		flex-direction: column;
	}

	.basic-info {
		font-size: 28rpx;
		color: #333;
		/* flex: 1; */
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.basic-perinfo {
		display: flex;
	}

	.phone {
		margin-right: 20rpx;
		display: flex;
		justify-content: space-between;
	}

	.phone img {
		width: 32rpx;
		height: 32rpx;
	}


	.item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
		margin-top: 32rpx;
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.time {
		font-size: 28rpx;
		color: #999;
	}

	.item-content {
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
		align-items: flex-start;
		flex: 1;
	}

	.item-context {
		width: 562rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		height: 100%;
		margin-left: 32rpx;
	}

	.tag {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 174rpx;
		height: 64rpx;
		background-color: rgba(243, 243, 243, 1);
		color: rgba(51, 51, 51, 1);
		font-size: 28rpx;
		border-radius: 8rpx;
	}

	.tag-inner {
		width: 30rpx;
		height: 30rpx;
		border: 2rpx solid #ccc;
		border-radius: 50%;
		margin-right: 10rpx;
	}

	.checked {
		background-color: #007AFF;
	}

	.tag-text {
		font-size: 30rpx;
	}

	.description {
		/* flex: 1; */
		height: 80rpx;
		width: 562rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.edit-icon {
		width: 40rpx;
		height: 40rpx;
		margin-left: 10rpx;
	}

	.delete-btn {
		background-color: #FF5050;
		color: #fff;
		text-align: center;
		border-radius: 24rpx;
		padding: 48rpx;
		height: 159rpx;
		width: 56rpx;
		margin-left: 48rpx;
		/* 		margin-left: 24rpx; */
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.description img {
		width: 32rpx;
		height: 32rpx;
	}

	.add {
		color: rgba(79, 140, 240, 1);
		font-size: 26rpx;
	}
</style>

<style>
	.description-text text {
		font-size: 28rpx;
		display: -webkit-box;
		/*弹性伸缩盒子模型显示*/
		-webkit-box-orient: vertical;
		/*排列方式*/
		-webkit-line-clamp: 2;
		/*显示文本行数(这里控制多少行隐藏)*/
		overflow: hidden;
		/*溢出隐藏*/
	}
</style>