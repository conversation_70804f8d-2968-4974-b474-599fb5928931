const AUTH_BASE = 'common-v2/auth'
// export const login = `${AUTH_BASE}/cellphone-login`
import {
    request
} from "./request"

//就业管家授权详情
export const authorizeIndex = (data) => {
    return request({
        url: `/member/authorize/index`,
        data,
        method: 'post'
    })
}

// 领取任务
export const getTask = (data) => {
    return request({
        url: `/task/store`,
        data,
        method: 'post'
    })
}

// 领取任务记录
export const getTaskIndex = (data) => {
    return request({
        url: `/task/index`,
        data,
        method: 'post'
    })
}
// 领取任务记录
export const getTaskPromotion = (data) => {
    return request({
        url: `/task/promotions`,
        data,
        method: 'post'
    })
}

// 任务记录详情
export const getTaskIndexDetail = (data) => {
    return request({
        url: `/task/show`,
        data,
        method: 'post'
    })
}

export const collectHeadhunter = (data) => {
    return request({
        url: `/collect/headhunter/store`,
        data,
        method: 'post'
    })
}

export const clearCollectHeadhunter = (data) => {
    return request({
        url: `/collect/headhunter/destroy`,
        data,
        method: 'post'
    })
}

export const selectAuthorize = (data) => {
    return request({
        url: `member-v2/member/authorize/user-audit`,
        data,
        method: 'post'
    })
}

export const memberAuthorizeUserAudit = (data) => {
    return request({
        url: `/member/authorize/user-audit`,
        data,
        method: 'post'
    })
}

export const memberAuthorizeAuditHandle= (data) => {
    return request({
        url: `/member/authorize/audit-handle`,
        data,
        method: 'post'
    })
}


export const memberDraftAudit= (data) => {
    return request({
        url: `/member/draft/audit`,
        data,
        method: 'post'
    })
}

export const memberDraftList= (data) => {
    return request({
        url: `/member/draft`,
        data,
        method: 'post'
    })
}

