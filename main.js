import App from './App';

// #ifndef VUE3
import store from '@/store/index.js';
import uView from 'uview-ui';
import Vue from 'vue';
import './uni.promisify.adaptor';
Vue.prototype.$store = store;
// Vue.prototype.$request = request
Vue.use(uView);
Vue.config.productionTip = false;
App.mpType = 'app';
const app = new Vue({
	store,
	...App,
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue';
export function createApp() {
	const app = createSSRApp(App);
	return {
		app,
	};
}
// #endif
