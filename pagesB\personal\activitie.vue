<template>
	<!-- 我的活动记录 -->
	<view class="container">
		<u-navbar bgColor="transparent" placeholder leftIconColor="#FFFFFF" :autoBack="true" />
		<scroll-view class="scroll-view" :scroll-y="true">
			<view class="scroll-container">
				<view class="title">我的活动记录</view>
				<view class="sub-title">您可以在此处管理您的活动记录数据，您可以自主查看和了解在哪些地方收集了您的活动记录数据</view>
				<view class="item" v-for="v in list" :key="v.key" @click="onRoute(v.key)">
					<image class="item-image" :src="v.image" mode=""></image>
					<view class="item-text">
						<view class="text-start">
							<text class="text">{{ v.title }}</text>
							<image class="text-image" src="/static/new/右箭头@2x1.png" alt="" />
						</view>
						<view class="text-end">{{ v.subTitle }}</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			list: [
				{
					key: 'search_record',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/h0GcG0qej6uR2wUIq1c4t0XONJhZ9riuI9HhPbY3.png',
					title: '搜索记录',
					subTitle: '有关你使用招聘呗的搜索记录信息可以帮助我们更好的个性化你的搜索结果',
				},
				{
					key: 'job_record',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/BVap6XP7UcFTz2SATxSP99fvySD45bdqFofrpGVn.png',
					title: '求职记录',
					subTitle: '有关你使用招聘呗的求职记录信息可以帮助我们更好的个性化你的在线体验',
				},
				{
					key: 'favor_record',
					image: 'https://api-test.zhaopinbei.com/storage/uploads/images/EScK7ViqhNk0QHWXvSZ8paVrjjuKWyRlezc5Djg3.png',
					title: '收藏记录',
					subTitle: '有关你使用招聘呗的收藏职位的信息可以帮助我们更好的个性化你的在线体验',
				},
			],
		};
	},
	methods: {
		onRoute(url) {
			uni.$u.route({
				url: `/pagesB/personal/${url}`,
			});
		},
	},
};
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f7;
	/*background-image: url(https://api-test.zhaopinbei.com/storage/uploads/images/eJD7hMDcwwkO7sZuoklBAKxIbSSeSqaj4JmFQpvk.png);*/
	background-image: url(https://api-test.zhaopinbei.com/storage/uploads/images/4QautJRmo568ayR1Nrmt6uW74w0Z4j0tFpUXBJBG.png);
	background-size: contain;
	background-repeat: no-repeat;
	display: flex;
	flex-direction: column;

	.scroll-view {
		flex: 1;
		overflow-y: auto;

		.scroll-container {
			padding: 32rpx;
			display: flex;
			flex-direction: column;
			gap: 32rpx;
			padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

			.title {
				color: #ffffff;
				font-size: 32rpx;
			}

			.sub-title {
				color: #ffffff;
				font-size: 24rpx;
			}

			.item {
				display: flex;
				gap: 24rpx;
				padding: 32rpx;
				border-radius: 24rpx;
				background-color: #ffffff;

				.item-image {
					width: 136rpx;
					height: 136rpx;
				}

				.item-text {
					flex: 1;
					display: flex;
					flex-direction: column;
					gap: 10rpx;

					.text-start {
						display: flex;
						align-items: center;

						.text {
							color: #333333;
							font-size: 28rpx;
							margin-inline-end: auto;
						}

						.text-image {
							width: 32rpx;
							height: 32rpx;
						}
					}

					.text-end {
						color: #666666;
						font-size: 24rpx;
					}
				}
			}
		}
	}
}
</style>
