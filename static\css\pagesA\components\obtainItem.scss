.wrap {
    display: flex;
    flex-direction: column;
    padding: 0 32rpx;
    margin-bottom: 32rpx;
    background-color: #FFFFFF;
    border-radius: 24rpx;
}

.item {
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 24rpx 0;
    
    .item-up {
        display: flex;
        flex: 1;
        
        & > image {
            width: 96rpx;
            height: 96rpx;
            border-radius: 50%;
        }
        
        .info {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1;
            padding-left: 24rpx;
            
            .user {
                display: flex;
                justify-content: space-between;
                align-items: center;
                
                .userInfo {
                    display: flex;
                    align-items: center;
                    
                    .name {
                        display: flex;
                        align-items: center;
                        font-weight: 600;
                        font-size: 32rpx;
                        color: #333333;
                        
                        image {
                            margin-left: 16rpx;
                            width: 32rpx;
                            height: 32rpx;
                        }
                    }
                }
            }
            
            .desc {
                display: flex;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
                margin-top: 16rpx;
                
                .desc-item {
                    border-right: 1px solid #999999;
                    padding: 0 12rpx;
                    
                    &:first-child {
                        padding-left: 0;
                    }
                    
                    &:last-child {
                        border-right: none;
                    }
                }
            }
            
            .flag {
                font-weight: 400;
                font-size: 22rpx;
                color: #999999;
            }
        }
    }
    
    .item-down {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20rpx;
        
        .tags {
            display: flex;
            
            .tag {
                display: flex;
                align-items: center;
                background: #F6F6F6;
                border-radius: 8rpx;
                height: 46rpx;
                padding: 0 12rpx;
                font-weight: 400;
                font-size: 22rpx;
                color: #666666;
                margin-right: 16rpx;
            }
        }
        
        .dot {
            transform: rotate(90deg);
        }
    }
    
    .arrow {
        position: absolute;
        right: 0;
        top: 50%;
    }
}

.flexRow {
    display: flex;
    flex-direction: row;
}


.bottomBtn3 {
	width: 100%;
	margin-bottom: 30rpx;
	display: flex;
	justify-content: flex-end;
	.comm {
		border-radius: 12rpx;
		background-color: #edf3fd;
		color: #4F8CF0;
		padding: 12rpx 16rpx;
		text-align: center;
		width: 130rpx;
	}
}
