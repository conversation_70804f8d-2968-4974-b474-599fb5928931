<template>
  <view class="warp">
    <view class="title">注销账号</view>
    <view class="cont">
      <view class="line"
        >很遗憾，招聘呗无法继续为您提供服务，感谢您一直以来的陪伴。注销账号前，我们需要您确认以下信息，以保障您的账号安全与财产权益。</view
      >
      <view class="line">
        (1)自愿放弃您在招聘呗的各类财产权益（招聘币、直播产品、付费课程、申请中退款等各类财产权益）
      </view>
      <view class="line"
        >(2)账号是您本人通过我们提供的官方渠道注册，且账号使用人系您本人</view
      >
      <view class="line">(3)账号内不存在已提供服务但未支付的订单</view>
      <view class="line">(4) 账号不存在任何未处理完毕的投诉、争议或纠纷</view>
      <view class="line"
        >(5)
        账号最近三个月内无任何被检测到异常登录或其他不安全、异常行为的记录</view
      >
      <view class="line"
        >(6) 账号无任何未处理完毕的违规被限制、冻结或其他处罚的记录</view
      >
      <view>详细内容请阅读<text class="tip">《注销协议》</text></view>
    </view>
    <view class="footer">
      <view class="next" @click="show = true"> 已慎重考虑，确认注销 </view>
    </view>
    <u-popup :show="show" mode="bottom" @close="show = false">
      <view class="pop">
        <view class="pop-tit">
          <text>注销提示</text>
          <u-icon name="close" color="#666666" size="24"></u-icon>
        </view>
        <view class="pop-cont"
          >点击确认注销即表示您自愿放弃招聘呗内的所有财产权益。</view
        >
        <view class="pop-btn">
          <view class="btn1" @click="show = false">取消</view>
          <view class="btn2" @click="show = false">确认注销</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>
<script>
export default {
  data() {
    return {
      show: false,
    };
  },
};
</script>
<style lang="scss" scoped>
.warp {
  padding: 32rpx;
  color: #333333;

  .title {
    font-size: 32rpx;
  }
  .cont {
    font-size: 28rpx;
    line-height: 56rpx;
    .tip {
      color: #4f8cf0;
    }
  }
}
.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 196rpx;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;

  .next {
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #999999;
    height: 88rpx;
    width: 90%;
    border: 2rpx solid #cccccc;
    border-radius: 16rpx;
  }
}
.pop {
  padding: 32rpx;
  .pop-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
    color: #999999;
    margin-bottom: 32rpx;
  }
  .pop-cont {
    font-size: 28rpx;
    color: #666666;
    margin-bottom: 32rpx;
  }
  .pop-my {
    font-size: 28rpx;
    color: #999999;
  }
  .pop-btn {
    width: 100%;
    height: 80rpx;
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    line-height: 80rpx;
    margin-top: 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn1 {
      width: 45%;
      text-align: center;
      line-height: 80rpx;
      border-radius: 12rpx;
      color: #666666;
      background: #f7f7f7;
    }
    .btn2 {
      width: 45%;
      background: #4f8cf0;
      text-align: center;
      line-height: 80rpx;
      border-radius: 12rpx;
    }
  }
  .pop-box {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    color: #999999;
    margin-bottom: 32rpx;
    gap: 230rpx;
    .pop-inn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-right: 64rpx;
      img {
        width: 76rpx;
        height: 76rpx;
      }
      text {
        font-size: 24rpx;
        color: #999;
        margin-top: 10rpx;
      }
    }
  }
}
</style>
