<template>
	<view class="">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="tabs">
					<u-tabs lineWidth="20" :current="tabIndex" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" @click="changeTab"></u-tabs>
				</view>
				<view class="search-wrap">
					<u-search placeholder="请输入任务名称" bgColor="#FFFFFF" v-model="page.form.title" @custom="custom"
						@clear="clear"></u-search>
				</view>
				<view class="filters">
					<!-- <view class="filter">
                        开始时间<image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        结束时间<image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        任务佣金<image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        任务状态<image src="/static/images/index/down.png" mode=""></image>
                    </view> -->

					<view class="filter">
						<picker mode="date" :value="page.form.created_at.start" @change="bindDateChange">
							<view class="d-picker">{{page.form.created_at.start?page.form.created_at.start:'开始时间'}}
							</view>
						</picker>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>
					<view class="filter">
						<picker mode="date" :value="page.form.created_at.end" @change="endBindDateChange">
							<view class="d-picker">{{page.form.created_at.end?page.form.created_at.end:'结束时间'}}</view>
						</picker>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>
					<view class="filter">
						<picker @change="changeCommission" :value="commissionIndex" :range="commissionList"
							range-key="name">
							<view class="d-picker">{{commissionList[commissionIndex]['name']}}</view>
						</picker>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>
					<view class="filter">
						<picker @change="changeStatus" :value="statusIndex" :range="statusList" range-key="name">
							<view class="d-picker">{{statusList[statusIndex]['name']}}</view>
						</picker>
						<image src="/static/images/index/down.png" mode=""></image>
					</view>
				</view>
			</view>

		</u-sticky>

		<view class="list">
			<qlm-task-item v-for="(item,index) in page.data" :key="index" :item="item"></qlm-task-item>
		</view>
		<Pages :status="page.status"></Pages>
	</view>
</template>

<script>
	import {
		getTaskIndex
	} from '../../config/member_api.js'
	import QlmTaskItem from '../components/qlmTaskItem.vue'
	import Pages from "../../components/pages.vue";

	export default {
		components: {
			Pages,
			QlmTaskItem,
		},
		data() {
			return {
				page: {
					form: {
						title: "",
						unit_total: null,
						active_status: "",
						page: 1,
						limit: 10,
						created_at: {
							start: "",
							end: "",
						},
						type: 'job',
					},
					status: 'loadmore',
					more: false,
					data: [],
				},

				tabIndex: 0,
				tabs: [
					// {
					// 	value: "custom",
					// 	name: '全部'
					// },
					// {
					// 	value: "custom",
					// 	name: '进行中'
					// },
					// {
					// 	value: "custom",
					// 	name: '已结束'
					// },
					{
						value: "job",
						name: '职位任务',
					},
					// 	{
					// 	name: '推广任务',
					// },
					{
						value: "custom",
						name: '自定义任务'
					},
				],
				statusList: [{
					value: "",
					name: '状态',
				}, {
					value: 'active',
					name: '进行中',
				}, {
					value: 'end',
					name: '已结束',
				}],
				statusIndex: 0,
				commissionList: [{
					value: null,
					name: '任务佣金',
				}, {
					value: 1000,
					name: 1000,
				}, {
					value: 2000,
					name: 2000,
				}, {
					value: 3000,
					name: 3000,
				}],
				commissionIndex: 0,
			}
		},
		onLoad() {
			// this.page.form.created_at.start = uni.$u.timeFormat(this.start, 'yyyy-mm-dd');
			// this.page.form.created_at.end = uni.$u.timeFormat(this.start, 'yyyy-mm-dd');
			this.getTaskIndex()
		},
		//触底加载更多
		onReachBottom() {
			if (this.page.more) {
				this.page.status = 'loading';
				this.page.form.page++
				this.getTaskList()
			}
		},
		methods: {
			initPage() {
				this.page.data = [];
				this.page.form.page = 1;
				this.page.status = 'loadmore';
			},
			// 获取领取任务记录
			async getTaskIndex() {
				const res = await getTaskIndex(this.page.form);
				this.page.data = this.page.data.concat(res.data.data);
				console.log('pagepage', this.page.data)
				// 返回false代表没有下一页
				this.page.more = res.data.more;
				this.page.status = this.more ? "loadmore" : "nomore";

			},
			clear() {
				this.page.form.title = "";
			},
			custom() {
				this.initPage();
				this.getTaskIndex();
			},
			changeTab(e) {
				this.tabIndex = e.index;
				this.page.form.type = this.tabs[this.tabIndex]['value'];
				this.page.form.created_at.start = '';
				this.page.form.created_at.end = '';
				this.page.form.unit_total = this.commissionList[0]['value'];
				this.commissionIndex = 0
				this.statusIndex = 0
				this.page.form.active_status = this.commissionList[0]['value'];
				// 更新列表
				this.initPage();
				this.getTaskIndex();
			},
			bindDateChange(e) {
				this.page.form.created_at.start = e.detail.value;
				this.initPage();
				this.getTaskIndex();
			},

			endBindDateChange(e) {
				this.page.form.created_at.end = e.detail.value;
				this.initPage();
				this.getTaskIndex();
			},

			changeStatus(e) {
				this.statusIndex = e.detail.value;
				this.page.form.active_status = this.statusList[this.statusIndex]['value'];
				console.log(this.page.form.active_status);
				this.initPage();
				this.getTaskIndex();
			},
			changeCommission(e) {
				this.commissionIndex = e.detail.value;
				this.page.form.unit_total = this.commissionList[this.commissionIndex]['value'];
				this.initPage();
				this.getTaskIndex();
			}
		}
	}
</script>
<style lang="scss" src="../../static/css/pagesA/list/qlm_task_management_list.scss"></style>