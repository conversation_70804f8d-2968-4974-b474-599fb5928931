<template>
	<view>
		<view class="container">
			<!-- 标题部分 -->
			<view class="card-container">
				<view class="card">
					<view class="card-content">
						<view class="card-title">分享工作时间有什么用？</view>
						<view class="card-desc">调研发现85%的优秀候选人在看公司时会查工作时间和休假政策，建议完善资料
							* 标准时间仅为求职者提供参考，不代表公司所有职位工作时间</view>
					</view>
				</view>
			</view>
			<view class="container-con">
				<!-- 工作时间部分 -->
				<view class="work-time-box">
					<text class="label">工作时间</text>
					<view class="time-content" @click="openTimePicker">
						<text>{{ `上午${selectedStartTime} - 下午${selectedEndTime}` }}</text>
						<image class="icon" :src="arrowDown" mode="aspectFit"></image>
					</view>
				</view>
				<view class="overtime-box">
					<text class="label">加班情况（可选）</text>
					<view class="option-group">
						<view class="option" :class="{ 'option-active': overtime === '不加班' }" @click="overtime = '不加班'">
							不加班
						</view>
						<view class="option" :class="{ 'option-active': overtime === '偶尔加班' }"
							@click="overtime = '偶尔加班'">
							偶尔加班
						</view>
						<view class="option" :class="{ 'option-active': overtime === '弹性工作' }"
							@click="overtime = '弹性工作'">
							弹性工作
						</view>
					</view>
				</view>
				<!-- 休息时间部分 -->
				<view class="rest-time-box">
					<text class="label">休息时间（可选）</text>
					<view class="option-group">
						<view class="option" :class="{ 'option-active': rest === '双休' }" @click="rest = '双休'">
							双休
						</view>
						<view class="option" :class="{ 'option-active': rest === '排班轮休' }" @click="rest = '排班轮休'">
							排班轮休
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 时间选择底部弹窗 -->
		<worktimeVue v-if="showTimePicker" @cancle="closeTimePicker" @confirm="selectTime"></worktimeVue>
	</view>
</template>

<script>
	import worktimeVue from '../companyNotice/worktime.vue';
	export default {
		components: {
			worktimeVue
		},
		data() {
			return {
				arrowDown:'https://api-test.zhaopinbei.com/storage/uploads/images/437h8NsrYviiLgTPxRPCpkqrP0wF7bZVxQZbJ22P.png',
				overtime: null,
				rest: null,
				showTimePicker: false,
				selectedStartTime: '09:00',
				selectedEndTime: '18:00',
				isLoading: false,
				timeError: '',
				// 存储 picker 组件的时间值
				selectedTimeValue: `${this.selectedStartTime}:00`
			};
		},
		methods: {
			// 点击时间区域，打开时间选择弹窗
			openTimePicker() {
				// 可以添加一些前置检查逻辑
				// if (this.isLoading) {
				// 	return;
				// }
				this.timeError = ''; // 清空错误提示
				this.showTimePicker = true;
				console.log('showTimePicker:', this.showTimePicker);
			},
			// 关闭时间选择弹窗
			closeTimePicker() {
				this.showTimePicker = false;
				this.isLoading = false; // 关闭弹窗时重置加载状态
			},
			// 选择时间
			selectTime(time) {
				if (!time || !time.start_time || !time.end_time) {
					this.timeError = '请选择有效的开始时间和结束时间';
					return;
				}
				// 可以添加时间顺序验证
				const start = new Date(`1970-01-01T${time.start_time}`);
				const end = new Date(`1970-01-01T${time.end_time}`);
				if (start >= end) {
					this.timeError = '结束时间必须晚于开始时间';
					return;
				}
				this.isLoading = true; // 模拟保存数据时的加载状态
				// 模拟异步操作
				setTimeout(() => {
					this.selectedStartTime = time.start_time;
					this.selectedEndTime = time.end_time;
					this.closeTimePicker();
					this.isLoading = false;
				}, 1000);
			}
		}
	}
</script>


<style scoped>
	.status-btn {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 92rpx;
		margin: 30rpx;
		background-color: #007AFF;
	}

	.card-container {
		width: 100%;
		padding: 16px;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0px;
		box-shadow: none;
	}

	.card {
		width: 686rpx;
		height: 166rpx;
		margin: 0px auto;
		position: relative;
		background-color: white;
		border-radius: 10rpx;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		background: linear-gradient(to bottom, rgba(242, 248, 255, 1), rgba(255, 255, 255, 1));
		border-radius: 10rpx;
		z-index: 1;
	}

	.card::before {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0rpx;
		left: 0rpx;
		content: '';
		border: 2rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	}

	.card::after {
		position: absolute;
		width: 100%;
		height: 100%;
		bottom: 0rpx;
		right: 0rpx;
		/* margin: 0px auto; */
		content: '';
		border: 2rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	}

	.card-content {
		padding: 20rpx;
		box-shadow: none;
	}

	.card-title {
		width: 606rpx;
		height: 34rpx;
		font-size: 24rpx;
		font-weight: bold;
		color: rgba(51, 51, 51, 1);
		margin-bottom: 20rpx;
		line-height: 28.12rpx;
	}

	.card-desc {
		width: 606rpx;
		height: 28rpx;
		font-size: 20rpx;
		color: #8D9AAA;
		line-height: 23.44rpx;
	}

	.container-con {
		width: 686rpx;
		height: 512rpx;
		margin: 24rpx auto;
		padding: 32rpx;
	}

	.example-body {
		/* #ifndef APP-NVUE */
		display: block;
		/* #endif */
		padding: 15px;
		flex-direction: row;
	}

	.container {
		padding: 20rpx;
		height: 726rpx;
	}

	.title-box {
		background-color: #f0f5ff;
		padding: 15rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}

	.title {
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}

	.description {
		font-size: 24rpx;
		color: #666;
		margin-top: 10rpx;
	}

	.work-time-box {
		margin-bottom: 24rpx;
		height: 116rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}

	.label {
		font-size: 24rpx;
		color: rgba(102, 102, 102, 1);
		/* height: 116rpx; */
		line-height: 28.12rpx;
		margin-bottom: 24rpx;
	}

	.time-content {
		display: flex;
		align-items: center;
		width: 622rpx;
		justify-content: space-between;
	}

	.time-content text {
		font-size: 28rpx;
		color: rgba(51, 51, 51, 1);
		line-height: 32.82.rpx;
	}


	.icon {
		width: 28rpx;
		height: 28rpx;
		margin-left: 10rpx;
	}

	.overtime-box,
	.rest-time-box {
		margin-bottom: 24rpx;
		height: 152rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}

	.option-group {
		display: flex;
		flex-wrap: wrap;
	}

	.option {
		padding: 10rpx 20rpx;
		border-radius: 8rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		font-size: 30rpx;
		color: #333;
		cursor: pointer;
		border: 2rpx solid rgba(230, 230, 230, 1);
	}

	.option-active {
		background-color: rgba(242, 247, 255, 1);
		color: rgba(79, 140, 240, 1);
		border: 1px solid rgba(79, 140, 240, 1);
	}

	/* 这里是示例样式，可根据实际需求调整 */
	.uni-datetime-picker {
		background-color: white;
		border-radius: 10rpx;
	}
</style>
