<template>
	<view class="content">
		<view class="product-intro">
			<text style="font-size: 44rpx; margin-bottom: 24rpx;">产品介绍</text>
			<view class="product-text">
				<textarea v-model="productDesc" placeholder="例如：xxxxxxxx" maxlength="500">

				</textarea>
				<text class="count">{{productDesc.length}}/500</text>
			</view>
		</view>
		<!-- <button type="primary" @click="saveProductDesc" class="save">保存</button> -->
		<view class="footview">
			<view class="footer-card">
				<button class="save" @click="saveProductDesc">保存</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				productDesc: ''
			};
		},
		methods: {
			saveProductDesc() {
				// 这里简单示例保存到本地缓存，实际可根据需求修改保存方式
				uni.setStorageSync('productDescription', this.productDesc);
				uni.showToast({
					title: '保存成功',
					icon: 'none'
				});
			}
		}
	};
</script>

<style>
	.content {
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		overflow: hidden;
	}

	.product-intro {
		height: 1224rpx;
		display: flex;
		flex-direction: column;
		width: 686rpx;
		margin-left: 24rpx;
	}

	.product-text {
		position: relative;
		display: flex;
		height: 678rpx;
	}

	textarea {
		width: 686rpx;
		height: 678rpx;
		border: 2rpx solid #ddd;
		border-radius: 10rpx;
		padding-top: 20rpx;
		padding-left: 20rpx;
		font-size: 24rpx;
		color: rgba(153, 153, 153, 1);
	}

	.count {
		display: block;
		text-align: right;
		color: #999;
		bottom: 0rpx;
		right: 0rpx;
		position: absolute;
		margin-right: 32rpx;
	}

	.save {
		width: 570rpx;
		height: 80rpx;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 28rpx;
	}

	.footer-card {
		/* margin-top: 40rpx; */
		background-color: white;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 1.1);
		/* 卡片阴影 */
		padding: 40rpx;
		/* height: 196rpx; */
		width: 100vw;
		/* margin-left: -56rpx; */
	}
</style>