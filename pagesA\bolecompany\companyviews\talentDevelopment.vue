<template>
	<view class="talent-development-container">
		<view class="talent-development">
			<!-- 人才发展部分 -->

			<view style="width: 686rpx;
					    margin: 0 auto;">
				<view class="info-card">
					<p class="font-bold">
						填写人才发展有什么用?
					</p>
					<p class="font-blod-subtitle">
						企业福利是基础信息，85%的千里马将此作为筛选条件，完善后可突出展示。
					</p>
				</view>
			</view>


			<!-- 晋升制度部分 -->
			<view class="section">
				<text class="section-title">晋升制度</text>
				<view class="option-container">
					<view v-for="(option, index) in promotionOptions.slice(0, 14)" :key="index" class="custom-tag"
						:class="{ 'is-selected': option.selected }" @click="togglePromotionSelection(index)">
						<text>{{ option.label }}</text>
						<view v-if="option.selected" class="selected-mark">
							<text class="checkmark">✓</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 人才激励部分 -->
			<view class="section">
				<text class="section-title">人才激励</text>
				<view class="option-container">
					<view v-for="(option, index) in incentiveOptions.slice(0, 14)" :key="index" class="custom-tag"
						:class="{ 'is-selected': option.selected }" @click="toggleIncentiveSelection(index)">
						<text>{{ option.label }}</text>
						<view v-if="option.selected" class="selected-mark">
							<text class="checkmark">✓</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 能力培养部分 -->
			<view class="section">
				<text class="section-title">能力培养</text>
				<view class="option-container">
					<view v-for="(option, index) in trainingOptions.slice(0, 14)" :key="index" class="custom-tag"
						:class="{ 'is-selected': option.selected }" @click="toggleTrainingSelection(index)">
						<text>{{ option.label }}</text>
						<view v-if="option.selected" class="selected-mark">
							<text class="checkmark">✓</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 更多信息部分 -->


			<view class="section">
				<text class="section-title">更多信息</text>
				<view class="input-container">
					<textarea v-model="moreInfo" :maxltext-plcength="500" placeholder="介绍更多相关信息，可以突出雇主品牌优势哦"
						@input="handleMoreInfoInput" :auto-height="{ minRows: 4, maxRows: 4}"
						placeholder-class="text-plc"></textarea>
					<text class="count-tip" :class="{ 'zero-count': moreInfo.length === 0 }">{{ moreInfo.length }}
						<text style="color: rgba(153, 153, 153, 1);">/500</text></text>
				</view>
			</view>

			<view class="footer-left" @click="toggleCarefulHide">
				<text class="footer-text">上传注意事项</text>
				<image class="footer-icon" :src="help"></image>
			</view>
		</view>

		<Careful v-show="carefulhide" />

		<view class="footer">
			<button class="save" @click="saveTalentDevelopmentInfo">保存</button>
		</view>
	</view>
</template>

<script>
	/* import help from 'https://api-test.zhaopinbei.com/storage/uploads/images/rYxkkwuyCNnqb4YUwWfJXzktOukmQ2dF2OuFvchb.png' */

	import Careful from '../companyNotice/carefulNotice.vue'

	export default {
		components: {

			Careful

		},
		data() {
			return {
				// 晋升制度选项
				promotionOptions: [{
						label: '考核晋升',
						selected: false
					},
					{
						label: '定期晋升',
						selected: false
					},
					{
						label: '完善的晋升机制',
						selected: false
					}
				],
				// 人才激励选项
				incentiveOptions: [{
						label: '定期普调',
						selected: false
					},
					{
						label: '定期绩效调薪',
						selected: false
					},
					{
						label: '晋级涨薪',
						selected: false
					},
					{
						label: '项目奖金',
						selected: false
					},
					{
						label: '团队奖金',
						selected: false
					},
					{
						label: '个人奖金',
						selected: false
					},
					{
						label: '绩效提成',
						selected: false
					},
					{
						label: '股票期权',
						selected: false
					},
					{
						label: '人才补贴',
						selected: false
					}
				],
				// 能力培养选项
				trainingOptions: [{
						label: '老员工带新',
						selected: false
					},
					{
						label: '导师一对一',
						selected: false
					},
					{
						label: '岗前带新培训',
						selected: false
					},
					{
						label: '内部定期培训',
						selected: false
					},
					{
						label: '专业技能培训',
						selected: false
					},
					{
						label: '内部课程资源',
						selected: false
					},
					{
						label: '人脉积累',
						selected: false
					},
					{
						label: '国内外进修',
						selected: false
					},
					{
						label: '校招培养',
						selected: false
					}
				],
				moreInfo: '',
				carefulhide: false,
				help: 'https://api-test.zhaopinbei.com/storage/uploads/images/rYxkkwuyCNnqb4YUwWfJXzktOukmQ2dF2OuFvchb.png',
			};
		},
		methods: {
			// 切换晋升制度选项选中状态
			togglePromotionSelection(index) {
				this.promotionOptions[index].selected = !this.promotionOptions[index].selected;
			},
			// 切换人才激励选项选中状态
			toggleIncentiveSelection(index) {
				this.incentiveOptions[index].selected = !this.incentiveOptions[index].selected;
			},
			// 切换能力培养选项选中状态
			toggleTrainingSelection(index) {
				this.trainingOptions[index].selected = !this.trainingOptions[index].selected;
			},
			// 处理输入事件，限制输入长度
			handleMoreInfoInput() {
				if (this.moreInfo.length > 500) {
					this.moreInfo = this.moreInfo.slice(0, 500);
				}
			},
			// 保存选中的标签
			saveTalentDevelopmentInfo() {
				const selectedPromotion = this.promotionOptions.filter(option => option.selected).map(option => option
					.label);
				const selectedIncentive = this.incentiveOptions.filter(option => option.selected).map(option => option
					.label);
				const selectedTraining = this.trainingOptions.filter(option => option.selected).map(option => option
					.label);
				// 这里需要根据你的实际存储逻辑完善保存操作，此处仅示例提示保存成功
				uni.showToast({
					title: '保存成功',
					icon: 'none'
				});
			},
			toggleCarefulHide() {
				console.log(1111111);
				this.carefulhide = !this.carefulhide;
				console.log(this.carefulhide);
			}
		}
	};
</script>

<style>
	.talent-development-container {
		height: 100vh;
		overflow-y: auto;
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.talent-development {
		padding: 10px;
		overflow-y: auto;
	}

	.info-card {
		width: 686rpx;
		display: flex;
		height: 166rpx;
		flex-direction: column;
		justify-content: center;
		margin: 20px auto;
		position: relative;
		background-image: linear-gradient(to bottom, rgba(242, 248, 255, 1), rgba(255, 255, 255, 1));
		border-radius: 16rpx;
		overflow: hidden;

	}

	/* .info-card::before {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 1px;
		left: 1px;
		content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	}

	.info-card::after {
		position: absolute;
		width: 100%;
		height: 100%;
		bottom: 1px;
		right: 1px;
		/* margin: 0px auto; */
	/* content: '';
		border: 4rpx solid;
		border-image: linear-gradient(to bottom, rgba(215, 233, 255, 1), rgba(229, 241, 255, 1)) 1;
		border-radius: 20rpx;
		z-index: 20000;
	} */

	.font-bold {
		padding: 10rpx 0;
		font-size: 24rpx;
		line-height: 14.06px;
		margin-left: 40rpx;
		margin-left: 40rpx;
	}

	.font-blod-subtitle {
		color: rgba(141, 154, 170, 1);
		padding: 10rpx 0;
		font-size: 20rpx;
		line-height: 23.44rpx;
		width: 606rpx;
		margin-left: 40rpx;
	}

	.option-container {
		display: flex;
		flex-wrap: wrap;
		overflow: hidden;
		margin-top: 24rpx;
	}

	.custom-tag {
		position: relative;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		box-sizing: border-box;
		text-align: left;
		color: black;
		background-color: transparent;
		border: 1px solid rgba(153, 153, 153, 1);
		height: 58rpx;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0px 20rpx;
		border-radius: 8rpx;
	}

	.custom-tag.is-selected {
		position: relative;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		box-sizing: border-box;
		text-align: left;
		color: rgba(79, 140, 240, 1);
		background-color: rgba(242, 247, 255, 1);
		border: 1px solid rgba(79, 140, 240, 1);
		height: 58rpx;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0px 20rpx;
		border-radius: 8rpx;
	}

	/* .custom-tag.is-selected::after {
		content: '';
		position: absolute;
		right: 0;
		bottom: 0;
		border-width: 10px;
		border-style: solid;
		border-color: transparent rgb(79 140 240) rgb(79 140 240) transparent;
	} */

	.selected-mark {
		position: absolute;
		right: 3px;
		bottom: -9px;
		z-index: 1;
	}

	.checkmark {
		color: white;
		font-size: 8px;
	}

	.input-container {
		width: 100%;
		position: relative;
	}

	.count-tip {
		position: absolute;
		right: 20rpx;
		bottom: 20rpx;
		color: #999;
		font-size: 24rpx;
	}

	.footer {
		height: 80rpx;
		box-shadow: 0 2px 4px rgb(0 0 0);
		padding: 24rpx;
		/* box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.05); */
		display: flex;
		flex-direction: column;
		/* justify-content: space-between; */
		/* padding: 20rpx; */
	}

	.footer-left {
		display: flex;
		align-items: center;
		height: 34rpx;
		margin-top: 20rpx;
	}

	.footer-text {
		color: rgb(121 121 121);
		/* height: 28px; */
		font-size: 24rpx;
		/* margin-left: 32rpx; */
		margin-right: 16rpx;
	}


	.save {
		width: 570rpx;
		height: 80rpx;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 16rpx;
		font-size: 28rpx;
		/* margin-top: 42px; */
	}

	.section {
		width: 686rpx;
		margin: 0 auto;
		margin-top: 32rpx;
	}

	.section-title {
		font-size: 32rpx;
		color: rgba(51, 51, 51, 1);
		line-height: 37.3rpx;
	}

	.footer-icon {
		width: 28rpx;
		height: 28rpx;
	}

	textarea {
		width: 686rpx;
		height: 240rpx !important;
		border-radius: 16rpx;
		padding-top: 24rpx;
		padding-left: 24rpx;
		background: rgba(246, 246, 246, 1);
	}

	.text-plc {
		/* color: red !important; */
		font-size: 24rpx;
		line-height: 28.12rpx;
	}

	.zero-count {
		color: rgba(79, 140, 240, 1);
	}
</style>
