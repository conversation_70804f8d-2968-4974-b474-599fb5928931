<template>
  <view class="warp">
    <view class="inner">
      <view class="tips">
        <view class="title"> 添加服务人才有什么用？ </view>
        <view class="cont">
          介绍公司可提供的员工培养和晋升制度，良好的职业成长空间对人才更有吸引力
        </view>
      </view>
      <view class="line">
        <view class="tit"> 已选人才 </view>
        <view class="add" @click="show = true"> 添加服务人才 </view>
      </view>
      <view class="list">
        <view class="item" v-for="item in list" :key="item">
          <u-avatar :src="item.ava" size="80rpx"></u-avatar>
          <view class="right">
            <view class="name">
              {{ item.name }}
            </view>
            <view class="tag">
              <view class="tag-item" v-for="v in item.tag">
                {{ v }}
              </view>
            </view>
          </view>
          <img
            class="del"
            @click="showPop = false"
            src="https://api-test.zhaopinbei.com/storage/uploads/images/Sl4y4bsafDNMgfpp372fiFnhD0jxCXPYoHzhs5aq.png"
            alt=""
          />
        </view>
      </view>
    </view>
    <u-popup :show="show" mode="bottom" @close="show = false">
      <view class="tip-box">
        <view class="tip-tit">
          <view>
            <text>添加服务人才</text>
          </view>
        </view>
        <view class="sear-box">
          <u-search></u-search>
        </view>
        <view class="list">
          <view class="item btm" v-for="item in list" :key="item">
            <u-avatar :src="item.ava" size="80rpx"></u-avatar>
            <view class="right">
              <view class="name">
                {{ item.name }}
              </view>
              <view class="tag">
                <view class="tag-item" v-for="v in item.tag">
                  {{ v }}
                </view>
              </view>
            </view>
            <img
              class="del"
              @click="showPop = false"
              src="https://api-test.zhaopinbei.com/storage/uploads/images/Sl4y4bsafDNMgfpp372fiFnhD0jxCXPYoHzhs5aq.png"
              alt=""
            />
          </view>
        </view>
        <view class="tip-btm">
          <view class="tip-btn1" @click="show = false">取消</view>
          <view class="tip-btn" @click="show = false">保存</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      type: "1",
      show: false,
      list: [
        {
          name: "赵先生",
          ava: "",
          tag: ["25岁", "2年经验", "本科"],
        },
        {
          name: "王先生",
          ava: "",
          tag: ["25岁", "2年经验", "本科"],
        },
        {
          name: "李先生",
          ava: "",
          tag: ["25岁", "2年经验", "本科"],
        },
      ],
    };
  },
  onLoad: function (options) {},
  methods: {
    delItem(item) {
      this.list = this.list.filter((i) => i !== item);
    },
    goAdd() {
      uni.setStorageSync("myList", this.list);
      uni.navigateTo({
        url: "./addTalent",
      });
    },
  },
};
</script>

<style lang="less" scoped>
.inner {
  padding: 32rpx;

  .tips {
    padding: 40rpx;
    background: linear-gradient(180deg, #f2f8ff 0%, #ffffff 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 2rpx solid rgba(215, 233, 255, 1);
    margin-bottom: 32rpx;

    .title {
      font-size: 24rpx;
      color: #333333;
      margin-bottom: 24rpx;
    }

    .cont {
      font-size: 20rpx;
      color: #8d9aaa;
    }
  }

  .line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 28rpx;

    .tit {
      font-size: 32rpx;
      color: #000000;
    }

    .add {
      font-size: 28rpx;
      color: #4f8cf0;
    }
  }
}
.sear-box {
  margin-top: 24rpx;
  margin-bottom: 32rpx;
}
.list {
  display: flex;
  flex-direction: column;

  .item {
    display: flex;
    align-items: center;
    gap: 32rpx;
    margin-bottom: 90rpx;

    .right {
		flex: 1;
      .name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
      }

      .tag {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .tag-item {
          padding: 4rpx 12rpx;
          background: #f5f5f5;
          border-radius: 8rpx 8rpx 8rpx 8rpx;
          font-size: 24rpx;
          color: #4e5c75;
          text-align: center;
        }
      }
    }
    .del {
      width: 32rpx;
      height: 32rpx;
    }
  }
  .btm{
	margin-bottom: 45rpx;
  }
}
.tip-box {
  padding: 32rpx;
  border-radius: 24rpx;
}
.tip-tit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  img {
    width: 32rpx;
    height: 32rpx;
  }
  text {
    font-size: 28rpx;
    color: #333333;
    margin-left: 24rpx;
  }
}
.tip-txt {
  font-size: 24rpx;
  color: #777777;
  line-height: 48rpx;
  padding: 32rpx 0;
}
.tip-btm {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .tip-btn {
    width: 96rpx;
    height: 56rpx;
    border-radius: 8rpx;
    background: #2370ee;
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
    line-height: 52rpx;
    margin-left: 24rpx;
  }
  .tip-btn1 {
    width: 96rpx;
    height: 56rpx;
    border-radius: 8rpx;
    background: #f2f2f2;
    font-size: 24rpx;
    color: #999999;
    text-align: center;
    line-height: 52rpx;
  }
}
::v-deep .u-popup__content {
  border-radius: 24rpx 24rpx 0 0;
}
</style>
