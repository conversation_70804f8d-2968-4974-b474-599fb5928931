<template>
	<view class="warp">
		<view class="upload-box">
			<video :src="tempFilePath" controls></video>
		</view>
		<u--textarea placeholder="输入标题" border="bottom" height="22" v-model="value" maxlength="30" count></u--textarea>
		<u--textarea v-model="value2" height="160" placeholder="输入正文" maxlength="1000" border="bottom"
			count></u--textarea>
		<view class="huati" @click="addhuati">
			<img src="/static/images/publishCenter/dynamic_icon2.png" alt="" />
			<text>添加话题</text>
			<u-modal :show="show" :title="title" :content='content' @confirm="confirm">
				<u-input v-model="inputValue" placeholder="请以#开头，#结尾!"></u-input>
			</u-modal>
		</view>
		<view class="bg-1" v-if="huatiList.length>0">
			<view class="d-1" v-for="(item,index) in huatiList">
				<text>{{item.name}}</text>
				<img src="/static/images/publishCenter/dynamic_icon2.png" alt="" @click="delClick(item)"/>
			</view>
			<!--<u-toast ref="uToast"></u-toast>-->
		</view>
		<view class="tips">
			*发布即代表您已阅读并同意 <text class="inn">《招聘呗创作者平台用户协议》</text>
		</view>
		<view class="btn" @click="fabuClick">
			发布视频
		</view>
	</view>
</template>

<script>
	import {
		uploadImgs,
		addArticle,
	} from "../../config/api.js"
	export default {
		data() {
			return {
				value: '',
				value2: '',
				tempPath: '',
				tempFilePath:'',
				show:false,
				title:'添加话题',
				content:'',
				inputValue:'',
				huatiList:[],
			}
		},
		created() {
			this.userType = uni.getStorageSync('roleType');
			this.company_id = uni.getStorageSync('userInfo')?.company.id;
			this.member_id = uni.getStorageSync('userInfo')?.member_info.member_id;
			this.getVideo()
		},
		methods: {
			submit() {
				uni.navigateTo({
					url: './publishSuccess'
				})
			},
			getVideo() {
				let that = this
				uni.chooseVideo({
					sourceType: ['album', 'camera'], // 可选值有 'album' 和 'camera'，分别表示从相册选择和使用相机拍摄，默认为两者都有
					compressed: true, // 是否压缩所选的视频源文件，默认值为true
					maxDuration: 60, // 拍摄视频最长拍摄时间，单位秒。设置值大于60的视为60，iOS 可能会自动调整为最大允许时间
					camera: 'back', // 默认拉起的是前置或者后置摄像头。部分 Android 手机下由于系统 ROM 不支持无法生效
					success: function(res) {
						//console.log(res);
						that.tempPath = res.tempFilePath
						// 成功回调
						//console.log(res.tempFilePath); // 返回视频的临时文件路径
						//console.log(res.duration); // 视频时长，单位为秒
						//console.log(res.size); // 视频大小，单位为字节
						//console.log(res.height); // 视频的高度
						//console.log(res.width); // 视频的宽度
						uni.uploadFile({
							url: 'https://api-test.zhaopinbei.com/common-v2/articles/upload',
							name: 'file',
							filePath:res.tempFilePath,
							formData: {},
							success: res1 => {
								const imgsList = JSON.parse(res1.data);
								that.tempFilePath = imgsList.data.url;
							}
						});
					},
					fail: function(err) {
						// 失败回调
						console.error('选择视频失败：', err);
					}
				});
			},
			// 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1);
			},
			//添加话题
			addhuati(){
				if(this.huatiList.length>=1){
					uni.showToast({
						icon: 'success',
						title: '最多添加1个话题',
						duration: 1000
					});
					return false;
				}
				this.inputValue = '';
				this.show = true;
			},
			//删除话题
			delClick(){
				this.huatiList.forEach((item,index)=>{
					this.huatiList.splice(index, 1);
				})
			},
			//关闭模态框
			confirm(){
				this.show = false;
				const obj = {};
				obj.name = this.inputValue;
				this.huatiList.push(obj);
			},
			//发布
			async fabuClick(){
				const ls = {
					name:this.inputValue + '\n' + this.value,
					content:this.value2,//textareaValue.value = formLis.value.name + '\n' + textareaValue.value
					//content:aaa,
					cover:this.tempFilePath,
					form:4,//1文章 2动态 3提问 4发布视频
					type:1,//1正文 2草稿 iocnFlag.value?2:1
				}
				if(this.userType=='company'){//伯乐
					ls.member_id = this.company_id;
				}else if(this.userType=='member'){//千里马
					ls.member_id = this.member_id;
				}else if(this.userType=='headhunters'){//就业管家

				}
				addArticle(ls).then(({ status_code, data }) => {
					if (status_code == '200') {
						//console.log('111数据', data)
						uni.navigateTo({
							url: './index',
						});
					}
				})
			},
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 32rpx;

		.upload-box {
			margin-top: 24rpx;
			margin-bottom: 24rpx;
		}

		.huati {
			margin-top: 24rpx;
			background: #f0f0f0;
			width: 160rpx;
			height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 20rpx;
			font-size: 24rpx;

			img {
				width: 28rpx;
				height: 28rpx;
				margin-right: 6rpx;
			}

		}
			.bg-1{
			.d-1{
				display: inline-block;
				background: #eaece8e8;
				color:#989697;
				padding: 5rpx;
				border-radius: 20rpx;
				font-size: 24rpx;
				margin: 7rpx;
			img {
				width: 28rpx;
				height: 28rpx;
				margin-right: 6rpx;
			}
			}
			}
		.tips {
			margin-top: 24rpx;
			font-size: 20rpx;
			color: #999;

			.inn {
				color: #4F8CF0;
			}
		}

		.btn {
			width: 686rpx;
			height: 80rpx;
			background: #4F8CF0;
			font-size: 28rpx;
			color: #FFFFFF;
			text-align: center;
			line-height: 80rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			position: fixed;
			bottom: 100rpx;
		}
	}
</style>
