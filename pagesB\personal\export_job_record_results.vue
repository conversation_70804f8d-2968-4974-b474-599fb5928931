<template>
	<!-- 求职记录导出结果 -->
	<view class="container">
		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<view class="text-box">
					<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png"></image>
					<text class="text">已提交</text>
				</view>
				<view class="desc">求职记录文件将会在72小时内发送至你的邮箱 <EMAIL></view>
				<view class="btn">我知道了</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #FFFFFF;
		display: flex;
		flex-direction: column;

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 32rpx;
				padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
				padding-block-end: calc(32rpx + env(safe-area-inset-bottom));


				.text-box {
					display: flex;
					align-items: center;
					justify-content: center;
					gap: 12rpx;

					.image {
						width: 40rpx;
						height: 40rpx;
					}

					.text {
						color: #333333;
						font-size: 28rpx;
					}
				}

				.desc {
					width: 480rpx;
					color: #666666;
					font-size: 24rpx;
					text-align: center;
				}

				.btn {
					width: 100%;
					padding-block: 20rpx;
					color: #FFFFFF;
					background-color: #4F8CF0;
					font-size: 28rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 12rpx;
				}
			}
		}
	}
</style>
