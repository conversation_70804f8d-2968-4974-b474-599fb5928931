<template>
	<view class="item" @click="go(item)">
		<view class="name">
			 {{item.address_info}}
		</view>
		<view class="action">
			<view class="area">
				{{item.province_name}}·{{item.city_name}}·{{item.district_name}}
			</view>
			<!-- <view class="mr" @click.stop="setDefaultAddress">
				设为默认 
				<image v-if="item.is_default==1" src="/static/images/plat/selected.png" mode="" ></image>
				<image v-else src="/static/images/plat/unselected.png" mode=""></image>
			</view> -->
		</view>
	</view>
</template>

<script>
	export default {
		name:"companyAddrItem",
		props:{
			item:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {
				
			};
		},
		methods:{
			// setDefaultAddress(){
			// 	this.$emit('setDefaultAddress',this.item)
			// },
			
			go(item){
				uni.navigateTo({
					url:"/pagesA/add/addCompanyAddress?id="+this.item.id
				})
			}
		}
	}
</script>

<style lang="less" scoped>
.item{
	display: flex;
	flex-direction: column;
	padding: 32rpx;
	margin-bottom: 24rpx;
	background-color: #FFFFFF;
	border-radius: 24rpx;
	.name{
		font-weight: 500;
		font-size: 28rpx;
		color: #333333;
	}
	.action{
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 24rpx;
		.area{
			font-weight: 400;
			font-size: 24rpx;
			color: #666666;
		}
		
		.mr{
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 24rpx;
			color: #333333;
			
			image{
				width: 28rpx;
				height: 28rpx;
				margin-left: 8rpx;
			}
		}
	}
}
</style>