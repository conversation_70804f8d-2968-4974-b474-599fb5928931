<template>
  <view id="app">
    <u-sticky bgColor="#F5F5F7">
      <view class="tabsBox">
        <u-tabs
          :list="list1"
          :current="tabsIndex"
          :activeStyle="{
            color: '#4F8CF0',
            fontWeight: 'bold',
            transform: 'scale(1.05)',
          }"
          @click="tabClick()"
        ></u-tabs>
        <view class="hrBox"></view>
        <u-search
          :placeholder="
            tabsIndex == 0
              ? '请输入签署记录'
              : tabsIndex == 1
              ? '请输入关键字'
              : '请输入印章'
          "
          bgColor="#f5f5f7"
          :showAction="true"
          actionText="搜索"
          v-model="keyword"
          @custom="custom"
          @clear="clear"
        ></u-search>
      </view>
    </u-sticky>

    <view class="contentBox">
      <!-- 合同列表 -->
      <block v-if="tabsIndex == 0 && list.length > 0">
        <signRecord
          v-for="(item, index) in list"
          :key="item.id"
          :item="item"
        ></signRecord>
      </block>
      <!-- 关键字 -->
      <block v-if="tabsIndex == 1 && list.length > 0">
        <keywords
          ref="childRef"
          v-for="(item, index) in list"
          :key="item.id"
          :item="item"
        ></keywords>
      </block>
      <!-- 印章 -->
      <block v-if="tabsIndex == 2 && list.length > 0">
        <seal v-for="(item, index) in list" :key="item.id" :item="item"></seal>
      </block>
    </view>

    <Pages :status="status"></Pages>
    <view style="height: 220rpx" v-if="tabsIndex == 1 || tabsIndex == 2"></view>
    <view class="bottomBtn" v-if="tabsIndex == 1 || tabsIndex == 2">
      <view class="bottomBtn_text" @click="goPage">{{
        tabsIndex == 1 ? "添加关键字" : "添加签署"
      }}</view>
    </view>
  </view>
</template>

<script>
import signRecord from "./signRecord.vue";
import keywords from "./keywords.vue";
import seal from "./seal.vue";
import Pages from "../../components/pages.vue";
import { contractIndex, contractSeal, getKeyword } from "../../config/api.js";
export default {
  components: {
    signRecord,
    keywords,
    seal,
    Pages,
  },
  data() {
    return {
      page: 1,
      limit: 5,
      status: "loadmore",
      more: false,
      list1: [
        {
          name: "签署记录",
        },
        {
          name: "关键字",
        },
        {
          name: "印章",
        },
      ],
      tabsIndex: 0,
      list: [],
    };
  },
  onLoad(item) {
    if (item.id == 1) {
      //关键字
      this.tabClick(1);
    } else if (item.id == 2) {
      //印章
      this.tabClick(2);
    } else if (!item.id) {
      //签署记录
      /*this.tabsIndex = 0
                this.contractIndex()*/
      this.tabClick(0);
    }
  },
  //触底加载更多
  onReachBottom() {
    if (this.more) {
      this.status = "loading";
      this.page++;
      if (this.tabsIndex == 2) {
        this.contractSeal();
      }
    } else {
      this.status = "nomore";
    }
  },
  methods: {
    goPage() {
      if (this.tabsIndex == 1) {
        uni.navigateTo({
          url: "/pagesA/contract/contractInput?type=" + "keywords",
        });
      }
      if (this.tabsIndex == 2) {
        uni.navigateTo({
          url: "/pagesA/contract/contractInput?type=" + "seal",
        });
      }
    },
    async contractIndex() {
      let params = {
        page: this.page,
        limit: this.limit,
      };
      const { status_code, data } = await contractIndex(params);
      ///this.list = this.list.concat(data.data);
      this.$set(this, "list", data.data);
      // 返回false代表没有下一页
      this.more = data.more;
      this.status = this.more ? "loadmore" : "nomore";
      //强制更新
      this.$forceUpdate();
    },
    // 关键字列表
    async getKeyword(val) {
      let params = {
        page: this.page,
        limit: this.limit,
        key_word: val ? val : "",
      };
      const { status_code, data } = await getKeyword(params);
      //this.list = this.list.concat(data.data);
      this.$set(this, "list", data.data);
      // 返回false代表没有下一页
      this.more = data.more;
      this.status = this.more ? "loadmore" : "nomore";
      //强制更新
      this.$forceUpdate();
    },
    // 印章列表
    async contractSeal() {
      let params = {
        page: this.page,
        limit: this.limit,
        title: "",
      };
      const { status_code, data } = await contractSeal(params);
      //this.list = this.list.concat(data.data);
      this.$set(this, "list", data.data);
      // 返回false代表没有下一页
      this.more = data.more;
      this.status = this.more ? "loadmore" : "nomore";
      //强制更新
      this.$forceUpdate();
    },
    tabClick(e) {
      this.tabsIndex = e.index ? e.index : e;
      this.page = 1;
      this.list = [];
      let res1 =
        Object.prototype.toString.call(this.tabsIndex) === "[object Object]";
      if (res1) {
        this.tabsIndex = e.index;
      }
      if (this.tabsIndex == 0) {
        this.contractIndex();
      }
      if (this.tabsIndex == 1) {
        this.getKeyword();
      }
      if (this.tabsIndex == 2) {
        this.contractSeal();
      }
    },
    custom(e) {
      console.log(e);
      console.log(this.tabsIndex);
      if (this.tabsIndex === 1) {
        this.getKeyword(e);
      } else if (this.tabsIndex === 0) {
        this.contractIndex(e);
      } else if (this.tabsIndex === 2) {
        this.contractSeal(e);
      }
    },
  },
};
</script>
<style>
page {
  background-color: #f5f5f7;
}
</style>
<style scoped lang="less">
view {
  box-sizing: border-box;
}

#app {
  width: 100%;
}

.tabsBox {
  background: #fff;
  padding: 24rpx 32rpx;
  margin-bottom: 32rpx;
}

.hrBox {
  width: 100%;
  height: 24rpx;
  // background: #f5f5f7;
}

.contentBox {
  width: 100%;
  padding: 0 32rpx;
}

.comp {
  width: 100%;
  height: 244rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
}

.bottomBtn {
  width: 100%;
  height: 196rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 24rpx 32rpx;
}

.bottomBtn_text {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  text-align: center;
  line-height: 80rpx;
  font-weight: 600;
  font-size: 28rpx;
  color: #ffffff;
}
</style>
