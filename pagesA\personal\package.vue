<template>
	<view class="home-index">
		<view class="header">
			<view class="pos">
				<image src="https://api-test.zhaopinbei.com/images/package_1.png" mode=""></image>
			</view>

			<view class="tou" :style="{'padding-top': searchHeight}">
				<view class="statusBarHeight">
				</view>
				<view class="top_box"
					:style="{paddingLeft: `${menuPaddingLF}px`,height: `${menuHeight}px`,width: `${menuWidth}px`}">
					<view style="height: 100%;">
						<view class="top_content">
							<view class="logo" style="margin-left: 10rpx;" @click="back">
								<u-icon name="arrow-left" size="20" color="#000000"></u-icon>
							</view>

							<view class="title">
								我的钱包
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="money">
				<view class="bg">
					<image src="https://api-test.zhaopinbei.com/images/package_2.png" mode=""></image>
				</view>
				<view class="wrap">
					<view class="title">
						我的钱包
					</view>
					<view class="num-list">
						<view class="num-item">
							<view class="name">
								我的招聘币
							</view>
							<view class="num">
								{{packageInfo.point_account.remain}}
							</view>
						</view>
						<view class="num-item">
							<view class="name">
								合同数量
							</view>
							<view class="num">
								{{packageInfo.contract.remain_count}}
							</view>
						</view>
						<view class="num-item">
							<view class="name">
								岗位数量
							</view>
							<view class="num">
								{{packageInfo.job.remain_count}}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="tabs">
			<u-tabs lineWidth="20" :current="tabIndex" lineColor="#4F8CF0" :activeStyle="{
					color: '#4F8CF0',
					fontWeight: 'bold',
					transform: 'scale(1.05)'
				}" :inactiveStyle="{
					color: '#999999',
					transform: 'scale(1)'
				}" :list="tabs" @click="changeTab"></u-tabs>
		</view>
		<scroll-view class="scroll-H" scroll-y>
			<view class="list">
				<view class="item" v-for="(item,index) in list" :key="index">
					<view class="up">
						<view class="name">
							{{item.change_type_name}}
						</view>
						<view :class="['num',item.type=='come'?'plus':'reduce']">
							{{item.type=='come'?'+':'-'}}{{item.point}}
						</view>
					</view>
					<view class="down">
						<view class="time">
							{{item.created_at}}
						</view>
						<view class="type">
							招聘币
						</view>
					</view>
				</view>
				<!-- <view class="item" v-for="item in 3">
					<view class="up">
						<view class="name">
							购买职位
						</view>
						<view class="num reduce">
							-8848
						</view>
					</view>
					<view class="down">
						<view class="time">
							2024-09-24 17:25:42
						</view>
						<view class="type">
							招聘币
						</view>
					</view>
				</view> -->
			</view>


		</scroll-view>

		<view class="footer" style="margin-bottom: 26rpx;">
			<view class="btns">
				<view class="btn buy" @click="goServicePackage">
					购买服务包
				</view>
				<view class="btn recharge" @click="goRecharge">
					充值
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getPackageInfo,
		getRechargeList
	} from "../../config/api.js"
	export default {
		data() {
			return {
				page: 1,
				limit: 10,
				tabIndex: 0,
				tabs: [{
					name: '充值记录',
				}, {
					name: '消费记录',
				}],

				list: [],

				packageInfo: {},

				isScroll: false,
				statusHeight: 0,
				menuPaddingTB: 0,
				menuPaddingLF: 0,
				menuHeight: 0,
				menuWidth: 0,
				disHeight: 0,
                navHeight:0,
                searchMarginTop: 0, // 搜索框上边距
                searchWidth: 0, // 搜索框宽度
                searchHeight: 0, // 搜索框高度
			}
		},
		onShow() {
			const app = getApp().globalData
			console.log("app:", app)
			const {
				systemInfo,
				MenuButtonInfo
			} = app
			console.log("~~~~~~~~", MenuButtonInfo, "打印")
			this.statusHeight = systemInfo.statusBarHeight
			this.menuPaddingTB = MenuButtonInfo.top - systemInfo.statusBarHeight
			this.menuPaddingLF = systemInfo.screenWidth - MenuButtonInfo.right
			this.menuHeight = MenuButtonInfo.height + this.menuPaddingTB * 2
			this.menuWidth = MenuButtonInfo.left
			this.disHeight = this.menuHeight + this.statusHeight
            
            

			this.getRechargeList()
			this.getPackageInfo()
		},
		onPageScroll(e) {
			this.isScroll = e.scrollTop > 0
		},
		onLoad() {
		},
        mounted() {
            this.menuButtonInfo = uni.getMenuButtonBoundingClientRect()
            const {
            	top,
            	width,
            	height,
            	right
            } = this.menuButtonInfo
            uni.getSystemInfo({
            	success: (res) => {
            		const {
            			statusBarHeight
            		} = res
            		const margin = top - statusBarHeight
            		this.navHeight = (height + statusBarHeight + (margin * 2)) + 'px',
            			this.searchMarginTop = statusBarHeight + margin + 'px', // 状态栏 + 胶囊按钮边距
            			this.searchHeight = height + 'px', // 与胶囊按钮同高
            			this.searchWidth = right - width - 30 + 'px' // 胶囊按钮右边坐标 - 胶囊按钮宽度 = 按钮左边可使用宽度
            	}
            })
        },
		methods: {
			back() {
				uni.navigateBack()
			},
			goRecharge() {
				uni.navigateTo({
					url: "/pagesA/personal/recharge"
				})
			},
			goServicePackage() {
				uni.navigateTo({
					url: "/pagesA/personal/servicePackage"
				})
			},

			changeTab(e) {
				console.log(e)
				this.tabIndex = e.index;
				this.page = 1
				this.getRechargeList()
			},

			//充值列表
			async getRechargeList() {
				let params = {
					type: this.tabIndex == 0 ? 'come' : 'out', //记录类型：come-充值，out-消费
					page: this.page,
					limit: this.limit
				}

				const {
					status_code,
					data,
					message
				} = await getRechargeList(params)
				if (status_code == 200) {
					this.list = data.data
				}
			},

			//钱包信息
			async getPackageInfo() {
				const {
					status_code,
					data,
					message
				} = await getPackageInfo()
				if (status_code == 200) {
					this.packageInfo = data;
				}
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		// padding-bottom: 172rpx;
		overflow: hidden;
		height: 100vh;
	}

	.header {
		display: flex;
		align-items: flex-end;
		position: relative;
		height: 460rpx;
		padding: 0 32rpx;

		.pos {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			z-index: -10;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.tou {
			position: absolute;
			left: 0;
			top: 0;
			z-index: 99;
			width: 100%;

			.top_box {

				// background-color: green;
				// position: relative;
				.top_content {
					display: flex;
					align-items: center;
					height: 100%;

					.logo {

						// display: flex;
						// position: absolute;
						// left: 40rpx;
						// top: 0;
						image {
							width: 126rpx;
							height: 44rpx;
						}
					}

					.title {
						display: flex;
						flex: 1;
						// justify-content: center;
						font-weight: 500;
						font-size: 34rpx;
						color: #000000;
						// background-color: red;

					}
				}
			}
		}

		.statusBarHeight {
			height: var(--status-bar-height);
			width: 100%;
		}

		.money {

			position: relative;
			height: 230rpx;
			width: 100%;

			.bg {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				z-index: -5;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.wrap {
				display: flex;

				flex-direction: column;
				justify-content: center;
				// align-items: center;
				height: 100%;

				.title {
					font-family: HYYaKuHeiW, HYYaKuHeiW;
					font-weight: 400;
					font-size: 32rpx;
					color: #FFFFFF;
					padding: 0 50rpx;
					margin-bottom: 24rpx;
				}

				.num-list {
					display: flex;

					.num-item {
						display: flex;
						flex-direction: column;
						flex: 1;
						justify-content: center;
						align-items: center;

						.name {
							font-weight: 400;
							font-size: 24rpx;
							color: #FFFFFF;
						}

						.num {
							font-weight: bold;
							font-size: 28rpx;
							color: #FFFFFF;
							margin-top: 8rpx;
						}
					}
				}
			}
		}
	}

	.tabs {
		height: 100rpx;
		margin-left: 16rpx;
	}

	.scroll-H {
		height: calc(100% - 100rpx - 460rpx - 160rpx);
	}

	.list {
		display: flex;
		flex-direction: column;
		padding: 0 32rpx;

		.item {
			display: flex;
			flex-direction: column;
			background: #FFFFFF;
			border-radius: 24rpx;
			padding: 24rpx 40rpx;
			margin-top: 32rpx;

			.up {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.name {
					font-weight: 600;
					font-size: 28rpx;
					color: #333333;
				}

				.num {
					font-weight: bold;
					font-size: 32rpx;

				}

				.plus {
					color: #4F8CF0;
				}

				.reduce {
					color: #FE4D4F;
				}
			}

			.down {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 16rpx;

				.time {
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
				}

				.type {
					font-weight: 400;
					font-size: 20rpx;
					color: #999999;
				}

			}
		}
	}

	.footer {
		position: fixed;
		display: flex;
		align-items: center;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 140rpx;
		background: #F5F5F7;

		.btns {
			display: flex;
			justify-content: space-between;
			width: 100%;
			padding: 0 32rpx;

			.btn {
				display: flex;
				flex: 1;
				align-items: center;
				justify-content: center;
				font-weight: 600;
				font-size: 34rpx;
				color: #4F8CF0;
				border-radius: 24rpx;
				height: 100rpx;

				&:first-child {
					margin-right: 26rpx;
				}
			}

			.buy {
				background: #FFFFFF;
			}

			.recharge {
				background: #4F8CF0;
				color: #FFFFFF;
			}
		}
	}
</style>