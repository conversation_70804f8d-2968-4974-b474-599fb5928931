<template>
	<!-- <view class="item" @click.stop="goList(item.job_active.id)"> -->
	<view class="">

		<view class="item" @click.stop="go(item.job_active.id)">
			<view class="up">
				<image :src="item.job_active.image.thumbnail_path_url" mode=""></image>
				<view class="info">
					<view class="name">
						{{ item.job_active.title}}
					</view>
					<view class="time">
						{{ item.job_active.created_at }}
					</view>
					<view class="desc" v-if="item.job_reports_count > 0">
						已有{{item.job_reports_count}}家企业报名
					</view>
					<block v-if="type=='qlm'">
						<view :class="['status',item.job_active.active_status=='end'?'over':'ing']">
							{{item.job_active.active_status_name}}
						</view>
					</block>
					<block v-else>
						<view
							:class="['status',item.job_active.active_status=='started'?'ing':item.job_active.active_status=='end'?'over':item.job_active.status==3?'bm':item.job_active.status==4?'wks':'']">
							{{item.job_active.active_status_name}}
						</view>

					</block>
				</view>
			</view>
			<!-- @click.stop="go(item.job_active.id)" -->
			<view class="down">
				<block v-if="roleType=='member' || roleType==''">
					<view class="desc">
						{{ item.job_active.start }}开始
					</view>

					<view class="look">
						<text>查看详情</text>
						<u-icon name="arrow-rightward" size="32rpx" color="#4F8CF0"></u-icon>
					</view>
				</block>

				<block v-if="roleType=='company'||roleType=='headhunters'">
					<view class="desc" v-if="item.job_reports_count > 0">
						·{{item.job_reports_count}}人参加
					</view>
					<view class="desc">
						{{ item.job_active.start }}开始
					</view>
				</block>

			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},

		computed: {
			roleType() {
				return this.$store.state.roleType || uni.getStorageSync('roleType')
			}
		},
		methods: {
			goList(id) {
				var _this = this;

				if (_this.roleType == 'member') {
					uni.navigateTo({
						url: "/pagesA/list/active_job_list?id=" + id
					})
				} else {
					_this.go(id);
				}
			},
			go(id) {
				// 根据身份判断做跳转
				console.log(this.roleType);
				if (this.roleType == 'member' || '') {
					uni.setStorageSync('id', id)
					uni.navigateTo({
						// url: '/pagesA/details/QlmjobFair/takePart?id=' + id
						url: "/pagesA/details/qlmActiveDetails?id=" + id
						// url: "/pagesA/details/qlmActiveShow?id=" + id

					})
				} else {
					uni.navigateTo({
						url: "/pagesA/details/blActiveDetails?id=" + id
					})
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		margin-bottom: 32rpx;

		.up {
			display: flex;
			border-radius: 16rpx;
			background-color: #FFFFFF;
			padding: 32rpx;

			&>image {
				width: 200rpx;
				height: 216rpx;
				border-radius: 8rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				padding-left: 24rpx;

				.name {
					font-weight: 500;
					font-size: 32rpx;
					color: #333333;
				}

				.time {
					font-weight: 400;
					font-size: 28rpx;
					color: #999999;
				}

				.desc {
					font-weight: 400;
					font-size: 24rpx;
					color: #CCCCCC;
				}

				.status {
					display: flex;
					align-items: center;
					justify-content: center;
					// padding: 0 16rpx;
					width: 110rpx;
					height: 50rpx;
					font-weight: 500;
					font-size: 24rpx;
					border-radius: 8rpx;
				}

				.ing {
					background: rgba(87, 213, 28, 0.1);
					color: #57D51C;
				}

				.over {
					background: #CCCCCC;
					color: #FFFFFF;
				}

				.bm {
					background: rgba(79, 140, 240, 0.1);
					color: #4F8CF0;
				}

				.wks {
					color: #27CDF2;
					background: rgba(39, 205, 242, 0.1);
				}
			}
		}

		.down {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 0 32rpx;
			padding: 0 24rpx;
			height: 66rpx;
			border-radius: 0 0 16rpx 16rpx;
			background: linear-gradient(90deg, #F5FCFC 0%, #FCFBFA 100%);

			.desc {
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
			}

			.look {
				display: flex;
				align-items: center;
				font-weight: 600;
				font-size: 24rpx;
				color: #4F8CF0;

				text {
					margin-right: 6rpx;
				}
			}
		}
	}
</style>