<template>
	<view class="home-index">
		<view class="wrap">
			<view class="title">
				请选择反馈类型
			</view>
			<view class="list">
				<view v-for="(item, index) in items" :key="index"
					:class="['item',{ 'selected': selectedItems.includes(index) }]" @click="toggleSelection(index)">
					{{ item }}
				</view>
			</view>
		</view>
		<view class="wrap">
			<view class="title">
				详细描述
			</view>
			<view class="inp">
				<u--textarea v-model="value1" placeholder="请输入内容"></u--textarea>
			</view>
		</view>

		<view class="wrap pic-wrap">
			<view class="title">
				图片描述
			</view>
			<view class="pics">
				<view class="pic">
					<image src="/static/images/index/up.png" mode=""></image>
				</view>
			</view>
		</view>

		<view class="btn">
			提交
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				items: [
					'账号相关 1',
					'账号相关 2',
					'账号相关 3',
					'账号相关 4',
					'账号相关 5'
				],
				selectedItems: []
			}
		},
		methods: {
			toggleSelection(index) {
				console.log('1111111')
				const itemIndex = this.selectedItems.indexOf(index);
				if (itemIndex > -1) {
					// 如果已经被选中，则取消选择
					this.selectedItems.splice(itemIndex, 1);
				} else {
					// 否则添加到选中项中
					this.selectedItems.push(index);
				}
				console.log(this.selectedItems)
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding: 0 32rpx;
	}

	.wrap {
		display: flex;
		flex-direction: column;
		margin-top: 32rpx;
		padding: 0 32rpx;
		background: #FFFFFF;
		border-radius: 24rpx;

		.title {
			font-weight: 500;
			font-size: 32rpx;
			color: #333333;
			padding: 24rpx 0;
			border-bottom: 1px solid #F5F5F7;
		}

		.list {
			display: flex;
			flex-wrap: wrap;
			padding: 24rpx 0 0 0;

			
		}

		.inp {
			padding: 24rpx 0;
		}

		.pics {
			display: flex;
			background: #F5F5F7;

			.pic {
				width: 200rpx;
				height: 200rpx;
				margin-right: 20rpx;

				&:last-child {
					margin-right: 0;
				}

				image {
					width: 100%;
					height: 100%;
				}
			}
		}
	}

	.pic-wrap {
		background: none;
	}

	.btn {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 88rpx;
		font-weight: 600;
		font-size: 34rpx;
		color: #FFFFFF;
		margin-top: 60rpx;
		background: #4F8CF0;
		border-radius: 24rpx;
	}
	
	.item {
		padding: 12rpx 24rpx;
		background: #F5F5F7;
		border-radius: 16rpx;
		margin-right: 24rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 24rpx;
	}

	.selected {
		color: #FFFFFF !important;
		/* 添加蓝色边框 */
		background-color: #4F8CF0 !important;
		/* 可选：改变背景色以突出显示 */
	}
</style>