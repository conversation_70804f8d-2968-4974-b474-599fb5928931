<template>
	<view class="item">
		<view class="up" @click="goQlmDetails">
			<view class="item-up">
				<image :src="item.member_info.image.path_url" mode=""></image>
				<view class="info">
					<view class="user">
						<view class="userInfo">
							<view class="name">
								{{ item.member_certification.name ? item.member_certification.name : item.member_info.nick_name }}
							</view>
							<view class="cert">
								{{ item.member.certification_status_name }}
							</view>
						</view>
						<view class="status" v-if="item.member_info.job_status_name">
							<view class="dot line"></view>
							{{ item.member_info.job_status_name }}
						</view>
					</view>
					<view class="tags">
						<view class="tag">
							{{ item.resume.resume_sex_name }}
						</view>
						<view class="tag">{{ item.member_info.age }}岁</view>
						<!--<view class="tag">-->
						<!--3年工作经验-->
						<!--</view>-->
						<view class="tag">
							{{ item.member_info.education_type_name }}
						</view>
					</view>
				</view>
			</view>

			<view class="pos" v-for="(address, index) in item.member_address" :key="index" :item="address">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png" mode=""></image>
				{{ address.address_info }}
			</view>
		</view>

		<view class="item-down">
			<view
				:class="[
					'status',
					item.member_share.status == 2
						? 'wait'
						: item.member_share.status == 1
						? 'nice'
						: item.member_share.status == 3
						? 'disgusting'
						: '',
				]">
				{{ item.member_share.status_name }}
			</view>
			<view class="btns">
				<view class="btn gray" @click.stop="shareInterview(item.member_share.id)">推荐记录</view>
				<view class="btn sure" v-if="item.but.audit_status == 1" @click.stop="auditBut()">审核</view>
			</view>
		</view>
		<u-popup :show="auditForm.show" round="10" mode="center" @close="auditClose" @open="auditOpen">
			<view class="credit" style="width: 70vw">
				<view class="title comp">审核意见</view>
				<view class="content">
					<u--textarea v-model="auditForm.form.remark" placeholder="请输入内容"></u--textarea>
				</view>

				<div class="butBox">
					<view class="agree" @click="auditSubmit(1)">满意</view>
					<view class="reject" @click="auditSubmit(3)">不满意</view>
				</div>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { memberShareResumeAudit } from '../../config/company_api.js';

export default {
	name: 'recommendResumeItem',
	props: {
		item: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {
			auditForm: {
				form: {
					remark: '',
					member_share_id: 0,
					status: '',
				},
				show: false,
			},
		};
	},
	methods: {
		auditOpen() {
			this.auditForm.show = true;
		},
		auditClose() {
			this.auditForm.show = false;
		},
		auditBut() {
			this.auditOpen();
		},
		auditSubmit(status) {
			var _this = this;
			_this.auditForm.form.member_share_id = _this.item.member_share.id;
			_this.auditForm.form.status = status;

			memberShareResumeAudit(this.auditForm.form).then(response => {
				if (response.status_code == '200') {
					this.$emit('auditHandel', response);
				}
			});
		},
		shareInterview(id) {
			uni.navigateTo({
				url: '/pagesA/list/recommendedRecords?id=' + id,
			});
		},
		goQlmDetails() {
			uni.navigateTo({
				url: '/pagesA/details/qlmDetails?id=' + this.item.member.id,
			});
		},
	},
};
</script>

<style lang="less" scoped>
.item {
	display: flex;
	flex-direction: column;
	padding: 32rpx;
	margin-bottom: 32rpx;
	background-color: #ffffff;
	border-radius: 24rpx;

	.up {
		display: flex;
		flex-direction: column;

		.item-up {
			display: flex;
			padding-bottom: 28rpx;

			& > image {
				width: 104rpx;
				height: 104rpx;
				border-radius: 16rpx;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				flex: 1;
				padding-left: 24rpx;

				.user {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.userInfo {
						display: flex;
						align-items: center;

						.name {
							font-weight: 600;
							font-size: 32rpx;
							color: #333333;
						}

						.cert {
							display: flex;
							align-items: center;
							margin-left: 28rpx;
							padding: 0 12rpx;
							height: 40rpx;
							background: rgba(87, 213, 28, 0.1);
							border-radius: 8rpx;
							font-weight: 600;
							font-size: 20rpx;
							color: #57d51c;
						}
					}

					.status {
						display: flex;
						align-items: center;
						font-weight: 400;
						font-size: 24rpx;
						color: #333333;

						.dot {
							width: 10rpx;
							height: 10rpx;
							border-radius: 50%;
							background: #999999;
							margin-right: 10rpx;
						}

						.line {
							background: #57d51c;
						}
					}
				}

				.tags {
					display: flex;

					.tag {
						display: flex;
						align-items: center;
						padding: 0 12rpx;
						font-weight: 400;
						font-size: 22rpx;
						color: #999999;
						// margin-right: 16rpx;
						border-right: 1px solid #999999;

						&:first-child {
							padding-left: 0;
						}

						&:last-child {
							border-right: none;
						}
					}
				}
			}
		}

		.pos {
			display: flex;
			font-weight: 400;
			font-size: 24rpx;
			color: #666666;
			margin-bottom: 24rpx;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 16rpx;
			}
		}
	}

	.item-down {
		display: flex;
		justify-content: space-between;
		align-items: center;
		.status {
			display: flex;
			align-items: center;
			height: 46rpx;
			font-weight: 600;
			font-size: 24rpx;
			padding: 0 12rpx;
			border-radius: 8rpx;
		}
		.wait {
			background: rgba(79, 140, 240, 0.1);
			color: #4f8cf0;
		}
		.nice {
			background: rgba(87, 213, 28, 0.1);
			color: #57d51c;
		}
		.disgusting {
			background: rgba(254, 77, 79, 0.1);
			color: #fe4d4f;
		}
		.btns {
			display: flex;
			align-items: center;
			.btn {
				display: flex;
				align-items: center;
				height: 56rpx;
				font-weight: 600;
				font-size: 24rpx;
				padding: 0 24rpx;
				border-radius: 8rpx;
				&:last-child {
					margin-left: 16rpx;
				}
			}
			.gray {
				background: #f5f5f7;
				color: #4f8cf0;
			}
			.sure {
				background: #4f8cf0;
				color: #ffffff;
			}
		}
	}
}

.credit {
	display: flex;
	flex-direction: column;
	padding: 40rpx 48rpx;

	.title {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.tip {
			image {
				width: 48rpx;
				height: 48rpx;
			}
		}
	}

	.comp {
		justify-content: center;
	}

	.content {
		display: flex;
		flex-direction: column;
		margin-top: 40rpx;

		.sub-title {
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			font-size: 28rpx;
			color: #000000;
		}
	}

	.agree {
		text-align: center;
		width: 40%;
		align-items: center;
		line-height: 88rpx;
		border-radius: 44rpx;
		height: 88rpx;
		font-weight: 500;
		font-size: 28rpx;
		color: #ffffff;
		background: #4f8cf0;
		margin-top: 32rpx;
	}

	.butBox {
		display: flex;
	}
	.reject {
		margin-left: 20%;
		text-align: center;
		width: 40%;
		align-items: center;
		border-radius: 44rpx;
		height: 88rpx;
		line-height: 88rpx;
		font-weight: 500;
		font-size: 28rpx;
		color: #ffffff;
		background: #f05e56;
		margin-top: 32rpx;
	}
}
</style>
