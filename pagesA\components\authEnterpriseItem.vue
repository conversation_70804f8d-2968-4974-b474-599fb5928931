<template>
	<view class="item">
		<view class="name">
			{{item.authorize_company.name}}
		</view>
		<view class="desc">
			{{item.authorize_company.type_name}}
		</view>
		<view class="butlerBox">
			<image :src="item.authorize_user_member_info.image.thumbnail_path_url" mode="" class="butlerHeadImg">
			</image>
			<view class="butlerText">
				<view class="butlerNameCla">{{item.authorize_user_member_certification.name}}
					{{item.authorize_user_member.cellphone}}
				</view>
				<view class="butlerCompanyCla">{{item.authorize_company.name}}</view>
			</view>
		</view>
		<view class="btns">
			<view :class="['status',item.status_show_flag]" v-if="item.status_show!='通过'">
				{{ item.status_show}}
			</view>
			<view class="btnBox">
				<view class="btn cancel" v-if="item.but.cancel_status == 1" @click="cancel">
					取消授权
				</view>
				<view class="btn selectButler"
					@click="changeButler(item.authorize_user_member_info.image.thumbnail_path_url,item.authorize_user_member_certification.name,item.authorize_company.name,item.authorize_company.id)"
					v-if="item.but.set_status==1">
					设置负责人
				</view>
				<!--				-->
				<!--					<view class="btn talk" @click="setPerson('edit','single')" v-if="item.but.modify_status==2">-->
				<!--						设置负责人-->
				<!--					</view>-->

				<!-- <view class="btn sure" v-if="item.but.sign_status==1" @click="sign">
                签署授权
            </view> -->

				<view class="btn talk" @click.stop="communicate('company',item.authorize_company_id)">
					聊聊呗
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		methods
	} from 'uview-ui/libs/mixin/mixin'
	import {
		communicate
	} from "../../common/common";
	export default {
		props: {
			item: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {}
		},
		methods: {
			communicate,
			changeButler(head, name, company, companyId) {
				uni.navigateTo({
					url: `/pagesA/list/change_butler?head=${head}&name=${name}&company=${company}&companyId=${companyId}`
				})

				// uni.navigateTo({
				// 	url:'/pagesA/list/butler_list'
				// })
			},
			sign() {
				this.$emit('sign', this.item)
			},
			cancel() {
				this.$emit('cancel', this.item)
			},
			//设置负责人
			setPerson(flag, type) {
				uni.navigateTo({
					url: "/pagesA/connectivity/inner?flag=" + flag + "&type=" + type
				})
			}
		}
	}
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		padding: 24rpx 32rpx;
		border-radius: 24rpx;
		background-color: #FFFFFF;
		margin-bottom: 24rpx;

		.name {
			font-weight: 500;
			font-size: 28rpx;
			color: #333333;
		}

		.desc {
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			margin-top: 16rpx;
		}

		.butlerBox {
			height: 88rpx;
			padding-top: 16rpx;
			margin-top: 16rpx;
			display: flex;
			align-items: center;
			border-top: 2rpx solid #F5F5F7;

			.butlerHeadImg {
				width: 88rpx;
				height: 88rpx;
				background: #D9D9D9;
				border-radius: 88rpx 88rpx 88rpx 88rpx;
				margin-right: 16rpx;
			}

			.butlerText {
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.butlerNameCla {
					font-weight: 600;
					font-size: 28rpx;
					color: #333333;
				}

				.butlerCompanyCla {
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
				}
			}
		}

		.btnBox {
			display: flex;
			white-space: nowrap;
		}

		.btns {
			white-space: nowrap;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 16rpx;

			.status {
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8rpx;
				height: 40rpx;
				font-weight: 600;
				font-size: 20rpx;
				padding: 0 12rpx;
			}

			.back {
				background: rgba(254, 77, 79, 0.1);
				color: #FE4D4F;
			}

			.pass {
				background: rgba(87, 213, 28, 0.1);
				color: #57D51C;
			}

			.wait {
				background: rgba(79, 140, 240, 0.1);
				color: #4F8CF0;
			}

			.btn {
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8rpx;
				// height: 64rpx;
				font-weight: 600;
				font-size: 28rpx;
				padding: 12rpx 32rpx;
				margin-left: 24rpx;
			}

			.selectButler {
				background: rgba(87, 213, 28, 0.1);
				color: #57D51C;
			}

			.sure {
				background: #4F8CF0;
				color: #FFFFFF;
			}

			.cancel {
				background: #FE4D4F;
				color: #FFFFFF;
			}

			.talk {
				background: rgba(79, 140, 240, 0.1);
				color: #4F8CF0;
			}
		}
	}
</style>