<template>
    <view id="app">
        <view class="contentBox">
            <!-- 关键字 -->
            <view :class="['cardBox', { 'selected': selectedId === item.id }]" @click="toggleSelect(item)"
                v-for="(item,index) in list" :key="item.id">
                <view class="cardBox_topLeft">签署关键字</view>
                <view class="cardBox_peopleName">{{item.key_word}}</view>
                <view class="abstractBox">
                    <view class="abstractBox_title">摘要</view>
                    <view class="abstractBox_text">{{item.summary}}</view>
                </view>
                <view class="hrCla"></view>
                <view class="cardBox_times marginBox">
                    <view>关键字创建时间：</view>
                    <view>{{item.created_at}}</view>
                </view>
                <view class="cardBox_times">
                    <view>关键字更新时间：</view>
                    <view>{{item.updated_at}}</view>
                </view>
            </view>
        </view>


        <Pages :status="status"></Pages>
        <view style="height: 220rpx;"></view>
        <view class="bottomBtn">
            <view class="bottomBtn_text" @click="addKeyword">选择完成</view>
        </view>

    </view>
</template>

<script>
    import Pages from "../../components/pages.vue";
    import {
        getKeyword
    } from '../../config/api.js'
    export default {
        components: {
            Pages
        },
        data() {
            return {
                type:1,
                page: 1,
                limit: 10,
                status: 'loadmore',
                more: false,
                list: [],
                selectedId: null, // 当前选中的ID
                keyWordItem:'',
            }
        },
        onLoad(option) {
            this.type = option.type
            this.getKeyword()
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getKeyword()
            } else {
                this.status = 'nomore'
            }
        },
        methods: {
            // 关键字列表
            async getKeyword() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    key_word: ''
                }
                const {
                    status_code,
                    data
                } = await getKeyword(params)
                this.list = this.list.concat(data.data);
                console.log("列表数据", data.data)
                // 返回false代表没有下一页
                this.more = data.more;
                this.status = this.more ? "loadmore" : "nomore"
            },
            toggleSelect(item) {
                // 如果当前选中的ID等于点击的ID，取消选择
                if (this.selectedId == item.id) {
                    this.selectedId = null; // 取消选中
                    this.keyWordItem = '';
                } else {
                    this.selectedId = item.id; // 设置为当前选中的ID
                    this.keyWordItem = item;
                }
            },
            addKeyword() {
                console.log(this.keyWordItem)
                if(this.type == 1) {
                    uni.setStorageSync('selectedKeyword',this.keyWordItem)
                } else {
                    uni.setStorageSync('selectedKeywordTwo',this.keyWordItem)
                }
                uni.navigateBack()
            }
        }
    }
</script>
<style>
    page {
        background-color: #F5F5F7;
    }
</style>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
    }

    .contentBox {
        width: 100%;
        padding: 0 32rpx;
    }

    .comp {
        width: 100%;
        height: 244rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
    }


    .bottomBtn {
        width: 100%;
        height: 196rpx;
        background: #FFFFFF;
        box-shadow: 0rpx 0rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
        position: fixed;
        bottom: 0;
        left: 0;
        padding: 24rpx 32rpx;
    }

    .bottomBtn_text {
        width: 100%;
        height: 80rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 80rpx;
        font-weight: 600;
        font-size: 28rpx;
        color: #FFFFFF;
    }


    .cardBox {
        width: 100%;
        // height: 338rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 24rpx 32rpx;
        margin-top: 24rpx;
        position: relative;
        border: 2rpx solid transparent;
    }
    
    .cardBox.selected {
      border: 2rpx solid #4F8CF0; /* 选中时边框颜色变为蓝色 */
    }

    .listMore {
        width: 48rpx;
        height: 48rpx;
        position: absolute;
        top: 50rpx;
        right: 32rpx;
    }

    .cardBox_topLeft {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        margin-bottom: 24rpx;
    }

    .cardBox_peopleName {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
    }

    .abstractBox {
        display: flex;
        justify-content: space-between;
        align-items: top;
    }

    .abstractBox_title {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .abstractBox_text {
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
    }

    .hrCla {
        width: 100%;
        height: 2rpx;
        background: #F5F5F7;
        margin: 24rpx 0;
    }

    .cardBox_times {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .marginBox {
        margin-bottom: 16rpx;
    }
</style>