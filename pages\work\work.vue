<template>
	<view class="container">
		<u-navbar placeholder>
			<template #left>
				<view class="nav-container">
					<view v-for="v in navTabsList" :key="v.key" @tap="onNavTabsClick(v.key)"
						:class="navTabsActiveKey === v.key && 'is_active'">
						{{v.name}}
					</view>
				</view>
			</template>
		</u-navbar>
		<u-sticky>
			<view class="sticky-container">
				<u-search v-model="params.title" placeholder="请输入任务名称" :showAction="false" bgColor="#FFFFFF"></u-search>
				<view class="filter-container">
					<view class="item" @click="isStartTime = true">
						<text>{{'开始时间'}}</text>
						<image class="image" src="/static/images/index/down.png" alt="">
					</view>
					<view class="item" @click="isEndTime = true">
						<text>{{'结束时间'}}</text>
						<image class="image" src="/static/images/index/down.png" alt="">
					</view>
					<template v-if="navTabsActiveKey === '1'">
						<view class="item" @click="isType = true">
							<text>任务类型</text>
							<image class="image" src="/static/images/index/down.png" alt="">
						</view>
					</template>
					<view class="item" @click="isCommis = true">
						<text>任务佣金</text>
						<image class="image" src="/static/images/index/down.png" alt="">
					</view>
				</view>
			</view>
		</u-sticky>
		<scroll-view :scroll-y="true" class="scroll-view" @scrolltolower="onScrollTolower" lower-threshold="400">
			<view class="scroll-container">
				<template v-if="navTabsActiveKey === '1'">
					<view class="item-square" v-for="v in taskList" :key="v.task.id" @click="onTapSquareItem(v)">
						<view class="item-start">
							<view class="start_left">
								<text>{{v.task.title}}</text>
								<text>{{v.task.type_name}}</text>
							</view>
							<view class="start_right">{{v.task.unit_total}}/人</view>
						</view>
						<u-line></u-line>
						<view class="item-end">
							<view>发布人：{{v.member_certification.name}}</view>
							<view>发布时间：{{v.task.created_at}}</view>
							<view class="end_bottom">
								<image class="image" :src="v.company_info.logo.thumbnail_path_url" mode=""></image>
								<text class="company">{{v.company.name}}</text>
								<view class="tag">{{v.task.active_status_name}}</view>
							</view>
						</view>
					</view>
				</template>
				<template v-if="navTabsActiveKey === '2'">
					<view class="item" v-for="_ in 20">
						<view class="item-start">
							<view class="start_top">
								<text class="top_1">2.20职位任务</text>
								<text class="top_2">1000/人</text>
							</view>
							<view class="start_bottom">
								<text class="bottom_1">职位任务</text>
								<text class="bottom_2">1/30</text>
							</view>
						</view>
						<u-line></u-line>
						<view class="item-end">
							<view class="end-top">
								<text>发布人：张瑞显</text>
								<image class="image" src="/static/images/icon/start.png"></image>
							</view>
							<view class="end_bottom">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode=""></image>
								<text class="company">公司名称公司名称公司名称</text>
								<view class="tag">进行中</view>
							</view>
						</view>
					</view>
				</template>
			</view>
			<u-datetime-picker title="选择开始时间" :show="isStartTime" v-model="params.start_time_timestamp" mode="datetime"
				:closeOnClickOverlay="true" @confirm="onStartDateTimeConfirm" @cancel="onStartDateTimeClose"
				@close="onStartDateTimeClose"></u-datetime-picker>
			<u-datetime-picker title="选择结束时间" :show="isEndTime" v-model="params.end_time_timestamp" mode="datetime"
				:closeOnClickOverlay="true" @confirm="onEndDateTimeConfirm" @cancel="onEndDateTimeClose"
				@close="onEndDateTimeClose"></u-datetime-picker>
			<u-picker title="选择任务佣金" :show="isCommis" :columns="taskCommisList" keyName="label"
				:closeOnClickOverlay="true" @confirm="onTaskCommisConfirm" @cancel="onTaskCommisClose"
				@close="onTaskCommisClose"></u-picker>
			<u-picker title="选择任务类型" :show="isType" :columns="taskTypeList" keyName="label" :closeOnClickOverlay="true"
				@confirm="onTaskTypeConfirm" @cancel="onTaskTypeClose" @close="onTaskTypeClose"></u-picker>
		</scroll-view>
		<Tabbar />
	</view>
</template>

<script>
	import Tabbar from "@/components/tabbar.vue";
	import {
		taskSquare
	} from '../../config/common_api.js'
	export default {
		components: {
			Tabbar
		},
		data() {
			return {
				navTabsActiveKey: '1',
				navTabsList: [{
						key: '1',
						name: '任务广场'
					},
					{
						key: '2',
						name: '我的任务'
					}
				],
				tabsList: [{
						key: '1',
						name: '职位任务'
					},
					{
						key: '2',
						name: '自定义任务'
					}
				],

				taskList: [],
				taskCommisList: [
					[{
							label: '任务佣金',
							value: null
						}, {
							label: '100',
							value: 100,
						},
						{
							label: '200',
							value: 200,
						},
						{
							label: '500',
							value: 500,
						},
						{
							label: '1000',
							value: 1000,
						},
						{
							label: '2000',
							value: 2000,
						},
						{
							label: '3000',
							value: 3000,
						},
						{
							label: '4000',
							value: 4000,
						}
					]
				],
				taskTypeList: [
					[{
							value: null,
							label: '任务类型'
						}, {
							value: 'job',
							label: '职位任务'
						},
						{
							value: 'custom',
							label: '自定义任务'
						}
					]
				],
				isStartTime: false,
				isEndTime: false,
				isCommis: false,
				isType: false,
				isLoading: false,
				params: {
					start_time_timestamp: Number(new Date()),
					end_time_timestamp: Number(new Date()),
					start_time: '',
					end_time: '',
					unit_total: null,
					type: null,
					page: 1,
					limit: 20,
					title: null,
				},
				more: true
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetTaskSquare();
				},
				deep: true
			}
		},
		methods: {
			onShow() {
				this.onGetTaskSquare();
			},
			onInitTaskList() {
				this.params.page = 1;
				this.taskList = [];
			},
			onScrollTolower() {
				if(!this.more) return;
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			formatTimestamp(timestamp) {
				const date = new Date(timestamp);
				const Y = date.getFullYear();
				const M = String(date.getMonth() + 1).padStart(2, '0');
				const D = String(date.getDate()).padStart(2, '0');
				const h = String(date.getHours()).padStart(2, '0');
				const m = String(date.getMinutes()).padStart(2, '0');
				const s = String(date.getSeconds()).padStart(2, '0');
				return `${Y}-${M}-${D} ${h}:${m}:${s}`;
			},
			onTaskTypeClose() {
				this.isType = false;
			},
			onTaskTypeConfirm(res) {
				this.isType = false;
				this.params.type = res.value[0].value;
				this.onInitTaskList();
			},
			onTaskCommisClose() {
				this.isCommis = false;
			},
			onTaskCommisConfirm(res) {
				this.isCommis = false;
				this.params.unit_total = res.value[0].value;
				this.onInitTaskList();
			},
			onEndDateTimeClose() {
				this.isEndTime = false;
			},
			onEndDateTimeConfirm(res) {
				this.isEndTime = false;
				this.params.end_time = this.formatTimestamp(res.value);
				this.onInitTaskList();
			},
			onStartDateTimeClose() {
				this.isStartTime = false;
			},
			onStartDateTimeConfirm(res) {
				this.isStartTime = false;
				this.params.start_time = this.formatTimestamp(res.value);
				this.onInitTaskList();
			},
			onTapSquareItem(v) {
				uni.$u.route({
					url: "/pagesA/details/taskDetails",
					params: {
						id: v.task.id
					}
				})
			},
			async onGetTaskSquare() {
				const res = await taskSquare(this.params);
				if (res.status_code !== '200') return;
				this.taskList = [...this.taskList, ...res.data.data];
				this.more = res.data.more;
				this.isLoading = false;
			},
			onNavTabsClick(key) {
				this.navTabsActiveKey = key;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #F5F5F7;
		display: flex;
		flex-direction: column;

		.sticky-container {
			padding: 24rpx;
			display: flex;
			flex-direction: column;
			gap: 24rpx;

			.filter-container {
				display: flex;
				align-items: center;
				gap: 16rpx;

				.item {
					color: #333333;
					font-size: 24rpx;
					display: flex;
					align-items: center;
					padding-block: 8rpx;
					padding-inline: 16rpx;
					border-radius: 8rpx;
					background-color: #FFFFFF;

					.image {
						width: 32rpx;
						height: 32rpx;
					}
				}
			}
		}

		.nav-container {
			display: flex;
			align-items: center;
			gap: 64rpx;
			padding-inline-start: 24rpx;

			.is_active {
				color: #3D82F3;
			}
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;
			.scroll-container {
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding-block-end: calc(128rpx + constant(safe-area-inset-bottom));
				padding-block-end: calc(128rpx + env(safe-area-inset-bottom));

				.item {
					padding: 32rpx;
					display: flex;
					flex-direction: column;
					gap: 24rpx;
					background-color: #FFFFFF;
					border-radius: 24rpx;

					.item-end {
						display: flex;
						flex-direction: column;
						gap: 8rpx;
						color: #333333;
						font-size: 28rpx;

						.end-top {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.image {
								width: 48rpx;
								height: 48rpx;
							}
						}

						.end_bottom {
							display: flex;
							align-items: center;
							gap: 16rpx;

							.image {
								width: 48rpx;
								height: 48rpx;
							}

							.company {
								color: #999999;
								font-size: 24rpx;
							}

							.tag {
								margin-inline-start: auto;
								background: rgba(79, 140, 240, 0.1);
								color: #4F8CF0;
								font-size: 24rpx;
								padding-block: 6rpx;
								padding-inline: 12rpx;
								border-radius: 8rpx;
							}
						}
					}

					.item-start {
						display: flex;
						flex-direction: column;
						gap: 16rpx;

						.start_top {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.top_1 {
								color: #333333;
								font-size: 32rpx;
							}

							.top_2 {
								color: #F98A14;
								font-size: 32rpx;
							}
						}

						.start_bottom {
							display: flex;
							align-items: center;
							justify-content: space-between;

							.bottom_1 {
								color: #4F8CF0;
								font-size: 24rpx;
							}

							.bottom_2 {
								color: #F98A14;
								font-size: 24rpx;
							}
						}
					}
				}

				.item-square {
					padding: 32rpx;
					display: flex;
					flex-direction: column;
					gap: 24rpx;
					background-color: #FFFFFF;
					border-radius: 24rpx;

					.item-start {
						display: flex;
						align-items: center;
						justify-content: space-between;

						.start_left {
							display: flex;
							flex-direction: column;
							gap: 16rpx;
						}

						.start_right {
							color: #F98A14;
							font-size: 32rpx;
						}
					}

					.item-end {
						display: flex;
						flex-direction: column;
						gap: 8rpx;
						color: #333333;
						font-size: 28rpx;

						.end_bottom {
							display: flex;
							align-items: center;
							gap: 16rpx;

							.image {
								width: 48rpx;
								height: 48rpx;
								border-radius: 999rpx;
							}

							.company {
								color: #999999;
								font-size: 24rpx;
							}

							.tag {
								margin-inline-start: auto;
								background: rgba(79, 140, 240, 0.1);
								color: #4F8CF0;
								font-size: 24rpx;
								padding-block: 6rpx;
								padding-inline: 12rpx;
								border-radius: 8rpx;
							}
						}
					}
				}
			}
		}
	}
</style>
