<template>
	<view class="home-index">
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						期望薪资<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeSalary" :value="salaryIndex" :range="sysData.salary_range"
							range-key="label">
							<view class="d-picker" :style="{color:salaryIndex==0?'#c0c4cc':'#303133'}">
								{{sysData && sysData.salary_range.length>0?sysData.salary_range[salaryIndex]['label']:''}}
							</view>
						</picker>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				
				<view class="inp-item">
					<view class="title">
						期望岗位<text class="star">*</text>
					</view>
					<view class="in se">
						<uni-data-picker :map="mapClass" placeholder="请选择岗位" popup-title="请选择岗位"
							:localdata="jobTypeList" v-model="job_class" @change="onchangeclass">
						</uni-data-picker>
					</view>
				</view>
				
				<view class="inp-item">
					<view class="title">
						期望工作地点<text class="star">*</text>
					</view>
					<view class="in se">
						<!-- <picker @change="bindPickerChange" :value="sexIndex" :range="sex">
							<view class="d-picker">{{sex[sexIndex]}}</view>
						</picker> <u-icon name="arrow-right"></u-icon> -->
						<uni-data-picker :map="map" placeholder="请选择工作地址" popup-title="请选择所在地区" :localdata="cityList"
							v-model="form.expect_address" @change="onchange">
						</uni-data-picker>
					</view>
				</view>
				
				<view class="inp-item">
					<view class="title">
						求职类型<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeWorkStatus" :value="workStatusIndex" :range="workStatusList" range-key="name">
							<view class="d-picker" :style="{color:workStatusIndex==0?'#c0c4cc':'#303133'}">
								{{workStatusList[workStatusIndex]['name']}}
							</view>
						</picker> 
						 <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
			</view>
		</view>
		
		
		
		<view class="footer">
			<view class="next pub" @click="save">
				保存
			</view>
		</view>
	</view>
</template>

<script>
	import { getResumeDetails,getClassList ,addResume,updateLoginInfo} from "../../config/api.js"
	export default{
		data(){
			return{
				salaryIndex:0,
				form:{
					expect_salary:0,
					job_class_id:'',
					expect_address:'',
					work_status:''
				},
				map: {
					text: 'label',
					value: 'value'
				},
				mapClass: {
					text: 'name',
					value: 'id'
				},
				workStatusList: [{
						name: '请选择',
						value: 0
					},
					{
						name: '全职',
						value: 'full'
					},
					{
						name: '兼职',
						value: 'part'
					},
					{
						name: '实习',
						value: 'internship'
					}
				
				],
				workStatusIndex: 0,
				job_class:'',
				jobTypeList: [],
			}
		},
		computed:{
			sysData(){
				return this.$store.state.sysData || uni.getStorageSync('sysData')
			},
			cityList(){
				return this.$store.state.cityList || uni.getStorageSync('cityList')
			}
		},
		onLoad(options) {
			this.id = options.id
			this.getResumeDetails()
			this.getClassList()
		},
		methods:{
			changeWorkStatus(e) {
				this.workStatusIndex = e.detail.value
				this.form.work_status = this.workStatusList[this.workStatusIndex]['value']
			},
			
			onchange(e) {
				// console.log("工作地址：", e);
				let data = e.detail.value
				this.form.expect_province_id = data[0]['value']
				this.form.expect_city_id = data[1]['value']
				this.form.expect_district_id = data[2]['value']
				this.form.expect_address = data.map(item => item.text).join('')
				// console.log("期望地址：", this.form.expect_address)
			},
			
			onchangeclass(e) {
				let data = e.detail.value
				this.form.job_class_id = data[data.length-1]['value']
                this.job_class = data.map(item => item.text).join('')
				console.log(data)
			},
			
			changeSalary(e) {
				this.salaryIndex = e.detail.value
				console.log(this.salaryIndex, "索引")
				this.form.expect_salary = this.sysData['salary_range'][this.salaryIndex]['value']
			},
			
			//岗位类型
			async getClassList() {
				const result = await getClassList()
				if (result.status_code == 200) {
					this.jobTypeList = result.data;
				}
			},
			
			//简历详情
			async getResumeDetails(){
				let params = {
					id:this.id
				}
				const {status_code,data} = await getResumeDetails(params)
				if(status_code==200){
					this.details = data;
					// this.salaryIndex = this.sysData.salary_range.findIndex(item=>item.label==data.expect_salary_k)
				}
			},
			
			async save(){
				let params = {
					id:this.id,
					...this.form
				}
				const result = await addResume(params)
				if(result.status_code==200){
					let loginInfo = await updateLoginInfo()
					if (loginInfo.status_code == 200) {
						this.$store.commit('setUserInfo', loginInfo.data)
						return uni.$u.toast('保存信息成功')
					}
					
					
					return uni.$u.toast('成功')
				}
			}
		}
	}
</script>
<style>
	page{
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index{
		padding-bottom: 170rpx;
	}
	.wrap {
		padding: 30rpx;
	
		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			margin-bottom: 32rpx;
			.avatar {
				display: flex;
				align-items: center;
	
				.pic {
					padding: 0 30rpx 0 0;
	
					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}
	
			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;
				.txt{
					padding: 24rpx 0;
				}
				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;
					.star{
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}
				
				.supTitle{
					font-weight: 500;
					font-size: 32rpx;
					color: #333333;
				}
	
				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;
					::v-deep uni-data-picker {
						width: 100%;
					}
					
					::v-deep .arrow-area {
						transform: rotate(-135deg);
					}
					
					::v-deep .input-arrow {
						width: 20rpx;
						height: 20rpx;
						border-left: 1px solid #606266;
						border-bottom: 1px solid #606266;
					}
					
					::v-deep .input-value-border {
						border: none;
					}
					
					::v-deep .input-value {
						padding: 0;
					}
					
					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}
					
					::v-deep picker{
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;
						.d-picker{
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
					
					
					
				}
	
				.se {
					color: #999;
				}
	
				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}
	
	.footer {
		display: flex;
		justify-content: space-around;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 16rpx 16rpx 0 0;
	
		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}
		.save{
			border: 1px solid #4F8CF0;
			color: #4F8CF0;
		}
		.pub {
			background: #4F8CF0;
			border: 1px solid #4F8CF0;
			color: #FFFFFF;
		}
	}
</style>