<template>
	<view id="app">
		<view class="content">
			<view class="title">协同名称  <text class="redIcon">*</text> </view>
			<input type="text"  placeholder="请输入项目名称" class="ipt" v-model="title"/>
		</view>
        <view class="hrBox">
        	
        </view>
		<view class="content">
			<view class="title">项目描述  <text class="redIcon">*</text> </view>
			<textarea name="" id="" cols="30" rows="10" class="ipt contentText" placeholder="请输入项目描述" v-model="content"></textarea>
		</view>
		
		<view class="bottomBox">
			<view class="bottomBtn" @click="saveCooperate">发起</view>
		</view>
	</view>
</template>

<script>
    import {saveCooperate} from '../../config/headhunterList_api.js'
    export default {
        data() {
            return {
               title:'',
               content:''
            }
        },
        methods:{
            async saveCooperate() {
                let params = {
                    title:this.title,
                    content:this.content,
                }
                
                const res = await saveCooperate(params)
                if(res.status_code==200) {
                    uni.showToast({
                    	title: '发布成功',
                    	icon: 'none'
                    });
                }
            }
        }
    }
</script>
<style scoped lang="less">
	view {
		box-sizing: border-box;
	}
	#app {
		width: 100%;
		min-height: 100vh;
		background: #fff;
	}
	
	.content {
		padding: 32rpx;
	}
	
	.title {
		font-weight: 400;
		font-size: 22rpx;
		color: #666666;
		margin-bottom: 16rpx;
	}
	
	.redIcon {
		color: #FE4D4F !important;
	}
	
	.ipt {
		font-weight: 400;
		font-size: 32rpx;
		// color: #999999;
	}
	
	.hrBox {
		width: 100%;
		height: 2rpx;
		background: #F5F5F7;
	}
	
	.rightIcon {
		width: 32rpx;
		height: 32rpx;
	}
	
	.contentIptBox {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.contentText {
		width: 100%;
	}
	
	.bottomBox {
		width: 100%;
		height: 196rpx;
		padding: 24rpx 22rpx 92rpx 42rpx;
		background: #FFFFFF;
		position: fixed;
		bottom: 0;
		left: 0;
		border-radius: 24rpx 24rpx 0 0;
	}
	
	.bottomBtn {
		width: 100%;
		height: 80rpx;
		background: linear-gradient( 135deg, #4F8CF0 0%, #1E6DEE 100%);
		border-radius: 16rpx 16rpx 16rpx 16rpx;
		text-align: center;
		line-height: 80rpx;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
	}
</style>