page {
    background-color: #F5F5F7;
}

.home-index {
    padding: 0 0 256rpx 0;
}

.step {
    padding: 32rpx 0;
    border-radius: 0 0 24rpx 24rpx;
}

::v-deep .placeholderClass {
    font-weight: 400;
    font-size: 32rpx;
}



.wrap {
    padding: 0 32rpx;
    margin-top: 32rpx;
    
    .wrap-title {
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        padding: 0 0 24rpx 0;
        
        .star {
            font-weight: 600;
            font-size: 22rpx;
            color: #FE4D4F;
            margin-left: 8rpx;
        }
    }
    
    .pic-list {
        display: flex;
        flex-wrap: wrap;
        
        .pic-item {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200rpx;
            width: calc(33.3% - 32rpx);
            margin-bottom: 32rpx;
            margin-right: 32rpx;
            position: relative;
            border-radius: 16rpx;
            
            &>image {
                width: 100%;
                height: 100%;
                border-radius: 16rpx;
            }
            
            .zz {
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: rgba(0, 0, 0, 0.5);
                width: 100%;
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;
                z-index: 10;
                border-radius: 16rpx;
                
                .del {
                    display: flex;
                    align-items: center;
                    border-radius: 16rpx;
                    padding: 0 16rpx;
                    height: 56rpx;
                    background: rgba(255, 255, 255, 0.5);
                    font-weight: 500;
                    font-size: 28rpx;
                    color: #FFFFFF;
                }
            }
        }
        
        .add {
            background-color: #FFFFFF;
            
            image {}
        }
        
    }
    
    .inp {
        background: #FFFFFF;
        border-radius: 16rpx;
        margin-bottom: 32rpx;
        
        .avatar {
            display: flex;
            align-items: center;
            
            .pic {
                padding: 0 30rpx 0 0;
                
                image {
                    width: 108rpx;
                    height: 108rpx;
                }
            }
        }
        
        .inp-item {
            display: flex;
            flex-direction: column;
            padding: 0 30rpx;
            flex: 1;
            
            .txt {
                padding: 24rpx 0;
                
            }
            
            .title {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 22rpx;
                color: #666666;
                margin: 16rpx 0 0 0;
                
                .star {
                    font-weight: 600;
                    font-size: 22rpx;
                    color: #FE4D4F;
                    margin-left: 8rpx;
                }
            }
            
            .supTitle {
                font-weight: 500;
                font-size: 32rpx;
                color: #333333;
            }
            
            .in {
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-bottom: 1px solid #F5F5F7;
                height: 88rpx;
                font-size: 32rpx;
                
                ::v-deep uni-data-picker {
                    width: 100%;
                }
                
                ::v-deep .arrow-area {
                    transform: rotate(-135deg);
                }
                
                ::v-deep .input-arrow {
                    width: 20rpx;
                    height: 20rpx;
                    border-left: 1px solid #606266;
                    border-bottom: 1px solid #606266;
                }
                
                ::v-deep .input-value-border {
                    border: none;
                }
                
                ::v-deep .input-value {
                    padding: 0;
                }
                
                ::v-deep .placeholderClass {
                    font-weight: 400;
                    font-size: 32rpx;
                }
                
                ::v-deep picker {
                    display: flex;
                    flex-direction: column;
                    flex: 1;
                    height: 88rpx;
                    
                    .d-picker {
                        display: flex;
                        align-items: center;
                        // width: 60vw;
                        height: 88rpx;
                    }
                }
            }
            
            .se {
                color: #999;
            }
            
            .lab {
                font-weight: 400;
                font-size: 22rpx;
                color: #999999;
            }
        }
    }
}

.footer {
    width: 100%;
    height: 224rpx;
    left: 0;
    bottom: 0;
    z-index: 10;
    position: fixed;
    background-color: #F5F5F7;
    
    .btns {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        padding: 0 32rpx;
        flex: 1;
        
        .btn {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 72rpx;
            border-radius: 36rpx;
            font-weight: 600;
            font-size: 28rpx;
            
            &:first-child {
                margin-bottom: 24rpx;
            }
        }
        
        .jump {
            background: #FFFFFF;
            color: #4F8CF0;
        }
        
        .next {
            background: #4F8CF0;
            color: #FFFFFF;
        }
    }
}

.delBtnCla {
    width: 100%;
    // height: 100rpx;
    text-align: right;
    box-sizing: border-box;
    margin-bottom: 32rpx;
}

.delCla {
    width: 100rpx;
    // height: 100rpx;
    background: red;
    color: #FFF;
    padding: 10rpx 20rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    // line-height: 100rpx;
}

.addCla {
    width: 100rpx;
    // height: 100rpx;
    background: #4F8CF0;
    color: #FFF;
    padding: 10rpx 20rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    // line-height: 100rpx;
}

