<template>
    <view class="home-index">
        <view class="wrap">
            <view class="active-info">
                <view class="name">
                    活动名称
                </view>
                <view class="cont">
                    {{details.job_active.title}}
                </view>
            </view>
        </view>
        <view class="wrap">
            <view class="inp">
                <view class="inp-item" @click="selectPeople(1)">
                    <view class="title">
                        活动负责人
                        <text class="star">*</text>
                    </view>
                    <view class="in se">
                        {{executorValue?executorValue:'请选择活动负责人'}}
                        <u-icon name="arrow-right"></u-icon>
                        <!-- <picker @change="changeTaskType" :value="taskTypeIndex" :range="taskTypeList" range-key="name">
                            <view class="d-picker" :style="{color:taskTypeIndex==0?'#c0c4cc':'#303133'}">{{taskTypeList[taskTypeIndex]['name']}}</view>
                        </picker> <u-icon name="arrow-right"></u-icon> -->
                    </view>
                </view>

                <view class="inp-item" @click="selectPeople(2)">
                    <view class="title">
                        协作人
                        <text class="star">*</text>
                    </view>
                    <view class="in se">
                        {{collaboratorValue?collaboratorValue:'请选择活动协作人'}}
                        <u-icon name="arrow-right"></u-icon>
                        <!-- picker @change="changeTaskType" :value="taskTypeIndex" :range="taskTypeList" range-key="name">
                            <view class="d-picker" :style="{color:taskTypeIndex==0?'#c0c4cc':'#303133'}">{{taskTypeList[taskTypeIndex]['name']}}</view>
                        </picker> <u-icon name="arrow-right"></u-icon> -->
                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        参与类型
                        <text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker @change="changeTaskType" :value="taskTypeIndex" :range="taskTypeList" range-key="name">
                            <view class="d-picker" :style="{color:taskTypeIndex==0?'#c0c4cc':'#303133'}">
                                {{taskTypeList[taskTypeIndex]['name']}}
                            </view>
                        </picker>
                        <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>

                <view class="pic" v-if="form.report_type == 'authorize'">
                    <view class="title">
                        <text>补充证明材料</text>
                    </view>

                    <view class="pic-list">
                        <view class="pic-item" v-for="(item,index) in fileData" :key="index">
                            <image :src="item.url" mode=""></image>
                            <view class="zz">
                                <view class="del" @click="delCert(index)">
                                    删除
                                </view>
                            </view>
                        </view>
                        <view class="pic-item add" @click="uploadCert">
                            <image src="/static/images/my/add.png" style="width: 40rpx;height: 40rpx;" mode=""></image>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="wrap">
            <view class="fee-info">
                <view class="name">
                    报名费用
                    <text class="star">*</text>
                </view>
                <view class="money">
                    ¥{{ price }}
                </view>
            </view>
        </view>

        <view class="footer">
            <!-- <view class="next save" @click="next">
                保存草稿
            </view> -->
            <view class="btns">
                <view class="next pub" @click="submit">
                    报名活动
                </view>
            </view>

        </view>
    </view>
</template>

<script>
    import {uploadImg} from "../../config/api";
    import {getQLMJobfairDetails} from "../../config/api";
    import {headhunterJobActiveReport} from "../../config/headhunterList_api";

    export default {
        data() {
            return {
                form: {
                    id: 0,
                    remark: "",
                    report_type: "general",
                    file: [],
                    user_id: [],
                    manage_user_id: [],
                },
                fileData: [],
                price: '0.00',
                details: {},
                taskTypeIndex: 0,
                taskTypeList: [
                    {
                        value: "",
                        name: '请选择'
                    },
                    {
                        value: 'general',
                        name: '普通报名'
                    },
                    {
                        value: 'authorize',
                        name: '委托报名'
                    }
                ]
            }
        },
        onLoad(options) {
            var _this = this;
            var job_active_id = options.id;

            _this.getHeadhuntersJobfairDetails(job_active_id);

            this.startTime = uni.$u.timeFormat(this.start, 'yyyy-mm-dd');
            this.endTime = uni.$u.timeFormat(this.end, 'yyyy-mm-dd');
        },
        computed: {
            roleType() {
                return this.$store.state.roleType || uni.getStorageSync("roleType")
            },
            sysData() {
                return this.$store.state.sysData || uni.getStorageSync('sysData')
            },
            cityList() {
                return this.$store.state.cityList || uni.getStorageSync('cityList')
            },

            executor() {
                return this.$store.state.executor || []
            },
            //负责人显示值
            executorValue() {
                var _this = this;
                _this.form.manage_user_id = [];

                _this.executor.map(item => {
                    _this.form.manage_user_id.push(item.id)
                });

                return this.executor.map(item => item.name).join(',')
            },
            collaborator() {
                return this.$store.state.collaborator || []
            },
            //协作人显示值
            collaboratorValue() {
                var _this = this;
                _this.form.user_id = [];

                this.collaborator.map(item => {
                    _this.form.user_id.push(item.id)
                });

                return this.collaborator.map(item => item.name).join(',')
            }

        },

        methods: {
            submit() {
                var _this = this;

                headhunterJobActiveReport(_this.form).then(response => {
                    uni.showToast({
                        title: response.message,
                        icon: 'none'
                    });

                    if (response.status_code == '200') {
                        return uni.navigateBack();
                    }
                });
            },
            getPrice() {
                var _this = this;
                _this.form.report_type = this.taskTypeList[this.taskTypeIndex]['value'];
                switch (_this.form.report_type) {
                    case 'authorize':
                        _this.price = _this.details.job_active.authorize_price_format || '0.00';
                        break;
                    case 'general':
                        _this.price = _this.details.job_active.price_format || '0.00';
                        break;
                }
            },
            async getHeadhuntersJobfairDetails(id) {
                let params = {
                    id: id * 1
                }
                const {
                    status_code,
                    data,
                    message
                } = await getQLMJobfairDetails(params)
                if (status_code == 200) {
                    this.details = data;
                    this.form.id = data.job_active.id;
                    this.getPrice();
                }
            },
            selectPeople(num) {
                if(num==1) {
                    uni.navigateTo({
                        url:'/pagesA/select/selectPeople?type=1'
                    })
                } else {
                    uni.navigateTo({
                        url:'/pagesA/select/selectPeople?type=2'
                    })
                }
            },
            //去选择执行人，协作人
            handleSelect(flag, type) {
                uni.navigateTo({
                    url: "/pagesA/connectivity/inner?flag=" + flag + "&type=" + type
                })
            },
            changeTaskType(e) {
                this.taskTypeIndex = e.detail.value;
                this.getPrice();

            },
            uploadCert() {
                let self = this
                uni.chooseMedia({
                    count: 1,
                    mediaType: ['image'],
                    sizeType: ['original', 'compressed'],
                    sourceType: ['album', 'camera'],
                    success: (tempFilePaths) => {
                        const path = tempFilePaths.tempFiles[0].tempFilePath;
                        // $dialog.loading('上传中')
                        uni.getFileSystemManager().readFile({
                            filePath: path,
                            encoding: 'base64',
                            success: async function (res) {
                                let imageParams = {
                                    ext: 'png',
                                    content: res.data,
                                    org_name: new Date().getTime() + '.png'
                                }
                                // 上传
                                const result = await uploadImg(imageParams)
                                if (result.status_code == 200) {
                                    self.fileData.push({
                                        id: result.data.id,
                                        url: result.data.url
                                    });
                                    self.form.file = [];
                                    self.fileData.map(item => {
                                        self.form.file.push(item.id);
                                    })
                                }
                            }
                        })
                    }
                });
            },

            delCert(index) {
                this.fileData.splice(index, 1);
                self.form.file = [];
                this.fileData.map(item => {
                    self.form.file.push(item.id);
                })
            },

        }
    }
</script>
<style>
    page {
        background-color: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    .home-index {
        padding-bottom: 190rpx;

    }

    .wrap {
        padding: 0 32rpx;

        .fee-info {
            display: flex;
            flex-direction: column;
            background-color: #FFFFFF;
            padding: 30rpx;
            border-radius: 16rpx;
            .name {
                font-weight: 400;
                font-size: 22rpx;
                color: #666666;
                .star {
                    font-weight: 600;
                    font-size: 22rpx;
                    color: #FE4D4F;
                    margin-left: 8rpx;
                }
            }
            .money {
                display: flex;
                flex-direction: row-reverse;
                font-weight: 600;
                font-size: 32rpx;
                color: #F9AD14;
                margin-top: 12rpx;
            }
        }

        .active-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #FFFFFF;
            height: 96rpx;
            border-radius: 16rpx;
            padding: 0 30rpx;
            margin-top: 32rpx;
            .name {
                font-weight: 400;
                font-size: 28rpx;
                color: #CCCCCC;
            }
            .cont {
                font-weight: 400;
                font-size: 28rpx;
                color: #333333;
            }
        }
        .inp {
            background: #FFFFFF;
            border-radius: 16rpx;
            margin-top: 32rpx;
            .avatar {
                display: flex;
                align-items: center;

                .pic {
                    padding: 0 30rpx 0 0;

                    image {
                        width: 108rpx;
                        height: 108rpx;
                    }
                }
            }

            .inp-item {
                display: flex;
                flex-direction: column;
                padding: 0 30rpx;
                flex: 1;

                .title {
                    display: flex;
                    align-items: center;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #666666;
                    margin: 16rpx 0 0 0;
                    .star {
                        font-weight: 600;
                        font-size: 22rpx;
                        color: #FE4D4F;
                        margin-left: 8rpx;
                    }
                }

                .in {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    border-bottom: 1px solid #F5F5F7;
                    height: 88rpx;
                    font-size: 32rpx;
                    // ::v-deep picker{
                    // 	display: flex;
                    // 	flex-direction: column;
                    // 	flex: 1;
                    // 	height: 88rpx;
                    // 	.d-picker{
                    // 		display: flex;
                    // 		align-items: center;
                    // 		// width: 60vw;
                    // 		height: 88rpx;
                    // 	}
                    // }

                    ::v-deep .placeholderClass {
                        font-weight: 400;
                        font-size: 32rpx;
                    }
                    ::v-deep picker {
                        display: flex;
                        flex-direction: column;
                        flex: 1;
                        height: 88rpx;
                        .d-picker {
                            display: flex;
                            align-items: center;
                            // width: 60vw;
                            height: 88rpx;
                        }
                    }
                }

                .se {
                    color: #999;
                }

                .lab {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #999999;
                }
            }

            .pic {
                display: flex;
                flex-direction: column;
                margin-bottom: 32rpx;
                padding: 0 32rpx 32rpx 32rpx;

                .title {
                    display: flex;
                    align-items: center;

                    & > text {
                        font-weight: 600;
                        font-size: 32rpx;
                        color: #333333;
                        margin-right: 16rpx;
                    }

                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    margin: 16rpx 0;

                    .star {
                        font-weight: 600;
                        font-size: 22rpx;
                        color: #FE4D4F;
                        margin-left: 8rpx;
                    }
                }

                .pic-list {
                    display: flex;
                    flex-wrap: wrap;

                    .pic-item {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 200rpx;
                        width: calc(33.3% - 32rpx);
                        margin-bottom: 32rpx;
                        margin-right: 32rpx;
                        position: relative;
                        border-radius: 16rpx;

                        &>image {
                            width: 100%;
                            height: 100%;
                            border-radius: 16rpx;
                        }

                        .zz {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            background-color: rgba(0, 0, 0, 0.5);
                            width: 100%;
                            height: 100%;
                            position: absolute;
                            left: 0;
                            top: 0;
                            z-index: 10;
                            border-radius: 16rpx;

                            .del {
                                display: flex;
                                align-items: center;
                                border-radius: 16rpx;
                                padding: 0 16rpx;
                                height: 56rpx;
                                background: rgba(255, 255, 255, 0.5);
                                font-weight: 500;
                                font-size: 28rpx;
                                color: #FFFFFF;
                            }
                        }
                    }

                    .add {
                        background-color: #FFFFFF;

                        image {}
                    }

                }
            }
        }
    }

    .footer {
        display: flex;
        justify-content: space-around;
        align-items: center;
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 158rpx;
        background-color: #FFFFFF;
        z-index: 10;
        border-radius: 16rpx 16rpx 0 0;
        .btns {
            display: flex;
            width: 90%;
            .next {
                display: flex;
                justify-content: center;
                align-items: center;
                font-weight: 600;
                height: 80rpx;
                // width: 45%;
                width: 100%;
                font-size: 28rpx;
                border-radius: 16rpx;
            }
            .save {
                border: 1px solid #4F8CF0;
                color: #4F8CF0;
            }
            .pub {
                background: #4F8CF0;
                border: 1px solid #4F8CF0;
                color: #FFFFFF;
            }
        }

    }
</style>
