<template>
  <view class="item" @click="go">
    <block v-if="index">
      <view class="status ing"> 已同意 </view>
    </block>
    <block v-if="item.list_status == 'draft'">
      <view class="status cg"> 待反馈 </view>
    </block>
    <block v-if="item.list_status == 'off_shelf'">
      <view class="status yxj"> 拒绝 </view>
    </block>

    <view class="item-up">
      <view class="title"> 王哈哈 | 女 | 21岁 | 本科 </view>
      <view class="item-up-one">
        <view class="name"> 郑州 | 设计 | 5-7K </view>
      </view>
      <view class="item-up-one">
        <view class="name">
          河南省郑州市金水区博学路街道万正商务大厦8层814
        </view>
      </view>
      <view class="item-up-one">
        <view class="name"> 报名时间：2025.3.16 16:20 </view>
      </view>
    </view>
    <block>
      <u-line color="#E6E6E6" length="100%"></u-line>
      <view class="item-down flexRow">
        <view class="btns">
          <view class="btn zd" @click.stop="apply(item)">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/YqgQKajQ46SxUZkUpVM5pOL8FYIBJ6TFvYv6c8Gy.png"
              mode=""
            ></image>
            审核
          </view>
          <view class="btn agree" @click.stop="look(index)">
            <image
              src="https://api-test.zhaopinbei.com/storage/uploads/images/FWlA6lhuUPTg38U7GCDCNLeyFcAHba59fInOM5WV.png"
              mode=""
            ></image>
            查看简历
          </view>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    index: {
      type: Number,
      default: 0,
    },
  },

  data() {
    return {};
  },
  computed: {
    roleType() {
      console.log("当前用户的身份：", uni.getStorageSync("roleType"));
      return this.$store.state.roleType || uni.getStorageSync("roleType");
    },
  },

  methods: {
    go() {},
    apply(item) {
      this.$emit("apply", item);
    },
    look(item) {
      this.$emit("look", item);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/css/pagesA/components/companyPubJobItem";
.item {
  width: 100%;
  height: 100%;
}
.item-up-one {
  margin: 10rpx 0 !important;
}
.title {
  font-weight: 400 !important;
}
</style>
