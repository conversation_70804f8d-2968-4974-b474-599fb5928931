<template>
    <view class="register-one" :style="{'paddingBottom':step==0?'178rpx':'266rpx'}">
        <u-steps :current="step" dot='true'>
            <u-steps-item title="第一步"></u-steps-item>
            <u-steps-item title="第二步"></u-steps-item>
        </u-steps>
        <view class="inp">

            <view class="inp-item">
                <view class="title">
                    薪资类型
                    <text class="star">*</text>
                </view>
                <view class="in se">
                    <picker @change="changeFinance" :value="salary_type_index" :range="salary_type" range-key="label">
                        <view class="d-picker">
                            {{salary_type[salary_type_index]['label']}}
                        </view>
                    </picker>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>
            <view class="inp-item" v-if="pubJobTwo.salary_type!='discuss'">
                <view class="title">
                    薪资范围
                    <text class="star">*</text>
                </view>
                <view class="in se">
                    <picker @change="changeSalary" :value="salaryIndex" :range="salary_range_search_name"
                            range-key="label">
                        <view class="d-picker">
                            {{salary_range_search_name[salaryIndex]['label']}}
                        </view>
                    </picker>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>
            <view class="inp-item">
                <view class="title">
                    工作经验
                    <text class="star">*</text>
                </view>
                <view class="in se">
                    <picker @change="changeJobExperience" :value="jobExperienceIndex" :range="experience"
                            range-key="label">
                        <view class="d-picker" :style="{color:jobExperienceIndex==0?'#c0c4cc':'#303133'}">
                            {{experience[jobExperienceIndex]['label']}}
                        </view>
                    </picker>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>
            <view class="inp-item">
                <view class="title">
                    工作地址
                    <text class="star">*</text>
                </view>
                <view class="in se">
                    <picker @change="changeAddress" :value="addressIndex" :range="addressList" range-key="map_address">
                        <view class="d-picker">{{addressList[addressIndex]['map_address']}}</view>
                    </picker>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>
            <view class="inp-item">
                <view class="title">
                    面试开始时间
                    <text class="star">*</text>
                </view>
                <view class="in se">
                    <picker mode="date" :value="pubJobTwo.activity_start_at" @change="bindStartDateChange">
                        <view class="d-picker">{{pubJobTwo.activity_start_at?pubJobTwo.activity_start_at:"请选择开始时间"}}
                        </view>
                    </picker>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>
            <view class="inp-item">
                <view class="title">
                    面试结束时间
                    <text class="star">*</text>
                </view>
                <view class="in se">
                    <picker mode="date" :value="pubJobTwo.activity_end_at" @change="bindEndDateChange">
                        <view class="d-picker">{{pubJobTwo.activity_end_at?pubJobTwo.activity_end_at:"请选择开始时间"}}</view>
                    </picker>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>
            <view class="inp-item">
                <view class="title">
                    到岗时间
                    <text class="star">*</text>
                </view>
                <view class="in se">
                    <picker mode="date" :value="pubJobTwo.registration_end_at" @change="bindArriveDateChange">
                        <view class="d-picker">{{pubJobTwo.registration_end_at?pubJobTwo.registration_end_at:"请选择到岗时间"}}
                        </view>
                    </picker>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>
            <view class="inp-item">
                <view class="title">
                    需求人数
                    <text class="star">*</text>
                </view>
                <view class="in">
                    <u--input placeholder="请输入需求人数" placeholderClass="placeholderClass" clearable border="none"
                              v-model="pubJobTwo.surplus_number"></u--input>
                </view>
            </view>
        </view>


        <view class="footer">
            <view class="btns">
                <view class="next prev" @click="prev">
                    上一步
                </view>
                <view class="next sure" @click="sure('draft')">
                    草稿
                </view>
                <view class="next sure" @click="sure('submit')">
                    提交
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import {
        addCompanyJob,
        getCompanyAddressList,
        getJobDetails
    } from "../../config/api.js"

    export default {
        data() {
            return {
                id: '',
                step: 2,
                value: '',
                salaryIndex: 0,
                addressIndex: 0,
                addressList: [],
                sex: ['男', '女'],
                sexIndex: 0,
                salary_type_index: 0,
                jobExperienceIndex: 0,
                pubJobTwo: {
                    salary_type: '',
                    salary_info: {
                        max: '',
                        min: '',
                        month: ''
                    },
                    experience: '',
                    address_id: [],
                    activity_start_at: uni.$u.timeFormat(new Date(), 'yyyy-mmm-dd'),
                    activity_end_at: uni.$u.timeFormat(new Date(), 'yyyy-mmm-dd'),
                    registration_end_at: uni.$u.timeFormat(new Date(), 'yyyy-mmm-dd'),
                    surplus_number: ''
                },
                copy:''
            }
        },
        computed: {
            sysData() {
                return this.$store.state.sysData || uni.getStorageSync('sysData')
            },
            salary_range_search_name() {
                return this.sysData.salary_range_search_name
            },

            salary_type() {
                return this.sysData.salary_type
            },
            experience() {
                return this.sysData.experience
            },
            pubJobOne() {
                return this.$store.state.pubJobOne
            }
        },
        onLoad(options) {
           if(options.id) {
               let id = options.id
               this.id = id;
           }
            this.getCompanyAddressList()

            if (this.id > 0) {
                this.addressIndex = 1
                this.getJobDetails()
            }
            if(options.copy) {
                this.copy = options.copy
            }
        },
        methods: {
            prev() {
                uni.navigateBack()
            },

            async sure(type) {
                if(this.copy) {
                    this.id = ''
                }
                let arr = this.addressList.filter(item => item.id == this.addressList[this.addressIndex]['id'])
                this.pubJobTwo.address_id = [arr[0]['id']]
                this.pubJobTwo.salary_type = this.salary_type[this.salary_type_index]['value']
                this.pubJobTwo.experience = this.experience[this.jobExperienceIndex]['value']

                if (this.pubJobTwo.salary_type == 'range') {
                    let str = this.salary_range_search_name[this.salaryIndex]['value']
                    let arr = str.split('-')
                    this.pubJobTwo.salary_info.max = parseInt(arr[1]) * 1000
                    this.pubJobTwo.salary_info.min = parseInt(arr[0]) * 1000
                    this.pubJobTwo.salary_info.month = 1
                } else {
                    this.pubJobTwo.salary_info.max = ''
                    this.pubJobTwo.salary_info.min = ''
                    this.pubJobTwo.salary_info.month = ''
                }

                let params = {}

                if (this.id) {
                    params = {
                        ...this.pubJobOne,
                        ...this.pubJobTwo,
                        submit_type: type,
                        id: this.id
                    }
                } else {
                    params = {
                        ...this.pubJobOne,
                        ...this.pubJobTwo,
                        submit_type: type,
                    }
                }

                const {
                    status_code,
                    message,
                    data,
                } = await addCompanyJob(params)

                uni.$u.toast(message || '失败');

                if (status_code == 200) {
                    uni.navigateBack({
                        delta: 2
                    });
                    return true;
                }
            },

            changeSalary(e) {
                console.log(e)
                this.salaryIndex = e.detail.value
                let str = this.salary_range_search_name[this.salaryIndex]['value']
                let arr = str.split('-')
                this.pubJobTwo.salary_info.max = parseInt(arr[1]) * 1000
                this.pubJobTwo.salary_info.min = parseInt(arr[0]) * 1000
                this.pubJobTwo.salary_info.month = 1
            },

            changeAddress(e) {
                this.addressIndex = e.detail.value
                this.pubJobTwo.address_id.push(this.addressList[this.addressIndex]['id'])
                // this.pubJobTwo.address_id[0] = this.addressList[this.addressIndex]['id']
            },

            changeFinance(e) {
                this.salary_type_index = e.detail.value
                this.pubJobTwo.salary_type = this.salary_type[this.salary_type_index]['value']
            },

            changeJobExperience(e) {
                this.jobExperienceIndex = e.detail.value
                this.pubJobTwo.experience = this.experience[this.jobExperienceIndex]['value']
            },

            bindStartDateChange(e) {
                this.pubJobTwo.activity_start_at = uni.$u.timeFormat(e.detail.value, 'yyyy-mmm-dd')
            },

            bindEndDateChange(e) {
                this.pubJobTwo.activity_end_at = uni.$u.timeFormat(e.detail.value, 'yyyy-mmm-dd')
            },

            bindArriveDateChange(e) {
                this.pubJobTwo.registration_end_at = uni.$u.timeFormat(e.detail.value, 'yyyy-mmm-dd')

            },

            async getCompanyAddressList() {
                const {
                    status_code,
                    data
                } = await getCompanyAddressList()
                this.addressList = data;
            },

            async getJobDetails() {
                let params = {
                    id: this.id
                }
                const {
                    status_code,
                    data
                } = await getJobDetails(params)
                if (status_code == 200) {
                    this.details = data

                    this.salary_type_index = this.salary_type.findIndex(item => item.value == this.details.salary_type)
                    this.pubJobTwo.salary_type = this.details.salary_type
                    this.salaryIndex = this.salary_range_search_name.findIndex(item => item.label == this.details.salary_info_str)

                    if (this.pubJobTwo.salary_type == 'range') {
                        this.pubJobTwo.salary_info = this.details.salary_info
                    }


                    this.pubJobTwo.experience = this.details.experience
                    this.jobExperienceIndex = this.experience.findIndex(item => item.value == this.details.experience)

                    this.pubJobTwo.address_id.push(this.details.addresses[0]['id'])
                    this.addressIndex = this.addressList.findIndex(item => item.id == this.details.addresses[0]['id'])
                    if(this.addressIndex==-1) {
                        this.addressIndex=0
                    }
                    this.pubJobTwo.activity_start_at = this.details.activity_start_at
                    this.pubJobTwo.activity_end_at = this.details.activity_end_at
                    this.pubJobTwo.registration_end_at = this.details.registration_end_at
                    this.pubJobTwo.surplus_number = this.details.surplus_number

                }
            },
        }
    }
</script>
<style lang="scss">
    @import "../../static/css/pagesA/add/pubJobTwo";
</style>