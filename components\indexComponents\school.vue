<template>
	<view class="container">
		<view class="tabs-container">
			<image class="image"
				src="https://api-test.zhaopinbei.com/storage/uploads/images/mNuy2sF3UdO3uWU8F9SbiJx3UP8KBZ88gBAUhNso.png">
			</image>
			<text>热门职位</text>
		</view>
		<view class="content">
			<view class="item" v-for="v in jobList" :key="v.job.job_id" @click="onDetail(v)">
				<view class="item-start">
					<view class="title-box">
						<view class="title_1">{{v.job.job_info_name}}</view>
						<text class="title_2">{{v.salary.salary_min}} - {{v.salary.salary_max}}</text>
					</view>
					<view class="type-text">{{v.job.work_type_name}}</view>
					<view class="time">{{v.job.job_introduction}}</view>
				</view>
				<u-line></u-line>
				<view class="item-end">
					<image class="avater" :src="v.company.company_logo.thumb_url"></image>
					<text class="name">{{v.company.company_name}}</text>
					<text class="address">{{v.area.area_type_name}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getJobPublishList
	} from '../../config';

	export default {
		data() {
			return {
				params: {
					limit: 20,
					page: 1,
					work_type_id: '1',
					job_type: '2'
				},
				jobList: [],
				isLoading: false,
				more: true
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetJobPublishList();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetJobPublishList();
		},
		methods: {
			onDetail(v) {
			    uni.$u.route({
			        url: `/pagesA/details/memberJobDetails`,
					params: {
						id: v.job.job_id,
					}
			    })
			},
			onScrollGetList() {
				if (!this.more) return;
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetJobPublishList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getJobPublishList(params);
				if (res.status_code !== '200') return;
				this.jobList = [...this.jobList, ...res.data.jobs_list?.data];
				this.isLoading = false;
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.tabs-container {
			padding-inline: 32rpx;
			color: #333333;
			background-color: #f5f5f7;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			gap: 16rpx;

			.image {
				width: 32rpx;
				height: 32rpx;
			}
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding-inline: 32rpx;

			.item {
				background-color: #ffffff;
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-end {
					display: flex;
					align-items: center;
					gap: 28rpx;

					.avater {
						width: 60rpx;
						height: 60rpx;
					}

					.name {
						color: #333333;
						font-size: 24rpx;
					}

					.address {
						margin-inline-start: auto;
						color: #999999;
						font-size: 24rpx;
					}
				}

				.item-start {
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.time {
						color: #999999;
						font-size: 24rpx;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
					}

					.type-text {
						color: #4f8cf0;
						font-size: 28rpx;
					}

					.title-box {
						display: flex;
						align-items: center;
						justify-content: space-between;
						gap: 24rpx;

						.title_1 {
							flex: 1;
							color: #333333;
							font-size: 32rpx;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.title_2 {
							color: #f98a14;
							font-size: 36rpx;
						}
					}
				}
			}
		}
	}
</style>