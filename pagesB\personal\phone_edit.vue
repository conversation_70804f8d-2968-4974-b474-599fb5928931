<template>
	<!-- 修改手机号 -->
	<view class="container">
		<view class="prompt-container">
			<image class="image" src="/static/new/cross.png"></image>
			<text class="text">每30天修改1次，您还可以修改1次</text>
		</view>

		<view class="phone-container">
			<view class="title">修改手机号</view>
			<view class="text">修改手机号码后，可以使用新手机号登录招聘呗，聊天时“交换电话”功能的手机号会一同修改当前手机号：192*****30</view>
			<u-form>
				<u-form-item borderBottom>
					<u-input border="none" placeholder="请输入新手机号"></u-input>
				</u-form-item>
				<u-gap height="16" bgColor="#FFFFFF"></u-gap>
				<u-form-item borderBottom>
					<template #right>
						<text class="code-text">发送验证码</text>
					</template>
					<u-input border="none" placeholder="请输入验证码"></u-input>
				</u-form-item>
			</u-form>
			<view class="desc">
				<text>长时间收不到验证码，可尝试</text>
				<text class="text">语音接听验证码</text>
			</view>

			<view class="btn">保存</view>
		</view>

		<u-toast ref="toast"></u-toast>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	},
};
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #ffffff;

	.prompt-container {
		display: flex;
		align-items: center;
		background-color: #ffe9e1;
		gap: 24rpx;
		padding-block: 20rpx;
		padding-inline: 32rpx;

		.image {
			width: 24rpx;
			height: 24rpx;
		}

		.text {
			color: #f77c53;
			font-size: 24rpx;
		}
	}

	.phone-container {
		padding-inline: 32rpx;
		padding-block: 48rpx;
		display: flex;
		flex-direction: column;
		gap: 38rpx;

		.title {
			color: #333333;
			font-size: 32rpx;
		}

		.text {
			color: #666666;
			font-size: 24rpx;
		}

		.code-text {
			color: #4f8cf0;
			font-size: 28rpx;
		}

		.desc {
			display: flex;
			align-items: center;
			color: #666666;
			font-size: 24rpx;

			.text {
				color: #4f8cf0;
			}
		}

		.btn {
			height: 74rpx;
			background-color: #4f8cf0;
			color: #ffffff;
			font-size: 24rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 8rpx;
		}
	}
}
</style>
