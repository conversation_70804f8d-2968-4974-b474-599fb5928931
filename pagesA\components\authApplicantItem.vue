<template>
    <view class="item" >
        <view class="item-up">
            <image :src=" item.member_info.image.thumbnail_path_url?item.member_info.image.thumbnail_path_url:'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png'"
                   mode=""></image>
            <view class="info" @click.stop="showInfo">
                <view class="user">
                    <view class="userInfo">
                        <view class="name">
                            {{ item.member_certification.name?item.member_certification.name: item.member_info.nick_name}}
                        </view>
                        <view :class="['cert',item.member.certification_status==1?'ing':'un']">
                            {{item.member.certification_status_name}}
                        </view>
                    </view>
                </view>
                <view class="tags">
                    <view class="tag">
                        {{item.member_info.sex_str}}
                    </view>
                    <view class="tag">
                        {{item.member_info.age}}岁
                    </view>

                    <view class="tag">
                        {{ item.member_info.education_type_name }}
                    </view>

                </view>
            </view>
        </view>


        <view class="item-down">
            <view class="addr" @click.stop>
                信息是否公开：
                <u-switch v-model="value" size="18" @change="change"></u-switch>
            </view>
            <!-- <view class="btn" @click.stop="draftResume">
                修改记录
            </view> -->
			<view class="btn" @click.stop="go">
			    查看简历
			</view>
            <view class="btn" @click.stop="selectComp">
                推送简历
            </view>
        </view>


    </view>
</template>

<script>
    import {resumeOpen} from "../../config/headhunterList_api.js"

    export default {
        name: "authApplicantItem",
        props: {
            item: {
                type: Object,
                default: () => {
                }
            }
        },
        data() {
            return {
                value: false
            };
        },
        mounted() {
            this.value = this.item.member.open_status == 1 ? true : false
        },
        methods: {
            async change() {
                let open_status = 1
                if (this.value) {
                    open_status = 1
                } else {
                    open_status = 2
                }
                let params = {
                    member_id: this.item.member.id,
                    open_status: open_status
                }

                await resumeOpen(params)
            },
            selectComp() {
                this.$emit('selectComp', {
                    item: this.item,
                    show: true
                })
            },
            draftResume() {
                uni.navigateTo({
                    url: "/pagesA/headhunterList/headhunterDraftResume?member_id=" + this.item.member_id
                })
            },
            go() {
                uni.navigateTo({
                    url: "/pagesA/details/authApplicantDetails?id=" + this.item.id,
                })
            },
			showInfo() {
				uni.navigateTo({
					url: "/pagesA/details/qlmDetails?id=" + this.item.member_info.id
				})
			}
        }
    }
</script>

<style lang="scss" src="../../static/css/pagesA/components/authApplicantItem.scss"></style>
