<template>
	<view class="container">
		<view class="tabs-container">
			<image class="image"
				src="https://api-test.zhaopinbei.com/storage/uploads/images/mNuy2sF3UdO3uWU8F9SbiJx3UP8KBZ88gBAUhNso.png">
			</image>
			<text>热门企业</text>
		</view>
		<view class="content">
			<view class="item" v-for="v in companyList" :key="v.id" @click="onDetail(v.id)">
				<image class="image" :src="v.company_info.logo.path_url" mode=""></image>
				<view class="item-detail">
					<text class="company">{{v.name}}</text>
					<text class="info">{{v.company_info.financing_type_name}}｜{{v.company_info.size_type_name}}</text>
					<view class="tags" v-if="v.tags.length">
						<view class="tag" v-for="k in v.tags" :key="k.id">{{k.title}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCompanyList
	} from '@/config';
	export default {
		data() {
			return {
				companyList: [],
				params: {
					limit: 20,
					page: 1
				},
				isLoading: false,
				more: true
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetCompanyList();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetCompanyList();
		},
		methods: {
			onDetail(id) {
				uni.$u.route({
					url: '/pagesA/details/companyDetail',
					params: {id},
				});
			},
			onScrollGetList() {
				if (!this.more) return;
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetCompanyList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getCompanyList(params);
				if (res.status_code !== '200') return;
				this.companyList = [...this.companyList, ...res.data.data];
				this.more = res.data.more;
				this.isLoading = false;
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.tabs-container {
			padding-inline: 32rpx;
			color: #333333;
			background-color: #f5f5f7;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			gap: 16rpx;

			.image {
				width: 32rpx;
				height: 32rpx;
			}
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding-inline: 32rpx;

			.item {
				display: flex;
				align-items: center;
				gap: 24rpx;
				padding: 32rpx;
				background-color: #FFFFFF;
				border-radius: 24rpx;
				overflow: hidden;

				.image {
					width: 144rpx;
					height: 144rpx;
					object-fit: cover;
					flex-shrink: 0;
				}

				.item-detail {
					display: flex;
					flex-direction: column;
					gap: 8rpx;
					min-width: 0;

					.company {
						color: #333333;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						font-size: 28rpx;
					}

					.info {
						color: #999999;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						font-size: 24rpx;
					}

					.tags {
						flex: 1;
						display: flex;
						align-items: center;
						gap: 16rpx;
						overflow-x: auto;
						white-space: nowrap;

						.tag {
							flex-shrink: 0;
							padding: 12rpx;
							border-radius: 8rpx;
							color: #666666;
							font-size: 22rpx;
							background-color: #F6F6F6;
						}
					}
				}
			}
		}
	}
</style>