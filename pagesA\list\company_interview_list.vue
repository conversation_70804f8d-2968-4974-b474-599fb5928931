<template>
	<view class="container">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="tabs">
					<u-tabs lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
						transform: 'scale(1.05)'
					}" :inactiveStyle="{
						color: '#999999',
						transform: 'scale(1)'
					}" :list="tabs" @click="changeTab"></u-tabs>
				</view>
				<view class="search-wrap">
					<u-search placeholder="请输入职位名称" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
				</view>
			</view>
		</u-sticky>

		<view class="list">
			<company-inter-view-item v-for="(item,index) in list" :key="index" :item="item" @open="openDetails"
				@approve="openApprove"></company-inter-view-item>
		</view>

		<u-popup :show="show" round="10" mode="center" @close="close" @open="open">
			<view>
				<view class="wrap">
					<view class="title">
						面试详情
					</view>
					<view class="items">
						<view class="item">
							<text class="lab">面试人：</text>
							<text class="name">{{currentItem.contact_name}}</text>
						</view>
						<view class="item">
							<text class="lab">报名渠道：</text>
							<text class="name">活动报名</text>
						</view>
						<view class="item">
							<text class="lab">联系电话：</text>
							<text class="name">{{currentItem.contact_cellphone}}</text>
						</view>
						<view class="item">
							<text class="lab">面试时间：</text>
							<text class="name">{{currentItem.interviewed_at}}</text>
						</view>
						<view class="item">
							<text class="lab">面试地址：</text>
							<text class="name"
							@click="openNavigation(currentItem.addresses[0])"
							>{{currentItem.addresses[0]['map_address']}}</text>
						</view>
						<view class="item">
							<text class="lab">面试备注：</text>
							<text class="name">{{currentItem.remark}}</text>
						</view>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import {
		getCompanyInterviewList,
		interviewApprove,
		getCompanyInterviewDetails
	} from "../../config/api.js"
	import {
		interviewResult
	} from '../../config/company_api.js'
	import CompanyInterViewItem from "../components/companyInterViewItem.vue"

	export default {
		components: {
			CompanyInterViewItem,
		},
		data() {
			return {
				page: 1,
				limit: 10,
				currentItem: {},
				model_type: "",
				show: false,
				start_date: '',
				end_date: '',
				tabIndex: 0,
				tabs: [{
					name: '全部',
				}, {
					name: '自主报名',
				}, {
					name: '活动报名'
				}, {
					name: '就业管家推荐'
				}],
				list: [],
				statusIndex: 0,
				statusList: [{
						value: 0,
						name: '面试状态'
					},
					{
						value: 1,
						name: '通过'
					},
					{
						value: 2,
						name: '备选'
					},
					{
						value: 3,
						name: '淘汰'
					}
				],
				options: ['通过', '备选', '淘汰']
			}
		},
		onLoad() {
			this.list = [];
			this.page = 1;
			this.getCompanyInterviewList()
		},
		methods: {
			open() {
				this.show = true
			},
			close() {
				this.show = false
			},

			changeStatus(e) {
				this.statusIndex = e.detail.value
			},

			changeTab(op) {
				this.tabIndex = op.index;

				this.list = [];
				this.page = 1;

				switch (this.tabIndex) {
					case 2:
						this.model_type = "job_active";
						break;
					default:
						this.model_type = "";
						break;
				}

				this.getCompanyInterviewList()
			},

			openDetails(item) {
				this.currentItem = item;
				this.show = true
			},

			// 审核
			openApprove(op) {
				const {
					item,
					status,
					examine_data
				} = op;
				// console.log('审核项:', item);
				// console.log('审核状态:', status);
				// console.log('审核理由:', examine_data);

				uni.showActionSheet({
					itemList: this.options, // 显示的选项
					success: async (res) => {
						let resultStatus = res.tapIndex == 0 ? 'pass' : res.tapIndex == 1 ? 'alternate' :
							'eliminate';
						let params = {
							interview_id: op.item.id,
							result_status: resultStatus,
							examine_data: examine_data // 将审核理由传递给接口
						};
						const data = await interviewResult(params);
						this.page = 1;
						this.list = [];
						this.getCompanyInterviewList();
					},
					fail: (err) => {
						console.error(err); // 处理错误
					},
				});
			},

			// 面试列表
			async getCompanyInterviewList() {
				let params = {
					page: this.page,
					limit: this.limit,
					model_type: this.model_type,
				};

				const {
					status_code,
					data
				} = await getCompanyInterviewList(params);
				if (status_code == 200) {
					this.list = data.data;
				}
			},
			
			// 导航地址
			openNavigation(address) {
				wx.openLocation({
					latitude: parseFloat(address.lat), // 纬度，浮点数，范围为-90~90
					longitude: parseFloat(address.lng), // 经度，浮点数，范围为-180~180
					name: address.map_address || '面试地点', // 位置名
					scale: 18 // 地图缩放级别
				})
			}
		}
	}
</script>

<style>
	page {
		background-color: #f5f5f5;
	}
</style>

<style lang="less" scoped>
	.list {
		padding: 0 32rpx;
	}

	.header {
		padding: 0 32rpx 32rpx 32rpx;

		.search-wrap {
			margin-top: 32rpx;
		}

		.filters {
			display: flex;
			margin-top: 32rpx;

			.filter {
				display: flex;
				align-items: center;
				height: 48rpx;
				background-color: #FFFFFF;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				padding: 0 12rpx;
				margin-right: 12rpx;
				border-radius: 8rpx;

				image {
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
	}

	.wrap {
		display: flex;
		flex-direction: column;
		width: 600rpx;

		.title {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 92rpx;
			background: rgba(79, 140, 240, 0.1)
		}

		.items {
			display: flex;
			flex-direction: column;

			.item {
				display: flex;
				justify-content: space-between;
				padding: 24rpx;

				.lab {
					display: flex;
					font-weight: 400;
					font-size: 28rpx;
					color: #666666;
				}

				.name {
					flex: 1;
					flex-wrap: wrap;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
				}
			}
		}
	}
</style>