<template>
	<!-- 岗位详情 -->
	<view class="container">
		<view class="context">
			<view class="box">
				<view class="title">期望岗位详情</view>
				<view class="text-box">
					<text :class="checkednmuber == 0 ? 'activetitle':'count'">
						{{checkednmuber}}
					</text>
					<text class="count-bottom">
						/31</text>
				</view>
			</view>
			<view class="category-text">设计现有31个类别</view>
			<view class="category-list">
				<view v-for="(category, index) in categories" :key="index" :class="{'active': category.check}"
					@click="toggleSelection(category)">
					{{category.name}}
				</view>
			</view>
		</view>
		<view class="footer">
			<button class="confirm-btn" @click="save">保存</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				categories: [{
						name: '技术美术',
						check: false
					},
					{
						name: '系统策划',
						check: false
					},
					{
						name: '游戏主美术',
						check: false
					},
					{
						name: '视觉设计师',
						check: false
					},
					{
						name: 'UI设计师',
						check: false
					},
					{
						name: '平面设计',
						check: false
					},
					{
						name: '3D设计',
						check: false
					},
					{
						name: '原画师',
						check: false
					},
					{
						name: '游戏特效',
						check: false
					},
					{
						name: '游戏UI设计',
						check: false
					},
					{
						name: '游戏场景',
						check: false
					},
					{
						name: '游戏角色',
						check: false
					},
					{
						name: '游戏动作',
						check: false
					},
					{
						name: 'CAD绘图员',
						check: false
					},
					{
						name: '美工',
						check: false
					},
					{
						name: '包装设计',
						check: false
					},
					{
						name: '设计师助理',
						check: false
					},
					{
						name: '动画设计',
						check: false
					},
					{
						name: '插画师',
						check: false
					},
					{
						name: '漫画师',
						check: false
					},
					{
						name: '修图师',
						check: false
					},
					{
						name: 'UX/交互设计师',
						check: false
					},
					{
						name: '游戏数值策划',
						check: false
					},
					{
						name: '展览/展示设计',
						check: false
					},
					{
						name: '照明设计',
						check: false
					},
					{
						name: '面料辅助开发',
						check: false
					},
					{
						name: '打样/制版',
						check: false
					},
					{
						name: '工业设计',
						check: false
					},
					{
						name: '家具设计',
						check: false
					},
					{
						name: '珠宝设计',
						check: false
					},
					{
						name: '陈列设计',
						check: false
					}
				],
				checkednmuber: 0,
			};
		},
		methods: {
			toggleSelection(category) {
				category.check = !category.check;
				this.checkednmuber = this.categories.filter(item => item.check).length;
			},
			save() {
				const selectedCategories = this.categories.filter(category => category.check).map(category => category
					.name);
				console.log('保存的数据：', selectedCategories);
			}
		}
	};
</script>

<style>
	.container {
		padding: 20rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
	}

	.context {
		flex: 1;
	}

	.box {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.count {
		color: rgba(79, 140, 240, 1);
		font-size: 28rpx;
		float: right;
		/* margin-top: 24rpx; */
	}

	.count-bottom {
		color: rgba(153, 153, 153, 1);
		font-size: 28rpx;
		/* margin-top: 24rpx; */
	}

	.text-box {
		display: flex;
		justify-content: flex-end;
	}

	.category-text {
		margin-top: 20rpx;
		color: #999;
	}

	.category-list {
		display: flex;
		flex-wrap: wrap;
		margin-top: 20rpx;
	}

	.category-list view {
		background-color: #f4f4f4;
		border-radius: 8rpx;
		padding: 10rpx 20rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		cursor: pointer;
	}

	.active {
		background-color: rgba(238, 244, 255, 1);
		color: rgba(79, 140, 240, 1);
		border: 1px solid rgba(79, 140, 240, 1);
	}

	.activetitle {
		color: red;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-image: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}
</style>