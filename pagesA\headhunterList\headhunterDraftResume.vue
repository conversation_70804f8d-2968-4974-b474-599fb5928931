<template>
    <view class="home-index draft-resume">
        <block v-if="page.data.length>0">
            <view class="list">
                <view class="item" v-for="(item,index) in page.data" :key="index" :item="item">
                    <view :class="['status', statusClass[item.member_resume.status]]">
                        {{ item.member_resume.status_name }}
                    </view>
                    <view class="up" @click="goDetail(item)">
                        <image src="https://api-test.zhaopinbei.com/storage/uploads/images/5Vb9rePKPy5xj8diaa2Ko4VLl6oJSU33d8qVJ8dx.png" mode=""></image>
                        <view class="info">
                            <view class="name">
                                {{item.member_resume.resume_name}}
                            </view>
                            <view class="time">
                                修改时间：{{item.member_resume.updated_at}}
                            </view>
                        </view>
                    </view>

                    <view class="bulerBoxBig">
                        <view class="butlerBox">
                            <image :src="item.member_info.image.path_url" mode="" class="butlerHeadImg"></image>
                            <view class="butlerText">
                                <view class="butlerNameCla">{{item.member_certification.name}}</view>
                            </view>
                        </view>
                    </view>

                    <view :class="['down flexEnd']">
                        <view class="btn edit-but" v-if="item.member_resume.status == 2" @click="draftEdit(index,item)">
                            编辑
                        </view>
                        <view class="btn del-but" v-if="item.member_resume.status == 2" @click="draftDel(index,item)">
                            删除
                        </view>
                    </view>

                </view>
            </view>

        </block>

        <block v-else>
            <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png"></u-empty>
        </block>
        <Pages v-if="page.data.length > 0" :status="page.status"></Pages>
    </view>
</template>

<script>
    import Pages from "../../components/pages.vue";

    import {
        delResume,
    } from "../../config/api";

    import {
        resumeList
    } from "../../config/headhunterList_api";

    export default {
        components: {
            Pages,
        },
        watch: {
            list: {
                handler(newValue, oldValue) {
                },
                deep: true,
                immediate: true
            }
        },
        data() {
            return {
                statusClass: {
                    1: 'agree',
                    2: 'pending',
                    3: 'reject',
                },
                audit: {
                    form: {
                        status: 1,
                        remark: "",
                        id: 0,
                    },
                },
                page: {
                    form: {
                        member_id: 0,
                        page: 1,
                        limit: 10,
                        type: 'draft',
                    },
                    data: [],
                    status: 'nomore',
                },

            }
        },
        computed: {
            userInfo() {
                return this.$store.state.userInfo || uni.getStorageSync('userInfo')
            },
        },
        onShow() {
            var _newData = uni.getStorageSync('editDraftResume');
            var _newIndex = uni.getStorageSync('editDraftResumeIndex');

            if (_newData && _newIndex >= 0) {
                this.page.data.splice(_newIndex, 1, _newData);
                uni.removeStorageSync('editDraftResume');
                uni.removeStorageSync('editDraftResumeIndex');
            }
        },
        onLoad(opts) {
            this.page.form.member_id = opts.member_id;
            this.open_status = this.userInfo.member.open_status
            this.getResumeList()
        },
        //触底加载更多
        onReachBottom() {
            if (this.page.more) {
                this.page.form.page++
                this.getResumeList();
            }
        },
        methods: {
            goDetail(item) {
                uni.navigateTo({
                    url: "/pagesA/details/resumeDetails?id=" + item.member_resume.id
                })
            },
            draftDel(_index, _item) {
                var _this = this;
                var _id = _item.member_resume.id;

                // 弹窗输入
                uni.showModal({
                    title: '确定删除该记录吗？', // 标题
                    confirmText: "是",
                    cancelText: "否",

                    success: (res) => {
                        if (res.confirm) {
                            delResume({
                                id: _id,
                            }).then(response => {
                                uni.$u.toast(response.message);
                                if (response.status_code == '200') {
                                    _this.page.data.splice(_index, 1);
                                }

                            });
                        } else if (res.cancel) {

                        }
                    }
                })

            },
            draftEdit(_index, _item) {
                uni.setStorageSync('editDraftResumeIndex', _index);
                var _id = _item.member_resume.id;

                uni.navigateTo({
                    url: "/pagesA/add/HeadhunterAddResume?resume_id=" + _id,
                })
            },
            auditHandel() {
                var _this = this;
                memberDraftAudit(_this.audit.form).then(response => {
                    uni.$u.toast(response.message);
                    if (response.status_code == '200') {
                        _this.initPage();
                        _this.getResumeList();
                    }
                });
            },
            initPage() {
                this.page.page = 1;
                this.page.data = [];
                this.page.status = 'loadmore';
            },
            //简历列表
            async getResumeList() {
                var _this = this;

                resumeList(_this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.more ? 'loading' : 'nomore';
                    }
                });

            },

        }
    }
</script>


<style lang="scss">
    @import "../../static/css/pagesA/personal/myResume";
    @import "../../static/css/pagesA/components/onlineResumeItem";
</style>
