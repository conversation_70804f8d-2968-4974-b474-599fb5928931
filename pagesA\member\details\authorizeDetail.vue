<template>
	<!--    <view id="app">

        <view class="changeTitle">
            授权的公司
        </view>
        <company-tag :item="companyInfo"></company-tag>

        <view class="changeTitle">
            现任负责人
        </view>

        <view class="bulerBoxBig" @click="goDetails()">
            <view class="butlerBox">
                <image :src="companyInfo.authorized_member_info.image.path_url" mode="" class="butlerHeadImg"></image>
                <view class="butlerText">
                    <view class="butlerNameCla">{{butlerName}}</view>
                    <view class="butlerCompanyCla">{{companyName}}</view>
                </view>
            </view>
            <view class="labelBox" v-if="companyInfo.authorized_user_headhunter">

                <view v-for="(item,index) in companyInfo.authorized_user_headhunter.label" :key="index" :item="item"
                    class="labelBox_child">
                    {{ item }}
                </view>
            </view>
        </view>

        <view class="changeTitle">
            申请变更
        </view>


        <view class="butlerBox" @click="selectButler" style="padding: 32rpx;">
            <view class="selectPeoCla" v-if="!butlerItem">
                <view>选择人员</view>
                <image src="/static/images/project/rightIcon.png" mode="" class="rightIcon"></image>
            </view>
            <image :src="butlerItem.head" mode="" class="butlerHeadImg" v-if="butlerItem"></image>
            <view class="butlerText" v-if="butlerItem">
                <view class="butlerNameCla">{{butlerItem.name}}</view>
                <view class="butlerCompanyCla">{{butlerItem.phone}}</view>
            </view>
        </view>

        <view class="btnBox">
            <view class="btnOne">
                <view :class="!butlerItem?'btnCla btnGrey': 'btnCla btnBlue'" @click="setAuthEnterpriseStaff">申请变更</view>
                <view v-if="companyInfo.but.send_audit_status == 1" class="btnCla btnBlue" @click="relieve">
                    解除授权申请
                </view>
                <view v-if="companyInfo.but.audit_status == 1" class="btnCla btnBlue" @click="cancelSubmit">
                    解除授权审核
                </view>
            </view>
            <view class="moreText" @click="goObtain">
                更多就业管家...
            </view>
        </view>

    </view> -->
	<view style='margin:20rpx 32rpx 0 32rpx;padding-bottom: 40rpx;'>
		<view class="label">
			<!--<view :class="['label1', 'labels',state=='1'?'color1':'color2']" @click="handoff('1')">当前委托</view>
			<view :class="['label2', 'labels',state=='2'?'color1':'color2']" @click="handoff('2')">委托记录</view>-->
			<view :class="['labels pg_1', state == 1 ? 'active' : '']"@click="handoff('1')">
				当前委托
			</view>
			<view :class="['labels pg_2', state == 2 ? 'active' : '']" @click="handoff('2')">
				委托记录
			</view>
		</view>
		<view class="list" v-for="item in 3" :key="item" @click="card(item)">
			<view class="list_1">
				<u-avatar :src="src" size='50'></u-avatar>
				<view>
					<view class="list_1_name">
						就业管家姓名
						<u-tag borderColor="#ECF5FFFF" bgColor="#ECF5FFFF" color="#4F8CF0FF" text="已认证" size="mini">
						</u-tag>
					</view>
					<view class="list_1_firm">学创联盟（北京）网络科技有限公司
						<u-icon name="arrow-right" style="margin-right: auto;"></u-icon>
					</view>
				</view>
			</view>
			<view class="list_2">
				<u-tag borderColor='#fff' bgColor="#ECF5FFFF" color="#7286A6FF" text="行业大牛" size="mini"> </u-tag>
				<u-tag borderColor='#fff' bgColor="#ECF5FFFF" color="#7286A6FF" text="目标管理" size="mini"> </u-tag>
				<u-tag borderColor='#fff' bgColor="#ECF5FFFF" color="#7286A6FF" text="完美主页" size="mini"> </u-tag>
			</view>
			<view class="Recording" v-show="state=='2'">
				变更记录：王哈哈（公司名称公司名称）
			</view>
			<view class="button">
				<view class="btn talk" @click.stop="">
					聊聊呗
				</view>
			</view>

			<!-- 委托卡片 -->
			<view class="entrust" v-show="index===item&&judgment&&state==='1'">
				<view class="entrust1 color1" @click="entrust('1')">
					<u--image :showLoading="true" src="/static/images/icon/委托.png" width="40rpx" height="40rpx"
						@click="click"></u--image>
					<view>
						委托
					</view>
				</view>
				<view class="entrust2 color2" @click="entrust('2')">
					<u--image :showLoading="true" src="/static/images/icon/解除.png" width="40rpx" height="40rpx"
						@click="click"></u--image>
					<view>
						解除
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		authorizeIndex,
		memberAuthorizeUserAudit,
		memberAuthorizeAuditHandle,
	} from "../../../config/member_api";

	import {
		memberAuthorizeRelieve
	} from "../../../config/api";
	import CompanyTag from "../../../public_label/companyTag.vue";

	export default {
		components: {
			CompanyTag,
		},
		data() {
			return {
				head: '',
				butlerName: '',
				companyName: '',
				reasonContent: '',
				butlerItem: {},
				companyInfo: {}, // 公司信息
				companyLabel: '', // 公司标签
				state: '1', //切换内容
				index: '', //卡片开关
				judgment: false //卡片开关

			}
		},
		onLoad(option) {
			this.getAuthorize()
		},
		onShow() {
			this.butlerItem = uni.getStorageSync('selectedInd');
		},
		methods: {

			goDetails() {
				uni.navigateTo({
					url: '/pagesA/details/obtainItemDetails?id=' + this.butlerId
				})
			},
			goBack() {
				uni.navigateBack()
			},
			draftResume() {
				uni.navigateTo({
					url: '/pagesA/personal/draftResume',
				})
			},
			selectButler() {
				var _url = '/pagesA/list/common_headhunter_list?company_id=' + this.companyInfo.company.id;
				uni.navigateTo({
					url: _url,
				})
			},
			async getAuthorize() {
				authorizeIndex().then(response => {
					if (response.status_code != '200') {
						uni.$u.toast(response.message);
						setTimeout(() => {
							uni.navigateBack();
						}, 3000);
					}
					console.log(response.data);
					this.companyInfo = response.data;
					this.companyName = response.data.company.name
					this.butlerName = response.data.authorized_member_certification.name
					this.butlerId = response.data.authorized_user.id
				});

			},

			goObtain() {
				uni.navigateTo({
					url: "/pagesA/list/obtain_management_list"
				})
			},

			cancelSubmit() {
				// 弹窗输入
				var _this = this;
				uni.showModal({
					title: '解除授权审核', // 标题
					editable: true, // 开启输入框
					placeholderText: '请输入审核意见', // 输入框提示语
					confirmText: "同意",
					cancelText: "驳回",
					success: (res) => {
						if (res.confirm) {
							// 如果没有输入内容
							if (!res.content) {
								return uni.$u.toast('请输入原因')
							}
							// 后续处理
							memberAuthorizeAuditHandle({
								remark: res.content,
								authorize_audit_status: 1,
							}).then(response => {
								uni.$u.toast(response.message);
								uni.navigateBack();
							});
						} else if (res.cancel) {
							// 后续处理
							memberAuthorizeAuditHandle({
								remark: res.content,
								authorize_audit_status: 3,
							}).then(response => {
								uni.$u.toast(response.message);
								uni.navigateBack();
							});
						}
					}
				})

			},
			relieve() {
				var _this = this;

				if (_this.companyInfo.but.send_audit_status != 1) {
					return;
				}

				uni.showModal({
					title: '解除授权申请', // 标题
					editable: true, // 开启输入框
					placeholderText: '请输入解除原因', // 输入框提示语
					confirmText: "确定",
					cancelText: "取消",
					success: (res) => {
						if (res.confirm) {
							// 如果没有输入内容
							if (!res.content) {
								return uni.$u.toast('请输入原因')
							}
							memberAuthorizeRelieve().then(response => {
								uni.$u.toast(response.message);
								if (response.status == 200) {
									this.getAuthorize();
								}
							});

						} else if (res.cancel) {

						}
					}
				})
			},
			async setAuthEnterpriseStaff() {
				var _this = this;
				if (!_this.butlerItem) {
					_this.selectButler()
					return
				}

				uni.showModal({
					title: '变更原因',
					content: '',
					cancelText: '取消',
					confirmText: '确定',
					editable: true,
					success: (res) => {
						if (res.confirm) {
							// 用户点击了同意
							memberAuthorizeUserAudit({
								remark: res.content,
								user_id: _this.butlerItem.id
							}).then(response => {
								if (response.status_code == '200') {
									uni.removeStorageSync('selectedInd');
									// uni.navigateBack();
								}
							});


						} else if (res.cancel) {
							// 用户点击了拒绝
							console.log('用户取消');
						}
					},
				});
			},
			//切换内容
			handoff(item) {
				this.state = item
				if (this.state !== '1') this.judgment = false
			},
			//显示卡片
			card(item) {
				if (this.state == '1') {
					if (item === this.index) {
						this.judgment = !this.judgment
					} else {
						this.index = item
						this.judgment = true
					}
				}
			},
			//委托/解除
			entrust(item) {
				if (item === '1') {
					uni.showModal({
						title: '是否申请委托', // 标题
						confirmText: "是",
						cancelText: "否",
						success() {
							console.log('申请委托');
						}
					})
				} else {
					uni.showModal({
						title: '是否申请解除委托', // 标题
						confirmText: "是",
						cancelText: "否",
						success() {
							uni.showModal({
								title: '已申请解除委托', // 标题
								confirmText: "我知道了",
								showCancel: false
							})
						}
					})
				}
			}
		}
	}
</script>
<style lang="scss">
	@import "../../../static/css/pagesA/member/details/authorizeDetail";
</style>
