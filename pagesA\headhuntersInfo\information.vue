<template>
  <view class="warp">
    <view class="inner">
      <view class="box">
        <view class="title"> 管家头像 </view>
        <u-avatar size="96rpx" :src="src"></u-avatar>
        <view class="tips1">
          <img src="/static/images/login/tips.png" alt="" />
          <text>若不想使用当前头像，可以恢复默认</text>
        </view>
        <u-line></u-line>
        <view class="line"> 管家身份 </view>
        <view class="" @click="show = true">
          <u--input
            v-model="ident"
            disabled
            border="bottom"
            disabledColor="#fff"
            suffixIcon="arrow-down"
          ></u--input>
        </view>
        <template v-if="ident == '学生'">
          <view class="line"> 学校名称 </view>
          <u--input
            placeholder="请输入内容"
            border="bottom"
            clearable
          ></u--input>
          <view class="line"> 专业 </view>
          <u--input
            placeholder="请输入内容"
            border="bottom"
            clearable
          ></u--input>
        </template>
        <template v-if="ident == '职场人'">
          <view class="line"> 管家姓名 </view>
          <u--input
            placeholder="请输入内容"
            border="bottom"
            clearable
          ></u--input>
        </template>
        <view class="line"> 所属公司 </view>
        <u--input placeholder="请输入内容" border="bottom" clearable></u--input>
        <view class="line"> 手机号 </view>
        <u--input placeholder="请输入内容" border="bottom" clearable></u--input>
        <view class="line"> 微信号 </view>
        <u--input placeholder="请输入内容" border="bottom" clearable></u--input>
        <view class="line"> 邮箱 </view>
        <u--input placeholder="请输入内容" border="bottom" clearable></u--input>
      </view>
      <view class="tips2" @click="showPop = true">
        <text>上传注意事项</text>
        <u-icon name="question-circle-fill" color="#999999" size="20"></u-icon>
      </view>
    </view>
    <view class="btn-warp">
      <view class="btn"> 保存 </view>
    </view>
    <u-action-sheet
      :actions="list"
      :title="title"
      :show="show"
      cancelText="取消"
      :closeOnClickOverlay="true"
      :closeOnClickAction="true"
      @close="show = false"
      @select="selectClick"
    ></u-action-sheet>
    <u-popup :show="showPop" mode="center" @close="showPop = false">
      <view class="tip-box">
        <view class="tip-tit">
          <view>
            <text>上传注意事项</text>
          </view>
          <img
            @click="showPop = false"
            src="https://api-test.zhaopinbei.com/storage/uploads/images/Sl4y4bsafDNMgfpp372fiFnhD0jxCXPYoHzhs5aq.png"
            alt=""
          />
        </view>
        <view class="tip-txt">
          <view>1、请上传清晰且完整的图片；</view>
          <view
            >2、请上传与品牌相关的图片，含有其他无关内容将无法通过审核（包括但不限于含无关水印、招聘信息、联系方式、二维码等）；</view
          >
          <view>
            3、上传图片须符合中国相关法律法规，不得含有违法内容或不良信息。</view
          >
        </view>
        <view class="tip-btm">
          <view class="tip-btn" @click="showPop = false">我知道了</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      ident: "职场人",
      show: false,
      showPop: false,
      list: [
        {
          name: "学生",
        },
        {
          name: "职场人",
        },
      ],
    };
  },
  methods: {
    selectClick(index) {
      this.ident = index.name;
    },
  },
};
</script>
<style>
page {
  background: #f5f5f7;
}
</style>
<style lang="less" scoped>
.inner {
  padding: 32rpx;

  .box {
    padding: 32rpx;
    background: #ffffff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-size: 24rpx;
    color: #666666;

    .title {
      margin-bottom: 16rpx;
    }

    .tips1 {
      margin-top: 16rpx;
      display: flex;
      align-items: center;
      gap: 16rpx;
      font-size: 20rpx;
      color: #999999;
      margin-bottom: 26rpx;

      img {
        width: 32rpx;
        height: 32rpx;
      }
    }

    .line {
      margin-top: 24rpx;
    }
  }

  .tips2 {
    margin-top: 16rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
    font-size: 24rpx;
    color: #999999;
  }
}

.btn-warp {
  position: fixed;
  bottom: 0;
  width: 750rpx;
  height: 196rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;

  .btn {
    width: 686rpx;
    height: 80rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    line-height: 80rpx;
    margin-top: 24rpx;
  }
}
.tip-box {
  width: 500rpx;
  padding: 32rpx;
  border-radius: 24rpx;
}
.tip-tit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  img {
    width: 32rpx;
    height: 32rpx;
  }
  text {
    font-size: 28rpx;
    color: #333333;
    margin-left: 24rpx;
  }
}
.tip-txt {
  font-size: 24rpx;
  color: #777777;
  line-height: 48rpx;
  padding: 32rpx 0;
}
.tip-btm {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .tip-btn {
    width: 134rpx;
    height: 52rpx;
    background: #2370ee;
    border-radius: 8rpx;
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
    line-height: 52rpx;
  }
}
::v-deep .u-popup__content {
  border-radius: 24rpx;
}
</style>
