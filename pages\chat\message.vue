<template>
	<view class="container">
		<u-sticky bgColor="#FFFFFF">
			<u-tabs :current="tabsIndex" :list="tabsList" :activeStyle="{color: '#4F8CF0',  transform: 'scale(1.05)'}"
				:inactiveStyle="{color: '#999999',  transform: 'scale(0.9)'}" @click="onTabsItemClick"></u-tabs>

			<div class="search-container">
				<u-search :showAction="false"></u-search>
			</div>

			<view class="tab-text__container">
				<view class="tab-text__item">
					全部
				</view>
				<view class="tab-text__item">
					已读
				</view>
			</view>
		</u-sticky>

		<scroll-view class="list-container" :scroll-y="true" :scroll-top="scrollTop">
			<template v-if="tabsIndex === 0">
				<Post />
			</template>
			<template v-if="tabsIndex === 1">
				<Task />
			</template>
			<template v-if="tabsIndex === 2">
				<Butler />
			</template>
			<template v-if="tabsIndex === 3">
				<System />
			</template>
			<template v-if="tabsIndex === 4">
				<Customer />
			</template>
		</scroll-view>

		<Tabbar />
	</view>
</template>

<script>
	import Tabbar from "../../components/tabbar.vue";
	import Post from './post.vue';
	import Task from './task.vue';
	import Butler from './butler.vue';
	import System from './system.vue';
	import Customer from './customer.vue';

	export default {
		components: {
			Tabbar,
			Post,
			Task,
			Butler,
			System,
			Customer
		},
		data() {
			return {
				tabsList: [{
					name: '岗位·聊',
				}, {
					name: '任务·聊',
				}, {
					name: '就业管家'
				}, {
					name: '系统消息'
				}, {
					name: '客服咨询'
				}],
				tabsIndex: 0,
				scrollTop: 0
			};
		},
		methods: {
			onTabsItemClick(res) {
				this.tabsIndex = res.index;
				setTimeout(() => {
					this.scrollTop = this.scrollTop - 9999999;
				}, 300)
			}
		}
	}
</script>
<style lang="scss" scoped>
	.container {
		height: calc(100vh - 102rpx);
		display: flex;
		flex-direction: column;

		.list-container {
			flex: 1;
			overflow-y: auto;

			.item-image__box {
				margin-inline-end: 24rpx;

				.avater {
					width: 88rpx;
					height: 88rpx;
					border-radius: 999rpx;
				}
			}

			.item-info__box {
				.info-start {
					display: flex;
					gap: 32rpx;
					align-items: center;

					.name {
						font-size: 28rpx;
						color: #041024;
					}

					.company,
					.time {
						font-size: 24rpx;
						color: #666666;
					}

					.time {
						margin-inline-start: auto;
					}
				}
			}
		}

		.search-container {
			padding: 32rpx;
		}

		.tab-text__container {
			padding-inline: 32rpx;
			padding-block: 12rpx;
			display: flex;
			align-items: center;
			gap: 48rpx;
			font-size: 28rpx;
			color: #434343;

			.is_active {
				color: #4F8CF0;
			}
		}
	}
</style>