<template>
	<view class="item" @click="goCompDetails">
		<view class="item-up">
			<image src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode=""></image>
			<view class="info">
				<view class="user">
					<view class="userInfo">
						<view class="name">腾讯视频有限公司</view>
					</view>
				</view>
				<view class="desc">
					<view class="desc-item">学创北京</view>
					<view class="desc-item">A轮</view>
					<view class="desc-item">0-20人</view>
				</view>

				<view class="pos">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png" mode=""></image>
					北京北京市昌平区1号
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'qlmActiveCompInfo',
	data() {
		return {};
	},
	methods: {
		goCompDetails() {
			uni.navigateTo({
				url: '/pagesA/details/companyDetail',
			});
		},
	},
};
</script>

<style lang="less" scoped>
.item {
	display: flex;
	flex-direction: column;
	padding: 32rpx;
	margin-bottom: 32rpx;
	background-color: #ffffff;
	border-radius: 24rpx;

	.item-up {
		display: flex;

		& > image {
			width: 144rpx;
			height: 144rpx;
		}

		.info {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			flex: 1;
			padding-left: 24rpx;

			.user {
				display: flex;
				justify-content: space-between;
				align-items: center;

				.userInfo {
					display: flex;
					align-items: center;

					.name {
						font-weight: 600;
						font-size: 32rpx;
						color: #333333;
					}
				}
			}

			.desc {
				display: flex;
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;

				.desc-item {
					border-right: 1px solid #999999;
					padding: 0 12rpx;

					&:first-child {
						padding-left: 0;
					}

					&:last-child {
						border-right: none;
					}
				}
			}

			.pos {
				display: flex;
				align-items: center;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				image {
					width: 32rpx;
					height: 32rpx;
				}
			}
		}
	}
}
</style>
