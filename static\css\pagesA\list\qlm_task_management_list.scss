page {
    background-color: #f5f5f7;
}

.header {
    padding-bottom: 32rpx;
    background-color: #F5F5F7;
    .tabs {
        background-color: #FFFFFF;
        border-radius: 0 0 24rpx 24rpx;
    }
    
    .search-wrap {
        margin: 32rpx;
    }
    
    .filters {
        display: flex;
        margin: 0 32rpx;
        .filter {
            display: flex;
            align-items: center;
            height: 48rpx;
            background-color: #FFFFFF;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding: 0 12rpx;
            margin-right: 12rpx;
            border-radius: 8rpx;
            
            image {
                width: 24rpx;
                height: 24rpx;
            }
        }
    }
}

.list {
    padding: 0 32rpx;
}