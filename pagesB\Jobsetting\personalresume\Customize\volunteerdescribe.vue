<template>
	<!-- 公司名称编辑 -->
	<view class="container">
		<view class="context">
			<view class="input-area">
				<view class="section">
					<input class="input" placeholder="1.项目概况 2.我的责任 3.项目成果" v-model="inputValue" maxlength="3000">
					</input>
				</view>
				<view class="box">
					<view class="text-box">
						<text :class="inputValue.length == 0 ? 'active':'count'">
							{{inputValue.length}}
						</text>
						<text class="count-bottom">
							/3000</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 完成按钮 -->
		<view class="footer">
			<button class="confirm-btn" @click="finish">确定</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				inputValue: ''
			};
		},
		methods: {
			closeTip() {
				this.noticehide = false
			},
			finish() {
				// 完成按钮点击后的逻辑，比如提交数据等
				console.log('完成操作，输入值为：', this.inputValue);
			},
		},
	};
</script>

<style scoped>
	.container {
		padding: 20rpx 0rpx;
		background-color: #fff;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.context {
		flex: 1;
	}

	.tip-bar {
		background-color: #ffd6b3;
		padding: 10rpx 15rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.tip-text {
		color: #f60;
		font-size: 28rpx;
		margin-left: 16rpx;
	}

	.close-icon {
		color: #ccc;
		font-size: 32rpx;
	}

	.input-area {
		margin-top: 20rpx;
		/* border-bottom: 1rpx solid #e5e5e5; */
		/* padding-bottom: 15rpx; */
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		flex: 1;
	}

	.label {
		font-size: 32rpx;
		color: #333;
	}

	.input {
		width: 100%;
		font-size: 32rpx;
		border: none;
		padding: 10rpx 0;
	}

	.section {
		/* margin-bottom: 30rpx; */
		width: 686rpx;
		padding: 32rpx 0rpx;
		border-bottom: 1rpx solid rgba(230, 230, 230, 1);
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.count {
		color: rgba(79, 140, 240, 1);
		font-size: 28rpx;
		float: right;
		margin-top: 24rpx;
	}

	.count-bottom {
		color: rgba(153, 153, 153, 1);
		font-size: 28rpx;
		margin-top: 24rpx;
	}

	.text-box {
		display: flex;
		justify-content: flex-end;

	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-image: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.active {
		color: red;
		margin-top: 24rpx;
		font-size: 28rpx;
	}
</style>