<template>
    <view id="app">
        <u-sticky bgColor="#F5F5F5">
            <u-search placeholder="请输入项目名称" bgColor="#FFFFFF" showAction @custom="custom" v-model="keyword"></u-search>
            <view class="projectType">项目状态
                <image src="../static/images/down.png" mode="" class="downImg"></image>
            </view>
        </u-sticky>

        <view class="content" v-for="(item,index) in page.data" :key="index" :item="item"  @click="goDetails(item)">
            <view :class="['rightIcon',type==1?'':'cccColor']">{{ item.active_status_name }}</view>
            <view class="proTitle">{{ item.name }}</view>
            <view class="proCenter">
                <view>{{ item.type_name }}</view>
                <view class="progress">项目进度：
                    <text class="progressNum">{{ item.push_member_count }}/{{ item.count }}</text>
                </view>
            </view>
            <view class="hrBox"></view>
            <view class="contentBottom">
                <view class="contentUser">
                    <image :src="item.user_member_info.image.path_url" mode="" class="headImg"></image>
                    <view>{{ item.user_member_certification.name }}</view>
                </view>
                <!--<view class="rightBtn" v-if="type ==1 ">推广码</view>-->
            </view>
        </view>
        <Pages :status="page.status"></Pages>
    </view>
</template>

<script>
    import {userProjects} from "../../config/headhunterList_api";

    export default {
        data() {
            return {
                type: 1,
                page: {
                    form: {
                        limit: 10,
                        page: 1,
                    },
                    data: [],
                    more: false,
                    status: 'nomore',
                }
            }
        },
        onLoad() {
           var _this = this;
           _this.getList();
        },
        onReachBottom() {
            var _this = this;
            if (_this.page.more) {
                _this.page.form.page++;
                _this.getList()
            }
        },
        methods: {
            getList() {
                var _this = this;
                userProjects(_this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.more ? 'loading' : 'nomore';
                    }
                });
            },
            goDetails(item) {
                uni.navigateTo({
                    url: '/pagesA/project/projectDetail?id='+item.id
                })
            },
            custom() {
                console.log('搜索')
            }

        }
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .downImg {
        width: 32rpx;
        height: 32rpx;
    }

    .projectType {
        width: 160rpx;
        height: 50rpx;
        background: #FFFFFF;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 24rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        margin-bottom: 24rpx;
    }

    .content {
        width: 100%;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 32rpx;
        position: relative;
        margin-bottom: 24rpx;
    }

    .rightIcon {
        width: 132rpx;
        height: 62rpx;
        background: #4F8CF0;
        text-align: center;
        line-height: 62rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
        border-radius: 0 24rpx 0 24rpx;
        position: absolute;
        top: 0;
        right: 0;
    }

    .cccColor {
        background: #CCC !important;
    }

    .proTitle {
        font-weight: 600;
        font-size: 32rpx;
        color: #000000;
        margin-bottom: 16rpx;
    }

    .proCenter {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 400;
        font-size: 28rpx;
        color: #000000;
    }

    .progress {
        color: #999;
    }

    .progressNum {
        color: #4F8CF0;
    }

    .hrBox {
        width: 100%;
        height: 2rpx;
        background: #F5F5F7;
        margin: 24rpx 0;
    }

    .contentBottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .contentUser {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }

    .headImg {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        margin-right: 16rpx;
    }

    .rightBtn {
        width: 120rpx;
        height: 56rpx;
        background: #4F8CF0;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 56rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #FFFFFF;
    }
</style>