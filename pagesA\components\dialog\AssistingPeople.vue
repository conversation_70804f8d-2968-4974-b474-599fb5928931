<template>
    <el-dialog v-if="isVisible" class="dialog">
        <view id="app">
            <view class="header">
                <view @click="hide" class="left-element">关闭</view>
                <view @click="submit" class="right-element">确定</view>
            </view>

            <view class="searchBox">
                <image src="/static/images/project/searchIcon.png" mode="" class="searchIcon"></image>
                <input type="text" class="searchIpt" placeholder="请输入协助人名称"/>
            </view>

            <view class="filterBox">
                <view class="filterBox_child">
                    郑州
                    <image src="/static/images/project/smallDown.png" mode="" class="smallDown"></image>
                </view>
                <view class="filterBox_child">
                    筛选
                    <image src="/static/images/project/smallDown.png" mode="" class="smallDown"></image>
                </view>
            </view>

            <view class="listBox" v-for="(userItem, index) in items" :key="index" :item="userItem"
                  :class="['item', { 'selected': selectedItems.includes(userItem) }]"
                  @click="toggleSelection(userItem)">
                <view class="listBox_top">
                    <image :src="userItem.member_info.image.path_url" mode="" class="topHead"></image>
                    <view class="listBox_top_right">
                        <view class="listBox_top_right_title">{{ userItem.member_certification.name }}</view>
                        <view class="listBox_top_right_synopsis">{{ userItem.member_info.sex_str}} | {{
                            userItem.member_info.age }}岁 | {{ userItem.member_info.education_type_name }}
                        </view>
                    </view>
                </view>
                <view class="labelBox" v-if="userItem.user_headhunter.label">
                    <view class="labelBox_child" v-for="(labelItem,index) in userItem.user_headhunter.label">{{
                        labelItem }}
                    </view>
                </view>
                <view class="hrBox"></view>
                <view class="listBox_bottom">
                    <image :src="userItem.company_info.logo.path_url" mode="" class="companyImg"></image>
                    <view class="listBox_top_right">
                        <view class="listBox_top_right_one">
                            <view class="listBox_top_right_title1">{{ userItem.company.name}}</view>
                            <image src="/static/images/project/authentication.png" mode=""
                                   class="authentication"></image>
                        </view>
                        <!--<view class="listBox_top_right_synopsis">男 | 23岁 | 本科</view>-->
                    </view>
                </view>
            </view>

        </view>
    </el-dialog>
</template>

<script>
    import {staffList} from "../../../config/company_api";

    export default {
        props: {
            // isVisible: {
            //     type: Boolean,
            //     default: true
            // }
        },
        data() {
            return {
                isVisible: false,
                items: [], // 列表项
                selectedItems: [] // 存储选中的项
            }
        },
        methods: {
            hide() {
                var _this = this;
                _this.isVisible = false;
                _this.selectedItems = [];
            },
            open() {
                var _this = this;
                _this.isVisible = true;
                _this.selectedItems = [];

                staffList({
                    member_certification_status: 1,
                    disable_status: 2,
                }).then(response => {
                    _this.items = response.data;
                })
            },
            mounted() {

            },
            toggleSelection(item) {
                var _this = this;

                const index = _this.selectedItems.indexOf(item);

                if (index > -1) {
                    // 如果已选中，则取消选中
                    _this.selectedItems.splice(index, 1);
                } else {
                    // 否则，添加到选中项中
                    _this.selectedItems.push(item);
                }
            },
            submit() {
                var _this = this;
                var backData = [];

                _this.selectedItems.forEach(function (_item, _index) {
                    backData.push(_item);
                });

                _this.$emit("custom-event", backData);

                _this.hide();
            }
        }
    }
</script>

<style scoped>
    .dialog {
        position: absolute;
        top: 0;
        left: 0;
        background-color: #fff;
        z-index: 999;
        width: 100%;
        height: 100%;
    }

    .header {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin: auto;
        padding: auto;
        background: #4F8CF0;
        color: #fff;
        height: 38px;
    }

    .left-element, .right-element {
        height: 38px;
        line-height: 38px;
    }

    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        min-height: 100%;
        height: auto;
        background-color: #F5F5F7;
    }

    .searchBox {
        width: 100%;
        height: 64rpx;
        background: #ffffff;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
        display: flex;
        align-items: center;
        padding: 0 24rpx;
        margin-bottom: 32rpx;
    }

    .searchIcon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 16rpx;
    }

    .searchIpt {
        width: 100%;
        height: 100%;
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        line-height: 64rpx;
    }

    /* 筛选start */

    .filterBox_child {
        width: 96rpx;
        height: 48rpx;
        background: #FFFFFF;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        margin-right: 16rpx;
    }

    .smallDown {
        width: 24rpx;
        height: 24rpx;
    }

    .filterBox {
        display: flex;
        margin-bottom: 32rpx;
    }

    /* 筛选end */

    .listBox {
        width: 100%;
        height: 360rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 32rpx;
        border: 1px solid transparent;
        margin-bottom: 24rpx;
    }

    .item.selected {
        border: 1px solid blue; /* 选中时边框蓝色 */
    }

    .listBox_top {
        display: flex;
        align-items: center;
    }

    .topHead {
        width: 88rpx;
        height: 88rpx;
        border-radius: 88rpx 88rpx 88rpx 88rpx;
        margin-right: 16rpx;
    }

    .listBox_top_right {
        height: 88rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .listBox_top_right_title {
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        width: 308rpx;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 省略号 */
    }

    .listBox_top_right_title1 {
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        width: 308rpx;
        white-space: nowrap;
        /* 不换行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 省略号 */
    }

    .listBox_top_right_synopsis {
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
    }

    .labelBox {
        display: flex;
        align-items: center;
        margin-top: 16rpx;
    }

    .labelBox_child {
        width: 112rpx;
        height: 46rpx;
        background: #F6F6F6;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        margin-right: 16rpx;
        text-align: center;
        line-height: 46rpx;
        font-weight: 400;
        font-size: 22rpx;
        color: #666666;
    }

    .hrBox {
        width: 100%;
        height: 2rpx;
        background: #F5F5F7;
        margin: 24rpx 0;
    }

    .companyImg {
        width: 96rpx;
        height: 96rpx;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        margin-right: 16rpx;
    }

    .listBox_bottom {
        display: flex;

    }

    .authentication {
        width: 32rpx;
        height: 32rpx;
    }

    .listBox_top_right_one {
        display: flex;
        align-items: center;
    }
</style>