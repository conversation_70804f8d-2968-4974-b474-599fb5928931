<template>
    <view>
        <u-index-list :index-list="indexList">
            <template v-for="(item, index) in itemArr">
                <u-index-item>
                    <u-index-anchor :text="index"></u-index-anchor>
                    <view class="list-cell" v-for="(cell, ind) in item" @click="getCityId(cell)">
                        {{cell.name}}
                    </view>
                </u-index-item>
            </template>
        </u-index-list>
    </view>
</template>

<script>
    import {
        getAllCity
    } from '../../config/api.js'
    export default {
        data() {
            return {
                indexList: [], // 调用获取字母的函数,
                itemArr: [],
                page:'',
            }
        },
        onLoad() {
            this.itemArr = this.$store.state.itemArr || uni.getStorageSync('itemArr')
            this.getAlphabet()
        },
        onShow() {
            if (!this.itemArr) {
                this.getAllCity()
            }
        },
        methods: {
            getCityId(cell) {
                uni.setStorageSync("userCity", cell)
                this.$store.commit('setSelectedCity', cell);
                uni.navigateBack()
            },
            getAlphabet() {
                this.indexList = '@ABCDEFGHIJKLMNOPQRSTUWXYZ'.split('');
            },
            //省市数据
            async getAllCity() {
                const result = await getAllCity()
                if (result.status_code == 200) {
                    this.itemArr = result.data;
                    this.$store.commit('setProvinceitemArr', result.data)
                    console.log('2', this.itemArr)
                }
            },
        }
    }
</script>

<style scoped>
    .allCity {
        height: 40rpx;
    }

    .list-cell {
        display: flex;
        box-sizing: border-box;
        width: 100%;
        padding: 10px 24rpx;
        overflow: hidden;
        color: #323233;
        font-size: 14px;
        line-height: 24px;
        background-color: #fff;
    }
</style>