<template>
	<view class="home-index">
		<top-bar-status :class="['topClass', isScroll ? 'isScroll' : '']">
			<template #default>
				<view class="top_content">
					<view class="logo">
						<image src="../../static/images/index/logo.png" mode=""></image>
					</view>
					<view class="title">
						工作台
					</view>
				</view>
			</template>
		</top-bar-status>
		<view class="content">
			<image src="https://api-test.zhaopinbei.com/images/plat.png" mode="widthFix"></image>
			<view class="list">
				<view class="item" v-for="(item,index) in list" :key="index" @click="go(item)">
					<image :src="item.icon" mode=""></image>
					<text>{{item.label}}</text>
				</view>
			</view>
		</view>

		<tabbar></tabbar>
	</view>
</template>

<script>
	import TopBarStatus from "../../components/topBarStatus.vue"
	import Tabbar from "../../components/tabbar.vue"
	export default {
		components: {
			TopBarStatus,
			Tabbar
		},
		data() {
			return {
				isScroll: false,
				member_list: [{
						icon: '/static/images/plat/wt.png',
						label: '我的就业管家',
						// path: '/pagesA/member/details/authorizeDetail'
						// path: '/pagesA/list/commission_list',
						path: '/pagesA/member/details/authorizeDetail'
					},
					{
						icon: '/static/images/plat/bm.png',
						label: '报名列表',
						path: '/pagesA/list/signup_list'
					},
					// {
					// 	icon: '/static/images/plat/addr.png',
					// 	label: '地址管理',
					// 	path: '/pagesA/list/addr_management_list'
					// },
                    {
						icon: '/static/images/plat/ms.png',
						label: '面试列表',
						path: '/pagesA/list/interview_list'
					}, {
						icon: '/static/images/plat/rw.png',
						label: '任务管理',
						path: '/pagesA/list/qlm_task_management_list',
					}
				],
				company_list: [
					{
						icon: '/static/images/plat/staff.png',
						label: '员工管理',
						path: '/pagesA/list/staff_management_list'
					},
					{
						icon: '/static/images/plat/staff.png',
						label: '员工管理',
						path: '/pagesA/list/staff_management_list'
					},
					{
						icon: '/static/images/plat/position.png',
						label: '职位管理',
						path: '/pagesA/list/company_job_management_list'
					},
					{
						icon: '/static/images/plat/contract.png',
						label: '合同管理',
						path: '/pagesA/contract/contract',
					},
					{
						icon: '/static/images/plat/recommend.png',
						label: '推荐简历',
						path: '/pagesA/list/recommend_resume_list',
					},
					{
						icon: '/static/images/plat/addr.png',
						label: '地址管理',
						path: '/pagesA/list/company_addr_management_list'
					},
					{
						icon: '/static/images/plat/ms.png',
						label: '面试列表',
						path: '/pagesA/list/company_interview_list'
					},
					{
						icon: '/static/images/plat/sq.png',
						label: '授权就业管家',
						path: '/pagesA/list/auth_employ_list',
					},
					{
						icon: '/static/images/plat/bm.png',
						label: '报名记录',
						path: '/pagesA/list/company_signup_list'
					},
					{
						icon: '/static/images/plat/active.png',
						label: '招聘会管理',
						path: '/pagesA/list/jobfair_management'
						//path: '/pagesA/list/company_jobfair_management_list', //杨浩修改的
					},
					{
						icon: '/static/images/plat/change.svg',
						label: '通讯录',
						path: '/pagesC/blMail/mailList'
					},
					{
						icon: '/static/images/plat/change.svg',
						label: '账号管理',
						path: '/pagesC/accountMan/index'
					},
				],
				headhunters_list: [{
						icon: '/static/images/plat/change.svg',
						label: '通讯录',
						path: '/pagesA/connectivity/mailList',
						// path: '/pagesA/connectivity/friend_follow_fans',
					},
					{
						icon: '/static/images/plat/change.png',
						label: '审核变更',
						path: '/pagesA/list/audit_List',
					}, {
						icon: '/static/images/plat/shareResume.svg',
						label: '分享的简历',
						path: '/pagesA/list/share_resume_list',
					}, {
						icon: '/static/images/plat/zwapply.png',
						label: '职位申请记录',
						path: '/pagesA/list/job_apply_list',
					}, {
						icon: '/static/images/plat/applicant.png',
						label: '授权应聘人',
						path: '/pagesA/list/auth_applicant_list',
					}, {
						icon: '/static/images/plat/company.png',
						label: '授权企业',
						path: '/pagesA/list/auth_enterprise_list',
					}, {
						icon: '/static/images/plat/company.png',
						label: '合同模板',
						path: '/pagesA/contract/contract',
					}, {
						icon: '/static/images/plat/ms.png',
						label: '面试列表',
						path: '/pagesA/list/interview_list'
					}, {
						icon: '/static/images/plat/position.png',
						label: '职位管理',
						path: '/pagesA/headhunterList/headhunters_job_management_list'
					}, {
						icon: '/static/images/plat/staff.png',
						label: '就业管家管理',
						path: '/pagesA/list/staff_management_list'
					},{
						icon: '/static/images/plat/staff.png',
						label: '项目管理',
						path: '/pagesA/project/projectPlat'
					}, {
						icon: '/static/images/plat/addr.png',
						label: '地址管理',
						path: '/pagesA/list/addr_management_list'
					}, {
						icon: '/static/images/plat/bm.png',
						label: '报名记录',
						path: '/pagesA/list/company_signup_list'
					}, {
						icon: '/static/images/plat/rw.png',
						label: '任务管理',
						path: '/pagesA/list/jy_taskPlat',
					}, {
						icon: '/static/images/plat/rw.svg',
						label: '招聘会管理',
						path: '/pagesA/list/jobfair_management',
						//path: '/pagesA/list/company_jobfair_management_list',//杨浩修改
					}, {
						icon: '/static/images/plat/zwapply.png',
						label: '职位管理',
						path: '/pagesA/list/job_management',
					}
				],
				all_list: [{
						icon: '/static/images/plat/wt.png',
						label: '我的就业管家',
						path: '/pagesA/list/commission_list'
					},
					{
						icon: '/static/images/plat/bm.png',
						label: '报名记录',
						path: '/pagesA/list/signup_list'
					},
					{
						icon: '/static/images/plat/bm.png',
						label: '报名记录',
						path: '/pagesA/list/company_signup_list'
					},
					{
						icon: '/static/images/plat/bm.png',
						label: '报名记录',
						path: '/pagesA/list/company_signup_list'
					},
					{
						icon: '/static/images/plat/ms.png',
						label: '面试列表',
						path: '/pagesA/list/interview_list'
					},
					{
						icon: '/static/images/plat/position.png',
						label: '职位管理',
						path: '/pagesA/list/job_management_list'
					},
					{
						icon: '/static/images/plat/staff.png',
						label: '员工管理',
						path: '/pagesA/list/staff_management_list'
					},
					{
						icon: '/static/images/plat/addr.png',
						label: '地址管理',
						path: '/pagesA/list/addr_management_list'
					}, {
						icon: '/static/images/plat/rw.png',
						label: '任务管理',
						path: '/pagesA/list/qlm_task_management_list',
					}, {
						icon: '/static/images/plat/rw.png',
						label: '任务管理',
						path: '/pagesA/list/jy_task_management_list',
					}, {
						icon: '/static/images/plat/zwapply.png',
						label: '职位申请记录',
						path: '/pagesA/list/job_apply_list',
					}, {
						icon: '/static/images/plat/shareResume.png',
						label: '分享的简历',
						path: '/pagesA/list/share_resume_list',
					},
					{
						icon: '/static/images/plat/sq.png',
						label: '授权就业管家',
						path: '/pagesA/list/auth_employ_list',
					},
					{
						icon: '/static/images/plat/recommend.png',
						label: '推荐简历',
						path: '/pagesA/list/recommend_resume_list',
					},
					{
						icon: '/static/images/plat/company.png',
						label: '授权企业',
						path: '/pagesA/list/auth_enterprise_list',
					},
					{
						icon: '/static/images/plat/contract.png',
						label: '合同管理',
						path: '/pagesA/contract/contract',
					},
					{
						icon: '/static/images/plat/applicant.png',
						label: '授权应聘人',
						path: '/pagesA/list/auth_applicant_list',
					}
				]
			}
		},
		computed: {
			roleType() {
				return this.$store.state.roleType || uni.getStorageSync('roleType')
			},
			list() {
				// return this.roleType == 'member' ? this.member_list : this.roleType == 'company' ? this.company_list : this
				// 	.roleType == 'headhunters' ? this.headhunters_list : this.all_list
                return this.roleType == 'member' ? this.member_list : this.roleType == 'company' ? uni.getStorageSync('userInfo').api_menu.plat.children : this.roleType == 'headhunters' ? uni.getStorageSync('userInfo').api_menu.work.children : this.all_list
			}
		},
		onPageScroll(e) {
			this.isScroll = e.scrollTop > 0
		},
		methods: {
			go(item) {
				console.log(item)
				if(item.path == '/pagesA/list/jobfair_management'){
					item.path = item.children[0].path;
				}
				uni.navigateTo({
					url: item.path
				})
			}
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding-bottom: 128rpx;
	}

	.topClass {
		position: inherit;
		width: 100%;
		z-index: 10;
		transition: all .5s;
		background: #FFFFFF;

		&.isScroll {
			position: fixed;
			top: 0;
			left: 0;
		}

		.top_content {
			display: flex;
			align-items: center;
			height: 100%;

			.logo {
				display: flex;
				position: absolute;

				image {
					width: 126rpx;
					height: 44rpx;
				}
			}

			.title {
				display: flex;
				flex: 1;
				justify-content: center;
				font-weight: 500;
				font-size: 34rpx;
				color: #000000;
			}
		}
	}

	.content {
		padding: 0 32rpx;

		&>image {
			width: 100%;
			margin: 32rpx 0;
		}
	}

	.list {
		display: flex;
		flex-wrap: wrap;
		overflow: hidden;
		margin-right: -32rpx;

		.item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: calc(25% - 32rpx);
			margin-right: 32rpx;
			background-color: #FFFFFF;
			margin-bottom: 32rpx;
			height: 148rpx;
			border-radius: 24rpx;

			image {
				width: 64rpx;
				height: 64rpx;
			}

			text {
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				margin-top: 12rpx;
			}
		}
	}
</style>
