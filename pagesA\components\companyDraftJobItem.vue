<template>
	<view class="item" @click="go">
		<block v-if="item.status==1">
			<view class="status ing">
				招聘中
			</view>
		</block>
		<block v-if="item.status==3">
			<view class="status yxj">
				已下架
			</view>
		</block>
		
		<view class="item-up">
			<view class="title">
				{{item.title}}
			</view>
			<view class="item-up-one">
				<view class="name">
					{{item.job_class.name}}
				</view>
				<view class="money">
					{{item.salary_info_str}}
				</view>
			</view>
			<view class="item-up-two">
				<view class="lab">
					发布人
				</view>
				<view class="money">
					{{item.send_user_member_certification.name}}
				</view>
			</view>
			<!--<view class="item-up-two">-->
				<!--<view class="lab">-->
					<!--抄送人-->
				<!--</view>-->
				<!--<view class="money">-->
					<!--张三-->
				<!--</view>-->
			<!--</view>-->
		</view>
	</view>
</template>

<script>
	export default {
		name: "companyDraftJobItem",
		props:{
			item:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {

			};
		},
		methods:{
			go(){
				// uni.navigateTo({
				// 	url:"/pagesA/add/pubJobOne"
				// })
                uni.navigateTo({
                    url: `/pagesA/details/companyJobDetailsCopy?type=${this.item.type}&id=${this.item.id}`
                })
			}
		}
	}
</script>

<style lang="less" scoped>
	.item{
		position: relative;
		display: flex;
		flex-direction: column;
		padding: 0 32rpx;
		margin-bottom: 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;
		.item-up{
			display: flex;
			flex-direction: column;
			padding: 30rpx 0;
			.title{
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;
				
			}
			.item-up-one{
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 30rpx;
				.name{
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
				}
				.money{
					font-weight: 600;
					font-size: 32rpx;
					color: #F98A14;
				}
			}
			
			.item-up-two{
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
				margin-top: 30rpx;
			}
		}
		
		.status {
			display: flex;
			justify-content: center;
			align-items: center;
			position: absolute;
			right: 0;
			top: 0;
			font-size: 28rpx;
			width: 132rpx;
			height: 62rpx;
			border-radius: 0 24rpx 0 24rpx;
		}
		.ing{
			background: #57D51C;
			color: #FFFFFF;
		}
		.wait {
			background: #4F8CF0;
			color: #FFFFFF;
		}
		
		.cg{
			background: #F9AD14;
			color: #FFFFFF;
		}
		
		.yxj {
			background: #cccccc;
			color: #FFFFFF;
		}
	}
	
</style>