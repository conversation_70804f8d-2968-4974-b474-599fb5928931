<template>
	<view class="container">
		<view class="content">
			<view class="form-item" @click="routerName">
				<label>姓名</label>
				<u--input placeholder="请输入您的姓名" :value="name" suffixIcon="arrow-right"
					suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
			</view>
			<view class="form-item">
				<label>当前求职状态</label>
				<picker mode="selector" :range="jobStatusList" v-model="jobStatus">
					<view class="selected-con">
						<view class="picker-view" v-if="jobStatus != ''">
							{{ jobStatus }}
						</view>
						<view class="picker-view" v-else>
							请选择求职状态
						</view>
						<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
					</view>
				</picker>
			</view>
			<view class="form-item">
				<label>性别</label>
				<picker mode="selector" :range="genderList" v-model="gender">
					<view class="selected-con">
						<view class="picker-view" v-if="gender != ''">
							{{ gender }}
						</view>
						<view class="picker-view" v-else>
							请选择性别
						</view>
						<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
					</view>
				</picker>
			</view>

			<view class="form-item">
				<label>我的千里马身份</label>
				<picker mode="selector" :range="identityList" v-model="identity">
					<view class="selected-con">
						<view class="picker-view" v-if="identity != ''">
							{{ identity }}
						</view>
						<view class="picker-view" v-else>
							请选择您的千里马身份
						</view>
						<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
					</view>
				</picker>
			</view>
			<view class="form-item">
				<label>出生年月</label>
				<picker mode="date" v-model="birthDate">
					<view class="selected-con">
						<view class="picker-view" v-if="birthDate != ''">
							{{ birthDate }}
						</view>
						<view class="picker-view" v-else>
							请选择出生年月日
						</view>
						<u-icon name="arrow-right" style="width: 32rpx; height: 32rpx;"></u-icon>
					</view>
				</picker>
			</view>
			<view class="form-item">
				<label>电话</label>
				<u--input type="number" placeholder="请输入您的手机号码" :value="phone" suffixIcon="arrow-right"
					suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
				<!-- <input type="number" placeholder="请输入您的手机号码" v-model="phone" /> -->
			</view>
			<view class="form-item" @click="routerWX">
				<label>微信号</label>
				<u--input type="text" placeholder="请输入您的微信号" :value="wechat" suffixIcon="arrow-right"
					suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
				<!-- <input type="text" placeholder="请输入您的微信号" v-model="wechat" /> -->
			</view>
			<view class="form-item" @click="routerEmail">
				<label>邮箱（选填）</label>
				<u--input type="email" placeholder="请输入您的邮箱" :value="email" suffixIcon="arrow-right"
					suffixIconStyle="color:rgba(51, 51, 51, 1)"></u--input>
				<!-- <input type="email" placeholder="请输入您的邮箱" v-model="email" /> -->
			</view>
		</view>

		<view class="footer">
			<button class="confirm-btn" @click="submitForm">完成</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				name: '',
				jobStatusList: ['在职', '离职', '待业'],
				jobStatus: '',
				genderList: ['男', '女'],
				gender: '',
				identityList: ['求职者', '其他'],
				identity: '',
				birthDate: '',
				phone: '',
				wechat: '',
				email: ''
			};
		},
		methods: {
			submitForm() {
				// 这里可以添加表单提交逻辑，例如调用接口发送数据等
				console.log('表单提交：', {
					name: this.name,
					jobStatus: this.jobStatus,
					gender: this.gender,
					identity: this.identity,
					birthDate: this.birthDate,
					phone: this.phone,
					wechat: this.wechat,
					email: this.email
				});
			},
			routerName() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/personalData/name'
				})
			},
			routerWX() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/personalData/WX'
				})
			},
			routerEmail() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/personalData/email'
				})
			}
		}
	};
</script>

<style scoped>
	.container {
		padding: 0rpx 20rpx;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		background-color: rgba(245, 245, 247, 1);
	}

	.content {
		flex: 1;
		width: 622rpx;
		padding: 32rpx;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.form-item {
		margin-bottom: 20rpx;
		width: 622rpx;
		border-bottom: 1rpx solid rgba(230, 230, 230, 1);
		padding-bottom: 32rpx;
	}

	.label {
		display: block;
		margin-bottom: 5rpx;
		height: 114rpx;
	}


	/* .input {
		width: 100%;
		padding: 10rpx;
		border: 1rpx solid #ccc;
		border-radius: 5rpx;
	} */

	::v-deep .u-input {
		border: none;
		padding: 0rpx !important;
		width: 622rpx;
	}

	.picker-view {
		width: 100%;
		/* padding: 10rpx; */
		/* border: 1rpx solid #ccc; */
		color: rgba(204, 204, 204, 1);
		margin-top: 16rpx;
		border-radius: 5rpx;
		font-size: 28rpx;
	}

	.selected-con {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 622rpx;
		height: 40rpx;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}
</style>