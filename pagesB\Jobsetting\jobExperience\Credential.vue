<template>
	<!-- 资格证书 -->
	<view class="container">
		<view class="context">
			<radio-group @change="chang" class="radio-group">
				<view class="recruitment-item" v-for="(item, index) in recruitmentList" :key="index">
					<scroll-view class="scroll-view_H" scroll-x="true">
						<view class="item-recrutment">
							<view class="item-box">
								<radio value="" :checked="item.isChecked" style="margin-left: 32rpx;" />
								<view class="item-context">
									<view class="item-header">
										<text class="title">{{item.name}}</text>
										<view class="description-text">
											<text>{{item.context1}}</text>
											<text>{{ item.context2 }}</text>
										</view>
									</view>
									<view class="item-content">
										<view class="description">
											<image :src="editor" @click="editItem(index)"
												style="width: 32rpx; height: 32rpx; align-self: center; margin-right: 32rpx; margin-bottom: 32rpx;">
											</image>
										</view>
									</view>
								</view>
							</view>
							<view class="delete-btn" v-if="item.showDelete" @click="deleteItem(index)">
								<text style="width: 56rpx; font-size: 28rpx;">删除</text>
							</view>
						</view>
					</scroll-view>

				</view>
				<view class="add" @click="add(e)">
					添加
				</view>
			</radio-group>
		</view>
		<view class="footer">
			<view class="confirm-btn" @click="confirmAction">确定</view>
		</view>
	</view>
</template>

<script>
	import editor from '../../../static/jobsetting/editor-setting.png'
	export default {
		data() {
			return {
				recruitmentList: [{
						name: 'TOEIC Bridge托业英语证书',
						context1: '英语专业四级',
						context2: '英语专业四级',
						checked: true,
						showDelete: true
					},
					{
						name: 'TOEIC Bridge托业英语证书',
						context1: '英语专业四级',
						context2: '英语专业四级',
						checked: true,
						showDelete: true
					},
				],
				activeRadio: '',
				editor
			};
		},
		methods: {
			editItem(index) {
				// 编辑逻辑，比如弹出编辑框等
				console.log('编辑第', index, '项');
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/Customize/certificates'
				})
			},
			deleteItem(index) {
				this.recruitmentList.splice(index, 1);
			},
			confirmAction() {
				// 确定操作逻辑
				console.log('执行确定操作');
			},
			chang(e) {
				this.activeRadio = e.detail.value;
				console.log(this.activeRadio);
			},
			add(e) {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/Customize/certificates'
				})
			}
		}
	};
</script>

<style scoped>
	.container {
		/* padding: 20rpx; */
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		background-color: rgba(245, 245, 247, 1);
	}

	.context {
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}


	.recruitment-item {
		width: 100%;
		margin-bottom: 42rpx;
	}

	.item-recrutment {
		display: flex;
		background-color: #fff;
		border-radius: 24rpx;
		/* padding: 20rpx; */
		margin-bottom: 20rpx;
		align-items: center;
		width: 686rpx;
		height: 272rpx;
	}

	.scroll-view_H {
		/* white-space: nowrap; */
		width: 100%;
	}

	.item-box {
		width: 686rpx;
		height: 272rpx;
		display: flex;
		align-items: center;
	}

	.item-header {
		/* width: 306rpx; */
		height: 152rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 10rpx;
		margin-top: 32rpx;
	}

	.title {
		font-size: 28rpx;
		/* font-weight: bold; */
		background-color: rgba(248, 248, 248, 1);
		padding: 12rpx 16rpx;
	}

	.time {
		font-size: 28rpx;
		color: #999;
	}

	.item-content {
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
		align-items: flex-start;
		flex: 1;
	}

	.item-context {
		width: 562rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
		height: 100%;
		margin-left: 32rpx;
	}

	.tag {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 174rpx;
		height: 64rpx;
		background-color: rgba(243, 243, 243, 1);
		color: rgba(51, 51, 51, 1);
		font-size: 28rpx;
		border-radius: 8rpx;
	}

	.tag-inner {
		width: 30rpx;
		height: 30rpx;
		border: 2rpx solid #ccc;
		border-radius: 50%;
		margin-right: 10rpx;
	}

	.checked {
		background-color: #007AFF;
	}

	.tag-text {
		font-size: 30rpx;
	}

	.description {
		/* flex: 1; */
		width: 562rpx;
		display: flex;
		justify-content: flex-end;
	}

	.edit-icon {
		width: 40rpx;
		height: 40rpx;
		margin-left: 10rpx;
	}

	.delete-btn {
		background-color: #FF5050;
		color: #fff;
		text-align: center;
		border-radius: 24rpx;
		padding: 48rpx;
		/* width: 112rpx; */
		height: 76rpx;
		margin-left: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.description img {
		width: 32rpx;
		height: 32rpx;
	}

	.add {
		color: rgba(79, 140, 240, 1);
		font-size: 26rpx;
	}

	.description-text {
		font-size: 28rpx;
		display: flex;
		/* 		justify-content: space-between;
		align-items: center; */
		width: 562rpx;

	}

	.description-text text {
		margin-right: 24rpx;
		background-color: rgba(248, 248, 248, 1);
		padding: 12rpx 16rpx;
	}
</style>