<template>
	<view class="page-container">
		<view class="content">
		</view>
		<!-- 遮罩层和弹窗 -->
		<view v-if="showModal" class="modal-mask" @click="closeModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text style="font-size: 28rpx;font-weight: bold;color: rgba(51, 51, 51, 1);">上传注意事项</text>
					<image class="qr-code" :src="close" @click="closeModal"></image>
				</view>
				<view class="modal-body">
					<text>1、请上传清晰且完整的图片；</text>
					<text>2、请上传与品牌相关的图片，含有其他无关内容将无法通过审核（包括但不限于无关水印、招聘信息、联系方式、二维码等）；</text>
					<text>3、上传图片须符合中国相关法律法规，不得含有违法内容或不良信息。</text>
				</view>
				<view class="modal-footer">
					<button type="primary" @click="closeModal" style="width: 160rpx; height: 52rpx;background-color: rgba(35, 112, 238, 1);
						 border-radius: 8rpx; font-size: 24rpx; display: flex; align-items: center;
						 justify-content: center;">我知道了</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/*import close from '../../static/images/close.png'*/
	export default {
		data() {
			return {
				close: 'https://api-test.zhaopinbei.com/storage/uploads/images/oTSzUI2bt0qYqMFDRcEudXVx3LrlynCxZAC2jWKD.png',
				showModal: true // 控制弹窗显示隐藏，这里默认显示，实际使用中可根据业务逻辑修改
			};
		},
		methods: {
			closeModal() {
				this.showModal = false;
			}
		}
	};
</script>

<style>
	/* 页面整体容器
	.page-container {
		padding: 20rpx;
	} */

	/* 遮罩层样式 */
	.modal-mask {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 999;
	}

	/* 弹窗内容样式 */
	.modal-content {
		background-color: white;
		padding: 20rpx;
		border-radius: 10rpx;
		width: 602rpx;
		max-width: 600rpx;
		height: 508rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}

	/* 弹窗头部样式 */
	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	/* 二维码样式 */
	.qr-code {
		width: 32rpx;
		height: 32rpx;
	}

	/* 弹窗主体内容样式 */
	.modal-body {
		margin-bottom: 20rpx;
		color: rgba(119, 119, 119, 1);
		font-size: 24rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
		flex: 1;
		height: 288rpx;
	}

	/* 弹窗底部样式 */
	.modal-footer {
		align-self: flex-end;
	}
</style>
