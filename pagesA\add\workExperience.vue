<template>
	<view class="home-index">
		<view class="wrap">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						公司名称
					</view>
					<view class="in">
						<u--input placeholder="请输入公司名称" placeholderClass="placeholderClass" clearable border="none"
							v-model="items.company"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						职务名称
					</view>
					<view class="in">
						<u--input placeholder="请输入职务名称" placeholderClass="placeholderClass" clearable border="none"
							v-model="items.job_name"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						开始时间
					</view>
					<view class="in se">
						<picker mode="date" :value="items.start_date" @change="bindStartDateChange">
							<view class="d-picker">{{items.start_date?items.start_date:"请选择开始时间"}}</view>
						</picker>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						结束时间
					</view>
					<view class="in se">
						<picker mode="date" :value="items.end_date" @change="bindEndDateChange">
							<view class="d-picker">{{items.end_date?items.end_date:"请选择结束时间"}}</view>
						</picker>
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						所属行业
					</view>
					<view class="in">
						<u--input placeholder="请输入所属行业" placeholderClass="placeholderClass" clearable border="none"
							v-model="items.industry"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						联系人
					</view>
					<view class="in">
						<u--input placeholder="请输入联系人" placeholderClass="placeholderClass" clearable border="none"
							v-model="items.contact_name"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						联系方式
					</view>
					<view class="in">
						<u--input placeholder="请输入联系方式" placeholderClass="placeholderClass" clearable border="none"
							v-model="items.contract_cellphone"></u--input>
					</view>
				</view>
			</view>

			<view class="inp">
				<view class="inp-item">
					<view class="title supTitle">
						工作经历
					</view>
					<view class="txt">
						<u--textarea v-model="items.content" placeholderClass="placeholderClass" border="none"
							placeholder="请输入工作经历"></u--textarea>
					</view>

				</view>
			</view>

			<view class="inp">
				<view class="inp-item">
					<view class="title supTitle">
						突出业绩
					</view>
					<view class="txt">
						<u--textarea v-model="items.achievement" border="none" placeholder="请输入突出业绩"></u--textarea>
					</view>

				</view>
			</view>
		</view>

		<view class="footer">
			<view class="next pub" @click="save">
				保存
			</view>
		</view>
	</view>
</template>

<script>
	import {
		addResume,
		updateLoginInfo,
		getResumeDetails
	} from "../../config/api.js"
	export default {
		data() {
			return {
				job_log: [],
				items: {
					achievement: "",
					company: "",
					contact_name: "",
					content: "",
					contract_cellphone: "",
					end_date: "",
					start_date: "",
					industry: "",
					job_name: "",
					date: ["", ""],
					
				},
				index: 0,
				flag: '',
				id: '',
				startTime: "2024-01-01",
				endTime: "2024-01-01",
			}
		},
		onLoad(options) {
			let flag = options.flag || ''
			if (flag == 'edit') {
				this.index = options.index
			}
			this.id = options.id
			this.flag = options.flag
			this.getResumeDetails()

		},
		methods: {
			bindStartDateChange(e) {
				console.log(e)
				this.items.start_date = uni.$u.timeFormat(e.detail.value, 'yyyy-mm-dd');
				console.log(this.items.start_date)
				this.items.date[0] = this.items.start_date
			},

			bindEndDateChange(e) {
				this.items.end_date = uni.$u.timeFormat(e.detail.value, 'yyyy-mm-dd');
				this.items.date[1] = this.items.end_date
			},


			//简历详情
			async getResumeDetails() {
				let params = {
					id: this.id
				}
				const {
					status_code,
					data
				} = await getResumeDetails(params)
				if (status_code == 200) {
					this.details = data;
					if (this.flag == 'edit') {
						this.items = this.details.member_resume['job_log'][this.index]
					}

					this.job_log = this.details.member_resume['job_log']
					console.log("简历信息：", this.job_log)
				}
			},
			
			

			check(obj) {
				let self = this;
				return Object.values(obj).some(value => {
					return value === '' || value === null
				});
			},

			async save() {
				let isEmpty = this.check(this.items)
				if(isEmpty) return uni.$u.toast('填写完整信息')
				console.log("是否为空：",isEmpty)
				if (this.flag == 'edit') {
					this.job_log.splice(this.index, 1, this.items)
				} else if (this.flag == 'add') {
					this.job_log.push(this.items)
				}

				let params = {
					id: this.id,
					job_log: this.job_log
				}
				
				console.log("参数：", params)

				const result = await addResume(params)
				if (result.status_code == 200) {
					let loginInfo = await updateLoginInfo()
					if (loginInfo.status_code == 200) {
						this.$store.commit('setUserInfo', loginInfo.data)
						return uni.$u.toast('保存信息成功')
					}
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding-bottom: 170rpx;
	}

	.wrap {
		padding: 30rpx;

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;
			margin-bottom: 32rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				.txt {
					padding: 24rpx 0;

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}
				}

				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}
				}

				.supTitle {
					font-weight: 500;
					font-size: 32rpx;
					color: #333333;
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					height: 88rpx;
					font-size: 32rpx;

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}

					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.footer {
		display: flex;
		justify-content: space-around;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 158rpx;
		background-color: #FFFFFF;
		z-index: 10;
		border-radius: 16rpx 16rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}

		.save {
			border: 1px solid #4F8CF0;
			color: #4F8CF0;
		}

		.pub {
			background: #4F8CF0;
			border: 1px solid #4F8CF0;
			color: #FFFFFF;
		}
	}
</style>