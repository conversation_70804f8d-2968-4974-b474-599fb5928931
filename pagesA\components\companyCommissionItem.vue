<template>
    <view class="wrap" @click.stop="goDetails(item.authorized_user.id)">
        <view class="item">
            <view class="item-up">
                <image :src="item.authorized_member_info.image.thumbnail_path_url" mode=""></image>
                <view class="info">
                    <view class="user">
                        <view class="userInfo">
                            <view class="name">
                                {{ item.authorized_member_certification.name }}
                            </view>
                        </view>
                    </view>
                    <view class="flag">
                        {{ item.company.name }}
                    </view>
                </view>
            </view>
        </view>
        <u-line color="#F5F5F7" length="100%"></u-line>
        <company-tag :item="item" :more="true" @more="clickMore(item)"></company-tag>

    </view>
</template>

<script>
    import CompanyTag from "../../public_label/companyTag.vue";

    export default {
        name: "companyCommissionItem",
        components: {CompanyTag},
        props: {
            item: {
                type: Object,
                default: () => {
                }
            }
        },
        data() {
            return {};
        },
        methods: {
            goDetails(id) {
                uni.navigateTo({
                    url: '/pagesA/details/obtainItemDetails?id=' + id
                })
            },
            clickMore(item) {
                this.$emit('more', {
                    item,
                    show: true
                })
            }
        }
    }
</script>


<style lang="less" scoped>
    .wrap {
        display: flex;
        flex-direction: column;
        padding: 0 32rpx;
        margin-bottom: 32rpx;
        background-color: #FFFFFF;
        border-radius: 24rpx;
    }

    .item {
        display: flex;
        flex-direction: column;
        position: relative;
        padding: 24rpx 0;
        .item-up {
            display: flex;
            flex: 1;
            & > image {
                width: 96rpx;
                height: 96rpx;
            }

            .info {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                flex: 1;
                padding-left: 24rpx;

                .user {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .userInfo {
                        display: flex;
                        align-items: center;

                        .name {
                            display: flex;
                            align-items: center;
                            font-weight: 600;
                            font-size: 32rpx;
                            color: #333333;
                            image {
                                margin-left: 16rpx;
                                width: 32rpx;
                                height: 32rpx;
                            }
                        }
                    }
                }

                .desc {
                    display: flex;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #999999;
                    margin-top: 16rpx;

                    .desc-item {
                        border-right: 1px solid #999999;
                        padding: 0 12rpx;

                        &:first-child {
                            padding-left: 0;
                        }

                        &:last-child {
                            border-right: none;
                        }
                    }
                }

                .flag {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #999999;
                }
            }
        }

        .item-down {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20rpx;
            .tags {
                display: flex;

                .tag {
                    display: flex;
                    align-items: center;
                    background: #F6F6F6;
                    border-radius: 8rpx;
                    height: 46rpx;
                    padding: 0 12rpx;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #666666;
                    margin-right: 16rpx;
                }
            }

            .dot {
                transform: rotate(90deg);
            }
        }

        .arrow {
            position: absolute;
            right: 0;
            top: 50%;
        }
    }

    .flexRow {
        display: flex;
        flex-direction: row;
    }
</style>