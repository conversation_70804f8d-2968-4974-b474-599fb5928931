<template>
	<view class="item" @click="goDetails">
		<!-- <view :class="['status',item.status==1?'ybm':item.status==2?'yqx':'byq']">
            {{item.status==1?'已报名':item.status==2?'已取消':'已邀约'}}
        </view> -->
		<view v-show="item.job_basic.type=='job'">
			<view class="status yqx" v-if="item.report_status=='cancel'">
				已取消
			</view>
			<view class="status ybm" v-if="item.report_status=='reported'">
				已报名
			</view>
			<view class="status Yellow" v-if="item.report_status=='interview'">
				已邀约
			</view>
			<view class="status byq" v-if="item.report_status=='pass'">
				通过
			</view>
		</view>
		<!-- 活动报名 -->
		<view v-show="item.job_basic.type=='job_active'">
			<view class="status ybm">
				待开始
			</view>
			<view class="status yqx" v-if="item.model_type.active_status=='end'">
				已结束
			</view>
			<view class="status Yellow">
				审核中
			</view>
			<view class="status byq">
				进行中
			</view>
			<view class="status red">
				已驳回
			</view>
			<view class="status yqx">
				未开始
			</view>
		</view>
		<!-- <view class="status yqx">
			{{item.report_status_name}}
		</view> -->
		<view class="up">
			<!-- 普通报名 -->
			<view class="" v-show="item.job_basic.type=='job'">
				<view class="info">
					<view class="name">
						{{item.job_basic.title}}
					</view>
					<view class="money">
						{{item.job_basic.salary_info_str}}
					</view>
				</view>
				<view class="comp">
					<image :src="item.company_info.logo?item.company_info.logo['path_url']:''" mode=""></image>
					<view class="name">
						{{item.job_basic.company_name}}
					</view>
				</view>
			</view>
			<!-- 活动报名 -->
			<view class="active" v-if="item.job_basic.type=='job_active'">
				<image style="width: 204rpx;height: 100rpx;margin-right: 24rpx;" :src="item.model_type.image.path_url"
					mode="aspectFill">
				</image>
				<view>
					<view class="name">
						{{item.model_type.title}}
					</view>
					<view class="time">
						{{item.model_type? item.model_type.start.replace("-","年").replace("-","月")+'日' : ''}}-
						{{item.model_type? item.model_type.end.replace("-", "年").replace("-", "月")+'日':''}}
					</view>
				</view>
			</view>
			<!-- 就业管家 -->
			<!-- 			<view style="display: flex;">
				<u-avatar :src="src"></u-avatar>
				<view class="" style="margin-left: 16rpx;">
					<view class="" style="display: flex;justify-content: center">
						<view style="font-size: 40rpx;margin-right: 16rpx;">王哈哈</view>
						<u-tag size="mini" text="已认证" plain plainFill> </u-tag>
					</view>
					<view style="font-size: 32rpx;">
						web前端
					</view>
				</view>
			</view> -->
			<view class="hd">
				<!-- <view class="time">
					面试时间：{{item.reported_at}}
				</view> -->
				<view class="time" v-if="item.job_basic.type=='job_active'">
					报名时间：{{item.model_type.updated_at}}
				</view>

				<!-- 就业管家 -->
				<!-- <view class="time">
					河南省郑州市金水区东风路16号
				</view> -->

				<!-- <view class="name" v-if="item.model_type">
					活动：{{ item.model_type.title }}
				</view> -->
			</view>

			<view class="sign-name" v-if="item.job_basic.type=='job'">
				<view class="lab">
					报名人：
				</view>
				<view class="lab">
					{{ item.member_certification.name ? item.member_certification.name : item.member.name }}
				</view>
				<!-- <view class="name">
                    {{item.job_basic.contact}}
                </view> -->
			</view>
		</view>
		<view :class="['down',item.report_status=='reported'?'flexEnd':item.report_status==2?'cancel':'']">
			<view class="reason" v-if="item.report_status=='cancel'">
				取消原因：{{item.cancel_reason}}
			</view>
			<view v-else></view>
			<view class="btnBox">
				<view class="btn grey" @click.stop="cancel" v-if="item.but_status.cancel_status==1">
					取消报名
				</view>

				<view class="btn talk" @click.stop="communicate('job_report',item.id)">
					聊聊呗
				</view>
			</view>
		</view>
		<u-modal :show="show" title="是否取消该职位报名" @cancel="show=false" :showCancelButton='true'></u-modal>
	</view>
</template>

<script>
	import {
		communicate
	} from "../../common/common.js";
	export default {
		name: "signUpItem",
		props: {
			itemIndex: {
				type: Number,
				default: 0
			},
			item: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
				show: false
			};
		},
		methods: {
			communicate,
			goDetails() {
				uni.navigateTo({
					url: "/pagesA/details/memberJobDetails?id=" + this.item.job_id
				})
			},
			cancel() {
				var _this = this;
				this.$emit('cancelSignup', {
					item: _this.item,
					index: _this.itemIndex
				});
				// if(){
				// 	this.show=true;
				// }
			},
			Confirm() {

			}
		}
	}
</script>

<style lang="scss" src="../../static/css/pagesA/components/signUpItem.scss"></style>