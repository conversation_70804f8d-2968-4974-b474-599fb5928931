<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="title">选择公司</view>
      <view class="subtitle"
        >请选择求职者真实工作的用工方，如果您为蓝领职位二手单业务，请选择此职位的最终用工方。</view
      >
    </view>

    <!-- 搜索框 -->
    <view class="search-box">
      <u-search
        v-model="keyword"
        :show-action="false"
        placeholder="搜索公司名称"
        height="80rpx"
        @search="getList"
        maxlength="20"
        shape="square"
        :clearabled="false"
        bgColor="#fff"
        searchIconSize="0"
      ></u-search>
      <text class="count">{{ keyword.length }}/20</text>
    </view>

    <!-- 公司列表 -->
    <view class="company-list">
      <view
        class="company-item"
        v-for="(item, index) in page.data"
        :key="index"
        @click="selectCompany(item)"
      >
        <image class="company-logo" :src="item.logo" mode="aspectFill"></image>
        <view class="company-info">
          <view class="company-name">
            {{ item.authorize_company.name }}
            <text class="tag">自招</text>
          </view>
          <view class="company-type">{{ item.authorize_company.name }}</view>
          <view class="tags"> 人力资源服务 </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next" @click="show = true"> 新增客户公司 </view>
    </view>
    <u-modal
      :show="show"
      title="确定要新增客户公司"
      content="此操作会中断职位发布，确定要新增客户公司吗？"
      confirmText="确定"
      cancelText="我再想想"
      showCancelButton
      @confirm="addNewCompany"
      @cancel="show = false"
    ></u-modal>
  </view>
</template>

<script>
import { getHeadhuntersAuthEnterpriseList } from "@/config/api";

export default {
  data() {
    return {
      page: {
        form: {
          page: 1,
          limit: 10,
          status: 1,
          cancel_status: 2,
        },
        status: "loadmore",
        more: false,
        data: [],
      },
      selectedItems: [], // 用于存储选中的项
      type: 2,
      keyword: "",
      show: false,
    };
  },

  onLoad() {
    this.getList();
  },

  //触底加载更多
  onReachBottom() {
    if (this.page.more) {
      this.status = "loading";
      this.page.form.page++;
      this.getList();
    }
  },

  methods: {
    selected() {},

    initPage() {
      this.page.data = [];
      this.page.form.page = 1;
      this.page.status = "loadmore";
    },

    getList() {
      getHeadhuntersAuthEnterpriseList(this.page.form).then((response) => {
        if (response.status_code == "200") {
          this.page.more = response.data.more;
          this.page.data = this.page.data.concat(response.data.data);
          this.page.status = this.page.more ? "loadmore" : "nomore";
        }
      });
    },

    selectCompany(item) {
      //   this.selectedItems.some((selected) => selected.id === item.id);
      this.$store.commit("setCompanyAuthorize", [item]);
      uni.navigateBack();
    },
    addNewCompany() {
      uni.navigateTo({
        url: "/pagesA/add/pubJobCompany/add",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
  padding: 32rpx;
}

.header {
  margin-bottom: 32rpx;

  .title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 24rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

.search-box {
  position: relative;
  margin-bottom: 32rpx;

  .count {
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24rpx;
    color: #999;
  }
}

.company-list {
  .company-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 32rpx;
    display: flex;
    align-items: center;
    margin-bottom: 32rpx;

    .company-logo {
      width: 136rpx;
      height: 136rpx;
      border-radius: 8rpx;
      margin-right: 24rpx;
    }

    .company-info {
      flex: 1;

      .company-name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 4rpx;
        display: flex;
        align-items: center;

        .tag {
          font-size: 20rpx;
          color: #fff;
          background-color: #4f8cf0;
          padding: 4rpx 12rpx;
          border-radius: 4rpx;
          margin-left: 16rpx;
        }
      }

      .company-type {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 12rpx;
      }
      .tags {
        width: 176rpx;
        height: 50rpx;
        background: #f4f4f4;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-size: 24rpx;
        color: #777777;
        text-align: center;
        line-height: 50rpx;
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  border-radius: 24rpx 24rpx 0 0;

  .next {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    width: 90%;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
    margin-top: 24rpx;
  }
}
</style>
