<template>
  <view class="container">
    <view class="report-list">
      <view
        class="report-item"
        v-for="(item, index) in reportList"
        :key="index"
      >
        <view class="report-header">
          <view class="report-title">职位虚假</view>
          <view class="report-detail" @click="viewDetail(item.id)"
            >查看详情</view
          >
        </view>
        <view class="report-time">举报时间：{{ item.reportTime }}</view>
        <u-line color="#e6e6e6"></u-line>
        <view class="reporter-info">
          <image
            class="reporter-avatar"
            :src="item.avatar"
            mode="aspectFill"
          ></image>
          <view class="reporter-detail">
            <view class="reporter-name">{{ item.name }}</view>
            <view class="reporter-desc">{{ item.description }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      reportList: [
        {
          id: 1,
          title: "职位虚假",
          reportTime: "2025-04-15 17:13:23",
          name: "王哈哈",
          description: "北京学创联盟 · 招聘者",
          avatar: "/static/images/default-avatar.png",
        },
        {
          id: 2,
          title: "职位虚假",
          reportTime: "2025-04-16 17:13:23",
          name: "王哈哈",
          description: "北京学创联盟 · 招聘者",
          avatar: "/static/images/default-avatar.png",
        },
      ],
    };
  },
  methods: {
    viewDetail(id) {
      uni.navigateTo({
        url: "/pagesC/complaint/detail",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f7;
  min-height: 100vh;
  padding-bottom: 20rpx;
}

.report-list {
  padding: 0;
}

.report-item {
  background-color: #ffffff;
  margin-bottom: 2rpx;
  padding: 32rpx;
  border-bottom: 12rpx solid #f5f5f7;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.report-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.report-detail {
  font-size: 28rpx;
  color: #4f8cf0;
}

.report-time {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 24rpx;
}

.reporter-info {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
}

.reporter-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  margin-right: 20rpx;
}

.reporter-detail {
  display: flex;
  flex-direction: column;
}

.reporter-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.reporter-desc {
  font-size: 24rpx;
  color: #999999;
}
</style>
