<template>
    <view id="app">
        <view class="releaseBox">
            <view class="releaseBox_top">
                <view class="releaseBox_top_left">
                    <image :src="details.member_info.image.path_url" mode="" class="releaseBox_head"></image>
                    <view>{{details.member_certification.name}}</view>
                </view>
                <view class="releaseBox_top_right">{{details.member.cellphone}}</view>
            </view>
            <view class="releaseBox_bottom">
                <view>已招人数</view>
                <view class="blueNum">{{details.need_count}}</view>
            </view>
        </view>


        <view class="contentBox">
            <view class="gridItem" v-for="(userPromotion,index) in userPromotions" :key="index" :item="userPromotion">
                <view class="gridItem_top">
                    <image :src="userPromotion.member_info.image.path_url" mode="" class="peopleHeadCla"></image>
                    <view>
                        <view class="peopleNameCla">{{ userPromotion.member_certification.name }}</view>
                        <view class="peopleTextCla">
                            {{ userPromotion.member_info.sex_str }}
                            | {{ userPromotion.member_info.education_type_name }}
                            | {{ userPromotion.member_info.age }}岁
                        </view>
                    </view>
                </view>

                <view class="gridItem_btm">
                    <view>当前状态：</view>
                    <view class="gridItem_btm_color">{{ userPromotion.audit_status_name }}</view>
                </view>

                <view class="gridItem_btm">
                    <!-- <view>时间：</view> -->
                    <view>{{ userPromotion.created_at }}</view>
                </view>
                <!--gridItemBtn_blue 审核 已入职  gridItemBtn_grey 未满足 已满足 -->
                <view class="gridItemBtn gridItemBtn_blue" @click="showActionSheet(userPromotion,index)">审核</view>

            </view>
        </view>
        <u-action-sheet :actions="list" :closeOnClickOverlay="true" :closeOnClickAction="true"
                        :show="show"></u-action-sheet>
    </view>
</template>

<script>
    import {getTaskUserDetail, taskUserPromotions} from '../../config/headhunterList_api.js'
    import {auditPromotions} from "../../config/headhunterList_api";

    export default {
        data() {
            return {
                options: [
                    {
                        'label': "未审核",
                        'value': 'none'
                    },
                    {
                        'label': "已入职",
                        'value': 'entry'
                    },
                    {
                        'label': "未入职",
                        'value': 'un_entry'
                    },
                    {
                        'label': "已满足条件",
                        'value': 'need'
                    },
                    {
                        'label': "未满足条件",
                        'value': 'un_need'
                    },
                ], // 选项列表
                details: {},
                userPromotions: [],
                taskUserId: 0,
            }
        },
        onLoad(option) {
            this.taskUserId = option.id;
            this.getTaskUserDetail()
            this.taskUserPromotions()
        },
        methods: {
            async taskUserPromotions() {
                var _this = this;
                let params = {
                    task_user_id: this.taskUserId
                }
                await taskUserPromotions(params).then(response => {
                    _this.userPromotions = response.data;
                })

            },
            async getTaskUserDetail() {
                let params = {
                    task_user_id: this.taskUserId
                }
                const res = await getTaskUserDetail(params)
                this.details = res.data
            },
            showActionSheet(userPromotion,ind) {
                var _options = this.options;
                uni.showActionSheet({
                    itemList: _options.map(item => {
                        return item.label;
                    }), // 显示的选项
                    success: (_index) => {
                        var _val = this.options[_index.tapIndex]['value'];

                        var params = {
                            task_promotion_id: userPromotion.id,
                            audit_status: _val,
                        };
                        
                        auditPromotions(params).then(response => {
                            console.log('替换前',ind)
                            if (response.status_code == '200') {
                                this.userPromotions.splice(ind,1,response.data)
                                return uni.$u.toast('审核成功');
                            } else {
                                return uni.$u.toast(response.message);
                            }
                        });
                    },
                    fail: (err) => {
                    },
                });
            },
        }
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .releaseBox {
        padding: 32rpx;
        background: #FFFFFF;
        border-radius: 24rpx;
        margin-bottom: 32rpx;
    }

    .releaseBox_top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2rpx solid #F5F5F7;
        padding-bottom: 24rpx;
    }

    .releaseBox_head {
        width: 48rpx;
        height: 48rpx;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        margin-right: 24rpx;
    }

    .releaseBox_top_left {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
    }

    .releaseBox_top_right {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .releaseBox_bottom {
        margin-top: 24rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }

    .blueNum {
        color: #4F8CF0;
    }

    .contentBox {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24rpx 22rpx;
    }

    .gridItem {
        width: 332rpx;
        height: 320rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 24rpx;
    }

    .gridItem_top {
        display: flex;
        align-items: center;
        border-bottom: 2rpx solid #F5F5F7;
        padding-bottom: 16rpx;
        margin-bottom: 16rpx;
    }

    .peopleHeadCla {
        width: 82rpx;
        height: 82rpx;
        background: #D9D9D9;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        margin-right: 16rpx;
    }

    .peopleNameCla {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 8rpx;
    }

    .peopleTextCla {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .gridItem_btm {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: nowrap;
        margin-bottom: 16rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .gridItem_btm_color {
        color: #333333;
    }

    .gridItemBtn {
        width: 100%;
        height: 56rpx;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 56rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
    }

    .gridItemBtn_blue {
        background: #4F8CF0;
    }

    .gridItemBtn_grey {
        background: #CCCCCC;
    }
</style>