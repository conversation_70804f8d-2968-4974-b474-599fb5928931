<template>
	<view class="item" @click="goDetails(item.id)">
		<view class="item-up">
			<image :src="item.member_info.image.thumbnail_path_url" mode=""></image>
			<view class="info">
				<view class="user">
					<view class="userInfo">
						<view class="name">
							{{item.member_certification.name}}
						</view>
					</view>
				</view>
				<view class="flag">
					{{item.company.name}}
				</view>
			</view>
		</view>


		<view class="item-down">
			<view class="tags">
				<view class="tag" v-for="v in item.user_headhunter.label">
					{{v}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "obtainItem",
        props: {
        	item: {
        		type: Object,
        		default: () => {}
        	}
        },
		data() {
			return {

			};
		},
        methods:{
            goDetails(id) {
                uni.navigateTo({
                	url:'/pagesA/details/obtainItemDetails?id='+id
                })
            }
        }
	}
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		padding: 32rpx;
		margin-bottom: 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;

		.item-up {
			display: flex;

			&>image {
				width: 88rpx;
				height: 88rpx;
				border-radius: 50%;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				flex: 1;
				padding-left: 24rpx;

				.user {
					display: flex;
					justify-content: space-between;
					align-items: center;

					.userInfo {
						display: flex;
						align-items: center;

						.name {
							font-weight: 600;
							font-size: 32rpx;
							color: #333333;
						}
					}
				}

				.flag{
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}



		.item-down {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 20rpx;
			.tags {
				display: flex;
			
				.tag {
					display: flex;
					align-items: center;
					background: #F6F6F6;
					border-radius: 8rpx;
					height: 46rpx;
					padding: 0 12rpx;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin-right: 16rpx;
				}
			}
		}
	}
</style>