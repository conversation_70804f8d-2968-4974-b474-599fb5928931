<template>
    <view class="">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="search-wrap">
                    <u-search placeholder="请输入招聘会名称" bgColor="#FFFFFF" :showAction="false"
                              v-model="keyword"></u-search>
                </view>
                <view class="filters">
                    <view class="filter" @click="selectCity">
                        {{userCity.name}}
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <!-- <view class="filter">
                        审核状态
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view> -->
                </view>
            </view>
        </u-sticky>
        <view class="list">
            <company-jobfair-item v-for="(item,index) in page.data" :key="index" :item="item"></company-jobfair-item>
            <Pages :status="page.status"></Pages>
        </view>
    </view>
</template>

<script>
    import {
        getCompanyJobfairList
    } from "../../config/api.js"
    import CompanyJobfairItem from '../components/companyJobfairItem.vue'

    export default {
        components: {
            CompanyJobfairItem
        },

        data() {
            return {
                userCity: {
                    name: '全国',
                    id: 0
                },
                page: {
                    form:{
                        limit: 10,
                        page: 1,
                        city_id:0
                    },
                    data: [],
                    more: false,
                    status: 'nomore',
                }
            }
        },
        onLoad() {
            var _this = this;
            _this.userCity = uni.getStorageSync('userCity');
            _this.page.form.city_id = _this.userCity.id
            _this.page.form.page = 1;
            _this.page.data = [];
            _this.getList();

        },
        onShow() {
            // 用户选择的城市从本地获取name、id
            if (this.userCity.name != uni.getStorageSync('userCity').name) {
                this.userCity = uni.getStorageSync('userCity');
                this.page.form.city_id = this.userCity.id
                this.page.form.page = 1;
                this.page.data = [];
                this.getList();
            }
        },
        onReachBottom() {
            var _this = this;
            if (_this.page.more) {
                _this.page.form.page++;
                _this.getList()
            }
        },
        methods: {
            selectCity() {
                uni.navigateTo({
                    url: '/pagesA/components/selectCitys'
                })
            },
            getList() {
                var _this = this;
                getCompanyJobfairList(this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.more ? 'loading' : 'nomore';
                    }
                });
            },
        }
    }
</script>
<style>
    page {
        background: #f5f5f7;
    }
</style>
<style lang="less" scoped>
    .header {
        // .tabs {
        // 	background: #FFFFFF;
        // 	border-radius: 0 0 24rpx 24rpx;
        // }
        padding: 32rpx;

        .search-wrap {
            padding: 0 32rpx;
            margin-bottom: 22rpx;
        }

        .filters {
            display: flex;
            align-items: center;
            // padding: 0 32rpx 32rpx 32rpx;

            .filter {
                display: flex;
                align-items: center;
                height: 48rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 0 12rpx;
                margin-right: 12rpx;
                border-radius: 8rpx;
                background-color: #FFFFFF;

                image {
                    width: 24rpx;
                    height: 24rpx;
                }
            }

        }
    }

    .list {
        padding: 0 32rpx;
    }

    .credit {
        display: flex;
        flex-direction: column;
        padding: 40rpx 48rpx;

        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .tip {
                image {
                    width: 48rpx;
                    height: 48rpx;
                }
            }
        }

        .comp {
            justify-content: center;
        }

        .content {
            display: flex;
            flex-direction: column;
            margin-top: 40rpx;

            .sub-title {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 28rpx;
                color: #000000;
            }
        }

        .agree {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 44rpx;
            height: 88rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #FFFFFF;
            background: #4F8CF0;
            margin-top: 32rpx;
        }
    }
</style>