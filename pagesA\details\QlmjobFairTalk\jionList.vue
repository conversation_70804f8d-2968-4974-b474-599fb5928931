<template>
	<view class="container">
		<uni-section type="line">
			<view class="example">
				<uni-forms ref="baseForm" :modelValue="baseFormData" label-position="top">
					<uni-forms-item label="学校" required>
						<uni-easyinput type="textarea" class="jobList-schoolname" v-model="baseFormData.introduction"
							placeholder="请输入学校名称" />
					</uni-forms-item>
					<uni-forms-item label="学校类型" required>
						<uni-data-checkbox v-model="baseFormData.schoolstyle" :localdata="schoolstyle" />
					</uni-forms-item>
					<uni-forms-item label="年级" required>
						<uni-data-checkbox v-model="baseFormData.style" :localdata="style" />
					</uni-forms-item>
					<uni-forms-item label="姓名" required>
						<uni-easyinput v-model="baseFormData.name" placeholder="请输入姓名" />
					</uni-forms-item>
					<uni-forms-item label="年龄" required>
						<uni-easyinput v-model="baseFormData.age" placeholder="请输入年龄" />
					</uni-forms-item>
					<uni-forms-item label="性别" required>
						<uni-data-checkbox v-model="baseFormData.sex" :localdata="sexs" />
					</uni-forms-item>
					<uni-forms-item label="联系方式" required>
						<!-- 新增一个数据绑定用于联系方式 -->
						<uni-easyinput v-model="baseFormData.contact" placeholder="请输入联系方式" />
					</uni-forms-item>
				</uni-forms>
			</view>
		</uni-section>
		<view class="footview">
			<view class="footer-card">
				<button class="add-button" type="primary" @click="submit('baseForm')">提交</button>
			</view>
		</view>
	</view>
</template>
<script>
	import uniForm from '../../../uni_modules/uni-forms/components/uni-forms/uni-forms.vue';
	import uniCard from '../../../uni_modules/uni-card/components/uni-card/uni-card.vue';
	import uniSection from '../../../uni_modules/uni-section/components/uni-section/uni-section.vue';
	import uniEasyinput from '../../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue';
	import uniDataCheckbox from '../../../uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue';

	export default {
		components: {
			uniForm,
			uniCard,
			uniSection,
			uniEasyinput,
			uniDataCheckbox
		},
		data() {
			return {
				// 基础表单数据
				baseFormData: {
					name: '',
					age: '',
					introduction: '',
					schoolstyle: [],
					style: [],
					sex: '',
					contact: ''
				},
				// 单选数据源
				sexs: [{
						text: '男',
						value: 0
					},
					{
						text: '女',
						value: 1
					}
				],
				// 分段器数据
				current: 0,
				items: ['左对齐', '顶部对齐'],
				// 校验表单数据
				valiFormData: {
					name: '',
					age: '',
					introduction: ''
				},
				// 校验规则
				rules: {
					name: {
						rules: [{
							required: true,
							errorMessage: '姓名不能为空'
						}]
					},
					age: {
						rules: [{
								required: true,
								errorMessage: '年龄不能为空'
							},
							{
								format: 'number',
								errorMessage: '年龄只能输入数字'
							}
						]
					}
				},
				// 新增动态列表定义
				dynamicLists: [],
				schoolstyle: [{
						value: '0',
						text: '专科'
					},
					{
						value: '1',
						text: '本科'
					}
				],
				style: [{
						value: '0',
						text: '大一'
					},
					{
						value: '1',
						text: '大二'
					},
					{
						value: '2',
						text: '大三'
					}
				]
			};
		},
		methods: {
			add() {
				this.dynamicLists.push({
					label: '域名',
					rules: [{
						required: true,
						errorMessage: '域名项必填'
					}],
					id: Date.now()
				});
			},
			del(id) {
				let index = this.dynamicLists.findIndex((v) => v.id === id);
				this.dynamicLists.splice(index, 1);
			},
			submit(ref) {
				this.$refs[ref].validate().then((res) => {
					console.log('success', res);
					uni.showModal({
						title: '',
						content: '报名成功',
						confirmText: '确认',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 点击确认后的逻辑，比如页面跳转等
								console.log('用户点击了确认');
							} else if (res.cancel) {
								console.log('用户点击了取消');
							}
						}
					});
				}).catch((err) => {
					console.log('err', err);
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.example {
		padding: 0px 30rpx;
		background-color: #fff;
	}

	.footview {
		width: 100vw;
		position: relative;
	}

	.footer-card {
		background-color: white;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 1.1);
		/* 卡片阴影 */
		padding: 20px;
		height: 196rpx;

	}

	.foottext {
		color: rgba(153, 153, 153, 1);
		font-size: 24rpx;
		line-height: 28.12rpx;
	}

	.add-button {
		background-color: rgba(79, 140, 240, 1);
	}
</style>

<style>
	.uni-section .uni-section-header__decoration.line {
		display: none;
	}

	.is-input-border {
		/* border: none !important; */
		height: 64rpx;
		margin-bottom: 24rpx;
		display: flex;
		align-items: start !important;
	}

	.input-padding {
		height: 64rpx !important;
	}

	.uni-easyinput__content-textarea {
		min-height: 64rpx;
	}

	.uni-forms-item {
		margin-bottom: 0rpx !important;
	}
</style>