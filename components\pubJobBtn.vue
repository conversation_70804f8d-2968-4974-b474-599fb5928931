<template>
  <view class="">
    <view class="btn" @click="go" v-if="roleType == 'member' && userInfo.resume_count < 1 ">
      <image src="/static/images/index/pen.png" mode=""></image>
      <text>发布简历</text>
    </view>
    <view class="btn" @click="go" v-else-if="roleType == 'company' || roleType == 'headhunters'">
      <image src="/static/images/index/pen.png" mode=""></image>
      <text>发布职位</text>
    </view>
  </view>

</template>

<script>
import {isAuth, isLogin} from '@/common/common.js'

export default {
  name: "pubJobBtn",
  data() {
    return {};
  },

  computed: {
    roleType() {
      console.log('当前用户的身份：', uni.getStorageSync('roleType'))
      return this.$store.state.roleType || uni.getStorageSync('roleType')
    },
    userInfo() {
      return this.$store.state.userInfo || uni.getStorageSync('userInfo')
    }
  },

  methods: {
    go() {
    if (!isAuth(["info"])) return
      if (this.roleType == 'member') {
        console.log("用户信息：", this.userInfo)
        // if(!isLogin()) return
        // 去认证
        // if(this.userInfo.member.certification_status==2){
        // 	uni.navigateTo({
        // 		url:"/pages/my/cert",
        // 	})
        // }else{
        	uni.navigateTo({
        		url:"/pagesA/add/addResume"
        	})
        // }
        return
      }
      if (!isAuth(["info", "auth"])) return
      if (this.roleType == 'company') {
        // console.log(this.userInfo.company.certification_status)
        if (!isAuth(["company_info"])) return

        uni.navigateTo({
          url: "/pagesA/add/pubJobOne",
        })

      }else if (this.roleType == 'headhunters') {
        // console.log(this.userInfo.company.certification_status)
        if (!isAuth(["company_info"])) return

        uni.navigateTo({
          url: "/pagesA/add/pubJobOne",
        })

      }
    }
  }
}
</script>

<style lang="less" scoped>
.btn {
  position: fixed;
  z-index: 999;
  bottom: 158rpx;
  left: 50%;
  margin-left: -108rpx;
  width: 216rpx;
  height: 64rpx;
  font-size: 28rpx;
  background: #4F8CF0;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
  color: #FFFFFF;
  border-radius: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;

  image {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
}

</style>