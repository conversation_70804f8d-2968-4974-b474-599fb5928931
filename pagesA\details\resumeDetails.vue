<template>
    <view class="home-index">
        <view class="userInfo">
            <view class="info">
                <view class="userName">
                    <view class="name">
                        {{details.member_certification.name?details.member_certification.name:details.member_info.nick_name}}
                    </view>
                    <view class="dot" v-if="details.member_info.job_status_name">

                    </view>
                    <view class="status" v-if="details.member_info.job_status_name">
                        {{details.member_info.job_status_name}}
                    </view>
                    <!-- <image src="/pagesA/static/images/edit.png" mode="" @click="goResumePersonal"></image> -->
                </view>

                <view class="tags">
                    {{details.member_info.sex_str}}·{{details.member_info.age}}岁·{{details.member_info.nation}}族·{{details.member_info.education_type_name}}
                </view>

                <view class="items">
                    <view class="item">
                        <u-icon name="phone" size="28rpx" color="#333333"></u-icon>
                        <text>{{details.member.cellphone}}</text>
                    </view>
                    <!-- <view class="item">
                        <u-icon name="email" size="28rpx" color="#333333"></u-icon>
                        <text><EMAIL></text>
                    </view> -->
                </view>
            </view>
            <image :src="details.member_info.image.thumbnail_path_url" mode=""></image>
        </view>

        <view class="wrap" @click="goJobExpection">
            <view class="title">
                <view class="name">
                    <text>求职期望</text>
                    <view class="star">*</view>
                </view>

            </view>
            <view class="content" v-if="details.member_resume_job_class.name">
                <view class="cont">
                    <view class="ui">
                        <view class="expert">
                            <view class="type">
                                {{details.member_resume_job_class.name}}
                            </view>
                            <view class="money">
                                {{details.member_resume.expect_salary_k}}
                            </view>
                        </view>
                        <view class="pos">
                            <view class="addr">
                                {{details.member_resume.expect_address}}
                            </view>
                            <view class="type" v-for="(types,ind) in details.member_resume_work_types" :key="types.id">
                                {{types.name}}
                            </view>
                        </view>
                    </view>
                    <u-icon name="arrow-right" size="28rpx"></u-icon>
                </view>
            </view>
        </view>

        <view class="wrap">
            <view class="title" @click="goSelf">
                <view class="name">
                    <text>自我介绍</text>
                    <view class="star">*</view>
                </view>
                <view class="plus">
                    <u-icon name="arrow-right" size="28rpx"></u-icon>
                </view>
            </view>
            <view class="content">
                <view class="intro">
                    {{details.member_resume.introduce}}
                </view>
            </view>
        </view>

        <view class="wrap" v-if="details.member_info.education_log[0].school">
            <view class="title">
                <view class="name">
                    <text>教育经历</text>
                    <view class="star">*</view>
                    <!-- <image src="../static/images/edit.png" mode="" @click="goEduExperience"></image> -->
                </view>
                <!-- <view class="plus">
                    <image src="/static/images/index/plus.png" mode="" @click="goEduExperience"></image>
                </view> -->
            </view>

            <view class="content">
                <view class="jy-list">
                    <view class="jy-item" v-for="(item,index) in details.member_info.education_log" :key="index"
                        :item="item">
                        <view class="name">
                            {{ item.school }}
                            <!-- <u-icon name="arrow-right" size="28rpx"></u-icon> -->
                        </view>
                        <view class="time">
                            {{ item.start}}-{{ item.end }}
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <view class="wrap">
            <view class="title">
                <view class="name">
                    <text>工作经历</text>
                </view>
                <view class="plus">
                    <image src="/static/images/index/plus.png" mode="" @click="goWorkExperience('add')"></image>
                </view>
            </view>
            <view class="content">
                <view class="sub-wrap" v-for="(item,index) in details.member_resume.job_log" :key="index" v-if="item.company">
                    <view class="exper" @click="goWorkExperience('edit',index)">
                        <view class="name">
                            {{item.company}}
                        </view>
                        <view class="time">
                            <text>{{item.start_date}}-{{item.end_date}}</text>
                            <u-icon name="arrow-right" size="28rpx"></u-icon>
                        </view>
                    </view>
                    <view class="user-info">
                        <text class="user-name">{{item.contact_name}}</text>
                        <text class="mobile">{{item.contract_cellphone}}</text>
                    </view>

                    <view class="types">
                        <view class="type" v-if="item.industry">
                            {{item.industry}}
                        </view>
                        <view class="type" v-if="item.job_name">
                            {{item.job_name}}
                        </view>
                    </view>

                    <view class="list">
                        <view class="item">
                            <view class="dot">
                            </view>
                            <view class="js">
                                业绩：{{item.achievement}}
                            </view>
                        </view>

                        <view class="item">
                            <view class="dot">
                            </view>
                            <view class="js">
                                经历：{{item.content}}
                            </view>
                        </view>
                    </view>
                </view>

            </view>
        </view>


        <view class="wrap">
            <view class="title">
                <view class="name">
                    <text>资格证书</text>
                </view>
            </view>

            <view class="content">
                <view class="pic-list">
                    <block v-if="certificates && certificates.length>0">
                        <view class="pic-item" v-for="(item,index) in certificates" :key="index">
                            <image :src="item.path_url" mode=""></image>
                            <view class="zz">
                                <view class="del" @click="delCert(index)">
                                    删除
                                </view>
                            </view>
                        </view>
                    </block>

                    <view class="pic-item add" @click="uploadCert">
                        <image src="/pagesA/static/images/add.png" style="width: 40rpx;height: 40rpx;" mode=""></image>
                    </view>
                </view>
            </view>
        </view>

        <view class="footer">
            <view class="btns">
                <view class="btn zx" v-if="details.member_resume.type == 'resume'">
                    保存
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import {
        uploadImg,
        getResumeDetails,
        addResume
    } from "../../config/api.js"

    export default {
        data() {
            return {
                certificates: [],
                details: {
                    member_resume: {
                        job_log: [],
                    }
                }
            }
        },
        computed: {
            userInfo() {
                return this.$store.state.userInfo || uni.getStorageSync('userInfo')
            }
        },
        onLoad(options) {
            this.id = options.id
            this.getResumeDetails()
        },
        onShow() {
            // console.log("show方法：", options)
            // let pages = getCurrentPages();
            // // 数组中索引最大的页面--当前页面
            // let currentPage = pages[pages.length - 1];
            // let options = currentPage.options

        },
        methods: {
            goResumePersonal() {
                uni.navigateTo({
                    url: "/pagesA/add/resumePersonal"
                })
            },
            goWorkExperience(flag, index) {
                uni.navigateTo({
                    url: "/pagesA/add/workExperience?id=" + this.id + '&flag=' + flag + '&index=' + index
                })
            },
            goEduExperience() {
                uni.navigateTo({
                    url: "/pagesA/add/eduExperience"
                })
            },
            goJobExpection() {
                uni.navigateTo({
                    url: "/pagesA/add/jobExpection?id=" + this.id
                })
            },

            goSelf() {
                uni.navigateTo({
                    url: "/pagesA/add/selfIntroduction?id=" + this.id
                })
            },


            //删除
            async delCert(index) {
                this.certificates.splice(index, 1)
                let ids = this.certificates.map(item => item.id)

                let params = {
                    id: this.id,
                    certificates: ids
                }
                const {
                    status_code,
                    data
                } = await addResume(params)
                if (status_code == 200) {
                    return uni.$u.toast('成功')
                }
            },

            //上传荣誉证书
            uploadCert() {
                let self = this
                uni.chooseMedia({
                    count: 1,
                    mediaType: ['image'],
                    sizeType: ['original', 'compressed'],
                    sourceType: ['album', 'camera'],
                    success: (tempFilePaths) => {
                        const path = tempFilePaths.tempFiles[0].tempFilePath;
                        // $dialog.loading('上传中')
                        uni.getFileSystemManager().readFile({
                            filePath: path,
                            encoding: 'base64',
                            success: async function(res) {
                                let imageParams = {
                                    ext: 'png',
                                    content: res.data,
                                    org_name: new Date().getTime() + '.png'
                                }
                                // 上传
                                const result = await uploadImg(imageParams)
                                if (result.status_code == 200) {
                                    self.certificates.push({
                                        id: result.data.id,
                                        path_url: result.data.url
                                    })

                                    let ids = self.certificates.map(item => item.id)

                                    let params = {
                                        id: self.id,
                                        certificates: ids
                                    }

                                    const {
                                        status_code,
                                        data
                                    } = await addResume(params)

                                    if (status_code == 200) {
                                        // self.getResumeDetails()
                                        return uni.$u.toast('成功')
                                    }
                                }
                            }
                        })
                    }
                });
            },
            //简历详情
            async getResumeDetails() {
                let params = {
                    id: this.id
                }
                const {
                    status_code,
                    data
                } = await getResumeDetails(params)
                if (status_code == 200) {
                    this.details = data;
                    this.certificates = data.member_resume.certificates
                    console.log(this.details, "简历详情")
                }
            }
        }
    }
</script>

<style lang="scss" src="../../static/css/pagesA/details/resumeDetails.scss"></style>