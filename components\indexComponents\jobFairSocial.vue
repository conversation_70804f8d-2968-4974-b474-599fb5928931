<template>
	<view class="container">
		<u-sticky>
			<view class="tabs-container">
				<u-tabs :current="tabsIndex" :list="tabsList"
					:activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
					:inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }" @click="onTabsItemClick"></u-tabs>
			</view>
		</u-sticky>
		<view class="content">
			<template v-if="params.type === '1'">
				<view class="item-type-1" v-for="v in jobActiveList" :key="v.job_active.id"
					@click="onDetail(v.job_active.id)">
					<view class="item-start">
						<view class="title">{{v.job_active.title}}</view>
						<view class="sub-title">500/天</view>
					</view>
					<view class="item-center">
						{{v.job_active.intro}}
					</view>
					<view class="item-end">
						<view>上岗时间：{{v.job_active.start}}</view>
						<view>报名截止时间：{{v.job_active.end}}</view>
					</view>
				</view>
			</template>
			<template v-if="params.type === '2'">
				<view class="item-type-2">
					<view class="item-start">
						<image class="image"
							src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"
							mode=""></image>
						<view class="start-info">
							<text class="title">xxx公司宣讲会</text>
							<text class="time">2022年12月12日</text>
							<view class="tag">进行中</view>
						</view>
					</view>
					<view class="item-end">
						<text class="time">9月15日 08:00:00开始</text>
						<text class="detail">查看详情</text>
					</view>
				</view>
			</template>
		</view>
	</view>
</template>

<script>
	import {
		getJobActiveList
	} from '@/config';

	export default {
		data() {
			return {
				tabsIndex: 0,
				tabsList: [{
						key: '1',
						name: '特推活动',
					},
					{
						key: '2',
						name: '宣讲会',
					},
					{
						key: '0',
						name: '招聘会',
					}
				],
				jobActiveList: [],
				params: {
					limit: 20,
					page: 1,
					type: '1',
				},
				isLoading: false,
				more: true
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetJobActiveList();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetJobActiveList();
		},
		methods: {
			onDetail(id) {
				uni.$u.route({
					url: "/pagesA/details/qlmActiveDetails",
					params: {
						id
					}
				})
			},
			onInitJobActiveList() {
				this.params.page = 1;
				this.jobActiveList = [];
			},
			onTabsItemClick(res) {
				this.params.type = res.key;
				this.onInitJobActiveList();
			},
			onScrollGetList() {
				if (!this.more) return;
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetJobActiveList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getJobActiveList(params);
				if (res.status_code !== '200') return;
				this.jobActiveList = [...this.jobActiveList, ...res.data.data];
				this.more = res.data.more;
				this.isLoading = false;
			}
		},
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.tabs-container {
			padding-inline: 16rpx;
			background-color: #f5f5f7;
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding-inline: 32rpx;

			.item-type-2 {
				display: flex;
				flex-direction: column;
				align-items: center;

				.item-end {
					width: 80%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 24rpx;
					background: linear-gradient(90deg, #F5FCFC 0%, #FCFBFA 100%);
					border-end-start-radius: 16rpx;
					border-end-end-radius: 16rpx;
					font-size: 24rpx;

					.time {
						color: #333333;
					}

					.detail {
						color: #4F8CF0;
					}
				}

				.item-start {
					width: 100%;
					box-sizing: border-box;
					display: flex;
					align-items: center;
					gap: 24rpx;
					background-color: #FFFFFF;
					border-radius: 16rpx;
					padding: 32rpx;

					.start-info {
						display: flex;
						flex-direction: column;
						gap: 16rpx;
						align-items: flex-start;

						.title {
							color: #333333;
							font-size: 32rpx;
						}

						.time {
							color: #999999;
							font-size: 28rpx;
						}

						.tag {
							padding-block: 8rpx;
							padding-inline: 16rpx;
							background: rgba(87, 213, 28, 0.1);
							border-radius: 8rpx;
							color: #57D51C;
							font-size: 24rpx;
						}
					}

					.image {
						width: 196rpx;
						height: 196rpx;
					}
				}
			}

			.item-type-1 {
				display: flex;
				flex-direction: column;
				gap: 24rpx;
				background-color: #FFFFFF;
				border-radius: 24rpx;
				padding: 32rpx;

				.item-end {
					display: flex;
					align-items: center;
					justify-content: space-between;
					color: #333333;
					font-size: 24rpx;
				}

				.item-center {
					color: #666666;
					font-size: 28rpx;
				}

				.item-start {
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 32rpx;

					.title {
						color: #333333;
					}

					.sub-title {
						color: #F98A14;
					}
				}
			}
		}
	}
</style>