<template>
	<view class="industry-list">
		<view style="padding:  0rpx 32rpx;">
			<scroll-view scroll-y="true" class="scroll-Y">
				<view class="recommend-title">所在行业</view>
				<view class="recommend">推荐</view>
				<view class="tags">
					<view class="tag" v-for="(tag, index) in recommendTags" :key="index">{{tag}}</view>
				</view>
				<view class="industry-item" v-for="(industry, index) in industries" :key="index">
					<view class="industry-name" @click="selectCourse(industry)">{{industry}}</view>
					<view class="arrow"></view>
					<u-icon name="arrow-right"></u-icon>

				</view>
			</scroll-view>
		</view>
		<!-- 完成按钮 -->
		<view class="footer">
			<view class="selected-courses">
				<view
					style="width: 96rpx; height: 42rpx; margin-left: 8rpx;font-size: 24rpx;text-align: center;display: flex; align-items: center;">
					已选：
				</view>
				<scroll-view class="scroll-view_H" scroll-x="true">
					<view style="display: flex; align-items: center;">
						<view v-for="(selectedCourse, index) in selectedTags" :key="index" style="padding:6rpx 8rpx; font-size: 20rpx; background-color: rgba(232, 241, 255, 1); color: rgba(79, 140, 240, 1);
														 margin: 0rpx 8rpx; display: flex; align-items: center;">
							{{ selectedCourse }}
							<img src="/static/images/Apages/close.png" mode=""
								style="width: 24rpx; height: 24rpx; align-self: center; margin-left: 6rpx;"
								@click="removeCourse(selectedCourse)">
							</img>
						</view>
					</view>
				</scroll-view>
			</view>
			<!-- <button class="confirm-btn" @click="finish">完成</button> -->
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				recommendTags: ['餐饮', '企业服务', '食品/饮料/饮酒'],
				industries: ['互联网/AI', '电子/通信/半导体', '服务业', '消费品/批发/零售', '房地产/建筑', '教育培训', '广告/传媒/文化/体育', '制造业', '专业服务',
					'制药/医疗', '汽车', '交通运输/物流', '能源/化工/环保', '金融', '政府/非盈利机构/其他'
				],
				selectedTags: []
			};
		},
		methods: {
			selectCourse(item) {
				this.selectedTags.push(item);
				uni.navigateBack({
					delta: 1
				})
			},
			removeCourse(item) {
				this.selectedTags.splice(item, 1);
			},
		}
	};
</script>
<style>
	.industry-list {
		/* padding: 10px; */
		background-color: #fff;
		height: 100vh;
		/* padding: 0rpx 32rpx; */
	}

	.recommend-title {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
	}

	.scroll-Y {
		height: 89vh;
	}

	.recommend {
		font-size: 28rpx;
		margin-bottom: 10rpx;
	}

	.scroll-view_H {
		white-space: nowrap;
		/* flex: 1; */
		width: 76%;
	}

	.tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 20rpx;
	}

	.tag {
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		padding: 10rpx 20rpx;
		background-color: #f0f0f0;
		border-radius: 12rpx;
		font-size: 24rpx;
	}

	.industry-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 2rpx solid #e0e0e0;
	}

	.industry-name {
		font-size: 28rpx;
	}

	.selected-tags {
		display: flex;
		flex-wrap: wrap;
		margin-top: 20rpx;
	}

	.selected-tag {
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		padding: 10rpx 20rpx;
		background-color: #b3e1ff;
		border-radius: 10rpx;
		font-size: 24rpx;
	}

	.footer {
		width: 100vw;
		height: 156rpx;
		display: flex;
		justify-content: center;
		align-self: center;
		padding: 0rpx 32rpx;
	}

	.selected-courses {
		margin-top: 30rpx;
		display: flex;
		width: 100%;
	}

	.selected-course {
		background-color: #e0e0e0;
		border-radius: 10rpx;
		padding: 10rpx 15rpx;
		margin-right: 10rpx;
		margin-bottom: 10rpx;
		font-size: 28rpx;
		position: relative;
	}
</style>