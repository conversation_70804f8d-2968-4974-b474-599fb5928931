<template>
	<!-- 项目描述-->
	<view class="container">
		<view class="context">
			<!-- 输入区域 -->
			<view class="input-area">
				<view class="section">
					<input class="input" placeholder="1.可根据熟练程度展开，如精通、熟悉、了解 2.点击下方参考技能词可点击输入" v-model="inputValue"
						maxlength="1000" />
				</view>
				<view class="box">
					<view class="text-box">
						<text :class="inputValue.length == 0 ? 'active':'count'">
							{{inputValue.length}}
						</text>
						<text class="count-bottom">
							/1000</text>
					</view>
					<view class="Model">
						<view class="model-left">
							<img src="/static/jobsetting/无序列表<EMAIL>" alt=""
								style="width: 40rpx; height: 40rpx;" />
							<img src="/static/jobsetting/无序列表<EMAIL>" alt=""
								style="width: 40rpx; height: 40rpx;" />
						</view>
						<view style="display: flex; align-items: center;" @click="toggle">
							<img src="/static/jobsetting/skill.png" alt="" style="width: 40rpx; height: 40rpx;" />
							<text style="color: rgba(79, 140, 240, 1); font-size: 28rpx;">参考技能词</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<button class="confirm-btn" @click="finish">确定</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				inputValue: '',
				noticehide: true,
				hidePicker: false,
				resume: [{
						style: '项目经理1',
						content: '1、担任学院学生会副主席，协助主席开展工作，完成主席分配的工作，主席不在时，代理主席部分工作； 2、获得学院2017年度二等奖奖学金； 3.获得“优秀共青团员”、“优秀学生干部”、“优秀毕业生”等荣誉称号。收起',
					},
					{
						style: '项目经理2',
						content: '1、担任学院学生会副主席，协助主席开展工作，完成主席分配的工作，主席不在时，代理主席部分工作； 2、获得学院2017年度二等奖奖学金； 3.获得“优秀共青团员”、“优秀学生干部”、“优秀毕业生”等荣誉称号。收起',
					},
					{
						style: '项目经理3',
						content: '1、担任学院学生会副主席，协助主席开展工作，完成主席分配的工作，主席不在时，代理主席部分工作； 2、获得学院2017年度二等奖奖学金； 3.获得“优秀共青团员”、“优秀学生干部”、“优秀毕业生”等荣誉称号。收起',
					},
				],
				templateList: [{
					name: '通用格式',
					desc: '通用介绍句式，可面向大多岗位',
					params: ['· 主修课程', '在校职务', '获得荣誉', '· 校内活动'],
					useBtnText: '使用'
				}, {
					name: '突出学业',
					desc: '突出专业和成绩，反映个人潜力与发展',
					params: ['· 专业课程', '· 在校成绩', '· 学术项目', '· 学术项目'],
					useBtnText: '使用'
				}, {
					name: '在校经历',
					desc: '通过担任职务、项目和奖项体现个人能力',
					params: ['· 担任XX职务', '· 参与XX项目活动 ', '· 荣获XX奖项 '],
					useBtnText: '使用'
				}, {
					name: '校内外活动',
					desc: '通过介绍在校经历丰富求职项目经验',
					params: ['· 校内外活动经验 ', '· 兼职实习经验', '· 学业项目经验 '],
					useBtnText: '使用'
				}],
				isTitleClicked: true,
				isSubtitleClicked: false
			}
		},
		methods: {
			closeTip() {
				this.$refs.popup.close();
			},
			finish() {
				// 完成按钮点击后的逻辑，比如提交数据等
				console.log('完成操作，输入值为：', this.inputValue);
			},
			showPicker() {
				this.hidePicker = true
			},
			toggle() {
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/Customize/refer'
				})
			},

		},
	};
</script>

<style scoped>
	.container {
		padding: 20rpx 0rpx;
		background-color: #fff;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.context {
		flex: 1;
	}

	.scroll-Y {
		height: 774rpx;
	}

	.tip-bar {
		background-color: #ffd6b3;
		padding: 10rpx 15rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.tip-text {
		color: #f60;
		font-size: 28rpx;
		margin-left: 16rpx;
	}

	.close-icon {
		color: #ccc;
		font-size: 32rpx;
	}

	.input-area {
		margin-top: 20rpx;
		padding: 32rpx;
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 1173rpx;
	}

	.label {
		font-size: 32rpx;
		color: #333;
	}

	.input {
		width: 100%;
		font-size: 32rpx;
		border: none;
		padding: 10rpx 0;
	}

	.section {
		/* margin-bottom: 30rpx; */
		width: 686rpx;
		padding: 32rpx 0rpx;
		/* border-bottom: 1rpx solid rgba(230, 230, 230, 1); */
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		flex: 1;
	}

	.count {
		color: rgba(79, 140, 240, 1);
		font-size: 28rpx;
		float: right;
		margin-top: 24rpx;
	}

	.count-bottom {
		color: rgba(153, 153, 153, 1);
		font-size: 28rpx;
		margin-top: 24rpx;
	}

	.text-box {
		display: flex;
		justify-content: flex-end;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-image: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.Model {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.model-left {
		width: 104rpx;
		height: 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.popup-content {
		/* width: 750rpx;
		height: 774rpx; */
		display: flex;
		flex-direction: column;
		align-items: center;

	}

	.popup-box {
		padding: 24rpx 32rpx;
	}

	.popup-context {
		display: flex;
		justify-content: space-between;
	}

	.popup-title {
		font-size: 32rpx;
		/* font-weight: bold; */
	}

	.popup-subtitle {
		font-size: 28rpx;
		color: #666;
		margin-left: 42rpx;
	}

	.popup-active {
		background-image: url('https://api-test.zhaopinbei.com/storage/uploads/images/sySQp2NnCVbjDxzMArI8JLcNwIZRVDHGn1KKKS5N.png');
		background-size: auto;
		font-size: 32rpx;
		font-weight: bold;
		color: rgba(51, 51, 51, 1);
	}

	.popup-close {
		font-size: 36rpx;
		cursor: pointer;
	}

	.popup-content {
		/* padding: 30rpx; */
		display: flex;
		justify-content: space-between;
	}

	.recommendation {
		margin-bottom: 30rpx;
	}

	.recommendation-desc {
		font-size: 24rpx;
		color: #999;
	}

	.resume-example {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		width: 686rpx;
		/* height: 364rpx; */
		padding: 24rpx;
		border-radius: 12rpx;
		background-color: rgba(245, 245, 245, 1);
	}

	.resume-icon {
		width: 30px;
		height: 30px;
		background-color: #ddd;
		border-radius: 50%;
		margin-right: 10px;
	}

	.resume-text {
		flex: 1;
	}

	.resume-title {
		font-size: 14px;
		margin-bottom: 5px;
	}

	.resume-detail {
		font-size: 26rpx;
		line-height: 1.5;
	}

	.resume-collapse {
		color: #1aad19;
		cursor: pointer;
	}

	.popup-footer {
		text-align: center;
		padding: 15px;
	}

	.save-button {
		width: 100%;
		padding: 10px;
		background-color: #007aff;
		color: #fff;
		border: none;
		border-radius: 5px;
		font-size: 14px;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
	}

	.active {
		color: red;
		margin-top: 24rpx;
		font-size: 28rpx;
	}

	.popup-desc {
		font-size: 28rpx;
		color: #999;
		margin-top: 32rpx;
		margin-bottom: 24rpx;
		align-self: flex-start;
	}

	.template-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.template-item {
		width: 283rpx;
		margin-bottom: 32rpx;
		background-color: #F5F7FA;
		padding: 24rpx;
		border-radius: 16rpx;
		box-shadow: 0 0 12px rgba(0, 0, 0, 0.13);
	}

	.template-title {
		display: flex;
		align-items: center;
	}

	.template-icon {
		width: 60px;
		height: 60rpx;
		background-color: #007AFF;
		margin-bottom: 20rpx;
	}

	.template-name {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
	}

	.template-desc {
		font-size: 20rpx;
		color: #666;
		margin-bottom: 20rpx;
	}

	.template-params {
		margin-bottom: 20rpx;
		align-self: flex-start;
	}

	.param {
		font-size: 20rpx;
		color: #999;
		display: block;
	}

	.use-btn {
		background-color: #007AFF;
		color: white;
		border: none;
		font-size: 24rpx;
		border-radius: 50rpx;
		width: 128rpx;
		height: 62rpx;
		margin: 0px;
	}

	.template-content {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.resume-box {
		display: flex;
		flex-direction: column;
		align-self: center;
		/* height: 280rpx; */
	}
</style>
