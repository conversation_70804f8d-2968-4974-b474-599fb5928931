<template>
    <view class="home">
        <view class="container">
            <view class="sup-title">
                HELLO～
            </view>
            <view class="title">
                欢迎来到<text>招聘呗</text>
            </view>
            <view class="tabs">
                <view :class="['tab',tabIndex=='member'?'active':'']" @click="changeTab('member')">
                    <view class="tab-tx ">
                        我要找工作
                    </view>
                </view>

                <view :class="['tab',tabIndex=='company'?'active':'']" @click="changeTab('company')">
                    <view class="tab-tx">
                        我要招聘
                    </view>
                </view>

                <view :class="['tab',tabIndex=='headhunters'?'active':'']" @click="changeTab('headhunters')">
                    <view class="tab-tx">
                        就业管家
                    </view>
                </view>
            </view>

            <view class="form">
                <view class="inp-item">
                    <!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
                    <!-- #ifndef APP-NVUE -->
                    <u-input placeholder="请输入手机号" fontSize="28rpx" clearable v-model="form.cellphone">
                    <!-- #endif -->
                        <!-- #ifdef APP-NVUE -->
                        <u--input placeholder="请输入手机号" fontSize="28rpx" clearable v-model="form.cellphone">
                        <!-- #endif -->
                            <u--text text="+86" slot="prefix" margin="0 3px 0 0" type="tips"></u--text>
                    <!-- #ifndef APP-NVUE -->
                    </u-input>
                    <!-- #endif -->
                    <!-- #ifdef APP-NVUE -->
                    </u--input>
                    <!-- #endif -->
                </view>

                <view class="inp-item" v-if="loginType=='code'">
                    <!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
                    <!-- #ifndef APP-NVUE -->
                    <u-input placeholder="请输入验证码" v-model="form.code" clearable fontSize="28rpx">
                    <!-- #endif -->
                        <!-- #ifdef APP-NVUE -->
                        <u--input placeholder="请输入验证码" v-model="form.code" clearable fontSize="28rpx">
                        <!-- #endif -->
                            <template slot="suffix">
                                <u-code ref="uCode" @change="codeChange" seconds="20" changeText="X秒重新获取"></u-code>
                                <u-button @tap="getCode" :text="tips" type="success" size="mini"></u-button>
                            </template>
                    <!-- #ifndef APP-NVUE -->
                    </u-input>
                    <!-- #endif -->
                    <!-- #ifdef APP-NVUE -->
                    </u--input>
                    <!-- #endif -->
                </view>

                <view class="inp-item" v-if="loginType=='account'">
                    <u--input v-model="form.password" fontSize="28rpx" clearable type="password"
                        placeholder="请输入密码"></u--input>
                </view>
            </view>

            <view class="regs">
                <!-- <view class="reg" @click="goReg">
					注册账号
				</view> -->
                <view class="reg" @click="changeLoginType">
                    <image src="/static/images/login/avatar.png" mode=""></image>
                    <text>{{loginType=='code'?'账号密码登录':'验证码登录'}}</text>
                </view>
            </view>
        </view>
        <view class="footer">
            <view class="rule" @click="changeCheckBox()">
                <view class="radio">
                    <u-checkbox-group shape="circle" size="14" v-model="isAgree">
                        <u-checkbox name="agree"></u-checkbox>
                    </u-checkbox-group>
                </view>
                <view class="desc">
                    已阅读并同意招聘呗
                    <text @click.stop="articleOpen('YHFWXY')">《用户服务协议》</text>
                    和
                    <text @click.stop="articleOpen('YSXY')">《隐私协议》</text>
                </view>
            </view>

            <view class="btns">
                <!-- <view class="btn wx" @click="open">
					
					手机号码快捷登录
				</view> -->
                <!-- <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
					微信账号一键登录
				</button> -->
                <view class="phoneBtnCla" v-if="isAgree.length == 0" @click="readClick">
                    <u-icon name="weixin-circle-fill" color="#FFFFFF" size="20"></u-icon><text>手机号码快捷登录</text>
                </view>
                <button open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" v-else>
                    <u-icon name="weixin-circle-fill" color="#FFFFFF" size="20"></u-icon><text>手机号码快捷登录</text>
                </button>
                <view class="btn" @click="login">
                    登录
                </view>
            </view>
        </view>


        <u-popup :show="show" :round="10" bgColor="#F5F5F5" mode="bottom" closeOnClickOverlay @close="close"
            @open="open">
            <view class="credit">
                <view class="title">
                    <view class="logo-content">
                        <image src="/static/images/login/logo.png" mode=""></image>
                        <text>招聘呗</text>
                    </view>
                    <view class="tip">
                        <image src="/static/images/login/tips.png" mode=""></image>
                    </view>
                </view>

                <view class="content">
                    <view class="sub-title">
                        申请获取并验证你的手机号
                    </view>
                    <view class="desc">
                        1.注册/登录招聘呗账号；2.用于在聊呗中进行交换双方手机号码。
                    </view>

                    <view class="btns">
                        <view class="btn">
                            <text>176****2234</text>
                            <text class="bind">微信绑定</text>
                        </view>
                        <view class="btn">
                            <text>不允许</text>
                        </view>
                    </view>
                </view>

                <view class="other">
                    使用其他号码
                </view>
            </view>
        </u-popup>


        <u-popup :show="showRule" :round="10" bgColor="transparent" mode="center" closeOnClickOverlay>
            <view class="rule-container">
                <view class="rule-pop">
                    <view class="rule-desc">
                        请阅读并同意招聘呗，<text @click.stop="articleOpen('YHFWXY')">《用户服务协议》</text>和<text
                            @click.stop="articleOpen('YSXY')">《隐私协议》</text>允许招聘呗管理平台账号信息。
                    </view>
                    <view class="btns">
                        <view class="btn refuse" @click="refuse">
                            拒绝
                        </view>
                        <view class="btn agree" @click="agree">
                            同意
                        </view>
                    </view>
                </view>
            </view>
        </u-popup>


        <u-popup :show="showComp" :round="10" bgColor="#F5F5F5" mode="bottom" closeOnClickOverlay @close="closeComp"
            @open="openComp">
            <view class="credit">
                <view class="title comp">
                    请选择企业登录
                </view>
                <view class="content">
                    <view class="btns">
                        <view :class="['btn',compIndex==1?'active':'']" @click="changeComp(1)">
                            <text class="">学创联盟（北京）网络科技公司</text>
                        </view>
                        <view :class="['btn',compIndex==2?'active':'']" @click="changeComp(2)">
                            <text>学创联盟（天津）网络科技公司</text>
                        </view>
                    </view>
                </view>

                <view class="agree" @click="goHome">
                    确定
                </view>
            </view>
        </u-popup>


        <!-- <u-popup :show="showRuleContent" :round="10" bgColor="#F5F5F5" mode="bottom" closeOnClickOverlay>
			<view class="rule-container-content">
				<view class="title">
					请仔细阅读协议内容
				</view>
				<view class="content">
					<view class="sub-title">
						{{ articleDetails.title }}
					</view>
					<view class="txt">
						<rich-text :nodes="articleDetails.content"></rich-text>
					</view>
				</view>
				<view class="btn" @click="closeRuleContent">
					我已阅读并同意协议
				</view>
			</view>

		</u-popup> -->
    </view>
</template>

<script>
    import {
        login,
        quickLogin
    } from "../../config/api.js"
    import {
        getTagShowDetails
    } from "../../config/common_api";
    import {
        webSoketInit
    } from "../../common/webSoket";
    export default {
        data() {
            return {
                articleDetails: {},
                tag: 'YHFWXY',
                user_code: '',
                tips: '',
                isAgree: [],
                value: '',
                tabIndex: 'member',
                form: {
                    cellphone: '', //手机号
                    code: '', //短信验证码
                    password: '', //密码
                },
                compIndex: 1,

                show: false,
                showRule: false,
                // showRuleContent: false,
                showComp: false,
                loginType: 'account',
                codeList: ''
            }
        },

        onShow() {
            console.log("场景", wx.getLaunchOptionsSync())
        },

        onLoad(options) {
            // this.user_code = options.scene || ''
            uni.login({
                success: (loginRes) => {
                    this.codeList = loginRes.code;
                },
                fail: (str) => {
                    uni.showToast({
                        title: '请确认您的微信是否登录',
                        icon: 'none'
                    })
                }
            });
        },
        methods: {
            changeCheckBox() {
                console.log(1111)
                if (this.isAgree.length > 0) {
                    this.isAgree = []
                } else {
                    this.isAgree = ['agree']
                }
            },
            articleOpen(e) {
                this.isAgree = ['agree']
                uni.navigateTo({
                    url: '/pagesA/list/privacyDetails?tag=' + e
                })
                // this.tag = e
                // this.showRuleContent = true
                // this.getShowDetails()
            },
            // async getShowDetails() {
            // 	let params = {
            // 		tag: this.tag,
            // 	}
            // 	const {
            // 		status_code,
            // 		data
            // 	} = await getTagShowDetails(params)
            // 	if (status_code == 200) {
            // 		console.log("获取数据", data)
            // 		this.articleDetails = data;
            // 	}
            // },
            changeLoginType() {
                if (this.loginType == 'account') {
                    this.loginType = 'code'
                    console.log(1, this.loginType)
                } else {
                    this.loginType = 'account'
                    console.log(2, this.loginType)
                }
            },
            changeTab(str) {
                this.tabIndex = str
            },
            open() {
                this.show = true
            },
            close() {
                this.show = false
            },

            openRuleContent() {
                this.showRule = false
                // this.showRuleContent = true
            },
            // closeRuleContent() {
            // 	this.showRuleContent = false
            // 	this.isAgree = ['agree']
            // },

            submit() {
                this.validate()
                if (this.tabIndex == 'member') {
                    uni.switchTab({
                        url: "/pages/index/index"
                    })
                } else if (this.tabIndex == 'company') {
                    this.showComp = true;
                } else if (this.tabIndex == 'headhunters') {
                    this.showComp = true;
                }
            },


            openComp() {
                this.showComp = true;
            },

            closeComp() {
                this.showComp = false;
            },

            changeComp(index) {
                this.compIndex = index;
            },


            goReg() {
                if (this.tabIndex == 'member') {
                    uni.navigateTo({
                        url: "/pages/register/register"
                    })
                } else {
                    uni.navigateTo({
                        url: "/pages/register/register_b?type=" + this.tabIndex
                    })
                }

            },


            goHome() {
                if (this.tabIndex == 'company') {
                    uni.navigateTo({
                        url: "/pages/index/index_bl"
                    })
                } else if (this.tabIndex == 'headhunters') {
                    uni.navigateTo({
                        url: "/pages/index/index_jy"
                    })
                }
            },



            validate() {
                console.log(isPhone)
                if (isPhone && this.form.phone) {
                    return uni.$u.toast('验证通过')
                } else {
                    return uni.$u.toast('验证失败')
                }

            },
            codeChange(text) {
                this.tips = text;
            },

            getCode() {
                if (this.$refs.uCode.canGetCode) {
                    // 模拟向后端请求验证码
                    uni.showLoading({
                        title: '正在获取验证码'
                    })
                    setTimeout(() => {
                        uni.hideLoading();
                        // 这里此提示会被this.start()方法中的提示覆盖
                        uni.$u.toast('验证码已发送');
                        // 通知验证码组件内部开始倒计时
                        this.$refs.uCode.start();
                    }, 2000);
                } else {
                    uni.$u.toast('倒计时结束后再发送');
                }
            },
            change(e) {
                console.log('change', e);
            },

            //展示规则是否同意弹框
            toggleRule() {
                if (this.isAgree.length == 0) {
                    this.showRule = true
                    return
                }
            },

            agree() {
                this.isAgree = ['agree']
                this.showRule = false
            },

            refuse() {
                this.isAgree = []
                this.showRule = false
            },

            async login() {
                if (!this.form.cellphone) return uni.$u.toast('请输入手机号')
                if (this.loginType == 'account') {
                    if (!this.form.password) return uni.$u.toast('请输入登录密码')
                } else if (this.loginType == 'code') {
                    if (!this.form.code) return uni.$u.toast('请输入验证码')
                }
                // const reg = /^1[3456789]\d{9}$/;
                // let isPhone = reg.test(this.form.phone)
                // if(!isPhone) return uni.$u.toast('请输入正确的手机号码')

                if (this.isAgree.length == 0) {
                    this.showRule = true
                    return
                }
                let params = {}
                if (this.loginType == 'account') { //账号登录
                    params = {
                        type: this.tabIndex,
                        cellphone: this.form.cellphone,
                        password: this.form.password
                    }
                } else if (this.loginType == 'code') { //验证码登录
                    params = {
                        type: this.tabIndex,
                        cellphone: this.form.cellphone,
                        code: this.form.code
                    }
                }

                const {
                    data,
                    status_code,
                    message
                } = await login(params)
                console.log(status_code, "状态码")
                const {
                    role_type,
                    token,
                    member,
                    job_class_count,
                    ws_url
                } = data

                // if(status_code==200){
                // 	this.$store.commit('setToken', token)
                // 	uni.navigateTo({
                // 		url:'/pagesA/tourist/select_tourist_one'
                // 	})
                // 	return;
                // }

                if (status_code == 200) {
                    this.$store.commit('setToken', token)
                    this.$store.commit('setToken', token)
                    this.$store.commit('setRoleType', role_type)
                    this.$store.commit('setUserInfo', data)
                    this.$store.commit('ws_url', ws_url)
                    uni.setStorageSync('ws_url', ws_url)
                    webSoketInit();
                    //登录用户没有选择期望岗位
                    if (job_class_count < 1) {
                        if (role_type == 'member') {
                            uni.navigateTo({
                                url: "/pagesA/tourist/select_tourist_two"
                            })
                            return
                        }
                        uni.navigateTo({
                            url: '/pagesA/tourist/selectJob'
                        })
                        return
                    }
                    uni.switchTab({
                        url: '/pages/index/index'
                    })
                }
            },

            readClick() {
                this.showRule = true
            },
            async getPhoneNumber(e) {
                console.log("获取手机号：", e);
                let detail = e.detail
                if (detail.errMsg === "getPhoneNumber:ok") {
                    let code = detail.code
                    let params = {
                        code: this.codeList,
                        encryptedData: e.detail.encryptedData,
                        encryptedCode: e.detail.code,
                        iv: e.detail.iv,
                        user_code: code,
                        type: this.tabIndex
                    }
                    console.log("接口参数：", params)
                    const {
                        data,
                        status_code,
                        message
                    } = await quickLogin(params)
					
                    const {
                        role_type,
                        token,
                        member,
                        job_class_count
                    } = data
                    if (status_code == 200) {
                        this.$store.commit('setToken', token)
                        this.$store.commit('setRoleType', role_type)
                        this.$store.commit('setUserInfo', data)
                        //登录用户没有选择期望岗位
                        if (job_class_count < 1) {
                            if (role_type == 'member') {
                                uni.navigateTo({
                                    url: "/pagesA/tourist/select_tourist_two"
                                })
                                return
                            }
                            uni.navigateTo({
                                url: '/pagesA/tourist/selectJob'
                            })
                            return
                        }
                        uni.switchTab({
                            url: '/pages/index/index'
                        })
						// const res_userInfo = uni.getStorageSync('userInfo')
						// let roles = res_userInfo.roles
						
                    }
                } else {
                    console.log("用户拒绝授权");
                }
            }

        }
    }
</script>
<style>
    page {}
</style>
<style lang="less" scoped>
    .home {
        .container {
            display: flex;
            flex-direction: column;
            padding: 0 30rpx;

            .sup-title {
                font-family: Microsoft YaHei UI, Microsoft YaHei UI;
                font-weight: bold;
                margin-top: 108rpx;
                font-size: 96rpx;
                color: rgba(79, 140, 240, 0.1);
            }

            .title {
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 64rpx;
                color: #333333;
                margin-top: -60rpx;

                text {
                    color: #4F8CF0;
                }
            }


            .tabs {
                display: flex;
                background: #F5F5F7;
                border-radius: 24rpx;
                height: 88rpx;
                margin-top: 40rpx;
                border: 1px solid #D9D9D9;

                .tab {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    flex: 1;
                    padding: 4rpx;
                    color: #999999;
                    font-size: 24rpx;

                    .tab-tx {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 94%;
                        height: 90%;
                    }
                }

                .active {
                    .tab-tx {
                        background: #FFFFFF;
                        border-radius: 24rpx;
                        color: #4F8CF0;
                        ;
                    }
                }
            }

            .form {
                margin-top: 28rpx;

                .inp-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 24rpx;

                    .u-button--success {
                        background-color: #4F8CF0;
                        border-color: #4F8CF0;
                    }
                }
            }

            .regs {
                display: flex;
                justify-content: space-between;
                font-size: 24rpx;
                font-weight: 400;
                margin-top: 24rpx;

                .reg {
                    display: flex;
                    align-items: center;
                    background: #F5F5F7;
                    border-radius: 36px 36px 36px 36px;
                    height: 60rpx;
                    padding: 0 24rpx;

                    image {
                        width: 32rpx;
                        height: 32rpx;
                    }
                }
            }


        }

        .footer {
            display: flex;
            flex-direction: column;
            position: fixed;
            // width: 100%;
            bottom: 100rpx;
            width: 90%;
            left: 5%;

            .rule {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 32rpx;
                width: 100%;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;

                text {
                    color: #4F8CF0;
                }
            }

            .btns {
                display: flex;
                flex-direction: column;

                button {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background: linear-gradient(90deg, #03E370 0%, #02CE62 100%);
                    width: 100%;
                    height: 88rpx;
                    line-height: 88rpx;
                    border-radius: 44rpx;
                    font-size: 28rpx;
                    color: #FFFFFF;
                    margin-bottom: 32rpx;

                    text {
                        margin-left: 16rpx;
                    }
                }

                .btn {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    justify-content: center;
                    color: #FFFFFF;
                    background: #4F8CF0;
                    height: 88rpx;
                    border-radius: 44rpx;
                    width: 100%;
                    font-size: 28rpx;

                    &:first-child {
                        margin-bottom: 32rpx;
                    }

                    image {
                        width: 40rpx;
                        height: 40rpx;
                    }
                }

                .wx {
                    background: #03D164;
                }
            }
        }


        .credit {
            display: flex;
            flex-direction: column;
            padding: 40rpx 48rpx;

            // background: #F5F5F7;
            .title {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .logo-content {
                    display: flex;

                    image {
                        width: 48rpx;
                        height: 48rpx;
                        margin-right: 16rpx;
                    }

                    text {
                        font-family: PingFang SC, PingFang SC;
                        font-weight: 500;
                        font-size: 15px;
                        color: rgba(0, 0, 0, 0.9);
                    }
                }

                .tip {
                    image {
                        width: 48rpx;
                        height: 48rpx;
                    }
                }
            }

            .comp {
                justify-content: center;
            }

            .content {
                display: flex;
                flex-direction: column;
                margin-top: 40rpx;

                .sub-title {
                    font-family: PingFang SC, PingFang SC;
                    font-weight: 600;
                    font-size: 28rpx;
                    color: #000000;
                }

                .desc {
                    display: flex;
                    font-weight: 400;
                    margin-top: 16rpx;
                    font-size: 24rpx;
                    color: #999999;
                }

                .btns {
                    display: flex;
                    flex-direction: column;
                    margin-top: 16rpx;

                    .btn {
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        background: #FFFFFF;
                        height: 140rpx;
                        border-radius: 16rpx;

                        &:last-child {
                            margin-top: 24rpx;
                        }

                        text {
                            font-weight: 400;
                            font-size: 32rpx;
                            color: rgba(0, 0, 0, 0.9);
                        }

                        .bind {
                            font-size: 24rpx;
                            color: #07C160;
                        }
                    }

                    .active {
                        color: #4F8CF0;
                        border: 1rpx solid #4F8CF0;
                    }


                }


            }

            .other {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 60rpx 0;
                color: #576B95;
                font-weight: 500;
                font-size: 24rpx;
            }

            .agree {
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 44rpx;
                height: 88rpx;
                font-weight: 500;
                font-size: 28rpx;
                color: #FFFFFF;
                background: #4F8CF0;
                margin-top: 32rpx;
            }
        }

        .rule-container {
            padding: 0 24rpx;

            .rule-pop {
                display: flex;
                flex-direction: column;
                background: #FFFFFF;
                padding: 48rpx;
                border-radius: 20rpx;

                .rule-desc {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #000000;

                    text {
                        color: #4F8CF0;
                    }
                }

                .btns {
                    display: flex;
                    justify-content: center;
                    margin-top: 24rpx;

                    .btn {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        width: 160rpx;
                        height: 60rpx;
                        border-radius: 16rpx;
                        border: 1px solid #4F8CF0;
                        font-size: 28rpx;

                        &:first-child {
                            margin-right: 24rpx;
                        }
                    }

                    .refuse {
                        color: #4F8CF0;
                    }

                    .agree {
                        background: #4F8CF0;
                        color: #FFFFFF;
                    }
                }
            }
        }

        // .rule-container-content {
        // 	display: flex;
        // 	flex-direction: column;
        // 	padding: 0 48rpx;

        // 	.title {
        // 		display: flex;
        // 		justify-content: center;
        // 		align-items: center;
        // 		font-weight: 400;
        // 		font-size: 14px;
        // 		color: #333333;
        // 		padding: 32rpx 0;
        // 	}

        // 	.content {
        // 		display: flex;
        // 		flex-direction: column;
        // 		background: #FFFFFF;
        // 		padding: 32rpx;

        // 		.sub-title {
        // 			display: flex;
        // 			justify-content: center;
        // 			align-items: center;
        // 			font-weight: 600;
        // 			font-size: 40rpx;
        // 			color: #333333;
        // 			padding-bottom: 32rpx;
        // 		}

        // 		.txt {
        // 			font-weight: 400;
        // 			font-size: 24rpx;
        // 			color: #666666;
        // 		}
        // 	}

        // 	.btn {
        // 		display: flex;
        // 		justify-content: center;
        // 		align-items: center;
        // 		border-radius: 44rpx;
        // 		height: 88rpx;
        // 		font-weight: 500;
        // 		font-size: 28rpx;
        // 		color: #FFFFFF;
        // 		background: #4F8CF0;
        // 		margin: 32rpx 0;
        // 	}
        // }
    }

    .phoneBtnCla {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(90deg, #03E370 0%, #02CE62 100%);
        border-radius: 58rpx 58rpx 58rpx 58rpx;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;

        font-weight: 500;
        font-size: 28rpx;
        color: #FFFFFF;
        margin-bottom: 32rpx;

        text {
            padding-left: 18rpx;
        }
    }
</style>