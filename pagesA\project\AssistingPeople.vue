<template>
	<view id="app">
		<view class="searchBox">
			<image src="/static/images/project/searchIcon.png" mode="" class="searchIcon"></image>
			<input type="text" class="searchIpt" placeholder="请输入协助人名称" />
		</view>

		<view class="filterBox">
			<view class="filterBox_child">
				郑州
				<image src="/static/images/project/smallDown.png" mode="" class="smallDown"></image>
			</view>
			<view class="filterBox_child">
				筛选
				<image src="/static/images/project/smallDown.png" mode="" class="smallDown"></image>
			</view>
		</view>

		<view class="listBox" v-for="(item, index) in items" :key="index"
			:class="['item', { 'selected': selectedItems.includes(item) }]" @click="toggleSelection(item)">
			<view class="listBox_top">
				<image src="/static/images/project/searchIcon.png" mode="" class="topHead"></image>
				<view class="listBox_top_right">
					<view class="listBox_top_right_title">就业管家姓名</view>
					<view class="listBox_top_right_synopsis">男 | 23岁 | 本科</view>
				</view>
			</view>
			<view class="labelBox">
				<view class="labelBox_child">管家标签</view>
				<view class="labelBox_child">管家标签</view>
				<view class="labelBox_child">管家标签</view>
			</view>
			<view class="hrBox"></view>
			<view class="listBox_bottom">
				<image src="https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png" mode="" class="companyImg"></image>
				<view class="listBox_top_right">
					<view class="listBox_top_right_one">
						<view class="listBox_top_right_title1">学创联盟（北京）网络测试长度</view>
						<image src="/static/images/project/authentication.png" mode="" class="authentication"></image>
					</view>
					<view class="listBox_top_right_synopsis">男 | 23岁 | 本科</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		data() {
			return {
				items: ['选项1', '选项2', '选项3', '选项4'], // 列表项
				selectedItems: [] // 存储选中的项
			}
		},
		methods: {
			toggleSelection(item) {
				const index = this.selectedItems.indexOf(item);
				if (index > -1) {
					// 如果已选中，则取消选中
					this.selectedItems.splice(index, 1);
				} else {
					// 否则，添加到选中项中
					this.selectedItems.push(item);
				}
			}
		}
	}
</script>

<style scoped>
	view {
		box-sizing: border-box;
	}

	#app {
		width: 100%;
		padding: 32rpx;
		min-height: 100vh;
		height: auto;
		background-color: #F5F5F7;
	}

	.searchBox {
		width: 100%;
		height: 64rpx;
		background: #ffffff;
		border-radius: 46rpx 46rpx 46rpx 46rpx;
		display: flex;
		align-items: center;
		padding: 0 24rpx;
		margin-bottom: 32rpx;
	}

	.searchIcon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 16rpx;
	}

	.searchIpt {
		width: 100%;
		height: 100%;
		font-weight: 400;
		font-size: 28rpx;
		color: #999999;
		line-height: 64rpx;
	}

	/* 筛选start */

	.filterBox_child {
		width: 96rpx;
		height: 48rpx;
		background: #FFFFFF;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 400;
		font-size: 24rpx;
		color: #333333;
		margin-right: 16rpx;
	}

	.smallDown {
		width: 24rpx;
		height: 24rpx;
	}

	.filterBox {
		display: flex;
		margin-bottom: 32rpx;
	}

	/* 筛选end */

	.listBox {
		width: 100%;
		height: 360rpx;
		background: #FFFFFF;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		padding: 32rpx;
		border: 1px solid transparent;
		margin-bottom: 24rpx;
	}

	.item.selected {
	  border: 1px solid blue; /* 选中时边框蓝色 */
	}

	.listBox_top {
		display: flex;
		align-items: center;
	}

	.topHead {
		width: 88rpx;
		height: 88rpx;
		border-radius: 88rpx 88rpx 88rpx 88rpx;
		margin-right: 16rpx;
	}

	.listBox_top_right {
		height: 88rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.listBox_top_right_title {
		font-weight: 600;
		font-size: 32rpx;
		color: #333333;
		width: 308rpx;
		white-space: nowrap;
		/* 不换行 */
		overflow: hidden;
		/* 超出部分隐藏 */
		text-overflow: ellipsis;
		/* 省略号 */
	}

	.listBox_top_right_title1 {
		font-weight: 600;
		font-size: 32rpx;
		color: #333333;
		width: 308rpx;
		white-space: nowrap;
		/* 不换行 */
		overflow: hidden;
		/* 超出部分隐藏 */
		text-overflow: ellipsis;
		/* 省略号 */
	}

	.listBox_top_right_synopsis {
		font-weight: 400;
		font-size: 28rpx;
		color: #666666;
	}

	.labelBox {
		display: flex;
		align-items: center;
		margin-top: 16rpx;
	}

	.labelBox_child {
		width: 112rpx;
		height: 46rpx;
		background: #F6F6F6;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		margin-right: 16rpx;
		text-align: center;
		line-height: 46rpx;
		font-weight: 400;
		font-size: 22rpx;
		color: #666666;
	}

	.hrBox {
		width: 100%;
		height: 2rpx;
		background: #F5F5F7;
		margin: 24rpx 0;
	}

	.companyImg {
		width: 96rpx;
		height: 96rpx;
		border-radius: 24rpx 24rpx 24rpx 24rpx;
		margin-right: 16rpx;
	}

	.listBox_bottom {
		display: flex;

	}

	.authentication {
		width: 32rpx;
		height: 32rpx;
	}

	.listBox_top_right_one {
		display: flex;
		align-items: center;
	}
</style>
