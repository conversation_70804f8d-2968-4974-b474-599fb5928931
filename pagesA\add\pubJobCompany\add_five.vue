<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <view class="title">上传合作材料</view>
      <view class="subtitle"
        >根据国家《人力资源市场暂行条例》等相关法律规定，为打造真实良好的网络招聘环境，请您提供真实有效的用人单位与人力资源公司之间的合同，以此作为直接合作证明。</view
      >
    </view>

    <!-- 上传区域 -->
    <view class="upload-section">
      <view class="upload-title">请上传合同</view>
      <view class="upload-grid">
        <!-- 双方公章及签约日期页 -->
        <!-- 双方公章及签约日期页 -->
        <view class="upload-item">
          <u-upload
            :fileList="fileList1"
            @afterRead="afterRead1"
            @delete="deletePic1"
            name="1"
            :maxCount="1"
            :previewFullImage="true"
          >
            <view class="upload-box">
              <u-icon name="plus" color="#4F8CF0" size="40"></u-icon>
            </view>
          </u-upload>
          <view class="upload-text">双方公章及签约日期页</view>
        </view>

        <!-- 合同有效期页 -->
        <view class="upload-item">
          <u-upload
            :fileList="fileList2"
            @afterRead="afterRead2"
            @delete="deletePic2"
            name="2"
            :maxCount="1"
            :previewFullImage="true"
          >
            <view class="upload-box">
              <u-icon name="plus" color="#4F8CF0" size="40"></u-icon>
            </view>
          </u-upload>
          <view class="upload-text">合同有效期页</view>
        </view>

        <!-- 合作类型页 -->
        <view class="upload-item">
          <u-upload
            :fileList="fileList3"
            @afterRead="afterRead3"
            @delete="deletePic3"
            name="3"
            :maxCount="1"
            :previewFullImage="true"
          >
            <view class="upload-box">
              <u-icon name="plus" color="#4F8CF0" size="40"></u-icon>
            </view>
          </u-upload>
          <view class="upload-text">合作类型页</view>
        </view>

        <!-- 委托的职位类型 -->
        <view class="upload-item">
          <u-upload
            :fileList="fileList4"
            @afterRead="afterRead4"
            @delete="deletePic4"
            name="4"
            :maxCount="1"
            :previewFullImage="true"
          >
            <view class="upload-box">
              <u-icon name="plus" color="#4F8CF0" size="40"></u-icon>
            </view>
          </u-upload>
          <view class="upload-text">委托的职位类型</view>
        </view>
      </view>

      <!-- 其他补充材料 -->
      <view class="upload-title" style="margin-top: 32rpx"
        >其他补充材料（选填）</view
      >
      <view class="upload-item">
        <u-upload
          :fileList="fileList5"
          @afterRead="afterRead5"
          @delete="deletePic5"
          name="5"
          :maxCount="1"
          :previewFullImage="true"
        >
          <view class="upload-box">
            <u-icon name="plus" color="#4F8CF0" size="40"></u-icon>
          </view>
        </u-upload>
      </view>

      <!-- 注意事项 -->
      <view class="notice">
        <view class="notice-title">注意事项：</view>
        <view class="notice-item"
          >1、合同需要按照提示上传相关页面，可以打码加盖，但是需要清晰展示相关信息和双方公章；</view
        >
        <view class="notice-item"
          >2、上传材料必须证实合作的真实合法性，否则您将承担一切相关法律责任，平台有权下架未获得真实授权的职位；</view
        >
        <view class="notice-item"
          >3、您所提供的信息仅用于职位审核，将不会在外部展示；</view
        >
        <view class="notice-item"
          >4、上传材料仅支持3M以内的jpg,jpeg,png格式的图片(请确保内容清晰)。</view
        >
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="next" @click="next">下一步</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      fileList1: [],
      fileList2: [],
      fileList3: [],
      fileList4: [],
      fileList5: [],
    };
  },
  methods: {
    // 上传后回调
    afterRead1(event) {
      this.fileList1.push(event.file);
    },
    afterRead2(event) {
      this.fileList2.push(event.file);
    },
    afterRead3(event) {
      this.fileList3.push(event.file);
    },
    afterRead4(event) {
      this.fileList4.push(event.file);
    },
    afterRead5(event) {
      this.fileList5.push(event.file);
    },

    // 删除图片
    deletePic1(event) {
      this.fileList1.splice(event.index, 1);
    },
    deletePic2(event) {
      this.fileList2.splice(event.index, 1);
    },
    deletePic3(event) {
      this.fileList3.splice(event.index, 1);
    },
    deletePic4(event) {
      this.fileList4.splice(event.index, 1);
    },
    deletePic5(event) {
      this.fileList5.splice(event.index, 1);
    },

    next() {
      // 验证是否都上传了必要的图片
      //   if (
      //     !this.fileList1.length ||
      //     !this.fileList2.length ||
      //     !this.fileList3.length ||
      //     !this.fileList4.length
      //   ) {
      //     uni.showToast({
      //       title: "请上传必要的合同材料",
      //       icon: "none",
      //     });
      //     return;
      //   }
      uni.reLaunch({
        url: "/pages/plat/plat",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #fff;
  padding: 32rpx;
}

.header {
  margin-bottom: 32rpx;

  .title {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 24rpx;
  }

  .subtitle {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

.search-box {
  position: relative;
  margin-bottom: 32rpx;

  .count {
    position: absolute;
    right: 32rpx;
    top: 50%;
    transform: translateY(-50%);
    font-size: 24rpx;
    color: #999;
  }
}

.company-list {
  .company-item {
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 24rpx 0rpx;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #e6e6e6;

    .company-logo {
      width: 88rpx;
      height: 88rpx;
      background: #d9d9d9;
      border-radius: 8rpx;
      margin-right: 24rpx;
    }

    .company-info {
      flex: 1;

      .company-name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
        display: flex;
        align-items: center;
      }
      .tag {
        font-size: 20rpx;
        color: #4f8cf0;
        background-color: #ecf5ff;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
      }
      .company-type {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 12rpx;
      }
      .tags {
        width: 176rpx;
        height: 50rpx;
        background: #f4f4f4;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        font-size: 24rpx;
        color: #777777;
        text-align: center;
        line-height: 50rpx;
      }
    }
  }
}

.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 196rpx;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  border-radius: 24rpx 24rpx 0 0;

  .next {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80rpx;
    width: 90%;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
    margin-top: 24rpx;
  }
}

.upload-section {
  .upload-title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 24rpx;
  }

  .upload-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 60rpx;
  }

  .upload-item {
    .upload-box {
      width: 126rpx;
      height: 126rpx;
      background: #fff;
      border: 2rpx dashed #4f8cf0;
      border-radius: 12rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .upload-text {
      font-size: 24rpx;
      color: #999;
      margin-top: 16rpx;
      text-align: center;
    }
  }
}

.notice {
  margin-top: 48rpx;

  .notice-title {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 16rpx;
  }

  .notice-item {
    font-size: 24rpx;
    color: #999;
    line-height: 1.6;
    margin-bottom: 8rpx;
  }
}
</style>
