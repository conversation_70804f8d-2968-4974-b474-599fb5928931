<template>
    <view>
       <input type="text" v-model="storageData" placeholder="输入一些内容模拟表单数据填写"/>
       <button @click="pageSet">返回上个页面将数据存储下</button>
    </view>
</template>

<script>
    export default {
        data() {
            return {
                storageData:''
            }
        },
        onLoad() {
            
        },
        methods: {
           pageSet() {
               // 将数据存储到本地
               uni.setStorageSync('ceshi',this.storageData)
               // 回退到上个页面
               uni.navigateBack()
           }
        }
    }
</script>

<style scoped lang="less">
</style>