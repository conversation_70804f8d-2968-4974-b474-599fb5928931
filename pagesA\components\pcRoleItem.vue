<template>
    <view class="item">
        <view class="name">
            {{ item.name }}
        </view>
        <view class="desc">
            {{ item.description }}
        </view>
        <view class="tags">
            <view class="tag">
                {{ item.tag }}
            </view>

        </view>
    </view>
</template>

<script>
    export default {
        name: 'pcRoleItem',
        props: {
            item: {
                type: Object,
                default: () => {
                }
            },
            itemIndex: {
                type: Number,
            },
        },

        data() {
            return {}
        }
    }
</script>

<style lang="scss">
    @import "../../static/css/pagesA/components/pcRoleItem";
</style>