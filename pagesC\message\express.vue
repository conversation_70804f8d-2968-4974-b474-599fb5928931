<template>
	<view class="container">
		<u-sticky>
			<view class="tabs-container">
				<view :class="{item: true, is_active: params.type === '1'}" @click="onTabsItemClick('1')">
					<text class="text">常用语</text>
					<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/VjsHJaEaXBBVabRlZcwOaaKXyiLPNq1pAckyd5qO.png"></image>
				</view>
				<view :class="{item: true, is_active: params.type === '2'}" @click="onTabsItemClick('2')">
					<text class="text">招呼语</text>
					<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/VjsHJaEaXBBVabRlZcwOaaKXyiLPNq1pAckyd5qO.png"></image>
				</view>
			</view>
		</u-sticky>

		<view class="content-container">
			<u-cell v-for="v in languageList" :key="v.id" @longpress="onCellLongDelete">
				<template #title>
					<view class="item">
						<view class="text">{{v.content}}</view>
						<view class="actions">
							<view class="action-item" @click="onSubmitClick(v)">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/H4yn99P2LJfT2lEpQXDZpnDlnWxk51imUP4g5K7A.png" mode=""></image>
								<text class="text">编辑</text>
							</view>
						</view>
					</view>
				</template>
			</u-cell>
		</view>
		<view class="fixed-container">
			<!-- <view class="sort" @click="onSortClick">排序</view> -->
			<view class="btn" @click="onSubmitClick">添加常用语/招呼语</view>
		</view>

		<u-modal width="500rpx" :show="deleteModal" title="确定删除" showCancelButton confirmText="确定"
			@confirm="onDeleteModalConfirm" @cancel="onDeleteModalCancel"></u-modal>
	</view>
</template>

<script>
	import {
		getInterviewHelloWordList
	} from '@/config';

	export default {
		data() {
			return {
				templatePopup: false,
				deleteModal: false,

				languageList: [],
				params: {
					type: '1'
				}
			};
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		onShow() {
			this.onGetInterviewHelloWordList();
		},
		methods: {
			onSortClick() {
				uni.$u.route({
					url: '/pagesC/message/express_sort',
					params: {
						type: this.params.type
					}
				});
			},
			onAddClick() {
				uni.$u.route({
					url: '/pagesC/message/express_submit',
					params: {
						type: this.params.type
					}
				});
			},
			onSubmitClick(v) {
				uni.$u.route({
					url: '/pagesC/message/express_submit',
					params: {
						id: v.id,
						content: v.content,
						type: this.params.type
					}
				});
			},
			onTabsItemClick(type) {
				this.params.type = type;
				this.onGetInterviewHelloWordList();
			},
			async onGetInterviewHelloWordList() {
				const res = await getInterviewHelloWordList({
					...this.params,
					...this.userTypeParams
				});
				if (res.status_code !== '200') return;
				this.languageList = res.data;
			},
			onCellLongDelete() {
				this.deleteModal = true;
			},
			onDeleteModalConfirm() {
				this.deleteModal = false;
			},
			onDeleteModalCancel() {
				this.deleteModal = false;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.container {
		height: calc(100vh - env(safe-area-inset-bottom) - 96rpx);
		box-sizing: border-box;
		padding: 32rpx;
		gap: 24rpx;
		display: flex;
		flex-direction: column;
		position: relative;

		.fixed-container {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			z-index: 9999;
			box-sizing: border-box;
			background-color: #ffffff;
			padding-inline: 32rpx;
			padding-block-start: 24rpx;
			padding-block-end: calc(24rpx + env(safe-area-inset-bottom));
			border-start-start-radius: 24rpx;
			border-start-end-radius: 24rpx;
			box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
			display: grid;
			grid-template-columns: 1fr;
			gap: 24rpx;

			.sort {
				background-color: #f5f5f7;
				padding-block: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 24rpx;
				color: #7f7f7f;
				font-size: 28rpx;
			}

			.btn {
				padding-block: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-image: linear-gradient(to right, #4f8cf0, #1e6dee);
				border-radius: 24rpx;
				color: #ffffff;
				font-size: 28rpx;
			}
		}

		.content-container {
			flex: 1;
			overflow-y: auto;

			.item {
				display: flex;
				text-align: start;
				flex-direction: column;
				gap: 24rpx;

				.text {
					color: #333333;
					font-size: 28rpx;
				}

				.actions {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					gap: 24rpx;

					.action-item {
						display: flex;
						align-items: center;
						gap: 12rpx;
						color: #333333;
						font-size: 24rpx;

						.image {
							width: 32rpx;
							height: 32rpx;
						}
					}
				}
			}
		}

		.tabs-container {
			display: flex;
			align-items: flex-end;
			gap: 40rpx;

			.item {
				position: relative;
				color: #666666;

				.text {
					font-size: 32rpx;
					position: relative;
					z-index: 2;
				}

				.image {
					width: 84rpx;
					height: 18rpx;
					position: absolute;
					display: none;
					bottom: 0;
					left: 0;
					z-index: 1;
				}
			}

			.is_active {
				color: #000000;

				.image {
					display: block;
				}
			}
		}
	}
</style>
