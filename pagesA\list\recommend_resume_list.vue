<template>
    <view class="">
        <u-sticky bgColor="#F5F5F5">
            <view class="search-wrap">
                <u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
            </view>
        </u-sticky>
        <view class="list">
            <recommend-resume-item v-for="(item,index) in list" :key="index" :item="item"
                                   @auditHandel="auditHandel(item)"></recommend-resume-item>
        </view>
        <Pages :status="status"></Pages>
    </view>

</template>

<script>
    import RecommendResumeItem from '../components/recommendResumeItem.vue'
    import Pages from "../../components/pages.vue";

    import {
        getMemberShareResume
    } from "../../config/company_api";

    export default {
        components: {
            RecommendResumeItem,
            Pages
        },
        data() {
            return {
                page: 1,
                limit: 10,
                status: 'loadmore',
                more: false,
                list: [],
                options: [
                    '满意',
                    '不满意',
                ],
            }
        },
        onLoad() {
            // this.getMemberShareResume()
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getMemberShareResume()
            } else {
                this.status = 'nomore'
            }
        },
        onShow() {
            this.page = 1;
            this.list = [];
            this.getMemberShareResume();
        },
        methods: {
            auditHandel(item) {
                console.log("item",item)
               uni.navigateTo({
                   url:'/pagesA/add/shareResumeInterview?member_id='+item.member.id+'&member_share_id='+item.member_share.id
               })
            },
            async getMemberShareResume() {
                let params = {
                    page: this.page,
                    limit: this.limit,
                    // status:审核状态1满意2待审核3不满意
                }

                const res = await getMemberShareResume(params)
                this.list = res.data.data
            },


        }
    }
</script>

<style>
    page {
        background-color: #F5F5F7;
    }
</style>

<style lang="less" scoped>
    .search-wrap {
        padding: 32rpx;
    }

    .list {
        padding: 0 32rpx 32rpx 32rpx;
    }
</style>