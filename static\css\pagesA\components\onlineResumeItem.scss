.item {
    display: flex;
    flex-direction: column;
    border-radius: 24rpx;
    margin-bottom: 32rpx;
    padding: 32rpx;
    background: #FFFFFF;
    .up {
        display: flex;
        padding-bottom: 24rpx;
        image {
            width: 96rpx;
            height: 96rpx;
        }
        .info {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            padding-left: 16rpx;
            flex: 1;
            .name {
                font-weight: 500;
                font-size: 28rpx;
                color: #333333;
            }
            .time {
                font-weight: 400;
                font-size: 20rpx;
                color: #999999;
            }
        }
    }
    
    .down {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 24rpx;
        border-top: 1px solid #F5F5F7;
        .desc {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
        }
        .dot {
            display: flex;
            justify-content: center;
            align-items: center;
            transform: rotate(90deg);
            width: 80rpx;
            height: 80rpx;
        }
    }
}

.draft-resume {
    .item{
        display: flex;
        flex-direction: column;
        background: #FFFFFF;
        border-radius: 24rpx;
        position: relative;
        margin-bottom: 32rpx;
    
    }
    .status {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        right: 0;
        top: 0;
        font-size: 28rpx;
        width: 132rpx;
        height: 62rpx;
        border-radius: 0 24rpx 0 24rpx;
    }
    
    .ybm {
        background: #4F8CF0;
        color: #FFFFFF;
    }
    
    .pending {
        background: #4F8CF0;
        color: #FFFFFF;
    }
    
    .reject {
        background: #cccccc;
        color: red;
    }
    
    .agree {
        background: #57D51C;
        color: #FFFFFF;
    }
    
    .btn {
        display: flex;
        align-items: center;
        padding: 0 24rpx;
        height: 50rpx;
        font-weight: 600;
        font-size: 24rpx;
        border-radius: 8rpx;
        margin-left: 14rpx;
    }
    
    .talk {
        background: rgba(79, 140, 240, 0.1);
        color: #4F8CF0;
    }
    .del-but {
        background: red;
        color: white;
    }
    
    .edit-but {
        background: #4F8CF0;
        color: white;
    }
    
    
    .bulerBoxBig {
        width: 80%;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        background: #FFFFFF;
        margin-bottom: 24rpx;
        padding: 32rpx;
    }
    
    .butlerBox {
        display: flex;
        align-items: center;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        background: #FFFFFF;
        margin-bottom: 16rpx;
        
        .butlerHeadImg {
            width: 88rpx;
            height: 88rpx;
            background: #D9D9D9;
            border-radius: 88rpx 88rpx 88rpx 88rpx;
            margin-right: 16rpx;
        }
        
        .butlerText {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            
            .butlerNameCla {
                font-weight: 600;
                font-size: 28rpx;
                color: #333333;
                margin-bottom: 16rpx;
            }
            
            .butlerCompanyCla {
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
            }
        }
    }
    
    
}
