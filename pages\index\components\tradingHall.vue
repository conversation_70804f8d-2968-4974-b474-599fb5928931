<template>
	<view class="container">
		<u-sticky>
			<view class="tabs-container">
				<u-tabs :list="tabsList" :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
					:inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }"></u-tabs>
			</view>
		</u-sticky>
		<view class="content">
			<view class="item" v-for="v in jobList" :key="v.job.job_id" @click="onDetail(v)">
				<view class="main-content">
					<view class="item-start">
						<view class="title-box">
							<image class="avater" :src="v.company.company_logo.thumb_url"></image>
							<view class="title_1">{{v.job.job_info_name}}</view>
							<view class="">
								|
							</view>
							<view style="flex: 1;" class="">
								2025-3-14
							</view>
						</view>
						<view class="type-text">{{v.job.work_type_name}}</view>
						<view class="time">{{v.job.job_introduction}}</view>
					</view>
					<view class="right">
						<image :src="v.company.company_logo.thumb_url"></image>
					</view>
				</view>
				<view class="bottom-info">
					<image class="small-icon"
						src="https://api-test.zhaopinbei.com/storage/uploads/images/vbMJCy8RKqGv0kzde7THmTgD80b7iY73YusWYJoW.png">
					</image>
					<text class="info-text">123</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getJobPublishList
	} from '@/config';

	export default {
		data() {
			return {
				tabsList: [{
						key: '1',
						name: '需求',
					},
					{
						key: '2',
						name: '供应',
					},
					{
						key: '3',
						name: '合作需求',
					},
				],
				jobList: [],
				params: {
					limit: 20,
					page: 1,
					work_type_id: '2',
					job_type: '2'
				},
				isLoading: false,
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				console.log(user)
				if (!user) return {};
				return {
					//member_id: user[user.role_type]?.id || 61,
					member_id: user.member_info.member_id,
				};
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetJobPublishList();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetJobPublishList();
		},
		methods: {
			onDetail(v) {
				// uni.$u.route({
				// 	url: `/pagesA/details/memberJobDetails`,
				// 	params: {
				// 		id: v.job.job_id,
				// 	}
				// })
			},
			onScrollGetList() {
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetJobPublishList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getJobPublishList(params);
				console.log(res)
				if (res.status_code !== '200') return;
				this.jobList = [...this.jobList, ...res.data.jobs_list?.data];
				this.isLoading = false;
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.tabs-container {
			background-color: #f5f5f7;
			z-index: 1;
			position: relative;
			width: 100%;
			top: 0;
			margin-left: -11px;
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			// padding-inline: 32rpx;

			.item {
				background-color: #ffffff;
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.main-content {
					display: flex;
					justify-content: space-between;
					gap: 24rpx;
				}

				.bottom-info {
					display: flex;
					align-items: center;
					justify-content: flex-end;

					.small-icon {
						width: 32rpx;
						height: 32rpx;
					}

					.info-text {
						font-size: 20rpx;
						color: #666666;
					}
				}

				.right {
					image {
						width: 176rpx;
						height: 176rpx;
						border-radius: 24rpx 24rpx 24rpx 24rpx;
					}
				}

				.avater {
					width: 60rpx;
					height: 60rpx;
					border-radius: 50%;
				}

				.item-end {
					display: flex;
					align-items: center;
					gap: 28rpx;


					.name {
						color: #333333;
						font-size: 24rpx;
					}

					.address {
						margin-inline-start: auto;
						color: #999999;
						font-size: 24rpx;
					}
				}

				.item-start {
					width: 400rpx;
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.time {
						color: #999999;
						font-size: 24rpx;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
					}

					.type-text {
						font-size: 28rpx;
						color: #041024;
					}

					.title-box {
						display: flex;
						align-items: center;
						// justify-content: space-between;
						gap: 12rpx;
						font-size: 24rpx;
						color: #666666;

						.title_1 {
							// flex: 1;
							width: 160rpx;
							color: #777777;
							font-size: 24rpx;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.title_2 {
							color: #f98a14;
							font-size: 36rpx;
							white-space: nowrap;
						}
					}
				}
			}
		}
	}
</style>
