<template>
	<view id="app">
		<view class="userDetailsBox">
			<view>
				<view class="userNameCla">{{ detail.member_certification.name }}</view>
				<view class="userPhoneCla">手机号：{{ detail.member.cellphone }}</view>
				<view class="labelBox" v-if="detail.user_headhunter && detail.user_headhunter.label">
					<view class="labelChild" v-for="(labelItem, index) in detail.user_headhunter.label" :key="index" :item="labelItem">
						{{ labelItem }}
					</view>
				</view>
			</view>
			<image :src="detail.member_info ? detail.member_info.image.path_url : ''" mode="" class="userHeadImg"></image>
		</view>

		<!-- <view class="contentBox" v-if="detail.user_headhunter.introduction">
            <view class="introduceTitle">自我介绍</view>
            <view class="introduceText">
                {{ detail.user_headhunter.introduction }}
            </view>
        </view> -->

		<view class="contentBox">
			<view class="introduceTitle">公司信息</view>
			<view class="companyList" @click="companyDetails(detail.company.id)">
				<image :src="detail.company_info.logo.path_url" mode="" class="companyLogoImg"></image>
				<view>
					<view class="companyName">{{ detail.company.name }}</view>
					<view class="companyText" v-if="detail.company_info.size_type_name && detail.company_info.financing_type_name">
						{{ detail.company_info.size_type_name }} | {{ detail.company_info.financing_type_name }}
					</view>
				</view>
			</view>
			<view class="posBox">
				<image src="/static/images/project/position2Img.png" mode="" class="position2Img"></image>
				<view class="posBoxText">{{ detail.company_register_address.address_info }}</view>
				<!-- <map :latitude="" :longitude="" class="mapCla"></map> -->
			</view>
		</view>
		<!-- <address-map title="公司地址" :addresses="detail.addresses"></address-map> -->

		<view class="titleCla" v-if="roleType != 'company'">代招职位</view>
		<view v-if="jobs && roleType != 'company'">
			<view class="contentBox" v-for="(jobItem, index) in jobs" :key="index" :item="jobItem" @click="goJob(jobItem)">
				<view class="contentTop">
					<view class="contentTop_title">{{ jobItem.job.title }}</view>
					<view class="contentTop_num">{{ jobItem.job.salary_info_str }}</view>
				</view>
				<view class="contentTop_one">
					{{ jobItem.company.name }} | {{ jobItem.company_info.financing_type_name }} | {{ jobItem.company_info.size_type_name }}
				</view>
				<view class="contentTop_two">{{ jobItem.intro }}</view>
				<view class="labelBox" v-if="jobItem.company_tags">
					<view class="labelChild" v-for="(tagItem, index) in jobItem.company_tags" :key="index" :item="tagItem">{{ tagItem.title }}</view>
				</view>
				<view class="hrBox"></view>
				<view class="contentBottom">
					<view v-if="jobItem.addresses">
						<view class="contentBottom_pos" v-for="(addressItem, index) in jobItem.addresses" :key="index" :item="addressItem">
							<image
								src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png"
								mode=""
								class="positionIcon"></image>
							<view class="contentBottom_posText">{{ addressItem.address_info }}</view>
						</view>
					</view>
					<view class="contentBottomBtn">聊聊呗</view>
				</view>
			</view>
		</view>
		<view class="performance" v-if="roleType == 'company'" @click="goListDetails(detail.id)">
			<view class="performanceTitle">业绩展示</view>
			<view class="performanceText">
				<view>累计签署千里马</view>
				<view class="numRight" style="color: #4f8cf0">
					<view>{{ detail.member_authorizes_count }}</view>
					<image src="../../static/images/project/rightIcon.png" mode="" class="rightIcon"></image>
				</view>
			</view>
			<view class="performanceText">
				<view>累计签署公司</view>
				<view class="numRight">
					<view>{{ detail.company_authorizes_count }}</view>
				</view>
			</view>
		</view>
		<view style="height: 196rpx"></view>
		<view class="sureBtnBox">
			<view :class="['sureBtnBox_left', collect ? 'yellow' : '']" @click="collectSelect">
				<image src="../../static/images/icon/start.png" mode="" class="startIcon" v-if="collect"></image>
				<image src="../../static/images/icon/nostart.png" mode="" class="startIcon" v-else></image>
				<view v-if="roleType == 'company'">{{ collect ? '已关注' : '关注' }}</view>
				<view v-if="roleType == 'member'">{{ collect ? '已收藏' : '收藏' }}</view>
			</view>
			<view class="btn" @click="communicate('user_headhunter', detail.id)">聊聊呗</view>
		</view>
	</view>
</template>

<script>
import { isAuth } from '@/common/common.js';
import { communicate } from '../../common/common';
import { headhunterJobs } from '../../config/common_api';
import { headhunterDetail } from '../../config/common_api.js';
import { clearCollectHeadhunter, collectHeadhunter } from '../../config/member_api';

export default {
	data() {
		return {
			id: 0,
			collect: false,
			detail: {},
			jobs: [],
			roleType: '',
		};
	},
	onLoad(options) {
		var _this = this;
		_this.id = options.id;
		_this.getHeadhunter();
		_this.roleType = uni.getStorageSync('roleType');
	},

	methods: {
		goNew() {
			uni.navigateTo({
				url: '/pagesA/details/headhuntersDetail',
			});
		},
		communicate,
		goListDetails(id) {
			uni.navigateTo({
				url: '/pagesA/list/obtainItemDetails_qlm_list?id=' + id,
			});
		},
		companyDetails(id) {
			uni.navigateTo({
				url: '/pagesA/details/companyDetail?id=' + id,
			});
		},
		goJob(jobDetail) {
			uni.navigateTo({
				url: '/pagesA/details/memberJobDetails?id=' + jobDetail.job.id,
			});
		},
		getHeadhunter() {
			var _this = this;
			headhunterDetail({
				id: _this.id,
			}).then(response => {
				_this.detail = response.data;
				_this.collect = response.data.collect_status == 1;

				var userId = _this.detail.id;

				if (_this.detail.id > 0) {
					headhunterJobs({
						send_user_id: userId,
						page: 1,
						limit: 5,
					}).then(response => {
						_this.jobs = response.data.data;
					});
				}
			});
		},
		collectSelect() {
			console.log('11111');
			var _this = this;

			if (!isAuth(['login'])) return;

			const res_userInfo = uni.getStorageSync('userInfo');

			var _roleType = res_userInfo.role_type;
			var _collectData = {
				id: _this.detail.id,
			};
			if (_this.collect) {
				clearCollectHeadhunter(_collectData).then(response => {
					if (response.status_code == 200) {
						_this.collect = false;
					}
				});
			} else {
				collectHeadhunter(_collectData).then(response => {
					if (response.status_code == 200) {
						_this.collect = true;
					}
				});
			}
			// switch (_roleType) {
			//     case 'member':
			//         if (_this.collect) {
			//             clearCollectHeadhunter(_collectData).then(response => {
			//                 if (response.status_code == 200) {
			//                     _this.collect = false;
			//                 }
			//             })

			//         } else {
			//             collectHeadhunter(_collectData).then(response => {
			//                 if (response.status_code == 200) {
			//                     _this.collect = true;
			//                 }
			//             })

			//         }
			//         break;
			// }
		},
	},
};
</script>

<style>
page {
	background: #f5f5f7;
}
</style>

<style lang="less" scoped>
view {
	box-sizing: border-box;
}

#app {
	width: 100%;
	padding: 32rpx;
}

.contentBox {
	width: 100%;
	// height: 242rpx;
	background: #ffffff;
	border-radius: 24rpx 24rpx 24rpx 24rpx;
	padding: 32rpx;
	margin-bottom: 32rpx;
}

.userDetailsBox {
	width: 100%;
	// height: 242rpx;
	background: #ffffff;
	border-radius: 24rpx 24rpx 24rpx 24rpx;
	padding: 32rpx;
	display: flex;
	justify-content: space-between;
	margin-bottom: 32rpx;
}

.userNameCla {
	font-weight: 600;
	font-size: 40rpx;
	color: #333333;
	margin-bottom: 22rpx;
}

.userPhoneCla {
	font-weight: 400;
	font-size: 24rpx;
	color: #333333;
	margin-bottom: 24rpx;
}

.labelBox {
	display: flex;
	align-items: center;
}

.labelChild {
	background: #f6f6f6;
	border-radius: 8rpx 8rpx 8rpx 8rpx;
	padding: 12rpx;
	font-weight: 400;
	font-size: 22rpx;
	color: #666666;
	margin-right: 16rpx;
}

.userHeadImg {
	width: 108rpx;
	height: 108rpx;
	background: #d9d9d9;
	border-radius: 50%;
}

.introduceTitle {
	font-weight: 500;
	font-size: 32rpx;
	color: #333333;
	margin-bottom: 24rpx;
}

.introduceText {
	font-weight: 400;
	font-size: 24rpx;
	color: #333333;
	line-height: 42rpx;
	text-align: justify;
}

.companyLogoImg {
	width: 72rpx;
	height: 72rpx;
	background: #d9d9d9;
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	margin-right: 16rpx;
}

.companyList {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
}

.companyName {
	font-weight: 500;
	font-size: 24rpx;
	color: #333333;
	margin-bottom: 10rpx;
}

.companyText {
	font-weight: 400;
	font-size: 20rpx;
	color: #999999;
}

.posBox {
	display: flex;
	align-items: center;
}

.position2Img {
	width: 34rpx;
	height: 34rpx;
	margin-right: 8rpx;
}

.posBoxText {
	font-weight: 400;
	font-size: 24rpx;
	color: #333333;
}

.mapCla {
	width: 100%;
	height: 296rpx;
}

.titleCla {
	font-weight: 600;
	font-size: 32rpx;
	color: #000000;
	margin-bottom: 32rpx;
}

.contentTop {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.contentTop_one {
	font-weight: 400;
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 16rpx;
}

.contentTop_two {
	font-weight: 400;
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;
}

.contentTop_title {
	font-weight: 600;
	font-size: 32rpx;
	color: #333333;
}

.contentTop_num {
	font-weight: 600;
	font-size: 32rpx;
	color: #f98a14;
}

.hrBox {
	width: 100%;
	height: 2rpx;
	background: #f5f5f5;
	margin: 24rpx 0 16rpx 0;
}

.positionIcon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 8rpx;
}

.contentBottom {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.contentBottom_pos {
	display: flex;
	align-items: center;
}

.contentBottom_posText {
	width: 400rpx;
	font-weight: 400;
	font-size: 24rpx;
	color: #333333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.contentBottomBtn {
	width: 144rpx;
	height: 56rpx;
	background: #4f8cf0;
	border-radius: 8rpx 8rpx 8rpx 8rpx;
	font-weight: 600;
	font-size: 24rpx;
	color: #ffffff;
	text-align: center;
	line-height: 56rpx;
}

.sureBtnBox {
	width: 100%;
	height: 196rpx;
	background: #ffffff;
	box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
	position: fixed;
	display: flex;
	justify-content: space-between;
	bottom: 0;
	left: 0;
	align-items: center;
	padding: 24rpx 32rpx 96rpx 32rpx;
}

.sureBtnBox_left {
	display: flex;
	align-items: center;
	font-weight: 600;
	font-size: 28rpx;
	color: #999999;
}

.yellow {
	color: #f9ad14 !important;
}

.startIcon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 8rpx;
}

.btn {
	width: 536rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	text-align: center;
	line-height: 80rpx;

	font-weight: 600;
	font-size: 28rpx;
	color: #ffffff;
}

.performance {
	width: 100%;
	background: #ffffff;
	border-radius: 24rpx 24rpx 24rpx 24rpx;
	padding: 24rpx 32rpx;
}

.performanceTitle {
	font-weight: 500;
	font-size: 32rpx;
	color: #333333;
	margin-bottom: 24rpx;
}

.performanceText {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-weight: 400;
	font-size: 24rpx;
	color: #333333;
	margin-bottom: 24rpx;
}

.rightIcon {
	width: 34rpx;
	height: 34rpx;
	margin-left: 8rpx;
}

.numRight {
	display: flex;
	align-items: center;
}
</style>
