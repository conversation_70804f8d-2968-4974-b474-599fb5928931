<template>
  <view class="job-management">
    <!-- 顶部 Tabs -->
    <view class="tabs-wrap">
      <view class="tabs-box">
        <u-tabs
          :list="tabs"
          :current="tabIndex"
          @change="onTabChange"
          :activeStyle="{ color: '#4F8CF0', fontSize: '32rpx' }"
        ></u-tabs>
      </view>
      <view class="tabs-box tabs_s2">
        <!-- 二级 Tabs -->
        <u-tabs
          :list="subTabs"
          :current="subTabIndex"
          lineWidth="0"
          :activeStyle="{ color: '#4F8CF0', fontSize: '32rpx' }"
        ></u-tabs>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-wrap">
      <u-search
        v-model="search"
        placeholder="请输入职位名称或发布人"
        :showAction="false"
        bgColor="#fff"
        height="64rpx"
      ></u-search>
    </view>

    <!-- 职位卡片列表 -->
    <view class="card-list">
      <u-swipe-action>
        <u-swipe-action-item
          v-for="(item, idx) in filteredList"
          :key="item.id"
          :options="swipeOptions"
          @click="onDelete(idx)"
          class="card-swipe"
        >
          <!-- 卡片内容 -->
          <view class="job-card">
            <view class="card-header">
              <view class="title">
                <text class="job-title">{{ item.title }}</text>
                <text class="job-salary">{{ item.salary }}</text>
              </view>
              <view class="status-tag" :class="item.statusClass">{{
                item.statusText
              }}</view>
            </view>
            <view class="company">{{ item.company }}</view>
            <view class="info-row">
              <text>活动时间：{{ item.date }}</text>
            </view>
            <view class="info-row">
              <text>推送人：{{ item.sender }}</text>
              <text class="ml24">保存者：{{ item.saver }}</text>
            </view>
            <view class="info-row">
              <text>备注：{{ item.remark }}</text>
            </view>
            <view class="card-actions">
              <view @click.stop="onEdit(item)" class="action-btn"> 编辑 </view>
              <view @click.stop="onPublish(item)" class="action-btn btn1">
                发布
              </view>
            </view>
          </view>
        </u-swipe-action-item>
      </u-swipe-action>
    </view>
    <view class="" style="height: 220rpx"> </view>
    <!-- 底部发布按钮 -->
    <view class="btn-warp">
      <view class="btn"> 发布职位 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 顶部 tabs
      tabs: [
        { name: "公司岗位库" },
        { name: "发布记录" },
        { name: "抄送记录" },
      ],
      tabIndex: 0,
      // 二级 tabs
      subTabs: [
        { key: "all", name: "全部岗位" },
        { key: "direct", name: "直招岗位" },
        { key: "agency", name: "代招岗位" },
        { key: "cooperation", name: "合作岗位" },
      ],
      subTabIndex: 0,
      // 搜索
      search: "",
      // 职位列表数据
      jobList: [
        {
          id: 1,
          title: "UI设计师",
          salary: "6~9k",
          status: "draft",
          statusText: "草稿",
          statusClass: "tag-draft",
          company: "学创联盟（北京）网络科技有限公司",
          date: "2024.01.01~2024.05.05",
          sender: "王哈哈",
          saver: "贾真",
          remark: "111",
        },
        {
          id: 2,
          title: "前端开发",
          salary: "8~12k",
          status: "recruiting",
          statusText: "招聘中",
          statusClass: "tag-recruiting",
          company: "学创联盟（北京）网络科技有限公司",
          date: "2024.01.01~2024.05.05",
          sender: "李四",
          saver: "王五",
          remark: "222",
        },
      ],
      swipeOptions: [
        {
          text: "删除",
          style: {
            backgroundColor: "#FE4D4F",
            // width: "140rpx",
            // borderRadius: "0 16rpx 16rpx 0",
          },
        },
      ],
    };
  },
  computed: {
    filteredList() {
      if (!this.search) return this.jobList;
      return this.jobList.filter(
        (item) =>
          item.title.indexOf(this.search) > -1 ||
          item.sender.indexOf(this.search) > -1
      );
    },
  },
  methods: {
    onTabChange(index) {
      //   this.tabIndex = index;
    },
    onEdit(item) {
      uni.showToast({ title: "编辑：" + item.title, icon: "none" });
    },
    onPublish(item) {
      uni.showToast({ title: "发布：" + item.title, icon: "none" });
    },
    onAddJob() {
      uni.showToast({ title: "发布职位", icon: "none" });
    },
    onDelete(idx) {
      this.jobList.splice(idx, 1);
      uni.showToast({ title: "已删除", icon: "none" });
    },
  },
};
</script>
<style lang="less">
body {
  background: #f5f6fa;
}
</style>

<style lang="less" scoped>
.job-management {
  padding-bottom: 120rpx;
  .tabs-wrap {
    background: #fff;
    padding: 0 32rpx;
    .tabs-box {
      height: 106rpx;
    }
  }
  .sub-tabs {
    display: flex;
    background: #fff;
    padding: 0 32rpx;
    margin-top: 24rpx;
    .sub-tab {
      font-size: 28rpx;
      color: #666;
      margin-right: 48rpx;
      padding: 32rpx 0;
      position: relative;
      &.active {
        color: #2979ff;
        font-weight: bold;
        &::after {
          content: "";
          display: block;
          width: 60rpx;
          height: 6rpx;
          background: #2979ff;
          border-radius: 3rpx;
          position: absolute;
          left: 50%;
          bottom: 8rpx;
          transform: translateX(-50%);
        }
      }
    }
  }
  .search-wrap {
    padding: 32rpx 32rpx 0 32rpx;
    // background: #fff;
  }
  .card-list {
    padding: 32rpx;
    .card-swipe {
      margin-bottom: 24rpx;
    }
    .job-card {
      background: #fff;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
      padding: 32rpx;
      position: relative;
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24rpx;
        .title {
          display: flex;
          align-items: baseline;
          .job-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #222;
            margin-right: 16rpx;
          }
          .job-salary {
            font-size: 28rpx;
            color: #f98a14;
            font-weight: bold;
          }
        }
        .status-tag {
          position: absolute;
          top: 0;
          right: 0;
          padding: 20rpx 24rpx;
          border-radius: 0rpx 24rpx 0rpx 24rpx;
          font-size: 24rpx;
          color: #fff;

          &.tag-draft {
            background: #f9ad14;
          }
          &.tag-recruiting {
            background: #57d51c;
          }
        }
      }
      .company {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 24rpx;
      }
      .info-row {
        font-size: 24rpx;
        color: #888;
        margin-bottom: 24rpx;
        .ml24 {
          margin-left: 24rpx;
        }
      }
      .card-actions {
        display: flex;
        justify-content: flex-end;
        margin-top: 16rpx;
        .action-btn {
          margin-left: 24rpx;
          width: 168rpx;
          height: 64rpx;
          background: #f5f5f7;
          border-radius: 12rpx;
          font-size: 28rpx;
          color: #4f8cf0;
          text-align: center;
          line-height: 64rpx;
        }
        .btn1 {
          background: linear-gradient(92deg, #4f8cf0 0%, #0061ff 100%);
          color: #ffffff;
        }
      }
    }
  }
  .btn-warp {
    position: fixed;
    bottom: 0;
    width: 750rpx;
    height: 196rpx;
    background: #ffffff;
    box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: center;

    .btn {
      width: 686rpx;
      height: 80rpx;
      background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      font-size: 28rpx;
      color: #ffffff;
      text-align: center;
      line-height: 80rpx;
      margin-top: 24rpx;
    }
  }
}

/deep/ .u-swipe-action-item__content {
  border-radius: 24rpx !important;
}
/deep/ .u-swipe-action-item__right__button {
  border-radius: 24rpx !important;
  margin-left: 24rpx !important;
}
/deep/ .u-swipe-action-item {
  margin-bottom: 24rpx !important;
}
</style>
