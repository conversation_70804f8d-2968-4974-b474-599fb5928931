<template>
	<view class="container">
		<u-sticky>
			<view class="sticky-container">
				<u-tabs :list="tabsList" :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
					:inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }" @click="onTabsItemClick"></u-tabs>
			</view>
		</u-sticky>

		<scroll-view :scroll-y="true" class="scroll-view">
			<view class="scroll-container">
				<template v-if="tabsActiveKey === '1'">
					<ArticleTabPage />
				</template>
				<template v-if="tabsActiveKey === '2'">
					<QuestionTabPage />
				</template>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import ArticleTabPage from './components/article.vue';
	import QuestionTabPage from './components/question.vue';
	export default {
		components: {
			ArticleTabPage,
			QuestionTabPage
		},
		data() {
			return {
				tabsActiveKey: '1',
				tabsList: [{
						key: '1',
						name: '文章',
					},
					{
						key: '2',
						name: '问答',
					},
				],
			};
		},
		methods: {
			onTabsItemClick(v) {
				this.tabsActiveKey = v.key;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.container {
		height: 100vh;
		background-color: #f5f5f7;
		display: flex;
		flex-direction: column;

		.sticky-container {
			background-color: #ffffff;
			padding-block: 24rpx;
			border-end-start-radius: 24rpx;
			border-end-end-radius: 24rpx;
		}

		.scroll-view {
			flex: 1;
			overflow-y: auto;

			.scroll-container {
				padding-block: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 32rpx;
				padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
				padding-block-end: calc(32rpx + env(safe-area-inset-bottom));
			}
		}
	}
</style>
