<template>
	<view class="warp">
		<view class="inner">
			<view class="tips">
				<view class="title">
					填写我的领地有什么用？
				</view>
				<view class="cont">
					添加主营业务，方便千里马快速了解公司。平台也将根据你所填写的业务，更好的为你推荐符合要求的千里马。
				</view>
			</view>
			<view class="forms">
				<view class="line">
					我的领地
				</view>
				<view class="" @click="show = true">
					<u--input v-model="ident" disabled border="bottom" disabledColor="#fff"
						suffixIcon="arrow-down"></u--input>
				</view>
				<view class="" v-if="ident == '地区'">
					<view class="line">
						1、历史沿革
					</view>
					<u--textarea height="184rpx" placeholder="请填写历史沿革" maxlength="1000" count></u--textarea>
					<view class="line">
						2、地理位置
					</view>
					<u--textarea height="184rpx" placeholder="请填写地理位置" maxlength="1000" count></u--textarea>
					<view class="line">
						3、历史文化
					</view>
					<u--textarea height="184rpx" placeholder="请填写历史文化" maxlength="1000" count></u--textarea>
					<view class="line">
						4、旅游景点
					</view>
					<u--textarea height="184rpx" placeholder="请填写旅游景点" maxlength="1000" count></u--textarea>
					<view class="line">
						5.经济发展
					</view>
					<u--textarea height="184rpx" placeholder="请填写经济发展" maxlength="1000" count></u--textarea>
					<view class="line">
						6、行政区划
					</view>
					<u--textarea height="184rpx" placeholder="请填写行政区划" maxlength="1000" count></u--textarea>
					<view class="line">
						7、特色美食
					</view>
					<u--textarea height="184rpx" placeholder="请填写特色美食" maxlength="1000" count></u--textarea>
				</view>
				<view class="" v-if="ident == '学校'">
					<view class="line">
						1、学校概况
					</view>
					<u--textarea height="184rpx" placeholder="请填写学校概况" maxlength="1000" count></u--textarea>
					<view class="line">
						2、学科实力
					</view>
					<u--textarea height="184rpx" placeholder="请填写学科实力" maxlength="1000" count></u--textarea>
					<view class="line">
						3、院系设置
					</view>
					<u--textarea height="184rpx" placeholder="请填写院系设置" maxlength="1000" count></u--textarea>
					<view class="line">
						4、科研与学术
					</view>
					<u--textarea height="184rpx" placeholder="请填写科研与学术" maxlength="1000" count></u--textarea>
					<view class="line">
						5.校园环境与生活
					</view>
					<u--textarea height="184rpx" placeholder="请填写校园环境与生活" maxlength="1000" count></u--textarea>
					<view class="line">
						6、就业与影响力
					</view>
					<u--textarea height="184rpx" placeholder="请填写就业与影响力" maxlength="1000" count></u--textarea>
					<view class="line">
						7、报考信息
					</view>
					<u--textarea height="184rpx" placeholder="请填写报考信息" maxlength="1000" count></u--textarea>
				</view>
				<view class="line">
					8、总结
				</view>
				<u--textarea height="184rpx" placeholder="请填写总结信息" maxlength="1000" count></u--textarea>
				<view class="line">
					9、上传图片
				</view>
				<u-upload :fileList="fileList1" name="1" multiple :maxCount="10"></u-upload>
				<view class="tips2">
					<text>上传注意事项</text> <u-icon name="question-circle-fill" color="#999999" size="20"></u-icon>
				</view>
			</view>
		</view>
		<view class="" style="height: 220rpx;">

		</view>
		<view class="btn-warp">
			<view class="btn">
				保存
			</view>
		</view>
		<u-action-sheet :actions="list" :title="title" :show="show" cancelText="取消" :closeOnClickOverlay="true"
			:closeOnClickAction="true" @close="show = false" @select="selectClick"></u-action-sheet>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				fileList1: [],
				ident: '地区',
				show: false,
				list: [{
						name: '地区',
					},
					{
						name: '学校',
					},
				],
			}
		},
		methods: {
			selectClick(index) {
				this.ident = index.name
			}
		}
	}
</script>

<style lang="less" scoped>
	.inner {
		padding: 32rpx;

		.tips {
			padding: 40rpx;
			background: linear-gradient(180deg, #F2F8FF 0%, #FFFFFF 100%);
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			border: 2rpx solid rgba(215, 233, 255, 1);
			margin-bottom: 32rpx;

			.title {
				font-size: 24rpx;
				color: #333333;
				margin-bottom: 24rpx;
			}

			.cont {
				font-size: 20rpx;
				color: #8D9AAA;
			}
		}

		.line {
			margin-top: 24rpx;
			margin-bottom: 24rpx;
		}

		.tips2 {
			margin-top: 16rpx;
			display: flex;
			align-items: center;
			gap: 16rpx;
			font-size: 24rpx;
			color: #999999;
		}
	}

	.btn-warp {
		position: fixed;
		bottom: 0;
		width: 750rpx;
		height: 196rpx;
		background: #FFFFFF;
		box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
		display: flex;
		justify-content: center;

		.btn {
			width: 686rpx;
			height: 80rpx;
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			border-radius: 16rpx 16rpx 16rpx 16rpx;
			font-size: 28rpx;
			color: #FFFFFF;
			text-align: center;
			line-height: 80rpx;
			margin-top: 24rpx;
		}
	}
</style>