page {
    background: #f5f5f7;
}

.header {
    padding: 32rpx;
    
    .search-wrap {
    }
    
    .filters {
        display: flex;
        margin-top: 32rpx;
        
        .filter {
            display: flex;
            align-items: center;
            height: 48rpx;
            background-color: #FFFFFF;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding: 0 12rpx;
            margin-right: 12rpx;
            border-radius: 8rpx;
            
            image {
                width: 24rpx;
                height: 24rpx;
            }
        }
    }
}

.list {
    padding: 0 32rpx 32rpx 32rpx;
}