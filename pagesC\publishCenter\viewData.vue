<template>
	<view class="warp">
		<view class="quer">
			<text>近7日数据</text>
			<u-icon name="play-right-fill" color="#777" size="12"></u-icon>
		</view>
		<view class="inner-warp">
			<view class="box" v-for="item in list" @click="show = true">
				<view class="top">
					<text>{{item.title}}</text>
					<img src="https://api-test.zhaopinbei.com/storage/uploads/images/cXTow2BggOdtY3t8gXb3Q2Hi5SnePYJMLMQsoSxz.png" alt="" />
				</view>
				<view class="mid">
					{{item.num}}
				</view>
				<view class="btm">
					昨日无变化
				</view>
			</view>
		</view>
		<u-popup :show="show" mode="bottom" @close="show = false">
			<view style="height: 646rpx;width: 750rpx;">
				<LineChart></LineChart>
			</view>
		</u-popup>
	</view>
</template>

<script>
	import LineChart from '@/components/lineChart.vue'
	export default {
		data() {
			return {
				list: [{
						title: '展示',
						num: 0
					},
					{
						title: '浏览',
						num: 0
					},
					{
						title: '点赞',
						num: 0
					},
					{
						title: '评论',
						num: 0
					},
					{
						title: '收藏',
						num: 0
					},

				],
				show: false
			}
		},
		methods: {

		},
		components: {
			LineChart
		}
	}
</script>
<style>
	page {
		background: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 32rpx 32rpx;


		.quer {
			width: 160rpx;
			display: flex;
			align-items: center;
			padding: 8rpx 16rpx;
			background: #FFFFFF;
			border-radius: 8rpx;
			font-size: 28rpx;
			color: #777777;
			margin: 32rpx 0;

			text {
				margin-right: 6rpx;
			}
		}

		.inner-warp {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
		}

		.box {
			width: 280rpx;
			padding: 24rpx;
			height: 150rpx;
			border-radius: 22rpx;
			background: #fff;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			margin-bottom: 20rpx;

			.top {
				display: flex;
				justify-content: space-between;
				align-items: center;

				img {
					width: 24rpx;
					height: 24rpx;
				}
			}

			.mid {
				font-size: 32rpx;
				font-weight: bold;
			}

			.btm {
				font-size: 24rpx;
				color: #999;
			}
		}
	}
</style>
