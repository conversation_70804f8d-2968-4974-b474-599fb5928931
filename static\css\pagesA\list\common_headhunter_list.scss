page {
    background-color: #f5f5f7;
}

view {
    box-sizing: border-box;
}

#app {
    width: 100%;
    padding: 32rpx;
}

.tabs {
    margin-bottom: 32rpx;
}

.footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    bottom: 0;
    height: 120rpx;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 10;
    border-radius: 24rpx 24rpx 0 0;
    
    .next {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        color: #FFFFFF;
        height: 88rpx;
        width: 90%;
        border-radius: 16rpx;
    }
    
    .sure {
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        color: #FFFFFF;
    }
}

.selectedCla {
    border: 2rpx solid #4F8CF0 !important;
}

.item {
    display: flex;
    align-items: center;
    border-radius: 24rpx;
    background: #FFFFFF;
    padding: 32rpx;
    margin-bottom: 24rpx;
	justify-content: space-between;
    border: 2rpx solid transparent;
    // &:last-child{
    // 	margin-bottom: 0;
    // }
    & > image {
        width: 104rpx;
        height: 104rpx;
    }
    
    .info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        padding-left: 24rpx;
        
        .user {
            display: flex;
            align-items: center;
            
            .name {
                font-weight: 600;
                font-size: 32rpx;
                color: #333333;
            }
            
            .status {
                display: flex;
                align-items: center;
                padding: 0 12rpx;
                height: 40rpx;
                font-weight: 600;
                font-size: 20rpx;
                margin-left: 16rpx;
                border-radius: 8rpx;
            }
            
            .yrz {
                background: rgba(87, 213, 28, 0.1);
                color: #57D51C;
            }
            
            .wrz {
                background: rgba(249, 173, 20, 0.1);
                color: #F9AD14;
                
            }
        }
        
        .phone {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            margin-top: 24rpx;
        }
    }
    
    .reason {
        font-weight: 500;
        font-size: 24rpx;
        color: #FE4D4F;
    }
}

.list {
	.leftCon {
		display: flex;
		.img {
			width: 104rpx;
			height: 104rpx;
			border-radius: 50%;
		}
	}
	.rightCon {
		background-color: #f1f6fe;
		border-radius: 10rpx;
		padding: 6rpx 12rpx;
		color: #2672ee;
		font-size: 26rpx;
	}
}