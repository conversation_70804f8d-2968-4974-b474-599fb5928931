<template>
	<!-- 项目描述-->
	<view class="container">
		<view class="context">
			<!-- 输入区域 -->
			<view class="input-area">
				<view class="section">
					<input class="input" placeholder="1.主要负责的内容 2.存在的问题 3.如何克服解决的 4.取得哪些成绩" v-model="inputValue"
						maxlength="1000" />
				</view>
				<view class="box">
					<view class="text-box">
						<text :class="inputValue.length == 0 ? 'active':'count'">
							{{inputValue.length}}
						</text>
						<text class="count-bottom">
							/1000</text>
					</view>
				</view>
			</view>
		</view>

		<view class="footer">
			<button class="confirm-btn" @click="finish">确定</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				inputValue: '',
			}
		},
		methods: {},
	};
</script>

<style scoped>
	.container {
		padding: 20rpx 0rpx;
		background-color: #fff;
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.context {
		flex: 1;
	}

	.scroll-Y {
		height: 774rpx;
	}

	.tip-bar {
		background-color: #ffd6b3;
		padding: 10rpx 15rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.tip-text {
		color: #f60;
		font-size: 28rpx;
		margin-left: 16rpx;
	}

	.close-icon {
		color: #ccc;
		font-size: 32rpx;
	}

	.input-area {
		margin-top: 20rpx;
		padding: 32rpx;
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 1173rpx;
	}

	.label {
		font-size: 32rpx;
		color: #333;
	}

	.input {
		width: 100%;
		font-size: 32rpx;
		border: none;
		padding: 10rpx 0;
	}

	.section {
		/* margin-bottom: 30rpx; */
		width: 686rpx;
		padding: 32rpx 0rpx;
		/* border-bottom: 1rpx solid rgba(230, 230, 230, 1); */
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		flex: 1;
	}

	.count {
		color: rgba(79, 140, 240, 1);
		font-size: 28rpx;
		float: right;
		margin-top: 24rpx;
	}

	.count-bottom {
		color: rgba(153, 153, 153, 1);
		font-size: 28rpx;
		margin-top: 24rpx;
	}

	.text-box {
		display: flex;
		justify-content: flex-end;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-image: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.Model {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.model-left {
		width: 104rpx;
		height: 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.popup-content {
		/* width: 750rpx;
		height: 774rpx; */
		display: flex;
		flex-direction: column;
		align-items: center;

	}

	.popup-box {
		padding: 24rpx 32rpx;
	}

	.popup-context {
		display: flex;
		justify-content: space-between;
	}

	.popup-title {
		font-size: 32rpx;
		/* font-weight: bold; */
	}

	.popup-subtitle {
		font-size: 28rpx;
		color: #666;
		margin-left: 42rpx;
	}

	.popup-active {
		background-image: url('https://api-test.zhaopinbei.com/storage/uploads/images/sySQp2NnCVbjDxzMArI8JLcNwIZRVDHGn1KKKS5N.png');
		background-size: auto;
		font-size: 32rpx;
		font-weight: bold;
		color: rgba(51, 51, 51, 1);
	}

	.popup-close {
		font-size: 36rpx;
		cursor: pointer;
	}

	.popup-content {
		/* padding: 30rpx; */
		display: flex;
		justify-content: space-between;
	}

	.recommendation {
		margin-bottom: 30rpx;
	}

	.recommendation-desc {
		font-size: 24rpx;
		color: #999;
	}

	.resume-example {
		display: flex;
		align-items: center;
		margin-bottom: 10px;
		width: 686rpx;
		/* height: 364rpx; */
		padding: 24rpx;
		border-radius: 12rpx;
		background-color: rgba(245, 245, 245, 1);
	}

	.resume-icon {
		width: 30px;
		height: 30px;
		background-color: #ddd;
		border-radius: 50%;
		margin-right: 10px;
	}

	.resume-text {
		flex: 1;
	}

	.resume-title {
		font-size: 14px;
		margin-bottom: 5px;
	}

	.resume-detail {
		font-size: 26rpx;
		line-height: 1.5;
	}

	.resume-collapse {
		color: #1aad19;
		cursor: pointer;
	}

	.popup-footer {
		text-align: center;
		padding: 15px;
	}

	.save-button {
		width: 100%;
		padding: 10px;
		background-color: #007aff;
		color: #fff;
		border: none;
		border-radius: 5px;
		font-size: 14px;
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
	}

	.active {
		color: red;
		margin-top: 24rpx;
		font-size: 28rpx;
	}

	.popup-desc {
		font-size: 28rpx;
		color: #999;
		margin-top: 32rpx;
		margin-bottom: 24rpx;
		align-self: flex-start;
	}

	.template-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.template-item {
		width: 283rpx;
		margin-bottom: 32rpx;
		background-color: #F5F7FA;
		padding: 24rpx;
		border-radius: 16rpx;
		box-shadow: 0 0 12px rgba(0, 0, 0, 0.13);
	}

	.template-title {
		display: flex;
		align-items: center;
	}

	.template-icon {
		width: 60px;
		height: 60rpx;
		background-color: #007AFF;
		margin-bottom: 20rpx;
	}

	.template-name {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
	}

	.template-desc {
		font-size: 20rpx;
		color: #666;
		margin-bottom: 20rpx;
	}

	.template-params {
		margin-bottom: 20rpx;
		align-self: flex-start;
	}

	.param {
		font-size: 20rpx;
		color: #999;
		display: block;
	}

	.use-btn {
		background-color: #007AFF;
		color: white;
		border: none;
		font-size: 24rpx;
		border-radius: 50rpx;
		width: 128rpx;
		height: 62rpx;
		margin: 0px;
	}

	.template-content {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.resume-box {
		display: flex;
		flex-direction: column;
		align-self: center;
		/* height: 280rpx; */
	}
</style>
