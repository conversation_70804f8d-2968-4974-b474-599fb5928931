<template>
	<view class="activity-list">
		<view class="flex-tabs">
			<view @click="isShow('detailsShow')" :class="{ 'active': currentTab === 'detailsShow' }">特准招聘</view>
			<view @click="isShow('qlmActiveShow')" :class="{ 'active': currentTab === 'qlmActiveShow' }">招聘会</view>
			<view @click="isShow('takePartShow')" :class="{ 'active': currentTab === 'takePartShow' }">宣讲会</view>
		</view>
		<view v-for="(activity, index) in activities" :key="index" class="activity-card">
			<view class="activity-box card">
				<view class="activity-left">
					<view class="activity-thumbnail" :style="{ background: '#ccc' }"></view>
				</view>
				<view class="activity-right">
					<view class="activity-title">{{ activity.activityTitle }}</view>
					<view class="activity-date">{{ activity.activityDate }}</view>
					<view class="activity-status" :class="{ 'active-status': activity.isOngoing }">
						{{ activity.statusText }}
					</view>
				</view>
			</view>
			<view class="activity-bottom">
				<view class="activity-start-time">
					{{ activity.startTime }}
				</view>
				<view class="activity-view-detail" @click="viewDetail(activity)">
					<span>
						查看详情
					</span>
					<uni-icons type="arrow-right" size="14" color="rgba(79, 140, 240, 1)"></uni-icons>
				</view>
			</view>
		</view>
	</view>

</template>
<script>
	import qlmActiveShow from '../qlmActiveShow.vue'
	export default {
		components: {
			qlmActiveShow
		},
		data() {
			return {
				activities: [{
						activityTitle: 'xxx公司宣讲会',
						activityDate: '2022年12月12日',
						isOngoing: true,
						statusText: '进行中',
						startTime: '9月15日 08:00:00开始'
					},
					// 可以在这里添加更多的活动数据
				],
				currentTab: '',
			};
		},
		created() {
			console.log(11111111);
			this.currentTab = 'takePartShow'
		},
		methods: {
			viewDetail() {
				// 跳转到详情页
				uni.navigateTo({
					url: '/pagesA/details/QlmjobFair/takePart'
				})
			},
			isShow(name) {
				this.qlmActiveDetails = false;
				this.detailsShow = false;
				this.takePartShow = false;

				if (name === 'qlmActiveShow') {
					this.currentTab = name
					uni.redirectTo({
						url: "/pagesA/list/job_fair_list"
					})
					this.qlmActiveDetails = true;
				} else if (name === 'detailsShow') {
					this.currentTab = name
					uni.redirectTo({
						url: '/pagesA/details/qlmActiveShow'
					})
					console.log(this.detailsShow);
				} else if (name === 'takePartShow') {
					this.currentTab = name
					uni.redirectTo({
						url: '/pagesA/details/QlmjobFairTalk/TalkList'
					})
				}
			}
		}
	};
</script>

<style scoped>
	.activity-list {
		display: flex;
		flex-direction: column;
		align-items: center;
		height: 100vh;
		background-color: rgba(245, 245, 247, 1);
	}

	.activity-card {
		display: flex;
		/* background-color: #f9f9f9; */
		border-radius: 10px;
		width: 686rpx;
		justify-content: center;
		height: 320rpx;
		position: relative;
	}

	.card {
		background: rgba(255, 255, 255, 1);
		border-radius: 16rpx;
	}

	.activity-box {
		display: flex;
		height: 185rpx;
		justify-content: center;
		align-items: center;
		margin-bottom: 32rpx;
		padding: 32rpx;
	}

	.activity-left {
		width: 80px;
		height: 80px;
		margin-right: 15px;
	}

	.activity-thumbnail {
		width: 100%;
		height: 100%;
	}

	.activity-right {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.activity-title {
		font-size: 18px;
		margin-bottom: 16rpx;
		width: 404rpx;
		font-weight: bold;
	}

	.activity-date {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 16rpx;
		width: 218rpx;
	}

	.activity-status {
		background-color: rgba(87, 213, 28, 0.10);
		color: rgba(87, 213, 28, 1);
		width: 104rpx;
		height: 50rpx;
		border-radius: 8rpx;
		/* text-align: center; */
		font-size: 24rpx;
		font-weight: bold;
		/* vertical-align: middle; */
		display: flex;
		justify-content: center;
		align-items: center;
	}


	.active-status {
		background-color: rgba(87, 213, 28, 0.10);
		color: rgba(87, 213, 28, 1);
		width: 104rpx;
		height: 50rpx;
		border-radius: 8rpx;
		/* text-align: center; */
		font-size: 24rpx;
		font-weight: bold;
		/* vertical-align: middle; */
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.activity-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		align-self: end;
		position: absolute;
		background: rgba(245, 252, 252, 1);
		bottom: 6rpx;
		width: 574rpx;
		height: 66rpx;
		padding: 0rpx 24rpx 0rpx 24rpx;
	}

	.activity-start-time {
		font-size: 14px;
		color: #666;
	}

	.activity-view-detail {
		font-size: 24rpx;
		color: rgba(79, 140, 240, 1);
		cursor: pointer;
	}

	.activity-view-detail sapn {
		margin-right: 12rpx;
	}

	.flex-tabs {
		width: 508rpx;
		height: 44rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-left: 32rpx;
		margin-top: 32rpx;
		margin-bottom: 62rpx;
		font-size: 28rpx;
		font-weight: bold;
	}

	.active {
		color: rgba(79, 140, 240, 1);
		font-size: 32rpx;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.active:before {
		content: '';
		position: absolute;
		width: 32rpx;
		height: 6rpx;
		background-color: rgba(79, 140, 240, 1);
		border-radius: 50rpx;
		bottom: -8rpx;
	}
</style>