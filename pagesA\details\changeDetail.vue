<template>
    <view id="app">

        <view class="changeTitle">
            申请变更公司
        </view>

        <view class="companyCla">
            <image :src="company.logo.thumbnail_path_url" mode="" class="companyLogoImg"></image>
            <view class="companyCla_right">
                <view class="companyCla_companyName">{{companyList.company.name}}</view>
                <view class="companyCla_center">
                    {{companyList.company.short_name}} | {{companyList.company_info.financing_type_name}} |
                    {{companyList.company_info.size_type_name}}
                </view>
                <view class="labelBox">
                    <view class="labelBox_child" v-for="(item,index) in companyList.company_tags" :key="item.id">
                        {{item.title}}
                    </view>
                </view>
            </view>
        </view>

        <view class="changeTitle">
            现任人员
        </view>

        <view class="bulerBoxBig">
            <view class="butlerBox">
                <image :src="companyList.authorized_member_info.image.thumbnail_path_url" mode="" class="butlerHeadImg">
                </image>
                <view class="butlerText">
                    <view class="butlerNameCla">{{companyList.authorized_member_certification.name}}</view>
                    <view class="butlerCompanyCla">{{companyList.company.name}}</view>
                </view>
            </view>
            <view class="labelBox">
                <view class="labelBox_child">世界500强</view>
                <view class="labelBox_child">上市公司</view>
                <view class="labelBox_child">游戏工厂</view>
            </view>
        </view>

        <!-- <textarea name="" id="" cols="30" rows="10" class="reasonBox" placeholder="请输入变更原因" v-model="reasonContent"></textarea> -->

        <view class="changeTitle">
            变更人员
        </view>


        <view class="butlerBox" @click="selectButler" style="padding: 32rpx;">
            <view class="selectPeoCla" v-if="!butlerItem">
                <view>选择人员</view>
                <image src="/static/images/project/rightIcon.png" mode="" class="rightIcon"></image>
            </view>
            <image :src="butlerItem.head" mode="" class="butlerHeadImg" v-if="butlerItem"></image>
            <view class="butlerText" v-if="butlerItem">
                <view class="butlerNameCla">{{butlerItem.name}}</view>
                <view class="butlerCompanyCla">{{butlerItem.phone}}</view>
            </view>
        </view>

        <view class="btnBox1" v-if="roleType == 'headhunters'">
            <view class="btnCla btnRed" @click="goBack">拒绝</view>
            <view :class="!butlerItem?'btnCla btnGrey': 'btnCla btnBlue'" @click="trueClick">同意</view>
        </view>
        <view class="btnBox" v-else>
            <view class="upCla" @click="updateUserAudit">提交申请</view>
        </view>


    </view>
</template>

<script>
    import {
        getAuthorizeShow,
        updateUserAudit
    } from "../../config/company_api.js"

    export default {
        data() {
            return {
                head: '',
                butlerItem: '',
                companyList: '', // 公司信息
                roleType: '',
                inputContent: '请输入驳回原因'
            }
        },
        onLoad(option) {
            this.companyId = option.id * 1
            this.roleType = uni.getStorageSync("roleType")
            if (this.roleType != 'headhunters') {
                this.getAuthorizeShow(option.id * 1)
            }
        },
        onShow() {
            this.butlerItem = uni.getStorageSync('selectedInd');
        },
        methods: {
            trueClick() {
                console.log("同意变更")
            },
            goBack() {
                uni.showModal({
                    title: '拒绝原由',
                    content: '',
                    cancelText: '取消',
                    confirmText: '确定',
                    editable: true,
                    success: (res) => {
                        if (res.confirm) {
                            // 用户点击了同意
                            console.log('用户输入：', this.inputContent);
                            // 返回上一级
                            uni.navigateBack()
                        } else if (res.cancel) {
                            // 用户点击了拒绝
                            console.log('用户取消');
                        }
                    },
                });
            },
            selectButler() {
                uni.navigateTo({
                    url: '/pagesA/list/common_headhunter_list?company_id=' + this.companyId,
                })
            },
            async getAuthorizeShow(id) {
                let params = {
                    company_id: id,
                }
                const {
                    status_code,
                    data,
                    message
                } = await getAuthorizeShow(params)
                // console.log(data)
                this.companyList = data
            },
            // 提交申请
            async updateUserAudit() {
                if (!this.butlerItem) {
                    this.selectButler()
                    return
                }
                let params = {
                    company_id: this.companyId,
                    user_id: this.butlerItem.id
                }
                const {
                    status_code,
                    data,
                    message
                } = await updateUserAudit(params)

                if (status_code == 200) {
                    // 提交审核后清空本地变更人员
                    uni.removeStorageSync('selectedInd')
                    uni.navigateBack()
                }
            }
        }
    }
</script>
<style>
    page {
        background-color: #F5F5F7;
    }
</style>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .changeTitle {
        font-weight: bold;
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
    }

    .bulerBoxBig {
        width: 100%;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        background: #FFFFFF;
        margin-bottom: 24rpx;
        padding: 32rpx;
    }

    .butlerBox {
        display: flex;
        align-items: center;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        background: #FFFFFF;
        margin-bottom: 16rpx;

        .butlerHeadImg {
            width: 88rpx;
            height: 88rpx;
            background: #D9D9D9;
            border-radius: 88rpx 88rpx 88rpx 88rpx;
            margin-right: 16rpx;
        }

        .butlerText {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .butlerNameCla {
                font-weight: 600;
                font-size: 28rpx;
                color: #333333;
                margin-bottom: 16rpx;
            }

            .butlerCompanyCla {
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
            }
        }
    }

    .reasonBox {
        width: 100%;
        min-height: 166rpx;
        height: auto;
        padding: 32rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        margin: 24rpx 0;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        line-height: 32rpx;
        text-align: justify;
        box-sizing: border-box;
    }

    .rightIcon {
        width: 32rpx;
        height: 32rpx;
    }

    .selectPeoCla {
        width: 100%;
        padding: 32rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 32rpx;
    }

    .btnBox {
        width: 100%;
        height: 196rpx;
        padding: 24rpx 32rpx;
        border-radius: 24rpx 24rpx 0 0;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 34rpx;
        color: #FFFFFF;
        position: fixed;
        bottom: 0;
        left: 0;
        box-sizing: border-box;
    }

    .upCla {
        width: 100%;
        height: 80rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 80rpx;
    }

    .companyCla {
        width: 100%;
        padding: 32rpx;
        background: #FFFFFF;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
    }

    .companyLogoImg {
        width: 144rpx;
        height: 144rpx;
        margin-right: 24rpx;
    }

    .companyCla_right {
        flex: 1;
        height: 144rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .companyCla_companyName {
        width: 450rpx;
        font-weight: bold;
        font-size: 28rpx;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .companyCla_center {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
    }

    .labelBox {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 22rpx;
        color: #666666;
    }

    .labelBox_child {
        min-width: 130rpx;
        max-width: 150rpx;
        text-align: center;
        padding: 12rpx;
        background: #F6F6F6;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        margin-right: 16rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .btnBox1 {
        width: 100%;
        height: 196rpx;
        padding: 28rpx 42rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 24rpx 24rpx 0 0;
        background: #FFFFFF;
        font-weight: 600;
        font-size: 34rpx;
        color: #FFFFFF;
        position: fixed;
        bottom: 0;
        left: 0;
        box-sizing: border-box;
    }

    .btnCla {
        width: 330rpx;
        height: 100rpx;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        text-align: center;
        line-height: 100rpx;
    }

    .btnRed {
        background: #FE4D4F;
        margin-right: 26rpx;
    }

    .btnGrey {
        background: #CCCCCC;
    }

    .btnBlue {
        background: #4F8CF0;
    }
</style>