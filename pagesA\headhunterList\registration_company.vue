<template>
    <view id="app">
        <view class="listBox" v-for="(item,index) in list" :key="item.id">
            <view class="listBox_top">
                <image :src="item.company_info.logo.thumbnail_path_url" mode="" class="companyLogoImg"></image>
                <view>
                    <view class="companyNameCla">{{ item.company.name }}
                        <view :class="[item.payment_status_name=='已支付'?'payTypeCla':'payTypeCla1']">{{ item.payment_status_name }}</view>
                    </view>
                    <view>{{ item.created_at}}</view>
                </view>
            </view>
            <view class="listBox_bottom">
                <view :class="['statusType',item.status_name=='驳回'?'statusType1':item.status_name=='通过' ?'statusType2':'statusType']">{{ item.status_name }}</view>
                <view class="processCla" @click="audit(item,index)" v-if="item.status==2">审核</view>
            </view>
        </view>
        <Pages :status="status"></Pages>
    </view>
</template>

<script>
    import {
        activeAudit,
        activeHandle
    } from '../../config/headhunterList_api.js'
    import Pages from "../../components/pages.vue";
    export default {
        data() {
            return {
                status: 'loadmore',
                more: false,
                page: 1,
                limit: 10,
                list: [],
                options: [
                    '通过',
                    '驳回'
                ]
            }
        },
        onLoad(options) {
            if(options.id) {
                this.jobActiveCompany(options.id)
            } else {
                this.jobActiveCompany()
            }
        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
               this.jobActiveCompany()
            } else {
                this.status = 'nomore'
            }
        },
        methods: {
            async audit(e,index) {
                console.log(e)
                uni.showActionSheet({
                    itemList: this.options, // 显示的选项
                    success: async (res) => {
                        console.log("选中了",res)
                        if(res.tapIndex==1) {
                            uni.showModal({
                                title: '驳回',
                                editable: true,
                                placeholderText: '请输入驳回原因',
                                success: async (result)=> {
                                    if (result.confirm) {
                                        console.log("result.confirm",result)
                                       let params = {
                                           id: e.audit.id,
                                           status: 3,
                                           remark: result.content
                                       }
                                       let data = await activeHandle(params)
                                       if(data.status_code==200) {
                                           this.list.splice(index,1,data.data)
                                       } else {
                                           return uni.$u.toast(data.message)
                                       }
                                       // this.jobActiveCompany()
                                    } else if (result.cancel) {
                                        
                                    }
                                }
                            })
                        } else {
                            let params = {
                                id: e.audit.id,
                                status: 1,
                                remark: '没意见'
                            }
                            let data = await activeHandle(params)
                            if(data.status_code==200) {
                                this.list.splice(index,1,data.data)
                            } else {
                                return uni.$u.toast(data.message)
                            }
                            // this.jobActiveCompany()
                        }
                        return
                        
                        
                    },
                    fail: (err) => {

                        console.error(err); // 处理错误
                    },
                });

            },
            async jobActiveCompany(id) {
                let params = {
                    job_active_id:id,
                    page: this.page,
                    limit: this.limit
                }
                const res = await activeAudit(params)
                this.list = this.list.concat(res.data.data);
                // 返回false代表没有下一页
                this.more = res.data.more;
                this.status = this.more ? "loadmore" : "nomore"
                console.log("res", res)
            }
        }
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .listBox {
        width: 100%;
        padding: 32rpx;
        background: #fff;
        border-radius: 24rpx;
        margin-bottom: 16rpx;
    }

    .listBox_top {
        display: flex;
        align-items: center;
    }

    .companyLogoImg {
        width: 104rpx;
        height: 104rpx;
        margin-right: 24rpx;
    }

    .companyNameCla {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        display: flex;
        align-items: center;
        margin-bottom: 8rpx;
    }

    .payTypeCla {
        width: 84rpx;
        height: 40rpx;
        background: rgba(87, 213, 28, 0.1);
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        margin-left: 18rpx;
        font-weight: 600;
        font-size: 20rpx;
        color: #57D51C;
        text-align: center;
        line-height: 40rpx;
    }
    
    .payTypeCla1 {
        width: 84rpx;
        height: 40rpx;
        background: #CCC;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        margin-left: 18rpx;
        font-weight: 600;
        font-size: 20rpx;
        color: #FFFFFF;
        text-align: center;
        line-height: 40rpx;
    }

    .listBox_bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 28rpx;
    }

    .statusType {
        width: 104rpx;
        height: 50rpx;
        background: rgba(79, 140, 240, 0.1);
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 50rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #4F8CF0;
    }
    
    .statusType1 {
        width: 104rpx;
        height: 50rpx;
        background: #FE4D4F;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 50rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #FFF;
    }
    
    .statusType2 {
        width: 104rpx;
        height: 50rpx;
        background: #4F8CF0;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 50rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #FFFFFF;
    }

    .processCla {
        width: 96rpx;
        height: 56rpx;
        background: #4F8CF0;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 56rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #FFFFFF;
    }
</style>