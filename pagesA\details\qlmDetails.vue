<template>
	<view class="home-index">
		<view :class="['header-wrap', isScroll ? 'isScroll' : '']">
			<image src="https://api-test.zhaopinbei.com/images/header_bg.png" mode=""></image>
			<view class="header">
				<view class="back" @click="back" :style="{ marginTop: `${backTop}px` }">
					<u-icon name="arrow-left" color="#FFFFFF"></u-icon>
				</view>
				<view class="info">
					<image :src="details.member_info.image['path_url']" mode=""></image>
					<view class="user-info">
						<view class="name">
							{{ details.member_certification.name ? details.member_certification.name : details.member_info.nick_name }}
						</view>
						<view class="desc">
							<view class="desc-item">
								{{ details.member_info.sex_str }}
							</view>
							<view class="desc-item">{{ details.member_info.age }}岁</view>
							<view class="desc-item">{{ details.member_info.nation }}族</view>
							<view class="desc-item">
								{{ details.member_info.education_type_name }}
							</view>
						</view>
					</view>

					<view class="contact" @click="communicate('member', details.id)">聊聊呗</view>
				</view>
			</view>
		</view>

		<view class="container" @click="setChat()">
			<view class="wrap">
				<view class="title">
					<image src="https://api-test.zhaopinbei.com/storage/uploads/images/OZUV5DxwQFRVskMOXuCMttGRZ20He3bMaEHQizxy.png" mode=""></image>
					<text>{{ details.certification_status == 1 ? '已实名核验' : '未实名核验' }}</text>
				</view>
				<view class="expect">
					<view class="name">求职期望</view>
					<view class="forward">
						<view class="dot"></view>
						<text>找工作</text>
					</view>
				</view>
				<view class="tags">
					<view class="tag" v-for="(item, index) in details.job_class" :key="index">
						{{ item.name }}
					</view>
				</view>
				<view class="expect" style="margin-bottom: 20rpx">
					<view class="name">就业方式</view>
					<view class="forward" v-for="(i, index) in details.work_types" :key="index">
						<text>{{ i.name || '暂无' }}</text>
					</view>
				</view>
				<view class="finance">
					<view class="name">
						期望工资：
						<text class="money">{{ details.member_resume.expect_salary_k }}</text>
					</view>
				</view>

				<view class="finance">
					<view class="name">
						期望工作地：
						<text>{{ details.member_resume.expect_address }}</text>
					</view>

					<view class="distance">
						<image
							src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png"
							mode=""></image>
						距离您{{ details.distance }}km
					</view>
				</view>

				<view class="cert">
					<view class="name">相关证书：</view>
					<view class="pics" v-if="details.member_info && details.member_info.certificates && details.member_info.certificates.length > 0">
						<view class="pic" v-for="(item, index) in details.member_info.certificates" :key="index">
							<image :src="item.path_url" mode=""></image>
						</view>
					</view>
					<view class="no">暂无证书</view>
					<view class="desc">
						{{ details.member_info.introduce || '' }}
					</view>
				</view>
			</view>
		</view>
		<view class="footer">
			<view v-if="roleType == 'company'">
				<view class="favor ysc" v-if="details.collect_status == 1" @click="cancelCollectCompanyMember">
					<u-icon name="star-fill" color="#F9AD14"></u-icon>
					已关注
				</view>
				<view class="favor" v-if="details.collect_status == 2" @click="collectCompanyMember">
					<u-icon name="star-fill" color="#999999"></u-icon>
					关注
				</view>
			</view>
			<view v-if="roleType == 'headhunters'">
				<view class="favor ysc" v-if="details.collect_status == 1" @click="cancelCollectCompanyMember">
					<u-icon name="star-fill" color="#F9AD14"></u-icon>
					已收藏
				</view>
				<view class="favor" v-if="details.collect_status == 2" @click="collectCompanyMember">
					<u-icon name="star-fill" color="#999999"></u-icon>
					收藏
				</view>
			</view>
			<view class="btns">
				<!-- <view :class="['btn',details.isCancel==1?'cancel':'']">
					{{details.isCancel==1?'取消报名':'立即报名'}}
				</view> -->
				<view class="btn agree" @click="communicate('member', details.id)">聊聊呗</view>
			</view>
		</view>
	</view>
</template>

<script>
import { communicate } from '../../common/common';
import { cancelCollectCompanyMember, collectCompanyMember, getCompanyMemberDetails } from '../../config/api.js';

export default {
	data() {
		return {
			id: '',
			isScroll: false,
			statusBarHeight: 0,
			menuButtonTop: 0,
			backTop: 0,
			details: {
				isFavor: 1,
			},
			roleType: '',
		};
	},
	mounted() {
		this.roleType = uni.getStorageSync('roleType');
	},
	onPageScroll(e) {
		this.isScroll = e.scrollTop > 0;
	},
	onShow() {
		const app = getApp().globalData;
		console.log('app:', app);
		const { systemInfo, MenuButtonInfo } = app;
		console.log('~~~~~~~~', MenuButtonInfo, '打印');
		this.statusHeight = systemInfo.statusBarHeight;
		this.menuButtonTop = MenuButtonInfo.top;

		this.backTop = this.statusBarHeight + this.menuButtonTop;
	},
	onLoad(options) {
		this.id = options.id;
		this.getCompanyMemberDetails();
	},
	methods: {
		communicate(type,id){
			uni.switchTab({
				url:`/pages/chat/message?type=${type}&id=${id}`
			})
		},
		back() {
			uni.navigateBack();
		},
		//千里马详情
		async getCompanyMemberDetails() {
			let params = {
				member_id: this.id,
				user_id: uni.getStorageSync('userInfo').login_user.id,
				company_id: uni.getStorageSync('userInfo').login_user.company_id,
				// name:uni.getStorageSync('userInfo').company.name
			};
			const { status_code, data } = await getCompanyMemberDetails(params);
			if (status_code == 200) {
				this.details = data;
			}
		},

		//收藏千里马
		async collectCompanyMember() {
			let params = {
				member_id: this.id,
			};
			const { status_code, data, message } = await collectCompanyMember(params);
			if (status_code == 200) {
				this.getCompanyMemberDetails();
				uni.$u.toast(message || '成功');
			}
		},

		//取消收藏千里马
		async cancelCollectCompanyMember() {
			let params = {
				member_id: this.id,
			};
			const { status_code, data, message } = await cancelCollectCompanyMember(params);
			if (status_code == 200) {
				this.getCompanyMemberDetails();
				uni.$u.toast(message || '成功');
			}
		},
	},
};
</script>
<style>
page {
	background-color: #f5f5f7;
}
</style>
<style lang="less" scoped>
.main {
	padding-bottom: 140rpx;
}

.isScroll {
	position: fixed;
	left: 0;
	top: 0;
}

.header-wrap {
	display: flex;
	flex-direction: column;

	width: 100%;
	height: 440rpx;
	z-index: 10;

	// background-color: red;
	& > image {
		width: 100%;
		height: 440rpx;
		position: absolute;
		left: 0;
		top: 0;
		z-index: -1;
	}

	.header {
		display: flex;
		flex-direction: column;
		flex: 1;
		// background-color: red;
		padding: 0 30rpx;

		.back {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 60rpx;
			height: 60rpx;
		}

		.info {
			display: flex;
			align-items: center;
			background: rgba(236, 242, 254, 0.7);
			border-radius: 16rpx;
			// border: 1px solid #6199FF;
			padding: 24rpx 16rpx;
			margin-top: 36rpx;
			border-image: linear-gradient(137deg, rgba(233, 240, 255, 1), rgba(97, 153, 255, 1)) 1 1;

			image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 50%;
			}

			.user-info {
				display: flex;
				flex-direction: column;
				justify-content: space-around;
				flex: 1;
				padding-left: 20rpx;

				.name {
					font-weight: 500;
					font-size: 32rpx;
					color: #000000;
				}

				.desc {
					display: flex;
					font-weight: 400;
					font-size: 24rpx;
					color: rgba(0, 0, 0, 0.6);
					margin-top: 16rpx;

					.desc-item {
						border-right: 1px solid #999999;
						padding: 0 12rpx;

						&:first-child {
							padding-left: 0;
						}

						&:last-child {
							border-right: none;
						}
					}
				}
			}

			.contact {
				display: flex;
				align-items: center;
				height: 60rpx;
				padding: 0 20rpx;
				font-weight: 600;
				font-size: 28rpx;
				color: #ffffff;
				background-color: #4787f0;
				border-radius: 12rpx;
			}
		}
	}
}

.container {
	padding: 0 32rpx;

	.wrap {
		display: flex;
		flex-direction: column;
		position: relative;
		border-radius: 16rpx;
		margin-top: -80rpx;
		padding: 0 32rpx;
		z-index: 1000;
		background: rgba(255, 255, 255, 0.6);

		.title {
			display: flex;
			padding: 32rpx 0;
			font-weight: 400;
			font-size: 32rpx;
			color: #4787f0;
			border-bottom: 1px solid #e6e6e6;

			image {
				width: 40rpx;
				height: 40rpx;
				margin-right: 8rpx;
			}
		}

		.expect {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-top: 20rpx;

			.name {
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(0, 0, 0, 0.9);
			}

			.forward {
				display: flex;
				align-items: center;

				.dot {
					background: #1bb327;
					width: 16rpx;
					height: 16rpx;
					border-radius: 50%;
					margin-right: 8rpx;
				}

				text {
					font-weight: 400;
					font-size: 28rpx;
					color: rgba(0, 0, 0, 0.9);
				}
			}
		}

		.tags {
			display: flex;
			flex-wrap: wrap;
			overflow: hidden;
			margin-right: -12rpx;

			.tag {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-bottom: 24rpx;
				margin-right: 12rpx;
				height: 48rpx;
				background: #f2f2f2;
				padding: 0 16rpx;
				border-radius: 4rpx;
				font-weight: 400;
				font-size: 24rpx;
				color: rgba(0, 0, 0, 0.8);
			}
		}

		.finance {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 24rpx;

			.name {
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(0, 0, 0, 0.9);

				.money {
					font-weight: 400;
					font-size: 28rpx;
					color: #4787f0;
				}
			}

			.distance {
				display: flex;
				align-items: center;
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(0, 0, 0, 0.8);

				image {
					width: 32rpx;
					height: 32rpx;
				}
			}
		}

		.cert {
			display: flex;
			flex-direction: column;

			.name {
				font-weight: 400;
				font-size: 28rpx;
				color: rgba(0, 0, 0, 0.9);
				margin-bottom: 12rpx;
			}

			.no {
				color: #999999;
				font-size: 24rpx;
				margin-bottom: 24rpx;
			}

			.pics {
				display: flex;
				flex-wrap: wrap;
				margin-bottom: 24rpx;

				.pic {
					width: 120rpx;
					height: 120rpx;
					margin-right: 24rpx;

					image {
						width: 100%;
						height: 100%;
					}
				}
			}

			.desc {
				font-weight: 400;
				margin-bottom: 24rpx;
				font-size: 26rpx;
				color: rgba(0, 0, 0, 0.6);
			}
		}
	}
}

.footer {
	position: fixed;
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 140rpx;
	width: 100%;
	left: 0;
	bottom: 0;
	background: #ffffff;
	font-weight: 600;
	font-size: 28rpx;

	.favor {
		display: flex;
		align-items: center;
		color: #999999;
		margin-left: 32rpx;
	}

	.ysc {
		color: #f9ad14;
	}

	.btns {
		display: flex;
		margin-right: 32rpx;
		flex: 1;

		.btn {
			display: flex;
			justify-content: center;
			align-items: center;
			// padding: 0 72rpx;
			width: 100%;
			margin-left: 32rpx;
			height: 80rpx;
			background: #f5f5f7;
			color: #333333;
			border-radius: 16rpx;

			&:first-child {
				margin-right: 20rpx;
			}
		}

		.cancel {
			background: rgba(254, 77, 79, 0.1);
			color: #fe4d4f;
		}

		.agree {
			background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
			color: #ffffff;
		}
	}
}
</style>
