<template>
	<view class="item" @click.stop="goDetails">
		<view class="item-up">
			<view class="name">
				<image
					src="https://api-test.zhaopinbei.com/storage/uploads/images/XYRLnqsxmjwIo8ITVKLDsbdfPxbGykz3B29iezGP.png"
					mode="" class="jobIcon" v-if="item.hot_status == 1"></image>
				<view class="text">{{ item.title }}</view>
			</view>
			<view class="work_type" v-if="item.work_types.length > 0">
				<view class="workType">
					<view class="type" v-for="(sub, idx) in item.work_types" :key="idx">
						{{ sub.name }}
					</view>
				</view>
			</view>
			<view class="money">
				{{ item.salary_info_str }}
			</view>
		</view>

		<!-- 简介 -->
		<view class="sub-name">
			{{ item.company.intro || '暂无简介' }}
		</view>

		<!-- 标签 -->
		<view class="tagsss">
			<block v-if="item && item.label.length > 0">
				<view class="tags">
					<view class="tag" v-for="(sub, idx) in item.tags" :key="idx">
						{{ sub.title }}
					</view>
				</view>
			</block>
			<block v-else>
				<view class="tags">
					<view class="tag">暂无</view>
				</view>
			</block>
			<view class="tagss">
				<img src="@/static/images/index/eye_icon.png" style="width: 48rpx; height: 48rpx" alt="" />
				<view style="font-size: 26rpx; color: #999">{{ item.browsing_records.count || '0' }}</view>
			</view>
		</view>

		<!-- 公司规模标签 -->
		<view class="Con">
			<view class="leftCon">
				<view class="desc">
					<view class="desc-item" v-if="item.company.short_name != null">
						{{ item.company.short_name }}
					</view>
					<view class="desc-item" v-if="item.company_info.financing_type_name != null">
						{{ item.company_info.financing_type_name }}
					</view>
					<view class="desc-item" v-if="item.company_info.size_type != null">
						{{ item.company_info.size_type }}
					</view>
				</view>
				<view class="addr">
					<image
						src="https://api-test.zhaopinbei.com/storage/uploads/images/WshcTAfqBot4YiTFwCjvY9oVXCEY8NMrTmVb66Vs.png"
						mode=""></image>
					<view class="addrText">{{ item.addresses[0].address_info }}</view>
					<view class="location">距您{{ item.distance }}km</view>
				</view>
			</view>
			<view class="item-down">
				<block v-if="type == 'active'">
					<view class="btn">立即报名</view>
				</block>
				<block v-else>
					<view class="btn" @click.stop="communicate('job', item.id)">聊聊呗</view>
				</block>
			</view>
		</view>

		<!-- 直聘跟保障标用label中的字段进行判断 -->
		<!-- <view class="bottomCon" v-if="item.safeguard==1 || item.direct == 1">
			<view class="oneCon"  v-if="item.safeguard==1"> -->
		<view class="bottomCon">
			<view class="oneCon">
				<image
					src="https://api-test.zhaopinbei.com/storage/uploads/images/le8MSU4hXxN9sSsWAMzKXTURXjPOIPzvpjTmhTe5.png"
					mode="" class="jobIcon"></image>
				平台保障
			</view>
			<!-- <view class="oneCon"  v-if="item.direct==1"> -->
			<view class="oneCon">
				<image
					src="https://api-test.zhaopinbei.com/storage/uploads/images/3eOpm8QGKeMEEjRt6dFxwWBqDnDQeFVeP84vCADJ.png"
					mode="" class="jobIcon"></image>
				平台自聘
			</view>
		</view>
	</view>
</template>

<script>
	import {
		communicate
	} from '../common/common';
	export default {
		name: 'jobItem',
		props: {
			item: {
				type: Object,
				default: () => {},
			},
			type: {
				type: String,
				default: '',
			},
		},
		data() {
			return {};
		},
		methods: {
			communicate,
			goDetails() {
				uni.navigateTo({
					url: '/pagesA/details/memberJobDetails?id=' + this.item.id,
				});
			},
		},
	};
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		padding: 32rpx;
		margin-bottom: 32rpx;
		background-color: #ffffff;
		border-radius: 24rpx;

		.workType {
			display: flex;
			color: #4f8cf0;
			font-weight: 600;

			.type:not(:last-child)::after {
				content: '/';
				color: #4f8cf0;
			}
		}

		.item-up {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.name {
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;
				display: flex;
				width: 40%;
				align-items: center;

				.text {
					width: 80%;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.jobIcon {
					width: 40rpx;
					height: 40rpx;
					margin-right: 8rpx;
				}
			}

			.work_type {
				width: 36%;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.money {
				font-weight: 600;
				font-size: 32rpx;
				width: 18%;
				color: #f98a14;
				text-align: right;
			}
		}

		.desc {
			display: flex;
			font-weight: 400;
			font-size: 24rpx;
			color: #999999;
			margin-top: 16rpx;

			.desc-item {
				border-right: 1px solid #999999;
				padding: 0 12rpx;

				&:first-child {
					padding-left: 0;
				}

				&:last-child {
					border-right: none;
				}
			}
		}

		.sub-name {
			font-weight: 400;
			font-size: 14px;
			color: #333333;
			margin-top: 16rpx;
		}

		.tags {
			display: flex;
			margin-top: 16rpx;
			margin-bottom: 16rpx;

			.tag {
				display: flex;
				align-items: center;
				background: #f6f6f6;
				border-radius: 8rpx;
				height: 46rpx;
				padding: 0 12rpx;
				font-weight: 400;
				font-size: 22rpx;
				color: #666666;
				margin-right: 16rpx;
			}
		}

		.tagsss {
			justify-content: space-between;
			display: flex;
			align-items: center;

			.tagss {
				display: flex;
				align-items: center;

				img {
					margin-bottom: 4rpx;
				}
			}
		}
	}

	.Con {
		display: flex;
		justify-content: space-between;
		align-items: center;

		.item-down {
			display: flex;
			justify-content: space-between;
			align-items: center;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #4f8cf0;
				border-radius: 8rpx;
				font-weight: 600;
				padding: 0 24rpx;
				height: 56rpx;
				font-size: 24rpx;
				color: #ffffff;
			}
		}

		.addr {
			display: flex;
			align-items: center;
			font-weight: 400;
			font-size: 12px;
			color: #999;
			margin: 18rpx 0;

			.addrText {
				width: 280rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			image {
				width: 30rpx;
				height: 30rpx;
				margin-right: 8rpx;
			}

			.location {
				background-color: #f6f6f6;
				padding: 6rpx;
				font-size: 18rpx;
				border-radius: 8rpx;
			}
		}
	}

	.bottomCon {
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
		border-top: 1px solid #f5f5f5;
		padding-top: 24rpx;

		.oneCon {
			font-size: 24rpx;
			color: #999;
			align-items: center;
			display: flex;

			.jobIcon {
				width: 44rpx;
				height: 44rpx;
				margin-right: 6rpx;
			}
		}
	}
</style>