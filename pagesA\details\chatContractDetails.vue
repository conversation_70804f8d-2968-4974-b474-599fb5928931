<template>
    <view id="app">
        <view v-if="type == 0">
            <view class="fileBox">
                <view class="fileImg">
                    <image src="https://api-test.zhaopinbei.com/storage/uploads/images/NKgeqfH5GSaU1krUtQWZoJx6bFlxeBQEb1NoxBps.png" mode=""></image>
                    <view class="fileText">{{details.type == 'company' ? '企业授权合同' : '千里马授权合同'}}</view>
                </view>
                <view>
                    <view class="fileName">{{details.title}}</view>
                    <view class="fileType">{{details.sign_status_name}}</view>
                    <view class="fileTime">签署截止日期：<text class="blackText">{{details.end_date}}</text> </view>
                    <view class="fileNotes">备注：<text class="blackText">{{details.remark}}</text> </view>
                </view>
            </view>
            <view class="hr1Box"></view>
            <view class="attachment">
                <view class="fileTitle">附件</view>
                <view class="attachmentText">
                    <image src="../static/images/file.png" mode="" class="fileIcon"></image>
                    <!-- showFile(放文件地址) -->
                    <view @click.stop="showFile(details.file[0].path_url)">委托待招聘协议合同</view>
                </view>
                <view class="attachmentText">
                    <image src="../static/images/file.png" mode="" class="fileIcon"></image>
                    <view @click.stop="showFile()">委托待招聘协议合同</view>
                </view>
                <view class="hrBox"></view>
                <view class="fileTitle">签署方</view>
                <view class="Signatory">
                    <image src="https://api-test.zhaopinbei.com/storage/uploads/images/9LKPyv7h3diO5slskL4dH1ysbhTPCtASgEXsOoxo.png" mode="" class="SignatoryImg"></image>
                    <view class="SignatoryText">
                        <view class="SignatoryName">{{selfData[0].name}}</view>
                        <view class="SignatoryPhone">{{selfData[0].cellphone}}</view>
                    </view>
                    <view class="leftIcon fqColor">发起方</view>
                </view>

                <view class="Signatory">
                    <image src="https://api-test.zhaopinbei.com/storage/uploads/images/oCPwLNe3lZhy5h47XKR2cRYV1r1dA3mTkdu1Gyls.png" mode="" class="SignatoryImg"></image>
                    <view class="SignatoryText">
                        <view class="SignatoryName">
                            <view>{{signerData[0].name}}</view>
                            <view class="SignatoryName_time">于{{signerData[0].updated_at}}签署</view>
                        </view>
                        <view class="SignatoryPhone">{{signerData[0].cellphone}}</view>
                    </view>
                    <view class="leftIcon qsColor">签收方</view>
                </view>
            </view>

            <view class="bottomBox">
                <view class="bottomBox_btn">
                    签署合同
                </view>
            </view>
        </view>

        <view v-if="type==1" class="">
            <view class="modal">
                <view class="modal-content" @click.stop>
                    <view class="close-button" @click="closeModal">×</view>
                    <view class="qr-code">
                        <image :src="qrCodeUrl" mode="aspectFit" />
                        <view class="fontCla">扫描二维码签署合同</view>
                    </view>
                </view>
            </view>
        </view>


    </view>
</template>

<script>
    import {
        contractShow
    } from '../../config/api.js'
    export default {

        data() {
            return {
                details: '',
                type: 0, // 控制页面展示数据
                qrCodeUrl: 'https://api-test.zhaopinbei.com/storage/uploads/images/QK7xnOAY2ZHFEtIeSJr59pqZ4w9FgcTtKnJhD3ix.png', // 二维码图片
                signerData: '',
                selfData: '',
            }
        },
        onLoad(option) {
            this.contractShow(option.id)
        },
        methods: {

            showFile(url) {
                uni.downloadFile({
                    url: url,
                    success: function(res) {
                        var filePath = res.tempFilePath;
                        uni.openDocument({
                            filePath: filePath,
                            showMenu: true,
                            success: function(res) {
                                console.log('打开文档成功');
                            },
                        });
                    },
                });
            },
            async contractShow(id) {
                let params = {
                    contract_id: id * 1
                }

                const res = await contractShow(params)
                this.details = res.data;
                // 过滤出 signer 类型的数据
                this.signerData = res.data.signers.filter(item => item.signer_type === 'signer');
                // 过滤出 user 类型的数据
                this.selfData = res.data.signers.filter(item => item.signer_type === 'self');
                console.log("res", this.signerData)
            }
        }
    }
</script>
<style scoped lang="less">
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
    }

    .fileBox {
        width: 100%;
        padding: 32rpx;
        display: flex;
        align-items: center;
        background: #fff;
    }

    .fileImg {
        width: 216rpx;
        height: 216rpx;
        margin-right: 40rpx;
        position: relative;

        image {
            width: 100%;
            height: 100%;
        }
    }

    .fileText {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        text-align: center;
        background: #1BB327;
        padding: 4rpx 0;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
    }

    .fileName {
        font-weight: bold;
        font-size: 32rpx;
        color: rgba(0, 0, 0, 0.9);
        margin-bottom: 18rpx;
    }

    .fileType {
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.9);
        margin-bottom: 12rpx;
    }

    .fileTime {
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 12rpx;
    }

    .fileNotes {
        font-weight: 400;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.6);
    }

    .blackText {
        color: #000000;
    }

    .fileTitle {
        font-weight: 400;
        font-size: 32rpx;
        color: rgba(0, 0, 0, 0.9);
        margin-bottom: 24rpx;
    }

    .fileIcon {
        width: 24rpx;
        height: 24rpx;
        margin-right: 12rpx;
    }

    .hr1Box {
        width: 100%;
        height: 32rpx;
        background: #F5F5F7;
    }

    .attachment {
        width: 100%;
        padding: 32rpx;
        background: #fff;
    }

    .attachmentText {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 28rpx;
        color: #4F8CF0;
        margin-bottom: 16rpx;
    }

    .hrBox {
        width: 686rpx;
        height: 2rpx;
        background: #F5F5F7;
        margin-top: 32rpx;
        margin-bottom: 24rpx;
    }

    .Signatory {
        width: 100%;
        height: 164rpx;
        background: #F5F5F7;
        border-radius: 24rpx 24rpx 24rpx 24rpx;
        padding: 24rpx 20rpx 52rpx 20rpx;
        display: flex;
        align-items: center;
        position: relative;
        margin-bottom: 36rpx;
    }

    .SignatoryText {
        width: 100%;
    }

    .SignatoryName {
        width: 100%;
        font-weight: bold;
        font-size: 28rpx;
        color: rgba(0, 0, 0, 0.9);
        margin-bottom: 12rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .SignatoryName_time {
        font-weight: 400;
        font-size: 24rpx;
        color: rgba(0, 0, 0, 0.6);
    }

    .SignatoryPhone {
        font-weight: 400;
        font-size: 26rpx;
        color: rgba(0, 0, 0, 0.9);
    }

    .SignatoryImg {
        width: 56rpx;
        height: 56rpx;
        margin-right: 16rpx;
        background: #fff;
    }

    .leftIcon {
        width: 90rpx;
        height: 32rpx;
        font-weight: 400;
        font-size: 20rpx;
        text-align: center;
        line-height: 32rpx;
        color: #FFFFFF;
        border-radius: 0 24rpx 0 24rpx;
        position: absolute;
        left: 0;
        bottom: 0;
    }

    .fqColor {
        background: #4787F0;
    }

    .qsColor {
        background: #00A870;
    }

    .bottomBox {
        width: 100%;
        padding: 32rpx;
        position: fixed;
        bottom: 100rpx;
        left: 0;
    }

    .bottomBox_btn {
        width: 100%;
        height: 100rpx;
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        text-align: center;
        line-height: 100rpx;

        font-weight: 600;
        font-size: 34rpx;
        color: #FFFFFF;
    }




    .modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        /* 半透明黑色背景 */
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        /* 确保模态窗在最上层 */
    }

    .modal-content {
        background-color: white;
        border-radius: 24rpx;
        padding: 20rpx;
        width: 600rpx;
        height: 600rpx;
        text-align: center;
    }

    .close-button {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 24px;
        cursor: pointer;
    }

    .qr-code {
        margin-top: 48rpx;
    }

    .fontCla {
        margin-top: 10rpx;
        color: grey;
    }
</style>
