<template>
	<view class="companyBox">
		<view class="cardIntroduction">
			<view>
				<view class="card">
					<!-- 公司Logo部分 -->
					<view class="logo-section">
						<view class="default-logo-placeholder">公司Logo</view>
						<view class="logo" v-if="logoSrc == '' " style="background-color: rgba(217, 217, 217, 1);">

						</view>
						<image :src="resetLogo" v-else class="logo" @click="resetLogo" />
						<view class="reset-logo-text" @click="resetLogo">
							<image :src="restlogoUrl" mode="" style="width: 28.8rpx; height: 28.8rpx;"></image>
							<text style="margin-left: 16rpx; font-size: 20rpx;">若不想使用当前logo，可以恢复默认</text>
						</view>
					</view>

					<view class="Dividing"></view>
					<!-- 品牌名称部分 -->
					<view class="info-section">
						<text class="label">品牌名称</text>
						<u--input class="value" placeholder="请输入品牌名称" border="none" v-model="brandName"
							@change="change"></u--input>
					</view>
					<view class="Dividing"></view>


					<!-- 公司全称部分 -->
					<view class="info-section">
						<text class="label">公司全称</text>
						<u--input class="value" placeholder="请输入公司名称" border="none" v-model="companyFullName"
							@change="change"></u--input>

					</view>

					<view class="Dividing"></view>
					<!-- 所在行业部分 -->
					<view class="info-section">
						<text class="label">所在行业</text>
						<view class="select-container">
							<view class="select-input" @click="showFiled()">
								<text class="selected-text">
									{{ FieldOption || '人力资源服务' }}
								</text>
								<!-- <i class="arrow" :class="{ 'up': showOptions }"></i> -->
								<img class="arrow" :src="arrow" alt="" />
							</view>
							<view class="select-options" v-if="Fieldhide">
								<view class="option" :class="{ 'selected': option === FieldOption}"
									v-for=" (option,index) in Field" :key="index" @click="selectOption(option)">
									{{ option }}
								</view>
							</view>
						</view>
					</view>

					<view class="Dividing"></view>
					<!-- 融资阶段部分 -->
					<view class="info-section">
						<text class="label">融资阶段</text>
						<view class="select-container">
							<view class="select-input" @click="showFinancing()">
								<text class="selected-text">
									{{ FinancingOption || '未融资' }}
								</text>
								<!-- <i class="arrow" :class="{ 'up': showFinance }"></i> -->
								<img class="arrow" :src="arrow" alt="" />
							</view>
							<view class="select-options" v-if="Financehide">
								<view class="option" :class="{ 'selected': option === FinancingOption }"
									v-for="(option, index) in Financing" :key="index" @click="FinancingOptions(option)">
									{{ option }}
								</view>
							</view>
						</view>
					</view>

					<view class="Dividing"></view>
					<!-- 公司规模部分 -->
					<view class="info-section">
						<text class="label">公司规模</text>
						<view class="select-container">
							<view class="select-input" @click="showPersonal()">
								<text class="selected-text">
									{{ PersonalOption || '20-99人' }}
								</text>
								<!-- <i class="arrow" :class="{ 'up': showFinance }"></i> -->
								<img class="arrow" :src="arrow" alt="" />
							</view>
							<view class="select-options" v-if="Personalhide">
								<view class="option" :class="{ 'selected': option === PersonalOption }"
									v-for="(option, index) in Personal" :key="index" @click="PersonalOptions(option)">
									{{ option }}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view style="width: 100%; display: flex;">
					<view class="card-notice" @click="showNotice">
						<text style="font-size: 24rpx;">
							请认真阅读并遵守《
							<text style="color: rgba(79, 140, 240, 1); font-size: 24rpx;">公司信息填写须知</text>
							》
						</text>
					</view>
				</view>
			</view>

			<view class="page-container">
				<view class="card-container">
					<!-- 这里放置除按钮外卡片内的其他内容 -->
					<button class="save-button" @click="saveData">
						<text style="font-size: 28rpx;">
							保存
						</text>
					</button>
				</view>
			</view>
		</view>
		<Notice v-show="isModalVisible" :visible="isModalVisible" @close="isModalVisible = false"
			@confirm="handleConfirm"></Notice>
	</view>
</template>

<script>
	import Notice from '../companyNotice/Notice.vue'
	export default {
		components: {
			Notice
		},
		data() {
			return {
				logoSrc: '', // 这里可以初始化为默认logo路径，或从后端获取实际logo路径
				brandName: '学创联盟',
				companyFullName: '学创联盟网络科技（北京）有限公司',
				industry: '人力资源服务',
				financingStage: '未融资',
				restlogoUrl:'https://api-test.zhaopinbei.com/storage/uploads/images/v5HrWGFZM8aZvmuAELrWMDaN0eLbG2zkiNJyZnFn.png',
				Field: ['人力资源服务1', '选项2', '选项3', '选项4'],
				Financing: ['未融资', '天使轮', 'A轮', 'B轮', 'C轮'],
				Personal: ['0-20人', '20-99人', '100-499人', '500-999人', '1000-9999人'],
				FieldOption: null,
				FinancingOption: null,
				PersonalOption: null,
				Fieldhide: false,
				Financehide: false,
				Personalhide: false,
				carefulnoticehide: false,
				isModalVisible: false,
				arrow:'https://api-test.zhaopinbei.com/storage/uploads/images/7loFLV79Afm6gvkXWPjWd5IRCYHNdGpsZZtR2hym.png'
			};
		},
		methods: {
			showNotice() {
				// this.carefulnoticehide = !this.carefulnoticehide
				this.isModalVisible = true;
				console.log(this.isModalVisible);
			},
			resetLogo() {
				// 将logoSrc重置为默认值
				this.logoSrc = '';
			},
			selectOption(option) {
				this.FieldOption = option;
				// this.showOptions = false;
				this.Fieldhide = false;

			},
			FinancingOptions(option) {
				this.FinancingOption = option;
				// this.showOptions = false;
				this.Financehide = false;
			},
			PersonalOptions(option) {
				this.Personal = option;
				// this.showOptions = false;
				this.Personal = false;
			},
			showFiled() {
				this.Fieldhide = !this.Fieldhide;
				// console.log(this.showOptions);
			},
			showFinancing() {
				// console.log(111);
				this.Financehide = !this.Financehide
			},
			showPersonal() {
				// console.log(111);
				this.Personalhide = !this.Personalhide
			},
			change(e, value) {
				console.log(value);
			},
			saveData() {
				console.log('点击了保存按钮，执行保存操作');
			}
		}
	};
</script>

<style>
	.Dividing {
		width: 622rpx;
		height: 0rpx;
		border: 1rpx solid rgba(245, 245, 247, 1);
		margin: 16rpx 0px;
		padding: 0rpx;
	}

	.companyBox {
		overflow: hidden;
	}

	.cardIntroduction {
		width: 100%;
		/* height: 100%; */
		height: 100vh;
		background-color: rgba(245, 245, 247, 1);
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		justify-content: space-between;
	}

	.card {
		width: 686rpx;
		height: 984rpx;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		margin-top: 2%;
	}

	.container {
		padding: 20rpx;
		background-color: #fff;
	}

	.logo-section {
		width: 96%;
		height: 24%;
		display: flex;
		font-size: 24rpx;
		flex-direction: column;
		justify-content: space-between;
		margin-top: 2.3%;
		align-items: flex-start;
	}

	.logo {
		width: 96rpx;
		height: 96rpx;
		margin-top: 16rpx;
		margin-bottom: 18rpx;
		border-radius: 50%;
	}

	.default-logo-placeholder {
		font-size: 28rpx;
		width: 96%;
		color: rgba(102, 102, 102, 1);
		line-height: 2.1%;
		margin-top: 32rpx;
	}

	.reset-logo-text {
		display: flex;
		align-items: center;
		font-size: 20rpx;
		color: rgba(153, 153, 153, 1);
		line-height: 1.7%;
	}


	.info-section {
		margin-bottom: 2.2%;
		display: flex;
		flex-direction: column;
		align-self: flex-start;
		align-items: flex-start;
		height: 10.6%;
		;
		margin-top: 1.7%;
		margin-left: 2.3%;
		justify-content: space-evenly;
		font-size: 0.875rem;
		line-height: 6.27%;
		width: 96%;
	}

	.label {
		font-size: 24rpx;
		color: rgba(102, 102, 102, 1);
		line-height: 24%;
		margin-bottom: 16rpx;
	}


	.value {
		color: rgba(51, 51, 51, 1);
		font-size: 28rpx;
		line-height: 28%;
		margin-top: 16rpx;
	}

	.select-container {
		width: 622rpx;
		height: 40rpx;
		position: relative;
		margin-top: 16rpx;
	}

	.selected-text {
		margin-top: 16rpx;
	}

	.select-input {
		padding: 8px 12px;
		border: 1px solid #dcdfe6;
		border-radius: 4px;
		font-size: 28rpx;
		cursor: pointer;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.arrow {
		width: 28rpx;
		height: 28rpx;
		/* border-left: 5px solid transparent;
		border-right: 5px solid transparent;
		border-top: 5px solid #909399;
		transition: transform 0.3s; */
	}

	.arrow.up {
		transform: rotate(180deg);
	}

	.select-options {
		position: absolute;
		top: 150%;
		left: 16rpx;
		width: 622rpx;
		height: 320rpx;
		border: 1px solid #dcdfe6;
		border-radius: 8rpx;
		background-color: #fff;
		z-index: 2;
		display: flex;
		flex-direction: column;
		justify-content: space-around;
	}

	.select-container .select-input {
		border: none;
		padding: 0;
	}

	.option {
		padding: 8px 12px;
		cursor: pointer;
		margin-left: 6px;
		margin-right: 6px;
		margin-top: 6rpx;
		font-size: 28rpx;
		height: 56rpx;
		display: flex;
		align-items: center;
	}

	.option:hover {
		background-color: rgba(242, 245, 250, 1);
	}

	.option.selected {
		background-color: rgba(242, 245, 250, 1);
		color: rgba(79, 140, 240, 1);
	}

	.page-container {
		width: 100%;
		/* margin-top: 16%; */
		/* height: 15%; */

		background-color: rgba(0, 0, 0, 0.05);
		/* 页面背景色 */
	}

	.card-container {
		background-color: white;
		border-radius: 8px;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		padding: 20px;
		/* height: 100%; */
		box-shadow: none;
	}

	.save-button {
		width: 100%;
		/* height: 48%; */
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 8rpx;
		font-size: 1rem;
	}

	.card-notice {
		width: 100%;
		height: 34rpx;
		font-size: 24rpx;
		color: rgba(119, 119, 119, 1);
		margin-left: 22rpx;
		margin-top: 24rpx;
	}
</style>
