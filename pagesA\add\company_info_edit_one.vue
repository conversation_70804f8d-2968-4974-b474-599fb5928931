<template>
	<view class="register-one">
		<view class="wrap" v-show="step==1">
			<view class="inp">
				<view class="avatar">
					<view class="inp-item">
						<view class="title fb">
							企业头像<text class="star">*</text>
						</view>
						<view class="in lab">
							请上传真实企业LOGO
						</view>
					</view>
					<view class="pic" @click="uploadLogo">
						<image :src="logo?logo:'https://api-test.zhaopinbei.com/storage/uploads/images/DjpYAiYx76keAZZls68bXyVPGHjYpURJJxA9YHcU.png'" mode=""></image>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						企业简称<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入企业简称" fontSize="32rpx" clearable :placeholderStyle="placeholderStyle"
							border="none" v-model="form.short_name"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						联系人邮箱<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入联系人邮箱" fontSize="32rpx" clearable :placeholderStyle="placeholderStyle"
							border="none" v-model="form.contact_email"></u--input>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						融资阶段<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changefinancingType" :value="financing_type_index" :range="financing_type"
							range-key="label">
							<view class="d-picker" :style="{color:financing_type_index==0?'#c0c4cc':'#303133'}">
								{{financing_type[financing_type_index]['label']}}
							</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						企业规模<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeCompanySize" :value="company_size_index" :range="company_size"
							range-key="label">
							<view class="d-picker" :style="{color:company_size_index==0?'#c0c4cc':'#303133'}">
								{{company_size[company_size_index]['label']}}
							</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>
				<view class="inp-item">
					<view class="title">
						所在地区<text class="star">*</text>
					</view>
					<view class="in se">
						<uni-data-picker v-model="form.address.district_id" :map="map" placeholder="请选择所在地区"
							popup-title="请选择所在地区" :localdata="cityList" @change="onchange">
						</uni-data-picker>
					</view>
				</view>

				<view class="inp-item" @click="choosePosition">
					<view class="title">
						详细地址<text class="star">*</text>
					</view>
					<view class="in se">
						{{form.address.address?form.address.address:'请选择地址'}}
						<u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						门牌号<text class="star">*</text>
					</view>
					<view class="in">
						<u--input placeholder="请输入门牌号" border="none" fontSize="32rpx"
							v-model="form.address.address_info"></u--input>
					</view>
				</view>
			</view>
		</view>


		<view class="wrap" v-show="step==2">
			<view class="inp">
				<view class="inp-item">
					<view class="title">
						所属行业<text class="star">*</text>
					</view>
					<view class="in se">
						<picker @change="changeIndustry" :value="industryIndex" :range="industry_classess"
							range-key="name">
							<view class="d-picker" :style="{color:industryIndex==0?'#c0c4cc':'#303133'}">
								{{industry_classess[industryIndex]['name']}}
							</view>
						</picker> <u-icon name="arrow-right"></u-icon>
					</view>
				</view>

				<view class="inp-item">
					<view class="title">
						企业标签<text class="star">*</text>
						<view class="plus">
							<image src="/static/images/index/plus.png" mode="" @click="open"></image>
						</view>
					</view>
					<view class="in">
						<view class="" style="color: #c0c4cc;" v-show="selectedTags.length==0">
							请选择就业方式
						</view>
						<view class="types" v-show="selectedTags.length>0">
							<view class="nav-item" v-for="(item,index) in selectedTags" :key="index">
								<view class="cont">
									<text>{{item.title}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="inp" style="margin-top: 32rpx;">
				<view class="inp-item">
					<view class="title">
						企业简介<text class="star">*</text>
					</view>
					<view class="in">
						<u--textarea v-model="form.intro" border="none" placeholder="请输入内容"></u--textarea>
					</view>
				</view>
			</view>

			<view class="pics">
				<view class="title">
					环境照片
				</view>
				<view class="pic-list">
					<block v-if="environment_url.length>0">
						<view class="pic-item" v-for="(item,index) in environment_url" :key="index">
							<image class="en" :src="item.path_url" mode=""></image>
							<!-- <view class="delete">删除</view> -->
						</view>
					</block>
					<block>
						<view class="pic-item" @click="uploadEnvironment">
							<image class="add" src="/static/images/my/add.png" mode=""></image>
						</view>
					</block>

				</view>
			</view>
		</view>
		<view class="footer">
			<view class="next sure" v-if="step==1" @click="next">
				下一步
			</view>
			<block v-if="step==2">
				<view class="next gray" @click="prev">
					上一步
				</view>

				<view class="next sure" @click="sure">
					提交
				</view>
			</block>
		</view>

		<!-- 行业弹框 -->
		<work-pop ref="workRef" :list="tags" :selected='selectedTags' @confirm="confirm"></work-pop>
	</view>
</template>

<script>
	import {
		getCompanyTags,
		getCompanyInfo,
		getIndustryList,
		editCompany,
		uploadImg,
		updateLoginInfo
	} from "../../config/api.js"
	import WorkPop from "../../components/workPop.vue"
	export default {
		components: {
			WorkPop
		},
		data() {
			return {
				tags: [],
				selectedTags: [],
				step: 1,
				map: {
					text: 'label',
					value: 'value'
				},

				financing_type_index: 0,
				company_size_index: 0,

				industryIndex: 0,
				industry_classess: [],

				business_license_img: '',
				logo: '',
				environment_url: [],

				form: {
					logo: [],
					industry_id: [],
					contact_email: "", //电子邮箱，比如 <EMAIL>
					short_name: "", //企业简称
					size_type: "",
					environment_images: [], //企业环境照片
					tag_id: [],
					financing_type: '',
					intro: '',
					address: {
						lat: "",
						lng: "",
						province_id: "",
						city_id: "",
						district_id: "",
						address: "",
						address_info: "",
					}
				},

				placeholderStyle: {
					'fontSize': '32rpx'
				},
			}
		},
		computed: {
			roleType() {
				return this.$store.state.roleType || uni.getStorageSync('roleType')
			},
			sysData() {
				return this.$store.state.sysData || uni.getStorageSync('sysData')
			},
			financing_type() {
				return this.sysData.financing_type
			},
			company_size() {
				return this.sysData.company_size
			},
			cityList() {
				return this.$store.state.cityList || uni.getStorageSync('cityList')
			}
		},
		onLoad() {
			this.getCompanyTags()
			this.getIndustryList()


			this.getCompanyInfo()
		},
		methods: {
			open() {
				this.$refs.workRef.open();
			},

			confirm(selected) {
                console.log("企业标签",selected)
				this.selectedTags = selected
				this.form.tag_id = this.selectedTags.map(item => item.id)
			},

			async getCompanyTags() {
				let params = {
					page: 1,
					limit: 20,
					tag_type: 'company'
				}
				const {
					status_code,
					data,
					message
				} = await getCompanyTags()
				this.tags = data;
			},
			async getCompanyInfo() {
				const {
					status_code,
					data,
					message
				} = await getCompanyInfo()
				if (status_code == 200) {
					this.details = data;
					console.log(this.details)
					this.form.short_name = this.details.company.short_name
					this.form.address.lat = this.details.address.lat
					this.form.address.lng = this.details.address.lng
					this.form.address.province_id = this.details.address.province_id
					this.form.address.city_id = this.details.address.city_id
					this.form.address.district_id = this.details.address.district_id
					//this.form.address.district_id = this.details.address.province_id+','+this.details.address.city_id+','+this.details.address.district_id;
					this.form.address.address = this.details.address.address
					this.form.address.address_info = this.details.address.address_info
					this.form.address_info = this.details.address.address_info

					this.financing_type_index = this.financing_type.findIndex(item => item.value == this.details
						.company_info.financing_type)
					this.form.financing_type = this.financing_type[this.financing_type_index]['value']

					this.company_size_index = this.company_size.findIndex(item => item.value == this.details
						.company_info.size_type)
					this.form.size_type = this.company_size[this.company_size_index]['value']

					this.form.contact_email = this.details.company_info.contact_email

					this.environment_url = this.details.company_info.environment_images

					this.logo = this.details.company_info.logo.path_url || ''
					this.form.logo = this.details.company_info.logo ? [this.details.company_info.logo.id] : [],

						this.selectedTags = this.details.tags
					this.form.tag_id = this.selectedTags.map(item => item.id)

					this.form.intro = this.details.company.intro

					//行业
					if (this.details && this.details.industry_classes && this.details.industry_classes.length > 0) {
						console.log("行业数据：", this.industry_classess[this.industryIndex]['id'])
						this.industryIndex = this.industry_classess.findIndex(item => item.id == this.details
							.industry_classes[0]['id'])
						console.log("索引：", this.industryIndex)
						this.form.industry_id = [this.industry_classess[this.industryIndex]['id']]
						console.log("行业参数id:", this.form.industry_id)
					}
				}
			},

			changeIndustry(e) {
				this.industryIndex = e.detail.value
				this.form.industry_id = [this.industry_classess[this.industryIndex]['id']]
			},

			changefinancingType(e) {
				this.financing_type_index = e.detail.value
				this.form.financing_type = this.financing_type[this.financing_type_index]['value']
			},

			changeCompanySize(e) {
				this.company_size_index = e.detail.value
				this.form.size_type = this.company_size[this.company_size_index]['value']
			},

			onchange(e) {
				let data = e.detail.value
				console.log(data)
				this.form.address.province_id = data[0]['value']
				this.form.address.city_id = data[1]['value']
				this.form.address.district_id = data[2]['value']
				//this.form.district_id = data.map(item => item.value).join(',')
				//console.log(this.form.district_id)
			},

			//获取行业类型
			async getIndustryList() {
				const {
					status_code,
					data
				} = await getIndustryList()
				this.industry_classess = data;
			},

			/*onchange(e) {
				let data = e.detail.value
				this.form.address.province_id = data[0]['value']
				this.form.address.city_id = data[1]['value']
				this.form.address.district_id = data[2]['value']
				// this.form.address.address = data.map(item => item.text).join('')
			},
*/
			//选择地图地址
			choosePosition() {
				console.log(1)
				let self = this;
				uni.chooseLocation({
					success: function(res) {
						console.log('位置名称：' + res.name);
						console.log('详细地址：' + res.address);
						console.log('纬度：' + res.latitude);
						console.log('经度：' + res.longitude);
						self.form.address.lat = res.latitude
						self.form.address.lng = res.longitude
						self.form.address.address = res.address
					}
				});
			},

			//上传logo
			uploadLogo() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								if (result.status_code == 200) {
									self.logo = result.data.url
									self.form.logo = [result.data.id]
								}
							}
						})
					}
				});
			},


			//上传环境照片
			uploadEnvironment() {
				let self = this
				uni.chooseMedia({
					count: 1,
					mediaType: ['image'],
					sizeType: ['original', 'compressed'],
					sourceType: ['album', 'camera'],
					success: (tempFilePaths) => {
						const path = tempFilePaths.tempFiles[0].tempFilePath;
						uni.getFileSystemManager().readFile({
							filePath: path,
							encoding: 'base64',
							success: async function(res) {
								let imageParams = {
									ext: 'png',
									content: res.data,
									org_name: new Date().getTime() + '.png'
								}
								// 上传
								const result = await uploadImg(imageParams)
								if (result.status_code == 200) {
									self.environment_url.push({
										id: result.data.id,
										path_url: result.data.url,
										pdf_url: '',
										thumbnail_path_url: result.data.url
									})
								}
							}
						})
					}
				});
			},

			next() {
				if(!this.form.short_name){
					uni.showToast({
						title: '请输入企业简称',
						icon: 'none'
					});
					return false;
				}
				if(!this.form.contact_email){
					uni.showToast({
						title: '请输入联系人邮箱',
						icon: 'none'
					});
					return false;
				}

				if(!this.form.address.district_id){
					uni.showToast({
						title: '请选择所在地区',
						icon: 'none'
					});
					return false;
				}
				if(!this.form.address_info){
					uni.showToast({
						title: '请输入门牌号',
						icon: 'none'
					});
					return false;
				}
				this.step = 2
			},

			prev() {
				this.step = 1
			},

			async sure() {
				this.form.environment_images = this.environment_url.map(item => item.id);
				if(!this.form.intro){
					uni.showToast({
						title: '请输入企业简介',
						icon: 'none'
					});
					return false;
				}
				let params = {
					...this.form
				}
				const {
					status_code,
					data
				} = await editCompany(params)
				if (status_code == 200) {
					let loginInfo = await updateLoginInfo()
					if (loginInfo.status_code == 200) {
						this.$store.commit('setUserInfo', loginInfo.data)
						uni.navigateBack()
						return uni.$u.toast('保存信息成功')

					}
				}
			}
		}
	}
</script>
<style>
	page {
		background: #f5f5f5;
		/* background-image: url('/static/images/login/bg.png');
		background-position: 100% 100%;
		background-size: 100% 100%;
		background-repeat: no-repeat; */
		font-family: PingFang SC, PingFang SC;
	}

	/* .placeholderClass{
		font-weight: 400;
		font-size: 32rpx;
		color: red;
	} */
</style>
<style lang="less" scoped>
	.register-one {
		display: flex;
		flex-direction: column;
		padding-bottom: 220rpx;
	}

	.wrap {
		padding: 30rpx;

		.pic {
			display: flex;
			flex-direction: column;
			margin-bottom: 32rpx;

			.title {

				font-weight: 500;
				font-size: 32rpx;
				color: #333333;
				margin: 16rpx 0;

				.star {
					font-weight: 600;
					font-size: 22rpx;
					color: #FE4D4F;
					margin-left: 8rpx;
				}


			}

			.pic-list {
				display: flex;
				flex-wrap: wrap;

				.pic-item {
					margin-right: 24rpx;

					&:last-child {
						margin-right: 0;
					}

					image {
						width: 200rpx;
						height: 180rpx;
					}
				}
			}
		}

		.inp {
			background: #FFFFFF;
			border-radius: 16rpx;

			.avatar {
				display: flex;
				align-items: center;

				.pic {
					padding: 0 30rpx 0 0;

					image {
						width: 108rpx;
						height: 108rpx;
					}
				}
			}

			.inp-item {
				display: flex;
				flex-direction: column;
				padding: 0 30rpx;
				flex: 1;

				// border-bottom: 1px solid #F5F5F7;
				.title {
					display: flex;
					align-items: center;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin: 16rpx 0 0 0;
					position: relative;

					.star {
						font-weight: 600;
						font-size: 22rpx;
						color: #FE4D4F;
						margin-left: 8rpx;
					}

					.plus {
						position: absolute;
						right: 0;
						top: 50%;
						margin-top: -16rpx;

						image {
							width: 32rpx;
							height: 32rpx;
						}
					}
				}

				.in {
					display: flex;
					align-items: center;
					justify-content: space-between;
					border-bottom: 1px solid #F5F5F7;
					min-height: 88rpx;
					font-size: 32rpx;

					::v-deep uni-data-picker {
						width: 100%;
					}

					::v-deep .arrow-area {
						transform: rotate(-135deg);
					}

					::v-deep .input-arrow {
						width: 20rpx;
						height: 20rpx;
						border-left: 1px solid #606266;
						border-bottom: 1px solid #606266;
					}

					::v-deep .input-value-border {
						border: none;
					}

					::v-deep .input-value {
						padding: 0;
					}

					::v-deep .placeholderClass {
						font-weight: 400;
						font-size: 32rpx;
					}

					::v-deep picker {
						display: flex;
						flex-direction: column;
						flex: 1;
						height: 88rpx;

						.d-picker {
							display: flex;
							align-items: center;
							// width: 60vw;
							height: 88rpx;
						}
					}
				}

				.scroll-W {
					width: 100%;
					white-space: nowrap;


				}

				.nav-item {
					display: inline-block;
					text-align: center;
					background: #F5F5F7;
					padding: 0 16rpx;
					height: 50rpx;
					line-height: 50rpx;
					font-weight: 500;
					font-size: 24rpx;
					color: #333333;
					border-radius: 8rpx;
					margin-right: 40rpx;

					&:last-child {
						margin-right: 0;
					}

					.cont {
						display: flex;
						align-items: center;
						width: 100%;
						height: 100%;

						.del {
							display: flex;
							align-items: center;

							text {
								margin-right: 8rpx;
							}
						}

					}
				}

				.active {
					background: #4F8CF0;
					color: #FFFFFF;
				}

				.se {
					color: #999;
				}

				.lab {
					font-weight: 400;
					font-size: 22rpx;
					color: #999999;
				}
			}
		}
	}

	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 24rpx;
		height: 64rpx;
		width: 260rpx;
		// margin-left: 32rpx;
		background: #4F8CF0;
		font-weight: 600;
		font-size: 28rpx;
		color: #FFFFFF;
		border-radius: 8rpx;
	}

	.pics {
		display: flex;
		flex-direction: column;

		.title {
			font-weight: 500;
			font-size: 32rpx;
			color: #333333;
			padding: 32rpx 0;
		}

		.pic-list {
			display: flex;
			flex-wrap: wrap;

			.pic-item {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 212rpx;
				height: 186rpx;
				background-color: #FFFFFF;
				margin-right: 20rpx;
				margin-bottom: 32rpx;

				.en {
					width: 100%;
					height: 100%;
				}

				.add {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
	}

	.footer {
		display: flex;
		justify-content: space-around;
		flex-direction: column;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 220rpx;
		// background-color: #FFFFFF;
		z-index: 10;
		border-radius: 24rpx 24rpx 0 0;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			width: 90%;
			border-radius: 44rpx;
		}

		.gray {
			background-color: #cccccc;
			color: #FFFFFF;
		}

		.sure {
			background-color: #4F8CF0;
			color: #FFFFFF;
		}
	}
</style>
