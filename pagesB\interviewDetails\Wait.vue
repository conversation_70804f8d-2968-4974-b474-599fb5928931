<template>
	<view class="waiting-interview-container">
		<!-- 顶部返回和更多操作区域 -->
		<view class="backcolor">
		</view>
		<view class="header">
			<view class="back-icon" @click="goBack">
				<u-icon name="arrow-left" color="wheat"></u-icon>
			</view>
			<view class="more-options">
				<image src="/static/more-dots.png"></image>
				<image src="/static/eye-icon.png"></image>
			</view>
		</view>
		<!-- 标题 -->
		<view class="interview-button">
			<view class="title" v-if="text == 0">等待面试</view>
			<view class="title" v-if="text == 1">面试待评价</view>
			<view class="title" v-if="text == 2">面试已取消</view>
			<view class="title" v-if="text == 3">面试超时未接受</view>
			<view @click="Sign()" class="sign">签到</view>
		</view>
		<!-- 面试信息卡片 -->
		<view class="interview-info-card">
			<view class="company-info">
				<image class="company-logo" src="/static/company-logo-placeholder.png"></image>
				<view class="Brief ">
					<view class="company-name">北京学创联盟有限公司</view>
					<view class="hr-info">
						<image class="hr-avatar" src="/static/hr-avatar-placeholder.png"></image>
						<view class="hr-name">王哈哈·人事HR</view>
					</view>
				</view>
			</view>
			<view class="interview-details">
				<view class="flex-top">
					<view class="detail-item">
						<view class="label">时间</view>
						<view class="value">2025-09-27 16:30</view>
					</view>
					<view class="detail-item">
						<view class="label">职位</view>
						<view class="value">UI设计师·4-7K</view>
					</view>
					<view class="detail-item">
						<view class="label">联系人</view>
						<view class="interview-bottom">
							<view class="value">哇哈哈</view>
							<view class="call-button">
								<u-icon name="phone-fill" color="#4F8CF0"></u-icon>
								<span style="margin-left: 10rpx;">
									呼叫
								</span>
							</view>
						</view>
					</view>
				</view>
				<view class="flex-bottom">
					<view class="detail-item">
						<view class="label">备注</view>
						<view class="value">面试请携带简历</view>
					</view>
					<view class="detail-item">
						<view class="label">地址</view>
						<view class="value">郑州金水区博学路街道万正商务大厦...</view>
					</view>
					<view class="map-placeholder"></view>
				</view>
			</view>
		</view>
		<!-- 面试进度 -->
		<view class="interview-progress step-style" v-if="interviewhide">
			<view class="step-wrapper">
				<view class="step-item" v-for="(step, index) in progressSteps" :key="index">
					<view class="step-dot" :class="{active: step.active}"></view>
					<view class="step-content">
						<view class="step-title">{{step.name}}</view>
						<view class="step-desc">
							{{step.desc}}
							<view class="Ongoing" v-if="step.active && step.name=='进行面试'" @click="Gocomplanit">
								{{step.tip}}
							</view>
						</view>
						<view class="step-tags" v-if="step.active && step.name == '面试签到'">
							已签到
						</view>
						<view class="step-button" v-if="step.active && step.name=='面试结果'">
							{{step.tip}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				progressSteps: [{
						name: '面试时间到',
						desc: '面试一开始，此时不能取消面试',
						active: true
					},
					{
						name: '面试签到',
						desc: '到场后请及时签到，可体验面试全流程服务',
						active: false
					},
					{
						name: '进行面试',
						desc: '面试过程中，如有任何问题，你可以',
						tip: '投诉',
						active: true
					},
					{
						name: '面试结果',
						desc: '完成面试后30天内可向伯乐获取面试结果',
						tip: '获取面试结果',
						active: true
					}
				],
				text: 0,
				titleText: '',
				interviewhide: true
			};
		},
		methods: {
			goBack() {
				const pages = getCurrentPages();
				if (pages.length > 1) {
					uni.navigateBack({
						delta: 1
					});
				} else {
					uni.redirectTo({
						url: '/pagesB/personal/find_job_record'
					});
				}
			},
			Sign() {
				this.text
				console.log(this.text);
				if (this.text == 0) {
					const signIndex = this.progressSteps.findIndex(step => step.name === '面试签到');
					if (signIndex !== -1) {
						for (let i = signIndex; i < this.progressSteps.length; i++) {
							this.progressSteps[i].active = true;
						}
					}
					this.interviewhide = true
					this.text++
				} else if (this.text == 1) {
					this.interviewhide = false
					this.text++
				} else if (this.text == 2) {
					this.interviewhide = false
					this.text++
				} else if (this.text >= 3) {
					this.interviewhide = true
					this.text = 0
				}
			},
			Gocomplanit() {
				uni.navigateTo({
					url: '/pagesB/interviewDetails/complaint'
				})
			},
		}
	};
</script>

<style lang="scss" scoped>
	.waiting-interview-container {
		padding: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.backcolor {
		width: 750rpx;
		height: 564rpx;
		position: absolute;
		top: 0px;
		left: 0px;
		background-image: linear-gradient(to top, rgba(245, 245, 247, 1), rgba(157, 211, 255, 1), rgba(79, 117, 240, 1));
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 88rpx;
		margin-bottom: 52rpx;
		align-self: flex-start;
		z-index: 5;
	}

	::v-deep.back-icon .u-icon__icon {
		font-size: 48rpx !important;
	}

	.Brief {
		display: flex;
		flex-direction: column;
	}

	.back-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.more-options {
		display: flex;
	}

	.more-options image {
		width: 30rpx;
		height: 30rpx;
		margin-left: 10rpx;
	}

	.Ongoing {
		font-size: 24rpx;
		color: rgba(150, 190, 255, 1);
	}

	.interview-button {
		flex: 1;
		display: flex;
		justify-content: space-between;
		width: 100%;
		z-index: 5;
	}

	.sign {
		width: 108rpx;
		height: 52rpx;
		background: rgba(255, 255, 255, 0.50);
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 8rpx;
		color: rgba(79, 140, 240, 1);
	}

	.title {
		color: white;
		font-size: 32rpx;
		margin-top: 20rpx;
		margin-bottom: 32rpx;
		line-height: 37.5rpx;
		align-self: flex-start;
	}

	.interview-info-card {
		background-color: white;
		border-radius: 20rpx;
		padding: 32rpx;
		padding-bottom: 0rpx;
		margin-top: 20rpx;
		width: 622rpx;
		height: 728rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		z-index: 5;
	}

	.flex-top {
		border-bottom: 1rpx solid #E6E6E6;
		padding-bottom: 32rpx;
	}

	.company-info {
		display: flex;
		align-items: center;
		border-bottom: 1rpx solid #E6E6E6;
		padding-bottom: 32rpx;
	}

	.company-logo {
		width: 108rpx;
		height: 108rpx;
		background-color: #ddd;
		margin-right: 20rpx;
	}

	.company-name {
		font-size: 32rpx;
	}

	.hr-info {
		display: flex;
		align-items: center;
	}

	.hr-avatar {
		width: 48rpx;
		height: 48rpx;
		background-color: #ddd;
		border-radius: 50%;
		margin-right: 10rpx;
	}

	.hr-name {
		font-size: 28rpx;
	}

	.interview-details {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.detail-item {
		display: flex;
		align-items: center;
		margin-top: 10rpx;
	}

	.label {
		width: 100rpx;
		color: #666;
		font-size: 28rpx;
	}

	.value {
		font-size: 28rpx;
		align-self: center;
		flex: 1;
	}

	.call-button {
		background-color: transparent;
		border: 2rpx solid #4F8CF0;
		color: #4F8CF0;
		border-radius: 10rpx;
		font-size: 24rpx;
		width: 108rpx;
		height: 48rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-left: 24rpx;
	}

	.map-placeholder {
		width: 100%;
		height: 200rpx;
		background-color: #ddd;
		margin-top: 20rpx;
	}

	.interview-progress {
		margin-top: 24rpx;
		background-color: white;
		border-radius: 20rpx;
		padding: 32rpx;
		width: 622rpx;
		height: 728rpx;
	}

	.interview-bottom {
		display: flex;
	}

	.step-wrapper {
		display: flex;
		flex-direction: column;
		border-left: 4rpx solid #ccc;
		padding-left: .5rpx;
		position: relative;
	}

	.step-item {
		margin-bottom: 40rpx;
		position: relative;
	}

	.step-dot {
		width: 24rpx;
		height: 24rpx;
		border-radius: 50%;
		border: 4rpx solid #ccc;
		position: absolute;
		left: -20rpx;
		top: 0px;
		background-color: white;
		color: #666;
	}

	.step-dot.active {
		border-color: #1989fa;
		background-color: #1989fa;
	}

	.step-content {
		padding-left: 40rpx;
	}

	.step-title {
		font-size: 32rpx;
		color: #333;
		margin-bottom: 10rpx;
	}

	.step-desc {
		font-size: 24rpx;
		color: #666;
		display: flex;
	}

	.step-item:last-child {
		border-left: none;

	}

	.step-tags {
		width: 558rpx;
		height: 88rpx;
		border-radius: 12rpx;
		background-color: #F5F5F5;
		display: flex;
		align-items: center;
		padding-left: 24rpx;
		margin-top: 18rpx;
	}

	.step-button {
		width: 176rpx;
		height: 50rpx;
		border: 1px solid rgba(204, 204, 204, 1);
		border-radius: 12rpx;
		font-size: 24rpx;
		color: rgba(204, 204, 204, 1);
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 16rpx;
	}
</style>