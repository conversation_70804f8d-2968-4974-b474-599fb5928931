<template>
	<view class="item" @click="goDetails">
		<view class="up">
			<view class="item-up">
				<image :src="item.company_info.logo.thumbnail_path_url" mode=""></image>
				<view class="info">
					<view class="user">
						<view class="userInfo">
							<view class="name">
								{{item.job.title}}
							</view>
							
						</view>
						<view class="money">
							{{item.job.salary_info_str}}
						</view>
					</view>
					<view class="flags">
						<view class="flag" v-if="item.company.short_name != null">
							{{item.company.short_name}}
						</view>
						<view class="flag" v-if="item.company_info.financing_type_name != null">
							{{item.company_info.financing_type_name}}
						</view>
						<view class="flag" v-if="item.company_info.size_type != null">
							{{item.company_info.size_type}}
						</view>
					</view>
				</view>
			</view>
			
			<view class="type" v-if="item.company.intro != null">
				{{item.company.intro}}
			</view>
			
			<view class="tags">
				<view class="tag" v-for="(sub,idx) in item.company_tags" :key="sub.id">
					{{sub.title}}
				</view>
				<!-- <view class="tag">
					上市公司
				</view>
				<view class="tag">
					游戏大厂
				</view> -->
			</view>
		</view>
		
		<view class="down">
			<view class="name">
				{{item.member_certification.name?item.member_certification.name:item.member_info.nick_name}}
			</view>
			<view class="time">
				{{item.reported_at}}
			</view>
		</view>
		
        <view class="hrBox"></view>
        <view class="bottomBox">
            <view v-if="item.interview_result.result_status_name" style="color: #4F8CF0;font-size: 28rpx;">面试结果：{{item.interview_result.result_status_name}}</view>
            <view v-else></view>
            <view>
                 <!-- v-if="intervew.member_show==2通知，member_show==1不通知" -->
                <view class="chatBtn1" @click.stop="pushInterview(item.interview.id)" v-if="item.interview.member_show==2">立即通知</view>
                <view class="chatBtn" @click.stop="communicate('job_report',item.id)">聊聊呗</view>
            </view>
        </view>
	</view>
</template>

<script>
    import {pushInterview} from '../../config/headhunterList_api.js'
    import {communicate} from "../../common/common";
	export default {
        props:{
            item:Object
        },
		name: "jobApplyItem",
		data() {
			return {

			};
		},
        methods:{
            communicate,
            async pushInterview(id) {
                console.log("推送面试")
                let params = {
                    interview_id:id
                }
               const res =  await pushInterview(params)
               uni.$u.toast(res.message);
            },
            goDetails(){
            	uni.navigateTo({
            		url:"/pagesA/details/memberJobDetails?id="+this.item.job_id
            	})
            }
        }
	}
</script>

<style lang="less" scoped>
	.item {
		display: flex;
		flex-direction: column;
		padding: 32rpx;
		margin-bottom: 32rpx;
		background-color: #FFFFFF;
		border-radius: 24rpx;
		.up{
			display: flex;
			flex-direction: column;
			padding-bottom: 24rpx;
			.item-up {
				display: flex;
			
				&>image {
					width: 88rpx;
					height: 88rpx;
				}
			
				.info {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					flex: 1;
					padding-left: 24rpx;
			
					.user {
						display: flex;
						justify-content: space-between;
						align-items: center;
			
						.userInfo {
							display: flex;
							align-items: center;
							justify-content: space-between;
							.name {
								font-weight: 600;
								font-size: 32rpx;
								color: #333333;
							}
						}
						.money{
							font-weight: 600;
							font-size: 32rpx;
							color: #F98A14;
						}
					}
			
					.flags {
						display: flex;
			
						.flag {
							display: flex;
							align-items: center;
							padding: 0 12rpx;
							font-weight: 400;
							font-size: 22rpx;
							color: #999999;
							border-right: 1px solid #999999;
			
							&:first-child {
								padding-left: 0;
							}
			
							&:last-child {
								border-right: none;
							}
						}
					}
			
					
				}
			}
			
			.tags {
				display: flex;
				margin-top: 16rpx;	
				.tag {
					display: flex;
					align-items: center;
					background: #F6F6F6;
					border-radius: 8rpx;
					height: 46rpx;
					padding: 0 12rpx;
					font-weight: 400;
					font-size: 22rpx;
					color: #666666;
					margin-right: 16rpx;
				}
			}
			
			.type{
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
				margin-top: 16rpx;	
			}
		}
		
		.down{
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-top: 24rpx;
			// border-top: 1px solid #F5F5F7;
			.name{
				font-weight: 600;
				font-size: 32rpx;
				color: #333333;
			}
			.time{
				font-weight: 400;
				font-size: 24rpx;
				color: #666666;
			}
		}
	}
    
    .hrBox {
        width: 622rpx;
        height: 2rpx;
        background: #F5F5F5;
        margin: 24rpx 0;

    }
    
    .bottomBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .chatBtn {
        width: 144rpx;
        height: 56rpx;
        background: #F5F5F7;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 56rpx;
        float: right;
        
        font-weight: 600;
        font-size: 24rpx;
        color: #4F8CF0;
    }
    
    .chatBtn1 {
        width: 144rpx;
        height: 56rpx;
        background: #4F8CF0;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        text-align: center;
        line-height: 56rpx;
        float: right;
        
        font-weight: 600;
        font-size: 24rpx;
        color: #FFFFFF;
        margin-right: 16rpx;
    }
</style>