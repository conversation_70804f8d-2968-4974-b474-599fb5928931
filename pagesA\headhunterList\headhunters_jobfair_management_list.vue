<template>
	<view class="main">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<u-tabs :current="tabIndex" lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
					color: '#4F8CF0',
					fontWeight: 'bold',
					transform: 'scale(1.05)'
				}" :inactiveStyle="{
					color: '#999999',
					transform: 'scale(1)'
				}" :list="tabs" @click="changeTab"></u-tabs>
				<view class="search-wrap">
					<u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
				</view>
				<view class="filters">
					<view class="filter" @click="selectCity">
						{{userCity.name}}<image src="/static/images/index/down.png" mode=""></image>
					</view>
					<!-- <view class="filter" @click="openFilter">
						筛选<image src="/static/images/index/down.png" mode=""></image>
					</view> -->
				</view>
			</view>

		</u-sticky>
		<view class="list">
			<headhunters-job-fair-item v-for="(item,index) in list" :key="index" :item="item" @del='del'></headhunters-job-fair-item>
		</view>

		<view class="footer">
			<view class="next sure" @click="go">
				发布招聘会
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getHeadhuntersJobfairList,
		delHeadhuntersJobfair
	} from "../../config/api.js"
	import HeadhuntersJobFairItem from "../components/headhuntersJobFairItem.vue"
	export default {
		components: {
			HeadhuntersJobFairItem
		},
		data() {
			return {
				page: 1,
				limit: 10,
				list: [],
                userCity: {
                    name: '全国',
                    id: 0
                },
			}
		},
		computed: {},
        onLoad() {
            if (uni.getStorageSync('userCity')) {
                this.userCity = uni.getStorageSync('userCity');
            }
            this.getHeadhuntersJobfairList()
        },
		onShow() {
            if (this.userCity.name != uni.getStorageSync('userCity').name) {
                this.userCity = uni.getStorageSync('userCity'); // 更新当前城市
                this.page=1;
                this.list = [];
                this.getHeadhuntersJobfairList()
            }
		},
		methods: {
            openFilter() {
                uni.showActionSheet({
                	itemList: ['通过', '未通过'],
                	success: function (res) {
                		console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
                	},
                	fail: function (res) {
                		console.log(res.errMsg);
                	}
                });
            },
            selectCity() {
                uni.navigateTo({
                    url: '/pagesA/components/selectCitys'
                })
            },
			go() {
				uni.navigateTo({
					url: "/pagesA/add/pubJobFair"
				})
			},
			async getHeadhuntersJobfairList() {
				let params = {
					page: this.page,
					limit: this.limit,
                    city_id:this.userCity.id
				}
				const {
					status_code,
					data,
					message
				} = await getHeadhuntersJobfairList(params)
				if (status_code == 200) {
					this.list = data.data;
				}
			},
			
			//删除
			del(item) {
				let self = this;
				uni.showModal({
					title: '确定要删除招聘会吗？',
					success: async (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							let params = {
								id: item.id
							}
							const {
								status_code,
								data,
								message
							} = await delHeadhuntersJobfair(params)
							if (status_code == 200) {
								self.page = 1
								self.list = []
								self.getHeadhuntersJobfairList()
								return uni.$u.toast('成功')
							} else {
								return uni.$u.toast(message || '失败')
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				})
			},
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.main {
		padding-bottom: 120rpx;
	}

	.header {
		padding: 32rpx;

		.search-wrap {
			// margin-top: 32rpx;
		}

		.filters {
			display: flex;
			margin-top: 32rpx;

			.filter {
				display: flex;
				align-items: center;
				height: 48rpx;
				background-color: #FFFFFF;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				padding: 0 12rpx;
				margin-right: 12rpx;
				border-radius: 8rpx;

				image {
					width: 24rpx;
					height: 24rpx;
				}
			}
		}
	}

	.list {
		padding: 0 32rpx;
	}

	.footer {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		height: 120rpx;
		width: 100%;
		background-color: #FFFFFF;
		z-index: 10;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 600;
			font-size: 28rpx;
			color: #FFFFFF;
			height: 88rpx;
			width: 90%;
			border-radius: 16rpx;
		}

		.sure {
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	}
</style>