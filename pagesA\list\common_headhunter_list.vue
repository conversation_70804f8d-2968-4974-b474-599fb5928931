<template>
	<view id="app">
		<u-sticky bgColor="#F5F5F5">
			<view class="search-wrap">
				<u-search placeholder="请输入姓名或手机号" bgColor="#FFFFFF" :showAction="true" actionText="搜索"
					v-model="page.form.keyword" @custom="custom" @clear="clear"></u-search>
			</view>
		</u-sticky>
		<view class="list" style="margin-top: 24rpx;">
			<view @click="edit(item)" v-for="(item,index) in page.data" :key="item.id"
				:class="item.id == selectedInd ? 'item selectedCla' :'item'">
				<view class="leftCon">
					<image :src="item.member_info.image.path_url" class="img"></image>
					<view class="info">
						<view class="user">
							<view class="name">
								{{ item.member.certification_status==1 ? item.member_certification.name : item.name }}
							</view>
							<view :class="['status',item.member.certification_status==1?'yrz':'wrz']">
								{{item.member.certification_status_name }}
							</view>
						</view>
						<view class="phone">
							手机号：{{ item.member.cellphone }}
						</view>
					</view>
				</view>
				<view class="rightCon" @click="goDetail(item)">管家详情</view>
			</view>
		</view>
		<Pages :status="page.status"></Pages>
		<view style="height: 120rpx;"></view>
		<view class="footer">
			<view class="next sure" @click="add">
				确定
			</view>
		</view>
	</view>
</template>

<script>
	import Pages from "../../components/pages.vue";
	import {
		headhunterList
	} from "../../config/common_api";

	export default {
		components: {
			Pages
		},
		data() {
			return {
				tabs: [{
						name: '已认证',
					},
					{
						name: '未注销',
					}
				],

				page: {
					form: {
						keyword: "",
						page: 1,
						limit: 10,
						member_certification_status: 1,
						disable_status: 2,
						company_id: 0,
					},
					data: [],
					status: 'nomore',
				},
				selectedInd: '',
				name: '',
				cellphone: '',
			}
		},
		mounted() {},
		onLoad(opts) {
			this.page.form.company_id = opts.company_id;
			this.staffList();
		},
		//触底加载更多
		onReachBottom() {
			if (this.page.status == 'loading') {
				this.page.form.page++;
				this.staffList();
			}
		},
		methods: {
			edit(item) {
				if (this.selectedInd == item.id) {
					this.selectedInd = '';
					this.cellphone = '';
					this.name = '';
					this.head = '';
				} else {
					this.selectedInd = item.id;
					this.cellphone = item.member.cellphone;
					this.name = item.member_certification.name;
					this.head = item.member_info.image.thumbnail_path_url
				}
			},
			add() {
				if (this.selectedInd) {
					uni.setStorageSync('selectedInd', {
						id: this.selectedInd,
						phone: this.cellphone,
						name: this.name,
						head: this.head
					})
					uni.navigateBack()
				} else {
					uni.showToast({
						icon: 'none',
						title: "未选择变更人",
						duration: 2000
					})
				}
			},
			clear() {
				this.page.form.keyword = "";
				this.custom()
			},
			custom() {
				this.page.form.page = 1;
				this.page.data = [];
				this.staffList()
			},
			async staffList() {
				var _this = this;
				headhunterList(_this.page.form).then(response => {
					if (response.status_code == '200') {
						_this.page.data = _this.page.data.concat(response.data.data);
						_this.page.more = response.data.more || false;
						_this.page.status = _this.page.more ? 'loading' : 'nomore';
					}
				});
			},
			goDetail(item) {
				// getButlerDetails
				uni.navigateTo({
					url: '/pagesA/details/obtainItemDetails?id=' + item.id
				})
			}
		}
	}
</script>
<style lang="scss" src="../../static/css/pagesA/list/common_headhunter_list.scss"></style>