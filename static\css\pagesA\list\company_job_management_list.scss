page {
    background: #F5F5F7;
}

.header {
    padding: 32rpx;
    
    .tabs {
        display: flex;
        justify-content: space-between;
        
        .tab {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 88rpx;
            display: flex;
            flex: 1;
            background: #FFFFFF;
            font-weight: 400;
            font-size: 28rpx;
            color: #999999;
            
            &:first-child {
                border-radius: 44rpx 0 0 44rpx;
            }
            
            &:last-child {
                margin-left: -40rpx;
                border-radius: 0 44rpx 44rpx 0;
            }
        }
        
        .pg_1 {
            clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
        }
        
        .pg_2 {
            clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
        }
        
        .active {
            color: #4F8CF0;
            font-weight: 600;
        }
    }
    
    .search-wrap {
        margin-top: 32rpx;
    }
    
    .filters {
        display: flex;
        align-items: center;
        margin-top: 32rpx;
        
        .filter {
            display: flex;
            align-items: center;
            height: 48rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding: 0 16rpx;
            margin-right: 12rpx;
            border-radius: 8rpx;
            background: #FFFFFF;
            
            image {
                width: 24rpx;
                height: 24rpx;
            }
        }
        
        .search-wrap {
            flex: 1;
        }
    }
}

.list {
    padding: 0 32rpx 0 32rpx;
	block {
		background-color: #f5f5f5;
	}
    
    ::v-deep .u-swipe-action-item__right {
        bottom: 32rpx;
        border-radius: 24rpx;
    }

    ::v-deep .u-demo-block__title {
            background: transparent !important;
        }
        
    ::v-deep .u-swipe-action-item__content {
        background: transparent !important;
    }
}

.footer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    bottom: 0;
    height: 120rpx;
    width: 100%;
    background-color: #FFFFFF;
    border-radius: 24rpx 24rpx 0 0;
    z-index: 10;
    
    .next {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 28rpx;
        color: #FFFFFF;
        height: 88rpx;
        width: 90%;
        border-radius: 16rpx;
    }
    
    .sure {
        background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
        color: #FFFFFF;
    }
}