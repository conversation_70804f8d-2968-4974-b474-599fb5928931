<template>
	<view class="" :style="{paddingBottom:tabIndex==1||tabIndex==2?'120rpx':''}">
		<u-sticky bgColor="#F5F5F5">
			<view class="header">
				<view class="tabs">
					<u-tabs  lineWidth="20"
					lineColor="#4F8CF0"
					:activeStyle="{
						color: '#4F8CF0',
						fontWeight: 'bold',
					}"
					:inactiveStyle="{
						color: '#999999',
					}"
					itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;" :list="tabs" @click="changeTab"></u-tabs>
				</view>

				<!-- <u-tabs :list="list1" @click="click"></u-tabs> -->

				<view class="search-wrap">
					<u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="false" v-model="keyword"></u-search>
				</view>
			</view>
		</u-sticky>
		<view class="list">
			<block v-if="tabIndex==0">
				<sign-record-item v-for="(item,index) in signRecords" :key="index" :item="item"></sign-record-item>
			</block>
			<block v-if="tabIndex==1 || tabIndex==2">
				<keyword-item v-for="item in 3" :tabIndex="tabIndex" @more="more"></keyword-item>
			</block>
		</view>

		<view class="footer" v-if="tabIndex==1 || tabIndex==2">
			<view class="next sure" @click="addKeyword" v-if="tabIndex==1">
				添加关键字
			</view>
			<view class="next sure" @click="addSeal" v-if="tabIndex==2">
				添加签署
			</view>
		</view>

		<u-action-sheet :actions="list" :show="show" round="24rpx" cancelText="取消" @select='select'
			@close="close"></u-action-sheet>
	</view>
</template>

<script>
	import SignRecordItem from '../components/signRecordItem.vue'
	import keywordItem from '../components/keywordItem.vue'
	import {getContractList} from '../../config/company_api.js'
	export default {
		components: {
			SignRecordItem,
			keywordItem
		},
		data() {
			return {
				page: 1,
				limit: 10,
				status: 'loadmore',
				NoMore: false,
				show: false,
				currentItem: {},
				tabIndex: 0,
				
				tabs: [{
					name: '签署记录',
				}, {
					name: '关键字',
				}, {
					name: '印章'
				}],
				signRecords: [{
						status: 1
					},
					{
						status: 2
					}, {
						status: 1
					},
					{
						status: 2
					}, {
						status: 1
					},
					{
						status: 2
					}, {
						status: 1
					},
					{
						status: 2
					}
				],
				list: [{
						flag: 'edit',
						name: '编辑',

					},
					{
						flag: 'delete',
						name: '删除',
					}

				],
				contractList:[]
			}
		},
		onLoad() {
			this.getContractList()
		},
		//触底加载更多
		onReachBottom() {
			if (this.NoMore) {
				this.status = 'loading'
				this.page++
				this.getContractList()
			} else {
				this.status = 'nomore'
			}
		},
		methods: {
			async getContractList() {
				let params = {
					page :this.page,
					limit:this.limit,
					// title:this.title
				}
				
				const res = await getContractList(params)
				this.contractList = this.contractList.concat(res.data.data);
				// 返回false代表没有下一页
				this.NoMore = res.data.more;
				this.status = this.NoMore ? "loadmore" : "nomore"
				console.log("合同列表",res)
			},
			changeTab(e) {
				this.tabIndex = e.index
			},
			more(item) {
				this.currentItem = item
				this.show = true
			},
			close() {
				this.currentItem = {}
				this.show = false
			},
			addKeyword() {
				uni.navigateTo({
					url: "/pagesA/add/addKeyword"
				})
			},
			addSeal() {
				uni.navigateTo({
					url: "/pagesA/add/addSeal"
				})
			},
			select(e) {
				console.log(e)
                // 执行编辑edit
				if (e.flag == 'edit' && this.tabIndex == 1) {
					uni.navigateTo({
						url: "/pagesA/add/addKeyword"
					})
				} else if (e.flag == 'edit' && this.tabIndex == 2) {
					uni.navigateTo({
						url: "/pagesA/add/addSeal"
					})
				}
			}
		}
	}
</script>
<style>
	page {
		background-color: #F5F5F7;
	}
</style>
<style lang="less" scoped>
	.header {
		display: flex;
		flex-direction: column;

		.tabs {
			background: #FFFFFF;
		}

		.search-wrap {
			padding: 32rpx;
		}
	}

	.list {
		padding: 0 32rpx;
	}

	.footer {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: fixed;
		left: 0;
		bottom: 0;
		height: 120rpx;
		width: 100%;
		background-color: #FFFFFF;
		z-index: 10;

		.next {
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: 600;
			font-size: 28rpx;
			color: #FFFFFF;
			height: 88rpx;
			width: 90%;
			border-radius: 16rpx;
		}

		.sure {
			background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
			color: #FFFFFF;
		}
	}
</style>