<template>
	<!-- 账号管理 -->
	<view class="container">
		<u-cell>
			<template #title>
				<view class="phone-container">
					<view class="phone-start" @click="onEditPhone">
						<text class="text">修改手机号</text>
						<text class="phone">17762271226</text>
						<image class="image" src="/static/new/右箭头@2x1.png" alt="" />
					</view>
					<view class="phone-end">修改成功后，可通过新手机号登录招聘呗</view>
				</view>
			</template>
		</u-cell>
		<u-cell>
			<template #title>
				<view class="phone-container">
					<view class="phone-start" @click="onOff">
						<text class="text">注销账号</text>
						<text class="phone"></text>
						<image class="image" src="/static/new/右箭头@2x1.png" alt="" />
					</view>
				</view>
			</template>
		</u-cell>
		<u-toast ref="toast"></u-toast>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	},
	methods: {
		onEditPhone() {
			uni.$u.route({
				url: '/pagesB/personal/phone_edit',
			});
		},
		onOff() {
			uni.$u.route({
				url: '/pagesB/personal/infoOff',
			});
		},
	},
};
</script>

<style lang="scss" scoped>
::v-deep .u-cell {
	.u-cell__body {
		padding: 0 !important;
	}
}
.container {
	height: 100vh;
	background-color: #ffffff;
	box-sizing: border-box;
	padding-inline: 32rpx;

	.title {
		padding-block-start: 72rpx;
	}

	.phone-container {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
		padding-block: 60rpx;

		.phone-end {
			color: #999999;
			font-size: 22rpx;
		}

		.phone-start {
			display: flex;
			align-items: center;

			.text {
				color: #333333;
				font-size: 28rpx;
			}

			.phone {
				color: #999999;
				font-size: 22rpx;
				margin-inline-start: auto;
			}

			.image {
				width: 32rpx;
				height: 32rpx;
			}
		}
	}
}
</style>
