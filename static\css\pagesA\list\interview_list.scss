page {
    background-color: #f5f5f5;
    
    /* background-image: url('/static/images/login/bg.png'); */
    /* background-image: url('https://h5.zhaopinbei.cn/1.png');
    background-position: 100% 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat; */
}

.list {
    padding: 0 32rpx;
}

.header {
    padding: 0 32rpx 32rpx 32rpx;
    
    .search-wrap {
        margin-top: 32rpx;
    }
    
    .filters {
        display: flex;
        margin-top: 32rpx;
        
        .filter {
            display: flex;
            align-items: center;
            height: 48rpx;
            background-color: #FFFFFF;
            font-weight: 400;
            font-size: 24rpx;
            color: #333333;
            padding: 0 12rpx;
            margin-right: 12rpx;
            border-radius: 8rpx;
            
            image {
                width: 24rpx;
                height: 24rpx;
            }
        }
    }
}

.wrap {
    display: flex;
    flex-direction: column;
    width: 600rpx;
    
    .title {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 92rpx;
        background: rgba(79, 140, 240, 0.1)
    }
    
    .items {
        display: flex;
        flex-direction: column;
        
        .item {
            display: flex;
            justify-content: space-between;
            padding: 24rpx;
            
            .lab {
                display: flex;
                font-weight: 400;
                font-size: 28rpx;
                color: #666666;
            }
            
            .name {
                flex: 1;
                flex-wrap: wrap;
                font-weight: 400;
                font-size: 28rpx;
                color: #333333;
            }
        }
    }
}
.custom-dialog {
	    position: fixed;
	    top: 0;
	    left: 0;
	    width: 100%;
	    height: 100%;
	    background-color: rgba(0, 0, 0, 0.5);
	    display: flex;
	    justify-content: center;
	    align-items: center;
	    z-index: 999;
	}
	
	.dialog-content {
	    padding: 40rpx;
	    background-color: #fff;
	    border-radius: 20rpx;
	    width: 600rpx;
	    text-align: center;
	}
	
	.dialog-title {
	    font-size: 32rpx;
	    font-weight: bold;
	    margin-bottom: 40rpx;
	    color: #333;
	}
	
	.dialog-buttons {
	    display: flex;
	    justify-content: space-between;
	}
	
	.dialog-buttons button {
	    flex: 1;
	    margin: 0 20rpx;
	    // padding: 12rpx 0;
		width: 300rpx;
	    background-color: #4f8cf0;
	    color: white;
	    border-radius: 10rpx;
	    text-align: center;
	    font-size: 28rpx;
	}
	
	.dialog-buttons button:active {
	    background-color: #3a6bb0;
	}