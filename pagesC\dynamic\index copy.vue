<template>
	<view class="container">
		<u-sticky>
			<view class="sticky-container">
				<u-tabs
					:list="tabsList"
					:activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
					:inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }"
					@click="onTabsItemClick"></u-tabs>
			</view>
		</u-sticky>

		<template v-if="tabsActiveKey === '1'">
			<scroll-view :scroll-y="true" class="scroll-y-view">
				<view class="scroll-y-container">
					<scroll-view :scroll-x="true" class="scroll-x-view">
						<view class="article">
							<view class="title-box">
								<image class="wenzheng" src="/static/new/火苗@2x.png" mode=""></image>
							</view>
							<view class="item">
								<image class="image" src="/static/new/jinpai.png"></image>
								<view class="text-box">
									<view class="text">2025年的春天，你在干嘛？</view>
									<view class="text-bot">
										<view class="company">学创联盟网络科技（北京）有限公司</view>
										<image class="image" src="/static/new/浏览量****************" mode=""></image>
										<text class="desc">124</text>
									</view>
								</view>
							</view>
							<view class="item">
								<image class="image" src="/static/new/yinpai.png"></image>
								<view class="text-box">
									<view class="text">2025年的春天，你在干嘛？</view>
									<view class="text-bot">
										<view class="company">学创联盟网络科技（北京）有限公司</view>
										<image class="image" src="/static/new/浏览量****************" mode=""></image>
										<text class="desc">124</text>
									</view>
								</view>
							</view>
							<view class="item">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/xfEwCPjT4oiDPnE9kmiTNkwAkjhER1eFPZAmp1jy.png"></image>
								<view class="text-box">
									<view class="text">2025年的春天，你在干嘛？</view>
									<view class="text-bot">
										<view class="company">学创联盟网络科技（北京）有限公司</view>
										<image class="image" src="/static/new/浏览量****************" mode=""></image>
										<text class="desc">124</text>
									</view>
								</view>
							</view>
						</view>
						<view class="article" style="margin-inline-end: 24rpx">
							<view class="title-box">
								<image class="wenzheng" src="/static/new/火苗@2x.png" mode=""></image>
							</view>
							<view class="item">
								<image class="image" src="/static/new/jinpai.png"></image>
								<view class="text-box">
									<view class="text">2025年的春天，你在干嘛？</view>
									<view class="text-bot">
										<view class="company">学创联盟网络科技（北京）有限公司</view>
										<image class="image" src="/static/new/浏览量****************" mode=""></image>
										<text class="desc">124</text>
									</view>
								</view>
							</view>
							<view class="item">
								<image class="image" src="/static/new/yinpai.png"></image>
								<view class="text-box">
									<view class="text">2025年的春天，你在干嘛？</view>
									<view class="text-bot">
										<view class="company">学创联盟网络科技（北京）有限公司</view>
										<image class="image" src="/static/new/浏览量****************" mode=""></image>
										<text class="desc">124</text>
									</view>
								</view>
							</view>
							<view class="item">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/xfEwCPjT4oiDPnE9kmiTNkwAkjhER1eFPZAmp1jy.png"></image>
								<view class="text-box">
									<view class="text">2025年的春天，你在干嘛？</view>
									<view class="text-bot">
										<view class="company">学创联盟网络科技（北京）有限公司</view>
										<image class="image" src="/static/new/浏览量****************" mode=""></image>
										<text class="desc">124</text>
									</view>
								</view>
							</view>
						</view>
					</scroll-view>
					<view class="column-container">
						<text class="title">精彩文章</text>
						<text>查看更多</text>
						<image class="image" src="/static/new/右箭头@2x1.png"></image>
					</view>
					<view class="article-item">
						<view class="item-start">
							<view class="start-left">
								<view class="user-box">
									<image class="avatar" src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"></image>
									<text>匿名</text>
									<u-line direction="col" length="40rpx"></u-line>
									<text>2025-3-14</text>
								</view>
								<view class="content-title">字节跳动Fink大规模云原生化实践</view>
								<view class="content-sub-title">
									本文整理自字节跳动基瑞架构工程师刘畅，在Flink Forrard Asia 生产实践专场的分享，字节跳动拥有业界领先的 Flink
									流式计算任务规模。随着云原生时代的到来，我
								</view>
							</view>
							<view class="start-right">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"></image>
							</view>
						</view>
						<view class="item-end">
							<image class="image" src="/static/new/浏览量****************" mode=""></image>
							<text style="margin-inline-end: auto">124</text>
							<image class="image" src="/static/new/dianzan.png" style="margin-inline-start: auto"></image>
							<text>点赞</text>
						</view>
					</view>
					<view class="column-container">
						<text class="title">精彩问答</text>
						<text>查看更多</text>
						<image class="image" src="/static/new/右箭头@2x1.png"></image>
					</view>
					<view class="question-item">
						<view class="item-start">
							<view class="start-left">
								<view class="user-box">
									<image class="avatar" src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"></image>
									<text>匿名</text>
									<u-line direction="col" length="40rpx"></u-line>
									<text>2025-3-14</text>
								</view>
								<view class="content-title">字节跳动Fink大规模云原生化实践</view>
								<view class="content-sub-title">
									本文整理自字节跳动基瑞架构工程师刘畅，在Flink Forrard Asia 生产实践专场的分享，字节跳动拥有业界领先的 Flink
									流式计算任务规模。随着云原生时代的到来，我
								</view>
							</view>
						</view>
						<view class="item-end">
							<view class="btn">回答</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</template>

		<template v-if="tabsActiveKey === '2'">
			<scroll-view :scroll-y="true" class="scroll-y-view">
				<view class="scroll-y-container">
					<scroll-view :scroll-x="true" class="scroll-x-view">
						<view class="Leaderboard">
							<view class="title-box">
								<image class="huomiao" src="/static/new/火苗@2x.png" mode=""></image>
								<text>热榜</text>
							</view>
							<view class="item">
								<image class="image" src="/static/new/jinpai.png"></image>
								<view class="text-box">
									<text class="text-1">2025年的春天，你在干嘛？</text>
									<text class="text-2">574次围观</text>
								</view>
							</view>
							<view class="item">
								<image class="image" src="/static/new/yinpai.png"></image>
								<view class="text-box">
									<text class="text-1">2025年的春天，你在干嘛？</text>
									<text class="text-2">574次围观</text>
								</view>
							</view>
							<view class="item">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/xfEwCPjT4oiDPnE9kmiTNkwAkjhER1eFPZAmp1jy.png"></image>
								<view class="text-box">
									<text class="text-1">2025年的春天，你在干嘛？</text>
									<text class="text-2">574次围观</text>
								</view>
							</view>
						</view>
						<view class="Leaderboard" style="margin-inline-end: 24rpx">
							<view class="title-box">
								<image class="huomiao" src="/static/new/火苗@2x.png" mode=""></image>
								<text>热榜</text>
							</view>
							<view class="item">
								<image class="image" src="/static/new/jinpai.png"></image>
								<view class="text-box">
									<text class="text-1">2025年的春天，你在干嘛？</text>
									<text class="text-2">574次围观</text>
								</view>
							</view>
							<view class="item">
								<image class="image" src="/static/new/yinpai.png"></image>
								<view class="text-box">
									<text class="text-1">2025年的春天，你在干嘛？</text>
									<text class="text-2">574次围观</text>
								</view>
							</view>
							<view class="item">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/xfEwCPjT4oiDPnE9kmiTNkwAkjhER1eFPZAmp1jy.png"></image>
								<view class="text-box">
									<text class="text-1">2025年的春天，你在干嘛？</text>
									<text class="text-2">574次围观</text>
								</view>
							</view>
						</view>
					</scroll-view>
					<view class="content-box">
						<view class="comment-box" v-for="_ in 20">
							<view class="user">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"></image>
								<text class="name">王哈哈</text>
								<text class="time">3月14日 18:46</text>
							</view>
							<view class="content">内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容内容</view>
							<view class="b-user">
								<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/RktSOPNJLvjadH1zP2OJPfRzY3Tnr5eff6T0qeWk.png"></image>
								<text class="name">王哈哈:</text>
								<view class="content">笑鼠了</view>
							</view>
							<view class="bot-box">
								<view class="bot-left">
									<image class="image" src="/static/new/dianzan.png"></image>
									<text>点赞</text>
								</view>
								<view class="bot-right">
									<image class="image" src="/static/new/pinglun.png"></image>
									<text>评论</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</template>
	</view>
</template>

<script>
export default {
	data() {
		return {
			tabsActiveKey: '1',
			tabsList: [
				{
					key: '1',
					name: '文章',
				},
				{
					key: '2',
					name: '评论',
				},
			],
		};
	},
	methods: {
		onTabsItemClick(v) {
			this.tabsActiveKey = v.key;
		},
	},
};
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f7;
	display: flex;
	flex-direction: column;

	.sticky-container {
		background-color: #ffffff;
		padding-block: 24rpx;
		border-end-start-radius: 24rpx;
		border-end-end-radius: 24rpx;
	}

	.scroll-y-view {
		flex: 1;
		overflow-y: auto;

		.scroll-y-container {
			padding-block: 32rpx;
			display: flex;
			flex-direction: column;
			gap: 32rpx;
			padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

			.question-item {
				background: linear-gradient(to bottom, #dbe8ff, #ffffff);
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-end {
					display: flex;
					justify-content: flex-end;

					.btn {
						display: inline;
						color: #4f8cf0;
						font-size: 24rpx;
						padding-block: 8rpx;
						padding-inline: 26rpx;
						background: rgba(79, 140, 240, 0.3);
						border: 1rpx #4f8cf0 solid;
						border-radius: 4rpx;
					}
				}

				.item-start {
					display: flex;
					align-items: center;
					gap: 32rpx;

					.start-right {
						.image {
							width: 176rpx;
							height: 176rpx;
							border-radius: 24rpx;
						}
					}

					.start-left {
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.content-title {
							flex: 1;
							color: #041024;
							font-size: 28rpx;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.content-sub-title {
							color: #666666;
							font-size: 24rpx;
							flex: 1;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.user-box {
							display: flex;
							align-items: center;
							gap: 24rpx;
							color: #777777;
							font-size: 24rpx;

							.avatar {
								width: 48rpx;
								height: 48rpx;
								border-radius: 999rpx;
							}
						}
					}
				}
			}

			.article-item {
				background-color: #ffffff;
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-end {
					display: flex;
					align-items: center;
					justify-content: space-between;
					color: #666666;
					font-size: 24rpx;
					gap: 12rpx;

					.image {
						width: 32rpx;
						height: 32rpx;
					}
				}

				.item-start {
					display: flex;
					align-items: center;
					gap: 32rpx;

					.start-right {
						.image {
							width: 176rpx;
							height: 176rpx;
							border-radius: 24rpx;
						}
					}

					.start-left {
						display: flex;
						flex-direction: column;
						gap: 24rpx;

						.content-title {
							color: #041024;
							font-size: 28rpx;
							width: 50%;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.content-sub-title {
							color: #666666;
							font-size: 24rpx;
							flex: 1;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.user-box {
							display: flex;
							align-items: center;
							gap: 24rpx;
							color: #777777;
							font-size: 24rpx;

							.avatar {
								width: 48rpx;
								height: 48rpx;
								border-radius: 999rpx;
							}
						}
					}
				}
			}

			.column-container {
				display: flex;
				align-items: center;
				color: #999999;
				font-size: 24rpx;
				padding-inline: 32rpx;

				.title {
					font-size: 32rpx;
					color: #041024;
					margin-inline-end: auto;
				}

				.image {
					width: 32rpx;
					height: 32rpx;
				}
			}

			.content-box {
				padding-inline: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;
			}

			.comment-box {
				padding: 32rpx;
				background-color: #ffffff;
				display: flex;
				flex-direction: column;
				gap: 16rpx;
				border-radius: 12rpx;

				.bot-box {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					color: #666666;
					font-size: 24rpx;
					gap: 32rpx;

					.bot-left,
					.bot-right {
						display: flex;
						align-items: center;
						gap: 12rpx;
					}

					.image {
						width: 32rpx;
						height: 32rpx;
					}
				}

				.content {
					color: #666666;
					font-size: 24rpx;
				}

				.b-user {
					background-color: #f8f8f8;
					display: flex;
					align-items: center;
					gap: 20rpx;
					padding: 24rpx;

					.content {
						color: #666666;
						font-size: 28rpx;
					}

					.name {
						color: #666666;
						font-size: 28rpx;
					}

					.image {
						width: 48rpx;
						height: 48rpx;
						border-radius: 999rpx;
					}
				}

				.user {
					display: flex;
					align-items: center;
					gap: 20rpx;

					.image {
						width: 64rpx;
						height: 64rpx;
						border-radius: 999rpx;
					}

					.name {
						color: #041024;
						font-size: 28rpx;
					}

					.time {
						color: #999999;
						font-size: 20rpx;
					}
				}
			}

			.scroll-x-view {
				white-space: nowrap;

				.article {
					margin-inline-start: 24rpx;
					display: inline-flex;
					padding: 32rpx;
					background: linear-gradient(180deg, #ffe3d6 0%, #ffffff 53%);
					border-radius: 24rpx;
					flex-direction: column;
					gap: 20rpx;

					.title-box {
						.wenzheng {
							width: 148rpx;
							height: 52rpx;
						}
					}

					.item {
						display: flex;
						align-items: flex-start;
						gap: 16rpx;

						.image {
							width: 40rpx;
							height: 40rpx;
						}

						.text-box {
							display: flex;
							flex-direction: column;

							.text {
								width: 70%;
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
							}

							.text-bot {
								display: flex;
								align-items: center;

								.company {
									color: #666666;
									font-size: 22rpx;
									width: 70%;
									white-space: nowrap;
									overflow: hidden;
									text-overflow: ellipsis;
								}

								.image {
									width: 32rpx;
									margin-inline-start: auto;
									height: 32rpx;
								}

								.desc {
									color: #666666;
									font-size: 20rpx;
								}
							}
						}
					}
				}

				.Leaderboard {
					margin-inline-start: 24rpx;
					display: inline-flex;
					padding: 32rpx;
					background: linear-gradient(180deg, #ffe3d6 0%, #ffffff 53%);
					border-radius: 24rpx;
					flex-direction: column;
					gap: 20rpx;

					.item {
						display: flex;
						align-items: flex-start;
						gap: 16rpx;

						.image {
							width: 40rpx;
							height: 40rpx;
						}

						.text-box {
							display: flex;
							flex-direction: column;

							.text-1 {
								color: #041024;
								font-size: 26rpx;
							}

							.text-2 {
								color: #999999;
								font-size: 22rpx;
							}
						}
					}

					.title-box {
						display: flex;
						align-items: center;
						gap: 16rpx;
						color: #041024;
						font-size: 32rpx;

						.huomiao {
							width: 40rpx;
							height: 40rpx;
						}
					}
				}
			}
		}
	}
}
</style>
