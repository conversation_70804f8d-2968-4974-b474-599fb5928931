<template>
  <view class="warp">
    <view class="inner">
      <view
        class="item"
        v-for="(item, index) in list"
        key="index"
        @click="goDetail"
      >
        <img src="" alt="" />
        <view class="right">
          <view class="top">
            {{ item.name }}
            <view class="tag"> 已认证 </view>
          </view>
          <view class="btm"> 17388888888 </view>
        </view>
        <u-icon name="arrow-right" color="#999" size="24"></u-icon>
      </view>
    </view>
    <view class="" style="height: 196rpx"> </view>
    <view class="footers">
      <view class="next sure" @click="next"> 新增人员 </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      list: [
        { img: "", name: "王哈哈", num: "17388888888" },
        { img: "", name: "王哈哈", num: "17388888888" },
        { img: "", name: "王哈哈", num: "17388888888" },
        { img: "", name: "王哈哈", num: "17388888888" },
        { img: "", name: "王哈哈", num: "17388888888" },
        { img: "", name: "王哈哈", num: "17388888888" },
      ],
    };
  },
  methods: {
    goDetail() {
      uni.navigateTo({
        url: `/pagesC/connectivity/detail`,
      });
    },
  },
};
</script>
<style>
body {
  background: #f5f5f7;
}
</style>
<style lang="scss" scoped>
.warp {
  padding: 32rpx;
  .inner {
    box-sizing: border-box;
    padding: 0 32rpx;
    background: #fff;
    border-radius: 24rpx;
    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 0;
      border-bottom: 1rpx solid #e6e6e6;
    }

    img {
      width: 88rpx;
      height: 88rpx;
      border-radius: 44rpx;
      background: #999;
      margin-right: 24rpx;
    }
    .right {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: 0 24rpx;
      font-size: 32rpx;
      color: #333333;
      .top {
        display: flex;
        align-items: center;
        .tag {
          width: 92rpx;
          height: 44rpx;
          background: #ecf5ff;
          border-radius: 8rpx 8rpx 8rpx 8rpx;
          border: 2rpx solid #dbedff;
          font-size: 20rpx;
          color: #4f8cf0;
          text-align: center;
          line-height: 44rpx;
          margin-left: 24rpx;
        }
      }
      .btm {
        font-size: 28rpx;
        color: #666666;
        margin-top: 20rpx;
      }
    }
  }
}
.footers {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 196rpx;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;

  .next {
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    color: #ffffff;
    height: 88rpx;
    width: 90%;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    border-radius: 16rpx;
  }

  .sure {
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    color: #ffffff;
  }
}
</style>
