<template>
  <view class="register-one">
    <view class="inp">
      <view
        class="inp-item"
        @click="goToCompanyList"
        v-if="roleType == 'headhunters'"
      >
      </view>
      <view class="inp-item">
        <view class="title"> 活动类型 </view>
        <view class="in se">
          <picker
            @change="changeType"
            :value="typeIndex"
            :range="typeList"
            range-key="name"
          >
            <view
              class="d-picker"
              :style="{ color: typeIndex == 0 ? '#c0c4cc' : '#303133' }"
            >
              {{ typeIndex ? typeList[typeIndex]["name"] : "请选择活动类型" }}
            </view>
          </picker>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item">
        <view class="title"> 活动标题 </view>
        <view class="in">
          <u--input
            placeholder="请填写活动标题"
            placeholderClass="placeholderClass"
            clearable
            border="none"
            v-model="pubJobOne.title"
          ></u--input>
        </view>
      </view>
      <view class="inp-item">
        <view class="title"> 活动简介 </view>
        <view class="in">
          <u--input
            placeholder="请一句话介绍活动"
            placeholderClass="placeholderClass"
            clearable
            border="none"
            v-model="pubJobOne.intro"
          ></u--input>
        </view>
      </view>
      <view class="inp-item">
        <view class="title">
          活动介绍
          <text class="star">*</text>
        </view>
        <view class="in">
          <u--input
            placeholder="请填写活动介绍"
            placeholderClass="placeholderClass"
            clearable
            border="none"
            v-model="pubJobOne.job_describe"
          ></u--input>
        </view>
      </view>
      <view
        class="inp-item"
        @click="showPop = true"
        v-if="typeList[typeIndex].name != '招聘会'"
      >
        <view class="title"> 岗位关联 </view>
        <view class="in se">
          <view> 添加岗位 </view>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view
        class="inp-item"
        @click="goAddress"
        v-if="typeList[typeIndex].name == '招聘会'"
      >
        <view class="title"> 活动地址 </view>
        <view class="in se">
          <view> 添加活动地址 </view>
          <u-icon name="arrow-right"></u-icon>
        </view>
      </view>
      <view class="inp-item">
        <view class="title"> 活动时间 </view>
        <view class="in">
          <view
            @click="showTime1 = true"
            :style="{
              color: pubJobOne.starttime == '' ? '#c0c4cc' : '#303133',
            }"
          >
            {{ pubJobOne.starttime ? pubJobOne.starttime : "请选择开始时间" }}
          </view>
          <view
            class="se1"
            @click="showTime2 = true"
            :style="{ color: pubJobOne.endtime == '' ? '#c0c4cc' : '#303133' }"
          >
            {{ pubJobOne.endtime ? pubJobOne.endtime : "请选择结束时间" }}
          </view>
          <u-datetime-picker
            ref="datetimePicker"
            :show="showTime1"
            mode="date"
            @confirm="onTimePickerConfirm1"
            @cancel="showTime1 = false"
          >
          </u-datetime-picker>
          <u-datetime-picker
            :show="showTime2"
            mode="date"
            @confirm="onTimePickerConfirm2"
            @cancel="showTime2 = false"
          >
          </u-datetime-picker>
        </view>
      </view>
      <view class="inp-item">
        <view class="title"> 活动要求 </view>
        <view class="in">
          <u--input
            placeholder="请输入活动要求"
            placeholderClass="placeholderClass"
            clearable
            border="none"
          ></u--input>
        </view>
      </view>
      <view class="inp-item">
        <view class="title"> 报名入口 </view>
        <view class="in" style="margin-top: 16rpx">
          <u-upload
            :fileList="fileList1"
            name="1"
            multiple
            :maxCount="1"
          ></u-upload>
        </view>
      </view>
    </view>

    <view class="footers">
      <view class="next prev"> 预览 </view>
      <view class="next sure" @click="next"> 下一步 </view>
    </view>

    <!-- 标签弹框 -->
    <u-modal
      :show="showLabel"
      showCancelButton
      cancelColor="#FE4D4F"
      @cancel="cancel"
      @confirm="sureLabel"
      title="添加标签"
    >
      <view class="slot-content">
        <view class="inp">
          <u--input
            placeholder="请输入标签内容"
            border="surround"
            v-model="labelValue"
          ></u--input>
        </view>
      </view>
    </u-modal>

    <!-- 行业弹框 -->
    <!-- <work-pop ref="workRef" :list="workTypes" :selected="selectedWork" @confirm="sureWork"></work-pop> -->
    <!-- 提交时候是否有记录，没有的话直接从confirm1里拿 -->
    <u-modal
      :show="show"
      title="选择就业方式"
      cancelText="取消"
      showCancelButton
      @cancel="cancel1"
      @confirm="confirm1"
    >
      <view class="slot-content">
        <view class="workList">
          <view
            v-for="(item, index) in workTypes"
            :key="index"
            @click="handleTab(item)"
            :class="[
              'workItem',
              { active: selectedItem.some((v) => item.id == v.id) },
            ]"
          >
            {{ item.name || item.title }}
          </view>
        </view>
      </view>
    </u-modal>
    <u-popup :show="showPop" mode="bottom" @close="showPop = false">
      <view class="tip-box">
        <view class="tip-tit">
          <view>
            <text>岗位库</text>
          </view>
        </view>
        <view class="sear-box">
          <u-search></u-search>
        </view>
        <view class="list">
          <view class="item btm" v-for="item in jobList" :key="item">
            <view class="right">
              <view class="name">
                {{ item.name }}
              </view>
            </view>
            <img
              class="del"
              @click="showPop = false"
              src="https://api-test.zhaopinbei.com/storage/uploads/images/Sl4y4bsafDNMgfpp372fiFnhD0jxCXPYoHzhs5aq.png"
              alt=""
            />
          </view>
        </view>
        <view class="tip-btm">
          <view class="tip-btn1" @click="showPop = false">取消</view>
          <view class="tip-btn" @click="showPop = false">保存</view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import {
  getWorkType,
  getCompanyJobClassList,
  getWelfareList,
  getIndustryList,
  getJobDetails,
  getHeadhuntersAuthEnterpriseList,
} from "../../config/api.js";
import WorkPop from "../../components/workPop.vue";

export default {
  components: {
    WorkPop,
  },
  data() {
    return {
      id: "",
      step: 0,
      mapClass: {
        text: "name",
        value: "id",
      },

      compIndex: 0,
      compList: [
        {
          value: 0,
          id: "",
          name: "请选择",
        },
      ],

      classList: [],
      classIndex: 0,
      fileList1: [],
      eduIndex: 0,
      typeIndex: 0,
      WelfareIndex: 0,

      industryIndex: "",
      industry_classess: [],

      selectedWork: [],
      selectedItem: [],
      workTypes: [],

      welfarelist: [],
      welfareIds: [],

      labelValue: "",
      showLabel: false,
      showPop: false,
      jobLabelList: [],
      companyAuthorizeData: [],
      showTime1: false,
      showTime2: false,
      pubJobOne: {
        copy_company_id: [],
        job_active_id: 0,
        type: "job",
        company_id: "",
        title: "",
        education: "",
        industry_id: [],
        work_type_id: [],
        class_id: "",
        contact: "",
        contact_phone: "",
        intro: "",
        tag_id: [],
        label: [],
        job_describe: "",
        typeJob: "",
        starttime: "",
        endtime: "",
      },
      show: false,
      copy: "",
      typeList: [
        {
          name: "特推招聘",
          value: "特推招聘",
        },
        {
          name: "宣讲会",
          value: "宣讲会",
        },
        {
          name: "招聘会",
          value: "招聘会",
        },
        {
          name: "岗位预定",
          value: "岗位预定",
        },
      ],
      jobList: [
        {
          name: "ui设计师",
        },
        {
          name: "前端",
        },
        {
          name: "PHP",
        },
      ],
    };
  },

  computed: {
    roleType() {
      return this.$store.state.roleType || uni.getStorageSync("roleType");
    },

    //系统数据
    sysData() {
      return this.$store.state.sysData || uni.getStorageSync("sysData");
    },

    education() {
      return this.sysData.education;
    },
    companyAuthorize() {
      console.log("companyAuthorize", this.$store.state.companyAuthorize);
      return this.$store.state.companyAuthorize || [];
    },
    companyAuthorizeValue() {
      console.log("companyAuthorize", this.$store.state.companyAuthorize);
      return this.companyAuthorize;
    },
  },
  onReady() {
    this.$refs.datetimePicker.setFormatter(this.formatter);
  },
  onLoad(options) {
    this.getWorkType(); //行业
    this.getWelfareList(); //福利
    this.getIndustryList();
    this.getCompanyJobClassList();

    if (uni.getStorageSync("roleType") == "headhunters") {
      this.getHeadhuntersAuthEnterpriseList();
    }

    if (options.type) {
      this.pubJobOne.type = options.type;
    }
    if (options.copy) {
      this.copy = options.copy;
    }
    this.pubJobOne.job_active_id = options.job_active_id || 0;

    if (options.id) {
      this.id = options.id;
      this.getJobDetails();
    }
  },

  methods: {
    goToCompanyList() {
      uni.navigateTo({
        url: "/pagesA/add/pubJobCompany/index",
      });
    },
    async getCompanyJobClassList() {
      const result = await getCompanyJobClassList();
      if (result.status_code == 200) {
        console.log(result);
        this.classList = result.data;
      }
    },
    async getWorkType() {
      const result = await getWorkType();
      if (result.status_code == 200) {
        this.workTypes = result.data;
      }
    },
    async getHeadhuntersAuthEnterpriseList() {
      let params = {
        status: 1,
        cancel_status: 2,
      };
      const result = await getHeadhuntersAuthEnterpriseList(params);
      if (result.status_code == 200) {
        result.data.forEach((item, index) => {
          this.compList.push({
            id: item.authorize_company.id,
            name: item.authorize_company.name,
          });
        });
      }
    },

    onchangeclass(e) {
      let data = e.detail.value;
      this.pubJobOne.class_id = data[data.length - 1]["value"];
    },

    next() {

     uni.navigateTo({
        url: "/pagesC/active/projectList",
      });
    },

    changeClass(e) {
      this.classIndex = e.detail.value;
      this.pubJobOne.class_id = this.classList[this.classIndex]["id"];
    },

    changeComp(e) {
      console.log(e);
      this.compIndex = e.detail.value;
      let id = this.compList[e.detail.value].id;
      this.pubJobOne.company_id = id;
      console.log(id);
    },

    changeEdu(e) {
      this.eduIndex = e.detail.value;
      this.pubJobOne.education = this.education[this.eduIndex]["value"];
    },
    changeType(e) {
      this.typeIndex = e.detail.value;
      this.pubJobOne.type = this.education[this.typeIndex]["value"];
    },
    formatter(type, value) {
      if (type === "year") {
        return `${value}年`;
      }
      if (type === "month") {
        return `${value}月`;
      }
      if (type === "day") {
        return `${value}日`;
      }
      return value;
    },
    onTimePickerConfirm1(event) {
      this.pubJobOne.starttime = this.formatTimestampToDay(event.value);
      this.showTime1 = false;
    },
    onTimePickerConfirm2(event) {
      this.pubJobOne.endtime = this.formatTimestampToDay(event.value);
      this.showTime2 = false;
    },
    formatTimestampToDay(timestamp) {
      const date = new Date(timestamp);
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    changeIndustry(e) {
      this.industryIndex = e.detail.value;
      this.pubJobOne.industry_id[0] =
        this.industry_classess[this.industryIndex]["id"];
    },

    changeWelfare(e) {
      // let isExsit = this.welfareIds.some(v => item.id == v)
      // if (isExsit) {
      // 	let index = this.welfareIds.findIndex(v => item.id == v)
      // 	this.welfareIds.splice(index, 1)
      // } else {
      // 	this.welfareIds.push(item.id)
      // }
      // this.pubJobOne.tag_id = this.welfareIds
      console.log(e);
      this.WelfareIndex = e.detail.value;
      this.pubJobOne.tag_id = this.welfarelist[this.WelfareIndex]["title"];
    },

    openWork() {
      this.show = true;
      // this.selectedItem = this.selectedWork
      // this.$refs.workRef.open();
    },

    // sureWork(selected) {
    //   this.selectedWork = selected
    //   this.pubJobOne.work_type_id = this.selectedWork.map(item => item.id)
    // },

    cancel() {
      this.labelValue = "";
      this.showLabel = false;
    },
    cancel1() {
      this.show = false;
    },
    confirm1() {
      this.selectedWork = this.selectedItem;
      this.show = false;
    },
    handleTab(item) {
      const index = this.selectedItem.indexOf(item);
      if (index === -1) {
        // 如果没有选中，添加到选中列表
        this.selectedItem.push(item);
      } else {
        // 如果已经选中，移除选中
        this.selectedItem.splice(index, 1);
      }
    },

    addLabel() {
      this.showLabel = true;
    },

    delLabel(item) {
      let index = this.jobLabelList.findIndex((v) => item == v);
      this.jobLabelList.splice(index, 1);
    },

    sureLabel() {
      if (!this.labelValue) return uni.$u.toast("请输入职位标签");
      this.jobLabelList.push(this.labelValue);
      this.pubJobOne.label = this.jobLabelList;
      this.showLabel = false;
    },

    async getIndustryList() {
      const { stauts_code, data } = await getIndustryList();
      this.industry_classess = data;
      console.log("行业类型：", this.industry_classess);
    },

    async getWelfareList() {
      const { stauts_code, data } = await getWelfareList();
      this.welfarelist = data;
    },
    goAddress() {
      uni.navigateTo({
        url: "/pagesC/active/components/address",
      });
    },

    async getJobDetails() {
      let params = {
        id: this.id,
      };
      const { status_code, data } = await getJobDetails(params);
      if (status_code == 200) {
        this.details = data;
        console.log(this.details.class_id, "类型di");
        this.pubJobOne.title = data.title;
        this.eduIndex = this.education.findIndex(
          (item) => item.value == this.details.education
        );
        // this.classIndex = this.classList.findIndex(item => item.value == this.details.class_id)
        this.selectedWork = this.details.work_types;
        this.pubJobOne.work_type_id = this.selectedWork.map((item) => item.id);
        this.pubJobOne.contact = this.details.contact;
        this.pubJobOne.contact_phone = this.details.contact_phone;
        this.pubJobOne.intro = this.details.intro;
        this.welfareIds = this.details.tags.map((item) => item.id);
        this.pubJobOne.tag_id = this.welfareIds;
        this.jobLabelList = this.details.label;
        this.pubJobOne.label = this.jobLabelList;
        this.pubJobOne.job_describe = this.details.job_describe;

        // 		this.covers[0]['latitude'] = this.details['addresses'][0]['lat']
        // 		this.covers[0]['longitude'] = this.details['addresses'][0]['lng']

        // 		this.latitude = this.details['addresses'][0]['lat']
        // 		this.longitude = this.details['addresses'][0]['lng']
      }
    },
  },
};
</script>
<style lang="scss">
@import "@/static/css/pagesA/add/pubJobOne";
</style>
<style lang="less" scoped>
.se1 {
  margin-left: 120rpx;
}
.footers {
  display: flex;
  align-items: center;
  position: fixed;
  left: 0;
  bottom: 0;
  height: 196rpx;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
  padding-left: 32rpx;
  

  .btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .next {
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 308rpx;
    height: 84rpx;
    background: #e8f1ff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-size: 32rpx;
    color: #4f8cf0;
  }

  .sure {
    background: linear-gradient(92deg, #4f8cf0 0%, #0061ff 100%);
    font-size: 32rpx;
    color: #ffffff;
    margin-left: 70rpx;
  }
}
.tip-box {
  padding: 32rpx;
  border-radius: 24rpx;
  .tip-tit {
    margin-bottom: 24rpx;
  }
}
.list {
  display: flex;
  flex-direction: column;

  .item {
    display: flex;
    align-items: center;
    gap: 32rpx;
    margin: 20rpx 0;

    .right {
      flex: 1;
      .name {
        font-size: 28rpx;
        color: #333333;
        margin-bottom: 16rpx;
      }
    }
    .del {
      width: 32rpx;
      height: 32rpx;
    }
  }
}
.tip-txt {
  font-size: 24rpx;
  color: #777777;
  line-height: 48rpx;
  padding: 32rpx 0;
}
.tip-btm {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .tip-btn {
    width: 96rpx;
    height: 56rpx;
    border-radius: 8rpx;
    background: #2370ee;
    font-size: 24rpx;
    color: #ffffff;
    text-align: center;
    line-height: 52rpx;
    margin-left: 24rpx;
  }
  .tip-btn1 {
    width: 96rpx;
    height: 56rpx;
    border-radius: 8rpx;
    background: #f2f2f2;
    font-size: 24rpx;
    color: #999999;
    text-align: center;
    line-height: 52rpx;
  }
}
::v-deep .u-popup__content {
  border-radius: 24rpx 24rpx 0 0;
}
</style>
