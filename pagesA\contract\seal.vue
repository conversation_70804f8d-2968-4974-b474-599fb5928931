<template>
  <view class="cardBox">
    <image
      src="/static/images/project/listMore.png"
      mode=""
      class="listMore"
      @click.stop="showActionSheet"
    ></image>
    <view class="cardBox_topLeft">印章标题</view>
    <view class="cardBox_peopleName">{{ item.title }}</view>
    <view class="hrCla"></view>
    <view class="cardBox_times marginBox">
      <view>印章创建时间：</view>
      <view>{{ item.created_at }}</view>
    </view>
    <view class="cardBox_times">
      <view>印章更新时间：</view>
      <view>{{ item.updated_at }}</view>
    </view>
  </view>
</template>

<script>
import { sealDestroy } from "../../config/api.js";
export default {
  name: "seal",
  props: {
    item: Object,
  },
  data() {
    return {
      options: ["编辑", "删除"], // 选项列表
      keyword: "",
    };
  },
  methods: {
    showActionSheet() {
      uni.showActionSheet({
        itemList: this.options, // 显示的选项
        success: async (res) => {
          console.log(res.tapIndex);
          if (res.tapIndex == 1) {
            let params = {
              id: this.item.id,
            };
            const res = await sealDestroy(params);
            if (res.status_code == 200) {
              uni.showToast({
                title: "删除成功",
                icon: "none",
              });
              this.$parent.tabClick(2); // 调用父组件的方法
            }
          } else {
            uni.navigateTo({
              url: `/pagesA/contract/contractInput?type=seal&id=${this.item.id}`,
            });
          }
        },
        fail: (err) => {
          // console.error(err); // 处理错误
        },
      });
    },
  },
};
</script>

<style scoped lang="less">
view {
  box-sizing: border-box;
}

.cardBox {
  width: 100%;
  // height: 338rpx;
  background: #ffffff;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 24rpx 32rpx;
  margin-top: 24rpx;
  position: relative;
}

.listMore {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  top: 50rpx;
  right: 32rpx;
}

.cardBox_topLeft {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 24rpx;
}

.cardBox_peopleName {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 16rpx;
}

.abstractBox {
  display: flex;
  justify-content: space-between;
  align-items: top;
}

.abstractBox_title {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
}

.abstractBox_text {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
}

.hrCla {
  width: 100%;
  height: 2rpx;
  background: #f5f5f7;
  margin: 24rpx 0;
}

.cardBox_times {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.marginBox {
  margin-bottom: 16rpx;
}
</style>
