<template>
    <view id="app">
        <u-sticky bgColor="#F5F5F5">
            <u-search placeholder="请输入面试人姓名" bgColor="#FFFFFF" showAction @custom="custom" v-model="keyword"></u-search>
            <view class="filterBox">
                <view class="filter">开始时间
                    <image src="../static/images/down.png" mode="" class="downImg"></image>
                </view>
                <view class="filter">结束时间
                    <image src="../static/images/down.png" mode="" class="downImg"></image>
                </view>
                <view class="filter">面试状态
                    <image src="../static/images/down.png" mode="" class="downImg"></image>
                </view>
                <view class="filter">关联项目
                    <image src="../static/images/down.png" mode="" class="downImg"></image>
                </view>
            </view>
        </u-sticky>


        <view class="content" v-for="(item,index) in page.data" :key="index" :item="item">
            <!-- 这个判断需要调整下-->
           <!-- <view class="topIcon" v-if="item.sign_status=='unsigned'">
                {{item.status_name}}
            </view> -->
            <view class="topIcon greenBtn" v-if="item.interview_result.result_status=='pass'">
            	通过
            </view>
            <view class="topIcon redBtn" v-if="item.interview_result.result_status=='eliminate'">
            	淘汰
            </view>
            <view class="topIcon yellowBtn" v-if="item.interview_result.result_status=='alternate'">
            	备选
            </view>
            <view class="topIcon greyBtn" v-if="item.interview_result.result_status=='none'">
            	未面试
            </view>
            <view class="content_top">
                <image :src="item.member_info.image.path_url" mode="" class="userHead"></image>
                <view style="flex: 1;">
                    <view class="userName">{{ item.member_certification.name }}</view>
                    <view class="userLabel">{{ item.job.title }}</view>
                </view>
            </view>
            <view class="signUpJob">
                <view class="signUpJob_title">关联项目</view>
                <view>{{ item.model.name}}</view>
            </view>

            <view class="timesBox">
                <view>面试时间：{{ item.interviewed_at }}</view>
                <image src="../../static/images/project/rightIcon.png" mode="" class="rightIcon"></image>
            </view>

            <view class="bottomBox">
                <view v-if="item.cancel_status == 1" :class="['fontCla',type==2?'redCla':'blueCla']">
                    拒绝原因:{{ item.cancel_reason }}
                </view>
                <!-- 这个空盒子是占位用的 -->
                <view></view>
                <view class="btnBox">
                    <view class="btn chat" @click="communicate('interview',item.id)">聊聊呗</view>
                    <view class="btn invit" v-if="item.sign_status=='singed'" @click="approve(item)">审核</view>
                </view>
            </view>


        </view>

    </view>
</template>

<script>
    import {communicate} from "../../common/common";
    import {commonInterviewList} from "../../config/api";
    import {interViewResult} from "../../config/headhunterList_api.js"
    export default {
        data() {
            return {
                page: {
                    form: {
                        model_user_id: 0,
                        limit: 10,
                        page: 1,
                        model_type: "project",
                    },
                    data: [],
                    more: false,
                    status: 'nomore',
                },
                options: ['通过', '备选', '淘汰']
            }
        },
        onShow() {
            var _this = this;
            _this.page.data = [];
            _this.page.form.page = 1;

            _this.getList();
        },
        methods: {
            communicate,
            //审核
            approve(op) {
                uni.showActionSheet({
                    itemList: this.options, // 显示的选项
                    // async 
                    success: async (res) => {
                        let resultStatus = res.tapIndex == 0?'pass':res.tapIndex == 1?'alternate':'eliminate'
                        let params = {
                            interview_id:op.id,
                            result_status:resultStatus
                        }
                        const data = await interViewResult(params)
                        this.page.form.page = 1;
                        this.page.data  = []
                        this.getList()
                    },
                    fail: (err) => {
                
                        console.error(err); // 处理错误
                    },
                });
            },
            
            getList() {
                var _this = this;
                const res_userInfo = uni.getStorageSync('userInfo');

                _this.page.form.model_user_id = res_userInfo.login_user.id;

                commonInterviewList(_this.page.form).then(response => {
                    if (response.status_code == '200') {
                        _this.page.data = _this.page.data.concat(response.data.data);
                        _this.page.more = response.data.more || false;
                        _this.page.status = _this.page.more ? 'loading' : 'nomore';
                    }
                })
            }
        }
    }
</script>
<style>
    page {
        background: #F5F5F7;
    }
</style>
<style lang="less" scoped>
    view {
        box-sizing: border-box;
    }

    #app {
        width: 100%;
        padding: 32rpx;
    }

    .downImg {
        width: 32rpx;
        height: 32rpx;
    }

    .filterBox {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
    }

    .filter {
        width: 160rpx;
        height: 50rpx;
        background: #FFFFFF;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 24rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        margin-right: 24rpx;
    }

    .content {
        width: 100%;
        padding: 32rpx;
        background: #fff;
        border-radius: 24rpx;
        margin-bottom: 24rpx;
        position: relative;
    }

    .topIcon {
        background: #4F8CF0;
        border-radius: 0 24rpx 0 24rpx;
        padding: 16rpx 24rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #FFFFFF;
        position: absolute;
        top: 0;
        right: 0;
    }

   .greenBtn {
       background: #57D51C;
       color: #FFFFFF;
   }
   
   .redBtn {
       background: #FE4D4F;
       color: #FFFFFF;
   }
   
   .yellowBtn {
       background: #F9AD14;
       color: #FFFFFF;
   }
   
   .greyBtn {
       background: #cccccc;
       color: #FFFFFF;
   }

    .content_top {
        display: flex;
        align-items: center;
    }

    .userHead {
        width: 104rpx;
        height: 104rpx;
        background: #D9D9D9;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        margin-right: 24rpx;
    }

    .userName {
        font-weight: 600;
        font-size: 32rpx;
        color: #333333;
        margin-right: 16rpx;
    }

    .userLabel {
        width: 502rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        margin-top: 24rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .userNum {
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
    }

    .signUpJob {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
        // margin-bottom: 16rpx;
        margin-top: 16rpx;
    }

    .signUpJob_title {
        color: #999 !important;
    }

    .timesBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 24rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #333333;
    }

    .rightIcon {
        width: 32rpx;
        height: 32rpx;
    }

    .bottomBox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16rpx;
    }

    .btnBox {
        float: right;
        display: flex;
        align-items: right;
    }

    .btn {
        // width: 144rpx;
        // height: 56rpx;
        border-radius: 8rpx 8rpx 8rpx 8rpx;
        padding: 12rpx 24rpx;
        font-weight: 600;
        font-size: 24rpx;
        color: #4F8CF0;
        margin-left: 24rpx;
    }

    .chat {
        color: #4F8CF0;
        background: rgba(79, 140, 240, 0.1);
    }

    .invit {
        background: #4F8CF0;
        color: #FFFFFF;
    }

    .fontCla {
        font-weight: 400;
        font-size: 24rpx;
    }

    .redCla {
        color: #FE4D4F;
    }

    .blueCla {
        color: #4F8CF0;
    }
</style>