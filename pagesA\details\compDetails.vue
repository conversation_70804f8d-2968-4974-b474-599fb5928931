<template>
	<view class="home-index">
		<view class="company-info" @click="goDetail()">
			<image class="logo" :src="details.company_info.logo.path_url" mode=""></image>
			<view class="company">
				<view class="name">
					{{details.name}}
				</view>
				<view class="desc">
					<text
						v-if="details.company_info.financing_type_name">{{details.company_info.financing_type_name}}·</text>
					<text v-if="details.company_info.size_type_name">{{details.company_info.size_type_name}}·</text>
					<text v-if="industry_classesName">{{industry_classesName}}</text>
				</view>
			</view>
		</view>

		<view class="wrap">
			<view class="sub-wrap">
				<view class="whileT">
					<view class="title">
						公司简介
					</view>
					<view class="title-right" @click="toCom()">
						<text>公司职位</text>
						<u-icon name="arrow-right" size="28rpx"></u-icon>
					</view>
				</view>
				<view class="desc">
					{{details.intro||'暂无介绍'}}
				</view>
			</view>
			<u-line color="#F5F5F7" length="100%"></u-line>
			<view class="pics"
				v-if="details.company_info && details.company_info.environment_images && details.company_info.environment_images.length>0">
				<view class="pic" v-for="(item,index) in details.company_info.environment_images" :key="index">
					<image class="env" :src="item.path_url" mode=""></image>
				</view>
			</view>
			<u-line color="#F5F5F7" length="100%"></u-line>

			<address-map title="公司地址" :addresses="addresses"></address-map>
		</view>

		<!-- <view class="job-wrap">
			<view class="title">
				公司职位
			</view>
			<view class="list">
				<job-item v-for="(item,index) in jobList" :key="index" :item="item"></job-item>
			</view>
		</view> -->

		<view class="footer" v-if="roleType!='company'">
			<view class="btns">
				<view class="btn agree" v-if="details.but.collect_status==1" @click="collectEnterprise">
					<u-icon name="star-fill" color="#FFFFFF"></u-icon>
					收藏
				</view>
				<view class="btn cancel" v-else @click="cancelCollectEnterprise">
					<u-icon name="star-fill" color="#FFFFFF"></u-icon>
					已收藏
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		cancelCollectEnterprise,
		collectEnterprise,
		getRecomendList,
		getEnterpriseDetails
	} from "../../config/api.js"
	import JobItem from "../../components/jobItem.vue"
	import AddressMap from "../../public_label/addressMap.vue";
	export default {
		components: {
			AddressMap,
			JobItem
		},
		data() {
			return {
				industry_classesName: '',
				roleType: '',
				id: '',
				details: {},
				jobList: [],
				addresses: [],
				latitude: '',
				longitude: '',
				covers: [{
					id: 1,
					latitude: '',
					longitude: '',
				}]
			}
		},

		onLoad(options) {
			this.id = options.id
			this.getEnterpriseDetails()
			// this.getRecomendList()
			this.roleType = uni.getStorageSync('roleType')
		},

		methods: {
			async getEnterpriseDetails() {
				let params = {
					id: this.id
				}
				const {
					status_code,
					message,
					data
				} = await getEnterpriseDetails(params)
				if (status_code == 200) {

					this.details = data;
					console.log('地址',this.details)
					this.latitude = this.details.register_address.lat
					this.longitude = this.details.register_address.lng

					let address = {
						map_address: this.details.register_address.address_info,
						lat: this.details.register_address.lat,
						lng: this.details.register_address.lng
					}
					this.addresses.push(address)
					if (this.details.industry_classes.length > 0) {
						this.industry_classesName = this.details.industry_classes[0]['name'];
					}

					this.covers[0]['latitude'] = this.details.register_address.lat
					this.covers[0]['longitude'] = this.details.register_address.lng
				}
			},

			// async getRecomendList() {
			// 	let params = {
			// 		company_id: this.id
			// 	}
			// 	const {
			// 		status_code,
			// 		data,
			// 		message
			// 	} = await getRecomendList(params)
			// 	if (status_code == 200) {
			// 		this.jobList = data
			// 	}
			// },
			
			toCom() {
				uni.navigateTo({
					url: "/pagesA/list/company_job_list?id=" + this.id
				})
			},

			async collectEnterprise() {
				let params = {
					id: this.id
				}
				const {
					status_code,
					data,
					message
				} = await collectEnterprise(params)
				if (status_code == 200) {
					this.getEnterpriseDetails()
					return uni.$u.toast(message || '成功')
				}
			},

			async cancelCollectEnterprise() {
				let params = {
					id: this.id
				}
				const {
					status_code,
					data,
					message
				} = await cancelCollectEnterprise(params)
				if (status_code == 200) {
					this.getEnterpriseDetails()
					return uni.$u.toast(message || '成功')
				}
			},
			
			goDetail() {
				uni.navigateTo({
					url: "/pagesA/details/companyDetail"
				})
			}
		}
	}
</script>
<style>
	page {
		background-color: #f5f5f7;
	}
</style>
<style lang="less" scoped>
	.home-index {
		padding: 32rpx 32rpx 170rpx 32rpx;
	}

	.title-right {
		display: flex;
		align-items: center;

		text {
			font-weight: 400;
			font-size: 28rpx;
			color: #999999;
		}
	}

	.whileT {
		display: flex;
		justify-content: space-between;
		
		width: 100%;
	}

	.company-info {
		display: flex;
		background-color: #FFFFFF;
		border-radius: 24rpx;
		padding: 32rpx;

		.logo {
			width: 88rpx;
			height: 88rpx;
			border-radius: 50%;
		}

		.company {
			display: flex;
			flex-direction: column;
			padding-left: 24rpx;

			.name {
				font-weight: 500;
				font-size: 32rpx;
				color: #333333;
			}

			.desc {
				font-weight: 400;
				font-size: 20rpx;
				color: #999999;
				margin-top: 16rpx;
			}
		}
	}

	.job-wrap {
		display: flex;
		flex-direction: column;

		.title {
			font-weight: 600;
			font-size: 32rpx;
			color: #000000;
			padding: 32rpx 0 24rpx 0;
		}
	}

	.wrap {
		display: flex;
		flex-direction: column;
		background-color: #FFFFFF;
		margin-top: 32rpx;
		padding: 32rpx;
		border-radius: 24rpx;

		.sub-wrap {
			display: flex;
			flex-direction: column;

			.title {
				font-weight: 500;
				font-size: 32rpx;
				color: #333333;
				padding-bottom: 24rpx;
			}

			.desc {
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				padding-bottom: 24rpx;
			}
		}

		.pics {
			display: flex;
			overflow: hidden;
			margin-right: -24rpx;
			padding: 24rpx 0;

			// &>image {
			// 	width: calc(33.33% - 24rrpx);
			// 	height: 128rpx;
			// 	margin-right: 24rpx;
			// }

			.pic {
				width: calc(33.33% - 24rpx);
				height: 128rpx;
				margin-right: 24rpx;

				&>image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx;
				}
			}
		}

		.map {
			padding: 24rpx 0;
			border-radius: 16rpx;
		}
	}


	.footer {
		position: fixed;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 140rpx;
		width: 100%;
		left: 0;
		bottom: 0;
		background: #FFFFFF;
		font-weight: 600;
		font-size: 28rpx;

		.btns {
			display: flex;
			width: 90%;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 1;
				height: 80rpx;
				background: #F5F5F7;
				color: #333333;
				border-radius: 16rpx;

				&:first-child {
					margin-right: 20rpx;
				}

			}

			.agree {
				background: linear-gradient(91deg, #F9AD14 0%, #FA9522 100%);
				color: #FFFFFF;
			}

			.cancel {
				background: #cccccc;
				color: #FFFFFF;
			}
		}
	}
</style>