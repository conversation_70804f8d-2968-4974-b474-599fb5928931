<template>
    <view class="container">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <u-tabs :current="tabIndex" lineWidth="20" lineColor="#4F8CF0" :activeStyle="{
					color: '#4F8CF0',
					fontWeight: 'bold',
					transform: 'scale(1.05)'
				}" :inactiveStyle="{
					color: '#999999',
					transform: 'scale(1)'
				}" :list="tabs" @click="changeTab"></u-tabs>
                <view class="search-wrap">
                    <u-search placeholder="请输入关键字" bgColor="#FFFFFF" :showAction="true"
                        @clear="clear" @custom="custom" v-model="keyword"></u-search>
                </view>
                <view class="filters">
                    <view class="filter" @click="selectCity()">
                        {{userCity.name}}
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <!-- <view class="filter" @click="openFilter">
                        筛选<image src="/static/images/index/down.png" mode=""></image>
                    </view> -->
                </view>
            </view>

        </u-sticky>

        <view class="list">
            <job-item v-for="(item,index) in jobList" :key="index" :item="item"></job-item>
        </view>

        <!-- 过滤组件 -->
        <filter ref="filterRef"></filter>
        <Pages :status="status"></Pages>

    </view>
</template>

<script>
    import {
        getRecomendList
    } from "../../config/api.js"
    import TopBarStatus from "../../components/topBarStatus.vue"
    import JobItem from "../../components/jobItem.vue"
    import Pages from "../../components/pages.vue"
    import Filter from "../../components/filter.vue"
    export default {
        components: {
            TopBarStatus,
            JobItem,
            Filter,
            Pages
        },
        data() {
            return {
                userCity: {
                    name: '全国',
                    id: 0
                },
                status: 'loadmore',
                more: false,
                page: 1,
                limit: 10,
                keyword: '',
                isScroll: false,
                tabIndex: 0,
                work_type_id: 0,
                jobList: [],
                tabs: [{
                    name: '全部',
                }, {
                    name: '实习专区',
                }, {
                    name: '零工市场'
                }, {
                    name: '全职专区',
                }]
            }
        },
        onPageScroll(e) {
            this.isScroll = e.scrollTop > 0


        },
        //触底加载更多
        onReachBottom() {
            if (this.more) {
                this.status = 'loading'
                this.page++
                this.getRecomendList()
            } else {
                this.status = 'nomore'
            }
        },
        onLoad(options) {
            // 用户选择的城市从本地获取name、id
            if (uni.getStorageSync('userCity')) {
                this.userCity = uni.getStorageSync('userCity');
            }
            this.tabIndex = parseInt(options.tabIndex)
            this.work_type_id = options.work_type_id
            this.getRecomendList()
        },
        onShow() {
            const cityOld = this.userCity.id; // 当前城市ID
            const cityNew = uni.getStorageSync('userCity').id; // 从缓存中获取选中的城市ID

            if (uni.getStorageSync('userCity')&& cityNew != cityOld) {
                this.userCity = uni.getStorageSync('userCity'); // 更新当前城市
                this.page=1;
                this.jobList = [];
                this.getRecomendList(); // 刷新列表数据
            }
        },
        methods: {
            selectCity() {
                uni.navigateTo({
                    url: '/pagesA/components/selectCitys'
                })
            },
            changeTab(e) {
                let work_type_id = e.index == 0 ? [] : e.index
                this.tabIndex = e.index
                this.work_type_id = work_type_id
                this.page = 1
                this.jobList = []
                this.getRecomendList()
            },

            openFilter() {
                this.$refs.filterRef.open()
            },
            clear() {
                this.keyword = '';
                this.page = 1
                this.jobList = []
                this.getRecomendList();
            },
            custom() {
                this.page = 1
                this.jobList = []
                this.getRecomendList();
            },
            //列表
            async getRecomendList() {
                let params = {
                    limit: this.limit,
                    page: this.page,
                    title: this.keyword,
                    tag_id: [],
                    work_type_id: this.work_type_id,
                    city_id:this.userCity.id
                }

                const {
                    status_code,
                    data
                } = await getRecomendList(params)
                if (status_code == 200) {
                    this.jobList = this.jobList.concat(data.data);
                    this.more = data.more
                }
            },
        }
    }
</script>
<style>
    page {
        background-color: #f5f5f5;

        /* background-image: url('/static/images/login/bg.png'); */
        /* background-image: url('https://h5.zhaopinbei.cn/1.png');
		background-position: 100% 100%;
		background-size: 100% 100%;
		background-repeat: no-repeat; */
    }
</style>
<style lang="less" scoped>
    .bg {
        width: 100vw;
        height: 100vh;
        position: fixed;
        left: 0;
        top: 0;
        z-index: -100;

        image {
            width: 100%;
            height: 100%;
        }
    }



    .list {
        padding: 0 32rpx;
    }

    .header {
        padding: 0 32rpx 32rpx 32rpx;

        .search-wrap {
            margin-top: 32rpx;
        }

        .filters {
            display: flex;
            margin-top: 32rpx;

            .filter {
                display: flex;
                align-items: center;
                height: 48rpx;
                background-color: #FFFFFF;
                font-weight: 400;
                font-size: 24rpx;
                color: #333333;
                padding: 0 12rpx;
                margin-right: 12rpx;
                border-radius: 8rpx;

                image {
                    width: 24rpx;
                    height: 24rpx;
                }
            }
        }
    }
</style>