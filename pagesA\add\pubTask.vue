<template>
    <view class="home-index">
        <view class="wrap">
            <view class="inp">
                <view class="inp-item">
                    <view class="title">
                        任务类型
                        <text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker @change="changeTaskType" :value="taskTypeIndex" :range="taskTypeList" range-key="name">
                            <view class="d-picker" :style="{color:taskTypeIndex==0?'#c0c4cc':'#303133'}">
                                {{taskTypeList[taskTypeIndex]['name']}}
                            </view>
                        </picker>
                        <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        任务标题
                        <text class="star">*</text>
                    </view>
                    <view class="in">
                        <u--input type="text" placeholder="请输入任务标题" placeholderClass="placeholderClass" border="none"
                                  v-model="title"
                        ></u--input>
                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        需要人数
                        <text class="star">*</text>
                    </view>
                    <view class="in">
                        <u--input type="number" placeholder="请输入需要人数" placeholderClass="placeholderClass" border="none"
                                  v-model="count"
                        ></u--input>
                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        任务简介
                        <text class="star">*</text>
                    </view>
                    <view class="in">
                        <u--input type="text" placeholder="请输入任务简介" placeholderClass="placeholderClass" border="none"
                                  v-model="intro"
                        ></u--input>
                    </view>
                </view>

                <view class="inp-item" v-if="type=='job'">
                    <view class="title">
                        关联职位
                        <text class="star">*</text>
                    </view>
                    <view class="se" @click="selectJob">
                        <!-- 多选 -->
                         <view v-if="selectedItems.length>0">
                            <view v-for="(item,index) in selectedItems" :key="item.id">
                                {{item.title}}
                            </view>
                        </view>
                        <view class="" v-else>
                            请选择关联职位
                        </view>
                        <view>
                            {{selectedItems?selectedItems.title:'请选择关联职位'}}
                        </view>

                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        任务佣金
                        <text class="star">*</text>
                    </view>
                    <view class="in">
                        <u--input placeholder="请输入任务佣金" placeholderClass="placeholderClass" border="none"
                                  v-model="unit_total"
                        ></u--input>
                        /人
                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        任务开始时间
                        <text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker mode="date" :value="startTime" @change="bindStartDateChange">
                            <view class="d-picker">{{startTime?startTime:"请选择开始时间"}}</view>
                        </picker>
                        <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>

                <view class="inp-item">
                    <view class="title">
                        任务结束时间
                        <text class="star">*</text>
                    </view>
                    <view class="in se">
                        <picker mode="date" :value="endTime" @change="bindEndDateChange">
                            <view class="d-picker">{{endTime?endTime:"请选择结束时间"}}</view>
                        </picker>
                        <u-icon name="arrow-right"></u-icon>
                    </view>
                </view>

                <view class="avatar">
                    <view class="inp-item">
                        <view class="title">
                            任务图片
                            <text class="star">*</text>
                        </view>
                        <view class="in lab" style="border: none;">
                            请选择上传活动封面，最多支持上传一张图片，每个图片不超过12MB
                        </view>
                    </view>
                    <view class="pic" @click="uploadAvatar">
                        <image :src="imagesData['path_url']?imagesData['path_url']:'https://api-test.zhaopinbei.com/storage/uploads/images/DjpYAiYx76keAZZls68bXyVPGHjYpURJJxA9YHcU.png'" mode=""></image>
                    </view>
                </view>
            </view>
        </view>

        <view class="footer">
            <view class="next save" @click="publishTask('draft')">
                保存草稿
            </view>
            <view class="next pub" @click="publishTask('submit')">
                发布任务
            </view>
        </view>
    </view>
</template>

<script>
    import {publishTask} from '../../config/headhunterList_api.js'
    import {uploadImg} from "../../config/api";

    export default {
        data() {
            return {
                startTime: '',
                start: '',
                endTime: '',
                end: '',
                taskTypeIndex: 0,
                imagesData: [],
                title: '', // 标题
                count: '', // 人数
                intro: '', // 简介
                unit_total: '', // 任务佣金
                type: '',
                taskTypeList: [
                    {
                        value: "",
                        name: '请选择',
                    },
                    {
                        value: "job",
                        name: '职位任务',
                    },
                    {
                        value: "custom",
                        name: '自定义任务',
                    },
                    // {
                    // 	value:2,
                    // 	name:'推广任务',
                    // 	type:'custom'
                    // },
                    // {
                    // 	value:3,
                    // 	name:'招聘会',
                    // 	type:'job_active'
                    // }
                ],
                selectedItems: ''
            }
        },
        onLoad() {
            this.startTime = uni.$u.timeFormat(this.start, 'yyyy-mm-dd');
            this.endTime = uni.$u.timeFormat(this.end, 'yyyy-mm-dd');
        },
        onShow() {
            if (uni.getStorageSync('selectedItems')) {
                this.selectedItems = uni.getStorageSync('selectedItems')
            }
        },
        methods: {
            uploadAvatar() {
                let self = this

                uni.chooseMedia({
                    count: 1,
                    mediaType: ['image'],
                    sizeType: ['original', 'compressed'],
                    sourceType: ['album', 'camera'],
                    success: (tempFilePaths) => {
                        const path = tempFilePaths.tempFiles[0].tempFilePath;
                        // $dialog.loading('上传中')
                        uni.getFileSystemManager().readFile({
                            filePath: path,
                            encoding: 'base64',
                            success: async function (res) {

                                let imageParams = {
                                    ext: 'png',
                                    content: res.data,
                                    org_name: new Date().getTime() + '.png'
                                }
                                // 上传

                                const result = await uploadImg(imageParams)

                                if (result.status_code == 200) {
                                    self.imagesData = {
                                        id: result.data.id,
                                        path_url: result.data.url,
                                        thumbnail_path_url: result.data.thumbnail_url
                                    };
                                }
                            }
                        })
                    }
                });
            },
            selectJob() {
                uni.navigateTo({
                    url: '/pagesA/headhunterList/selectJobList'
                })
            },
            bindPickerChange(e) {
                uni.navigateTo({
                    url: "/pagesA/details/companyDetail"
                })
            },
            changeTaskType(e) {
                this.taskTypeIndex = e.detail.value
                if (this.taskTypeIndex != 0) {
                    this.type = this.taskTypeList[this.taskTypeIndex].value;
                }
            },
            async publishTask(submitType) {
                // 多选
                // const jobId = this.selectedItems.map(item => item.id);
                const jobId = [this.selectedItems.id];
                // 差关联职位 任务图片 。其他的参数已经准备好
                let params = {
                    type: this.type,
                    title: this.title,
                    count: this.count,
                    intro: this.intro,
                    start_at: this.startTime,
                    end_at: this.endTime,
                    submit_type: submitType,
                    unit_total: this.unit_total,
                    job_id: jobId,
                    images: [
                        this.imagesData.id || 0
                    ]
                }


                const result = await publishTask(params)
                if (result.status_code == 200) {
                    uni.showToast({
                        icon: 'none',
                        title: '发布成功',
                    })
                    uni.removeStorageSync('selectedItems');
                    uni.navigateBack();
                }
            },
            bindStartDateChange(e) {
                this.startTime = e.detail.value
            },
            bindEndDateChange(e) {
                this.endTime = e.detail.value
            }
        }
    }
</script>
<style>
    page {
        background-color: #FFFFFF;
    }
</style>
<style lang="less" scoped>
    .home-index {
        padding-bottom: 170rpx;
    }

    .wrap {
        padding: 30rpx;

        .inp {
            background: #FFFFFF;
            border-radius: 16rpx;

            .avatar {
                display: flex;
                align-items: center;

                .pic {
                    padding: 0 30rpx 0 0;

                    image {
                        width: 108rpx;
                        height: 108rpx;
                    }
                }
            }

            .inp-item {
                display: flex;
                flex-direction: column;
                padding: 0 30rpx;
                flex: 1;

                .title {
                    display: flex;
                    align-items: center;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #666666;
                    margin: 16rpx 0 0 0;
                    .star {
                        font-weight: 600;
                        font-size: 22rpx;
                        color: #FE4D4F;
                        margin-left: 8rpx;
                    }
                }

                .in {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    border-bottom: 1px solid #F5F5F7;
                    height: 88rpx;
                    font-size: 32rpx;
                    // ::v-deep picker{
                    // 	display: flex;
                    // 	flex-direction: column;
                    // 	flex: 1;
                    // 	height: 88rpx;
                    // 	.d-picker{
                    // 		display: flex;
                    // 		align-items: center;
                    // 		// width: 60vw;
                    // 		height: 88rpx;
                    // 	}
                    // }

                    ::v-deep .placeholderClass {
                        font-weight: 400;
                        font-size: 32rpx;
                    }
                    ::v-deep picker {
                        display: flex;
                        flex-direction: column;
                        flex: 1;
                        height: 88rpx;
                        .d-picker {
                            display: flex;
                            align-items: center;
                            // width: 60vw;
                            height: 88rpx;
                        }
                    }
                }

                .se {
                    color: #999;
                }

                .lab {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #999999;
                }
            }
        }
    }

    .footer {
        display: flex;
        justify-content: space-around;
        align-items: center;
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 158rpx;
        background-color: #FFFFFF;
        z-index: 10;
        border-radius: 16rpx 16rpx 0 0;

        .next {
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 600;
            height: 80rpx;
            width: 45%;
            font-size: 28rpx;
            border-radius: 24rpx;
        }
        .save {
            border: 1px solid #4F8CF0;
            color: #4F8CF0;
        }
        .pub {
            background: #4F8CF0;
            border: 1px solid #4F8CF0;
            color: #FFFFFF;
        }
    }
</style>
