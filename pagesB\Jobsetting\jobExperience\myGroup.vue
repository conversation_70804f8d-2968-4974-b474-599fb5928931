<template>
	<!-- 社团经历 -->
	<view class="container">
		<view class="context">
			<radio-group @change="chang" class="radio-group">
				<view class="recruitment-item" v-for="(item, index) in recruitmentList" :key="index">
					<scroll-view class="scroll-view_H" scroll-x="true">
						<view class="item-recrutment">
							<view class="item-box">
								<radio value="" :checked="item.isChecked" style="margin-left: 32rpx;" />
								<view class="item-context">
									<view class="item-header">
										<view class="title">
											<text style="margin-right: 32rpx;">{{ item.stylename}}</text>
											<text style="    font-weight: 500;">{{item.Level}}</text>
										</view>
										<text class="time" style="margin-right: 32rpx;">{{ item.time }}</text>
									</view>
									<view class="item-content">
										<view class="description">
											<text>{{item.context1}}</text>
											<text>{{ item.context2 }}</text>
										</view>
										<image :src="editor" @click="editItem(index)"
											style="width: 32rpx; height: 32rpx; align-self: flex-end;margin-right: 32rpx;">
										</image>
									</view>
								</view>
							</view>
							<view class="delete-btn" v-if="item.showDelete" @click="deleteItem(index)">
								<text style="width: 56rpx; font-size: 28rpx;">删除</text>
							</view>
						</view>
					</scroll-view>

				</view>
				<view class="add" @click="add(e)">
					添加
				</view>
			</radio-group>
		</view>
		<view class="footer">
			<view class="confirm-btn" @click="confirmAction">确定</view>
		</view>
	</view>
</template>

<script>
	import editor from '../../../static/jobsetting/editor-setting.png'
	export default {
		data() {
			return {
				recruitmentList: [{
						time: '2023.09 - 2024.04',
						stylename: '羽毛球',
						Level: '副部长',
						context1: '1.负责什么什么',
						context2: '2.负责什么什么',
						showDelete: true,
						isChecked: true
					},
					{
						time: '2023.09 - 2024.04',
						stylename: '羽毛球',
						Level: '副部长',
						context1: '1.负责什么什么',
						context2: '2.负责什么什么',
						showDelete: true,
						isChecked: false
					}
				],
				activeRadio: '',
				editor
			};
		},
		methods: {
			editItem(index) {
				// 编辑逻辑，比如弹出编辑框等
				// console.log('编辑第', index, '项');
				uni.navigateTo({
					url: '/pagesB/Jobsetting/personalresume/organize'
				})
			},
			deleteItem(index) {
				this.recruitmentList.splice(index, 1);
			},
			confirmAction() {
				// 确定操作逻辑
				console.log('执行确定操作');
			},
			chang(e) {
				this.activeRadio = e.detail.value;
				console.log(this.activeRadio);
			}
		}
	};
</script>

<style scoped>
	.container {
		/* padding: 20rpx; */
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		background-color: rgba(245, 245, 247, 1);
	}

	.context {
		padding: 32rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}


	.recruitment-item {
		width: 100%;
		margin-bottom: 42rpx;
	}

	.item-recrutment {
		display: flex;
		background-color: #fff;
		border-radius: 24rpx;
		/* padding: 20rpx; */
		margin-bottom: 20rpx;
		align-items: center;
		width: 686rpx;
		height: 268rpx;
	}

	.scroll-view_H {
		/* white-space: nowrap; */
		width: 100%;
	}

	.item-box {
		width: 686rpx;
		height: 172rpx;
		display: flex;
		align-items: center;
	}

	.item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.time {
		font-size: 28rpx;
		color: #999;
	}

	.item-content {
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
		align-items: flex-start;
		flex: 1;
	}

	.item-context {
		width: 562rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		height: 100%;
		margin-left: 32rpx;
	}

	.tag {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 174rpx;
		height: 64rpx;
		background-color: rgba(243, 243, 243, 1);
		color: rgba(51, 51, 51, 1);
		font-size: 28rpx;
		border-radius: 8rpx;
	}

	.tag-inner {
		width: 30rpx;
		height: 30rpx;
		border: 2rpx solid #ccc;
		border-radius: 50%;
		margin-right: 10rpx;
	}

	.checked {
		background-color: #007AFF;
	}

	.tag-text {
		font-size: 30rpx;
	}

	.description {
		/* flex: 1; */
		height: 80rpx;
		width: 562rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-evenly;
	}

	.edit-icon {
		width: 40rpx;
		height: 40rpx;
		margin-left: 10rpx;
	}

	.delete-btn {
		background-color: #FF5050;
		color: #fff;
		text-align: center;
		border-radius: 24rpx;
		padding: 48rpx;
		/* width: 112rpx; */
		height: 172rpx;
		margin-left: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.footer {
		width: 100vw;
		height: 196rpx;
		display: flex;
		justify-content: center;
		align-self: center;
	}

	.confirm-btn {
		background-color: #007AFF;
		color: #fff;
		text-align: center;
		border-radius: 10rpx;
		/* padding: 20rpx; */
		margin-top: 20rpx;
		width: 622rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 28rpx;
	}

	.description img {
		width: 32rpx;
		height: 32rpx;
	}

	.add {
		color: rgba(79, 140, 240, 1);
		font-size: 26rpx;
	}
</style>

<style>
	.description-text text {
		font-size: 28rpx;
		display: -webkit-box;
		/*弹性伸缩盒子模型显示*/
		-webkit-box-orient: vertical;
		/*排列方式*/
		-webkit-line-clamp: 2;
		/*显示文本行数(这里控制多少行隐藏)*/
		overflow: hidden;
		/*溢出隐藏*/
	}
</style>