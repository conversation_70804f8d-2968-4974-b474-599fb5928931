<template>
	<view class="container">
		<view class="con-form">
			<view class="form-item-header">
				<label class="form-label">高管形象</label>
				<view class="form-content" @click="showImagePicker">
					<image :src="executiveImage" class="executive-image" mode="aspectFill"></image>
				</view>
			</view>
			<view class="form-item">
				<label class="form-label">高管姓名</label>
				<input v-model="executiveName" class="form-input" placeholder="请输入" />
			</view>
			<view class="form-item">
				<label class="form-label">担任职位</label>
				<input v-model="executivePosition" class="form-input" placeholder="请输入" />
			</view>
			<view class="form-item">
				<label class="form-label">高管介绍</label>
				<textarea v-model="executiveIntroduction" class="form-textarea" placeholder="请输入"
					@click="addmanagertext"></textarea>
			</view>
		</view>
		<view v-if="showActionSheet" class="action-sheet">
			<view class="action-sheet-item" @click="takePhoto">拍照</view>
			<view class="action-sheet-item" @click="chooseFromAlbum">从相册中选择</view>
			<view class="action-sheet-item" @click="hideActionSheet" style="border-bottom: none;
		margin-top: 8px; height: 90px;">取消</view>
		</view>
		<view class="footer">
			<view class="footview">
				<view class="footer-card">
					<button class="save" @click="saveData">添加高管</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				executiveImage: '', // 这里暂设为空，实际可根据需求设置默认图片路径等
				executiveName: '',
				executivePosition: '',
				executiveIntroduction: '',
				showActionSheet: false
			};
		},
		methods: {
			saveData() {
				// 这里可以编写保存数据到后端接口等逻辑
				console.log('保存的数据：', {
					executiveImage: this.executiveImage,
					executiveName: this.executiveName,
					executivePosition: this.executivePosition,
					executiveIntroduction: this.executiveIntroduction
				});
			},
			// 打开图片选择器
			showImagePicker() {
				this.showActionSheet = true;
				console.log(this.showActionSheet);
			},
			// 保存相册逻辑（这里只是简单模拟，实际需与后端交互）
			saveAlbum() {
				uni.showToast({
					title: '保存成功',
					icon: 'none'
				});
				// 添加上传图片到服务器的代码uni.uploadFile
			},
			// 打开图片选择菜单
			openImagePicker() {
				this.showActionSheet = true;
				console.log(this.showActionSheet);
			},
			// 关闭图片选择菜单
			hideActionSheet() {
				this.showActionSheet = false;
			},
			// 调用相机拍照
			takePhoto() {
				uni.chooseImage({
					count: 1, // 最多选1张
					sourceType: ['camera'],
					success: (res) => {
						this.executiveImage = this.executiveImage.concat(res.tempFilePaths);
						this.hideActionSheet();
					},
					fail: (err) => {
						uni.showToast({
							title: '拍照失败',
							icon: 'none'
						});
					}
				});
			},
			// 从相册选择图片
			chooseFromAlbum() {
				uni.chooseImage({
					count: 20 - this.executiveImage.length, // 限制最多选20张
					sourceType: ['album'],
					success: (res) => {
						this.executiveImage = this.executiveImage.concat(res.tempFilePaths);
						this.hideActionSheet();
					},
					fail: (err) => {
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},
			addmanagertext() {
				uni.navigateTo({
					url: '/pagesA/bolecompany/companyviews/addtopmanagerment'
				})
			}
		}
	};
</script>

<style scoped>
	.container {
		/* width: 750rpx; */
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.con-form {
		padding-top: 32rpx;
	}

	.form-item-header {
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 686rpx;
		height: 90rpx;
	}

	.form-item {
		margin-bottom: 20rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		width: 686rpx;
		height: 116rpx;
	}


	.form-label {
		display: block;
		margin-bottom: 10rpx;
		color: #333;
		margin-left: 40rpx;
		font-size: 32rpx;
	}

	.form-content {
		display: flex;
		align-items: center;
		margin-right: 32rpx;
	}

	.executive-image {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		background-color: #ddd;
		/* 占位背景色，实际可替换为图片 */
	}

	.form-input {
		width: 686rpx;
		padding: 10rpx;
		border-radius: 5rpx;
		margin-left: 21rpx;
		border: none;
		font-size: 28rpx;
		height: 80rpx;
	}

	.form-textarea {
		width: 686rpx;
		padding: 10rpx;
		border-radius: 5rpx;
		margin-left: 21rpx;
		border: none;
		font-size: 28rpx;
		height: 140rpx;
	}

	.footer {
		/* width: 750rpx; */
		height: 128rpx;
		padding-top: 24rpx;
		box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 1.1);
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		/* padding: 10px; */
		/* box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.15); */
	}

	.footer-left {
		display: flex;
		align-items: center;
		height: 34rpx;
	}

	.footer-text {
		color: rgb(121 121 121);
		/* height: 28px; */
	}

	.footer-icon {
		width: 28rpx;
		height: 28rpx;
		margin-left: 24rpx;
	}

	.save {
		width: 686rpx;
		height: 80rpx;
		background: linear-gradient(to bottom, rgba(79, 140, 240, 1), rgba(30, 109, 238, 1));
		color: white;
		border: none;
		border-radius: 8px;
		font-size: 28rpx;
		/* margin-top: 42px; */
	}

	input,
	textarea {
		width: 100%;
		border: none;
		/* height: 64rpx; */
	}

	textarea {
		height: 40rpx;
	}

	.action-sheet {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: rgba(237, 237, 237, 0.5);
		border-top-left-radius: 10rpx;
		border-top-right-radius: 10rpx;
		z-index: 4;
		width: 100vw;
		/* height: 112px; */
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: flex-end;
	}

	.action-sheet-item {
		padding-top: 16px;
		text-align: center;
		border-bottom: 1rpx solid #eee;
		font-size: 32rpx;
		/* margin-bottom: 8px; */
		background: white;
		align-self: center;
		justify-content: center;
		width: 375px;
		height: 52px;
	}

	.action-sheet-item:last-child {
		border-bottom: none;
	}
</style>