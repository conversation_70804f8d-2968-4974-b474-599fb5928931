<template>
	<view class="warp">
		<view class="row" v-for="item in rowList" :key="item.id">
			<view class="top">
				<view class="tit-warp">
					<view class="tit">
						{{item.title}}
					</view>
					<u-icon name="error-circle" color="#999999" size="14"></u-icon>
				</view>
				<u-switch v-model="item.checked" active-color="#4F8CF0" @change="changeSwitch(item)"></u-switch>
			</view>
			<view class="btm">
				{{item.txt}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				rowList: [{
						title: '对伯乐隐藏简历',
						txt: '开启后，将不会在伯乐的推荐列表里出现',
						checked: false,

						id: 1
					},
					{
						title: '个人主页隐藏简历',
						txt: '开启后，伯乐访问个人主页时简历隐藏',
						checked: false,

						id: 2
					},
					{
						title: '对伯乐隐藏求职偏好',
						txt: '开启后，伯乐将无法查看你的办公区域偏好，沟通时间偏好等',
						checked: false,

						id: 3
					},
					{
						title: '对伯乐隐藏标签信息',
						txt: '开启后，对伯乐隐藏活跃度、在线状态或新千里马标签',
						checked: false,

						id: 4
					},
					{
						title: '对伯乐隐藏出生地信息',
						txt: '开启后，伯乐无法查看您的出生地信息',
						checked: false,

						id: 5
					},
				],
			}
		},
		methods: {
			back() {
				uni.navigateBack()
			},
			changeSwitch(item) {
				console.log(item);
				switch (item.id) {
					case 1:
						// 切换为true
						if (item.checked) {
							uni.showModal({
								title: '对伯乐隐藏简历',
								content: '隐藏简历后，您将不会被推荐给伯乐，除非您主动建立联系，否则伯乐无法查看您的简历，也无法与您沟通',
								success: function(res) {
									if (res.confirm) {
										uni.navigateTo({
											url: './viewPermission_reason'
										})
									} else {
										item.checked = false
									}
								}
							});
						} else {
							uni.showToast({
								icon: 'none',
								title: '已取消对伯乐隐藏简历',
							})
						}
						break;
					case 2:
						if (item.checked) {
							uni.showModal({
								title: '个人主页隐藏简历',
								content: '隐藏简历后，伯乐在社区等地方访问你的个人主页时，就无法查看您的简历。',
								success: function(res) {
									if (res.confirm) {
										item.checked = true
										uni.showToast({
											icon: 'none',
											title: '已在个人主页隐藏简历',
										})
									} else {
										item.checked = false
									}
								}
							});
						} else {
							uni.showToast({
								icon: 'none',
								title: '已取消个人主页隐藏简历',
							})
						}
						break;
					case 3:
						if (item.checked) {
							uni.showModal({
								title: '对伯乐隐藏求职偏好',
								content: '隐藏后，伯乐将无法看到您的办公区域偏好、通过时间偏好等信息，可能会影响您的求职效率',
								success: function(res) {
									if (res.confirm) {
										item.checked = true
										uni.showToast({
											icon: 'none',
											title: '已对伯乐隐藏求职偏好',
										})
									} else {
										item.checked = false
									}
								}
							});
						} else {
							uni.showToast({
								icon: 'none',
								title: '对伯乐展开求职偏好',
							})
						}
						break;
					case 4:
						if (item.checked) {
							uni.showModal({
								title: '对伯乐隐藏标签信息',
								content: '开启后，伯乐将无法查看千里马的活跃度、在线状态等信息，可能会影响您的简历曝光度，从而影响求职效果',
								success: function(res) {
									if (res.confirm) {
										item.checked = true
										uni.showToast({
											icon: 'none',
											title: '对伯乐隐藏标签信息',
										})
									} else {
										item.checked = false
									}
								}
							});
						} else {
							uni.showToast({
								icon: 'none',
								title: '已对伯乐展开标签信息',
							})
						}
						break;
					case 5:
						if (item.checked) {
							uni.showModal({
								title: '对伯乐隐藏出生地信息',
								content: '开启后，伯乐将无法查看您的出生地信息，可能会影响您的求职效率。',
								success: function(res) {
									if (res.confirm) {
										item.checked = true
										uni.showToast({
											icon: 'none',
											title: '成功隐藏出生地信息的展示',
										})
									} else {
										item.checked = false
									}
								}
							});
						} else {
							uni.showToast({
								icon: 'none',
								title: '出生地可正常展示',
							})
						}
						break;
				}
			}
		}
	}
</script>

<style lang="less" scoped>
	.warp {
		width: 686rpx;
		padding: 0 32rpx;
		height: 100vh;
		background: #fff;

		.row {
			padding: 32rpx 0;
			border-bottom: 1rpx solid #E6E6E6;

			.top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 14rpx;

				.tit-warp {
					display: flex;
					align-items: center;

					.tit {
						font-size: 28rpx;
						color: #333333;
						margin-right: 22rpx;
					}
				}

			}

			.btm {
				font-size: 24rpx;
				color: #999999;
			}
		}
	}
</style>