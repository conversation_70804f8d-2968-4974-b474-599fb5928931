<template>
	<view class="container">
		<u-sticky>
			<view class="tabs-container">
				<u-tabs :list="tabsList" :activeStyle="{ color: '#4F8CF0', transform: 'scale(1.1)' }"
					:inactiveStyle="{ color: '#999999', transform: 'scale(0.9)' }"></u-tabs>
			</view>
		</u-sticky>
		<view class="content">
			<view class="item" v-for="v in jobList" :key="v.job.job_id" @click="onDetail(v)">
				<view class="item-start">
					<view class="title-box">
						<view class="title_1">{{v.job.job_info_name}}</view>
						<text class="title_2">{{v.salary.salary_min}} - {{v.salary.salary_max}}</text>
					</view>
					<view class="type-text">{{v.job.work_type_name}}</view>
					<view class="time">{{v.job.job_introduction}}</view>
				</view>
				<u-line></u-line>
				<view class="item-end">
					<image class="avater" :src="v.company.company_logo.thumb_url"></image>
					<text class="name">{{v.company.company_name}}</text>
					<text class="address">{{v.area.area_type_name}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getJobPublishList
	} from '../../config';

	export default {
		data() {
			return {
				tabsList: [{
						key: '1',
						name: '推荐',
					},
					{
						key: '2',
						name: '日结',
					},
					{
						key: '3',
						name: '校内',
					},
					{
						key: '4',
						name: '校外',
					},
					{
						key: '5',
						name: '校园',
					},
				],
				jobList: [],
				params: {
					limit: 20,
					page: 1,
					work_type_id: '2',
					job_type: '2'
				},
				isLoading: false,
			}
		},
		computed: {
			userTypeParams() {
				const user = uni.getStorageSync('userInfo');
				if (!user) return {};
				return {
					member_id: user[user.role_type].id,
				};
			}
		},
		watch: {
			params: {
				handler(value) {
					this.onGetJobPublishList();
				},
				deep: true
			}
		},
		mounted() {
			this.onGetJobPublishList();
		},
		methods: {
			onDetail(v) {
				uni.$u.route({
					url: `/pagesA/details/memberJobDetails`,
					params: {
						id: v.job.job_id,
					}
				})
			},
			onScrollGetList() {
				if (this.isLoading) return;
				this.isLoading = true;
				this.params.page++;
			},
			async onGetJobPublishList() {
				const params = {
					...this.params,
					...this.userTypeParams
				}
				const res = await getJobPublishList(params);
				if (res.status_code !== '200') return;
				this.jobList = [...this.jobList, ...res.data.jobs_list?.data];
				this.isLoading = false;
			}
		}
	}
</script>

<style scoped lang="scss">
	.container {
		display: flex;
		flex-direction: column;
		gap: 24rpx;

		.tabs-container {
			padding-inline: 16rpx;
			background-color: #f5f5f7;
		}

		.content {
			display: flex;
			flex-direction: column;
			gap: 24rpx;
			padding-inline: 32rpx;

			.item {
				background-color: #ffffff;
				border-radius: 24rpx;
				padding: 32rpx;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.item-end {
					display: flex;
					align-items: center;
					gap: 28rpx;

					.avater {
						width: 60rpx;
						height: 60rpx;
					}

					.name {
						color: #333333;
						font-size: 24rpx;
					}

					.address {
						margin-inline-start: auto;
						color: #999999;
						font-size: 24rpx;
					}
				}

				.item-start {
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.time {
						color: #999999;
						font-size: 24rpx;
						overflow: hidden;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 1;
					}

					.type-text {
						color: #4f8cf0;
						font-size: 28rpx;
					}

					.title-box {
						display: flex;
						align-items: center;
						justify-content: space-between;
						gap: 24rpx;

						.title_1 {
							flex: 1;
							color: #333333;
							font-size: 32rpx;
							overflow: hidden;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 1;
						}

						.title_2 {
							color: #f98a14;
							font-size: 36rpx;
							white-space: nowrap;
						}
					}
				}
			}
		}
	}
</style>