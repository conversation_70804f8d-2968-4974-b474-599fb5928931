<template>
	<view class="item" @click="go(item.id)">
		<view class="up">
			<view class="desc">
				<view class="name">
					{{item.task.title}}
				</view>
				<view class="num">
					{{item.task.unit_total}}/人
				</view>
			</view>

			<view class="desc">
				<view :class="['type','green']">
					{{item.task.type_name}}
				</view>
				<view class="num">
					{{item.task.unit_total}}/30
				</view>
			</view>
            <!-- <view>{{item.daily_completion_count}}/{{item.promotions_count}}  {{item.need_count}}/{{item.task.count}}</view> -->
		</view>
		<view class="down">
			<view class="name">
				<text>发布人：{{item.send_member_certification.name}}</text>
				<image src="../static/images/favor.png" mode=""></image>
			</view>
			<view class="comp">
				<view class="info">
					<image :src="item.company_info.logo.thumbnail_path_url" mode=""></image>
					<text>{{item.company.name}}</text>
				</view>
				<view :class="[status,item.task.active_status=='not-started' ? 'not-started':item.task.active_status=='started' ? 'started':item.task.active_status=='end'?'end':'' ]">
					{{item.task.active_status_name}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name:"qlmTaskItem",
		props:{
			item:{
				type:Object,
				default:()=>{}
			}
		},
		data() {
			return {

			};
		},
		methods:{
			go(id){
				uni.navigateTo({
					url:"/pagesB/details/qlmManaTaskDetails?id="+id
				})
			}
		}
	}
</script>

<style lang="less" scoped>
.item{
	display: flex;
	flex-direction: column;
	padding: 0 32rpx;
	background: #FFFFFF;
	border-radius: 24rpx;
	margin-bottom: 24rpx;
	// &:last-child{
	// 	margin-bottom: 0;
	// }
	.up{
		display: flex;
		flex-direction: column;
		padding: 24rpx 0;
		border-bottom: 1px solid #F5F5F7;
		.name{
			font-weight: 600;
			font-size: 32rpx;
			color: #333333;
		}
		.desc{
			display: flex;
			justify-content: space-between;
			margin-top: 16rpx;
			align-items: center;
			.type{
				font-weight: 600;
				font-size: 24rpx;
			}

			.yellow{
				color: #F9AD14;
			}
			.blue{
				color: #1690FF;
			}
			.green{
				color: #57D51C;
			}

			.num{
				font-weight: 600;
				font-size: 32rpx;
				color: #F98A14;
			}
		}
	}

	.down{
		display: flex;
		flex-direction: column;
		padding: 24rpx 0;
		.name{
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-weight: 400;
			font-size: 28rpx;
			color: #333333;
			image{
				width: 50rpx;
				height: 50rpx;
			}
		}

		.comp{
			display: flex;
			align-items: center;
			justify-content: space-between;
			.info{
				display: flex;
				align-items: center;
				margin-top: 16rpx;
				image{
					width: 50rpx;
					height: 50rpx;
					margin-right: 16rpx;
					border-radius: 8rpx;
				}
				text{
					font-weight: 400;
					font-size: 24rpx;
					color: #999999;
				}
			}

			.status{
				display: flex;
				align-items: center;
				border-radius: 8rpx;
				height: 50rpx;
				padding: 0 12rpx;
				font-weight: 600;
				font-size: 24rpx;

			}
			.not-strated {
				background: #F9AD14;
				color: #fff;
			}

			.started {
				background: rgba(79,140,240,0.1);
				color: #4F8CF0;
				border-radius: 10rpx;
				padding: 6rpx 10rpx;
			}

			.end {
				background: #CCC;
				color: #fff;
				border-radius: 10rpx;
				padding: 6rpx 10rpx;
			}


		}

	}
}
</style>
