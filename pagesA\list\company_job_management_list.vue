<template>
    <view class="home-index" :style="{paddingBottom:tabIndex==0?'120rpx':'0'}">
        <u-sticky bgColor="#F5F5F5">
            <view class="header">
                <view class="tabs">
                    <view :class="['tab pg_1',tabIndex==0?'active':'']" @click="changeTab(0)">
                        我发布职位
                    </view>
                    <view :class="['tab pg_2',tabIndex==1?'active':'']" @click="changeTab(1)">
                        抄送记录
                    </view>
                </view>
                <view class="search-wrap">
                    <u-search placeholder="请输入职位名称或发布人" bgColor="#FFFFFF" :showAction="true" v-model="page.form.title"
                              @custom='searchClick'></u-search>
                </view>
                <view class="filters">
                    <view class="filter">
                        <picker @change="changeEdu" :value="eduIndex" :range="eduList" range-key="label">
                            <view class="d-picker">{{eduList[eduIndex]['label']}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker @change="changeJobExperience" :value="jobExperienceIndex" :range="jobExperienceList" range-key="label">
                            <view class="d-picker">{{jobExperienceList[jobExperienceIndex]['label']}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker mode="date" :value="date" @change="bindDateChange" @cancel="clearDate">
                            <view class="d-picker">{{date?date:"发布时间"}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                    <view class="filter">
                        <picker @change="changeStatus" :value="statusIndex" :range="statusList" range-key="name">
                            <view class="d-picker">{{statusList[statusIndex]['name']}}</view>
                        </picker>
                        <image src="/static/images/index/down.png" mode=""></image>
                    </view>
                </view>
            </view>
        </u-sticky>

        <view class="list">
            <block v-if="tabIndex==0">
                <u-swipe-action style="background: red;">
                    <u-swipe-action-item :options="options" v-for="(item,index) in page.data" :name="index" :key="index"
                                         @click="handleSwipe(item,$event)" >
                        <company-pub-job-item :item="item" @expose="expose" @backTop="backTop"></company-pub-job-item>
                    </u-swipe-action-item>
                    <view style="height: 20rpx;"></view>
                </u-swipe-action>

            </block>
            <block v-else>
                <company-draft-job-item v-for="(item,index) in copyPage.data" :key="index"
                                        :item="item"></company-draft-job-item>
            </block>
        </view>

        <view class="footer" v-if="tabIndex==0">
            <view class="next sure" @click="add">
                发布职位
            </view>
        </view>
        <Pages v-if="tabIndex == 0" :status="page.status"></Pages>
        <Pages v-if="tabIndex == 1" :status="copyPage.status"></Pages>
    </view>
</template>

<script>
    import {
        topCompanyJob,
        exposeCompanyJob,
        delCompanyJob,
        getCompanyJobList,
        jobCopyIndex,
    } from "../../config/api.js"

    import Pages from "../../components/pages.vue";
    import CompanyPubJobItem from '../components/companyPubJobItem.vue'
    import CompanyDraftJobItem from '../components/companyDraftJobItem.vue'

    export default {
        components: {
            Pages,
            CompanyPubJobItem,
            CompanyDraftJobItem
        },
        data() {
            return {
                page: {
                    form: {
                        title: '',
                        page: 1,
                        limit: 10,
                        education:0,
                        experience:0,
                        recruiting_status:'',
                        created_at:'',
                    },
                    status: 'loadmore',
                    more: false,
                    data: [],
                },
                copyPage: {
                    form: {
                        title: '',
                        page: 1,
                        limit: 10,
                    },
                    status: 'loadmore',
                    more: false,
                    data: [],
                },
                show: false,
                date: '',
                options: [
                    {
                        text: '删除',
                        style: {
                            backgroundColor: '#FE4D4F',
                            borderRadius: '24rpx',
                            // bottom: '32rpx',
                            height: '86%',
                            width: '150rpx',
                            marginLeft: '24rpx',
							marginBottom: '30rpx',
							marginRight: '2rpx'
                        }
                    }
                ],
                tabIndex: 0,
                eduList: [],
                eduIndex: 0,
                jobExperienceList: [],
                jobExperienceIndex: 0,
                // statusList: [
                //     {
                //         value: 'all',
                //         name: '全部',
                //     }, {
                //         value: 'active',
                //         name: '招聘中',
                //     }, {
                //         value: 'draft',
                //         name: '草稿',
                //     }, {
                //         value: 'off_shelf',
                //         name: '已下架',
                //     }
                // ],
                statusList: [
                    {
                        value: '',
                        name: '全部',
                    }, {
                        value: 'open',
                        name: '招聘中',
                    },{
                        value: 'off',
                        name: '已下架',
                    }
                ],
                statusIndex: 0
            }
        },

        onLoad() {
            this.eduList = this.sysData['education'];
            this.jobExperienceList = this.sysData['experience'];
        },
        onShow() {
            this.initPage();
            this.getList();
        },
        computed:{
            sysData(){
                return this.$store.state.sysData || uni.getStorageSync('sysData');
            },
        },
        onReachBottom() {
            switch (this.tabIndex) {
                case 0:
                    if (this.page.more) {
                        this.page.status = 'loading';
                        this.page.form.page++;
                        this.getList();
                    }
                    break;
                case 1:
                    if (this.copyPage.more) {
                        this.copyPage.status = 'loading';
                        this.copyPage.form.page++;
                        this.getList();
                    }
                    break;
            }

        },
        methods: {
            async getSysData() {
                console.log(uni.getStorageSync('sysData'));
            },
            initPage() {
                switch (this.tabIndex) {
                    case 0:
                        this.page.data = [];
                        this.page.form.page = 1;
                        this.page.status = 'loadmore';
                        break;
                    case 1:
                        this.copyPage.data = [];
                        this.copyPage.form.page = 1;
                        this.copyPage.status = 'loadmore';
                        break;
                }

            },
            getList() {
                switch (this.tabIndex) {
                    case 0:
                        this.getCompanyJobList()
                        break;
                    case 1:
                        this.jobCopyIndex();
                        break;
                }
            },
            async jobCopyIndex() {
                jobCopyIndex(this.copyPage.form).then(response => {
                    if (response.status_code == '200') {
                        this.copyPage.more = response.data.more
                        this.copyPage.data = this.copyPage.data.concat(response.data.data);
                        this.copyPage.status = this.copyPage.more ? 'loadmore' : 'nomore';
                    }
                });

            },
            open() {
                this.show = true
            },
            close() {
                this.show = false
            },
            openDetails(item) {
                this.show = true
            },
            changeTab(index) {
                this.tabIndex = index;
                this.initPage();
                this.getList();
            },
            changeEdu(e) {
                this.eduIndex = e.detail.value
                this.page.form.education = e.detail.value*1
                this.initPage();
                this.getList();
            },
            changeJobExperience(e) {
                this.jobExperienceIndex = e.detail.value
                this.page.form.experience = e.detail.value*1
                this.initPage();
                this.getList();
            },

            changeStatus(e) {
                this.statusIndex = e.detail.value
                this.page.form.recruiting_status = this.statusList[e.detail.value].value
                if(e.detail.value==0) {
                    this.page.form.recruiting_status
                }
                this.initPage();
                this.getList();
            },

            bindDateChange(e) {
                this.date = e.detail.value
                this.page.form.created_at = e.detail.value
                this.initPage();
                this.getList();
            },
            clearDate() {
                this.date = ''
                this.page.form.created_at = ''
                this.initPage();
                this.getList();
            },
            add() {
                uni.navigateTo({
                    url: '/pagesA/add/pubJobOne'
                })
            },
            searchClick() {
                this.initPage();
                this.getList();
            },
            //获取列表
            async getCompanyJobList() {
                getCompanyJobList(this.page.form).then(response => {
                    if (response.status_code == '200') {
                        this.page.more = response.data.more
                        this.page.data = this.page.data.concat(response.data.data);
                        this.page.status = this.page.more ? 'loadmore' : 'nomore';
                    }
                });
            },

            handleSwipe(item, e) {
                console.log(item, e)
                let self = this;
                //表示点击了删除按钮
                if (e.index == 0) {
                    uni.showModal({
                        title: '确定要删除该职位吗？',
                        success: async (res) => {
                            if (res.confirm) {
                                console.log('用户点击确定');
                                let params = {
                                    id: item.id
                                }
                                const {
                                    status_code,
                                    data,
                                    message
                                } = await delCompanyJob(params)
                                if (status_code == 200) {
                                    self.page = 1
                                    self.getCompanyJobList()
                                    return uni.$u.toast('成功')
                                } else {
                                    return uni.$u.toast(message || '失败')
                                }
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    })
                }
            },

            //曝光
            async expose(item) {
                let self = this;
                uni.showModal({
                    title: '确定要曝光该职位吗？',
                    success: async (res) => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            let params = {
                                id: item.id
                            }
                            const {
                                status_code,
                                data,
                                message
                            } = await exposeCompanyJob(params)
                            if (status_code == 200) {
                                self.page = 1
                                self.getCompanyJobList()
                                return uni.$u.toast('成功')
                            } else {
                                return uni.$u.toast('失败' || message)
                            }
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                })
            },

            //置顶
            async backTop(item) {
                let self = this;
                uni.showModal({
                    title: '确定要置顶该职位吗？',
                    success: async (res) => {
                        if (res.confirm) {
                            console.log('用户点击确定');
                            let params = {
                                id: item.id,
                                day: 10
                            }
                            const {
                                status_code,
                                data,
                                message
                            } = await topCompanyJob(params)
                            if (status_code == 200) {
                                self.page = 1
                                self.getCompanyJobList()
                                return uni.$u.toast('成功')
                            } else {
                                return uni.$u.toast('失败' || message)
                            }
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                })
            }
        }
    }
</script>
<style lang="scss">
    @import "../../static/css/pagesA/list/company_job_management_list";
</style>