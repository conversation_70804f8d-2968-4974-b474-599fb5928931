import {
    request
} from "./request"



//好友列表

export const friendList = (data) => {
    return request({
        url: `common-v2/friend`,
        data,
        method: 'post',
        common: 'common'
    })
}

//查询账号
export const friendInfo = (data) => {
    return request({
        url: `common-v2/friend/query`,
        data,
        method: 'post',
        common: 'common'
    })
}
//添加好友
export const friendStore = (data) => {
    return request({
        url: `common-v2/friend/store`,
        data,
        method: 'post',
        common: 'common'
    })
}
export const friendNew = (data) => {
    return request({
        url: `common-v2/friend/new-list`,
        data,
        method: 'post',
        common: 'common'
    })
}

export const friendAgree = (data) => {
    return request({
        url: `common-v2/friend/agree`,
        data,
        method: 'post',
        common: 'common'
    })
}

//就业管家列表
export const headhunterList = (data) => {
    return request({
        url: `common-v2/headhunter/user/index`,
        data,
        method: 'post',
        common: 'common'
    })
}
// 就业管家详情
export const headhunterDetail = (data) => {
    return request({
        url: `common-v2/headhunter/user/show`,
        data,
        method: 'post',
        common: 'common'
    })
}

export const headhunterJobs = (data) => {
    return request({
        url: `common-v2/job/user-job`,
        data,
        method: 'post',
        common: 'common'
    })
}
//文章列表
export const articleList = (data) => {
    return request({
        url: `common-v2/article/index`,
        data,
        method: 'post',
        common: 'common'
    })
}
// 获取文章详情
export const getShowDetails = (data) => {
    return request({
        url: `common-v2/article/show`,
        data,
        method: 'post',
        common: 'common'
    })
}
// 根据tag获取文章详情
export const getTagShowDetails = (data) => {
    return request({
        url: `common-v2/article/tag-show`,
        data,
        method: 'post',
        common: 'common'
    })
}

//获取聊天人列表

export const getChatUserList = (data) => {
    return request({
        url: `common-v2/web-socket/member/user/lists`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 任务广场
export const taskSquare = (data) => {
    return request({
        url: `common-v2/task`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 任务广场-任务详情
export const taskSquareDetail = (data) => {
    return request({
        url: `common-v2/task/show`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 获取二维码
export const getErCode = (data) => {
    return request({
        url: `common-v2/code/code-image`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 任务职位
export const getTaskJobs = (data) => {
    return request({
        url: `common-v2/task/jobs`,
        data,
        method: 'post',
        common: 'common'
    })
}

export const jobReports = (data) => {
    return request({
        url: `common-v2/job/report`,
        data,
        method: 'post',
        common: 'common'
    })
}
//查询该招聘会下所有报名的企业
export const activeCompany = (data) => {
    return request({
        url: `common-v2/job/active/company`,
        data,
        method: 'post',
        common: 'common'
    })
}
//创建聊天
export const authChat = (data) => {
    return request({
        url: `common-v2/web-socket/auth/chat`,
        data,
        method: 'post',
        common: 'common'
    })
}
//获取聊天人列表
export const chatList = (data) => {
    return request({
        url: `common-v2/web-socket/member/user/lists`,
        data,
        method: 'post',
        common: 'common'
    })
}
//获取聊天记录
export const messageRecord = (data) => {
    return request({
        url: `common-v2/web-socket/member/user/resource`,
        data,
        method: 'post',
        common: 'common'
    })
}
//发送消息接口
export const sendMessage = (data) => {
    return request({
        url: `common-v2/web-socket/member/user/send-message`,
        data,
        method: 'post',
        common: 'common'
    })
}

//简历详情
export const getResumeDetails = (data) => {
    return request({
        url: `common-v2/member/member-resume/show`,
        data,
        method: 'post',
        common: 'common'
    })
}

//修改用户定位信息
export const updateMemberLocation = (data) => {
    return request({
        url: `upload-v2/upload/updateMemberLocation`,
        data,
        method: 'post',
        common: 'common'
    })
}

//修改用户定位信息(企业)
export const getCompanyLocation = (data) => {
    return request({
        url: `company-v2/company/getCompanyLocation`,
        data,
        method: 'post',
        common: 'common'
    })
}


//加入协同
export const addFriendStore = (data) => {
    return request({
        url: `common-v2/friend/store`,
        data,
        method: 'post',
        common: 'common'
    })
}

//分享简历面试记录
export const shareInterview = (data) => {
    return request({
        url: `common-v2/interview/share-interview`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 协同列表
export const coordinationIndex = (data) => {
    return request({
        url: `common-v2/coordination/index`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 协同详情
export const coordinationShow = (data) => {
    return request({
        url: `common-v2/coordination/show`,
        data,
        method: 'post',
        common: 'common'
    })
}

// 常见问题
export const jobQuestionList= (data) => {
    return request({
        url: `common-v2/job/question`,
        data,
        method: 'post',
        common: 'common'
    })
}