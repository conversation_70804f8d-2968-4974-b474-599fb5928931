<template>
  <view class="warp">
    <view class="inner">
      <view class="tips">
        <view class="title"> 填写个人介绍有什么用？ </view>
        <view class="cont">
          详尽的个人信息呈现，能有效增强求职者信任，带来更多有效沟通。
        </view>
      </view>
      <view class="line">
        <view class="txt"> 个人介绍 </view>
      </view>
      <u--textarea
        height="184rpx"
        v-model="text"
        placeholder="个人优势、个人经验等"
        maxlength="1000"
        count
      ></u--textarea>
      <view class="coll">
        <view class="coll-header" @click="show = !show">
          <view class="coll-tit"> 优质范例 </view>
          <view class="right">
            <view class=""> {{ show ? "收起" : "展开" }}全部 </view>
            <u-icon
              name="arrow-down"
              v-if="!show"
              color="#8D9AAA"
              size="16"
            ></u-icon>
            <u-icon name="arrow-up" v-else color="#8D9AAA" size="16"></u-icon>
          </view>
        </view>
        <view class="coll-body" v-if="show">
          <view class="body-tit"> 案例一 </view>
          <view class="body-con">
            您好，我是王哈哈，是一名资深的就业管家。在人力资源行业，我已经拥有3年的丰富经验，专注于为各类企业寻找和招募各种人才，帮助众多千里马成功开启职业生涯的新篇章。
          </view>
          <view class="body-tit"> 案例一 </view>
          <view class="body-con">
            您好，我是王哈哈，是一名资深的就业管家。在人力资源行业，我已经拥有3年的丰富经验，专注于为各类企业寻找和招募各种人才，帮助众多千里马成功开启职业生涯的新篇章。
          </view>
        </view>
      </view>
    </view>
    <view class="btn-warp">
      <view class="btn"> 保存 </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      show: false,
    };
  },
};
</script>

<style lang="less" scoped>
.inner {
  padding: 32rpx;

  .tips {
    padding: 40rpx;
    background: linear-gradient(180deg, #f2f8ff 0%, #ffffff 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 2rpx solid rgba(215, 233, 255, 1);
    margin-bottom: 32rpx;

    .title {
      font-size: 24rpx;
      color: #333333;
      margin-bottom: 24rpx;
    }

    .cont {
      font-size: 20rpx;
      color: #8d9aaa;
    }
  }

  .line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    color: #333333;

    .txt {
      margin-bottom: 28rpx;
    }

    img {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .coll {
    padding: 24rpx;
    background: #f2f8ff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    margin-top: 24rpx;

    .coll-header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .coll-tit {
        height: 46rpx;
        font-size: 24rpx;
        color: #333333;
        border-bottom: 6rpx solid #4f8cf0;
      }

      .right {
        display: flex;
        align-items: center;
        gap: 16rpx;
        font-size: 22rpx;
        color: #8d9aaa;
      }
    }

    .coll-body {
      .body-tit {
        margin-top: 28rpx;
        font-size: 24rpx;
        color: #333333;
      }

      .body-con {
        font-size: 22rpx;
        color: #666666;
        margin-top: 24rpx;
      }
    }
  }
}

.btn-warp {
  position: fixed;
  bottom: 0;
  width: 750rpx;
  height: 196rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 32rpx 0rpx rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;

  .btn {
    width: 686rpx;
    height: 80rpx;
    background: linear-gradient(135deg, #4f8cf0 0%, #1e6dee 100%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    font-size: 28rpx;
    color: #ffffff;
    text-align: center;
    line-height: 80rpx;
    margin-top: 24rpx;
  }
}
</style>
