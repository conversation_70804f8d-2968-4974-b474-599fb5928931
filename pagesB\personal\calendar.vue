<template>
	<!-- 日历权限 -->
	<view class="container">
		<u-navbar bgColor="transparent" placeholder :autoBack="true" />
		<scroll-view class="scroll-view" :scroll-y="true">
			<view class="scroll-container">
				<view class="title">日历权限</view>
				<view class="sub-title">查看管理您的日历权限，了解招聘呗如何收集、使用您的日历信息，以及如何保证您的日历信息安全</view>

				<view class="card-container">
					<view class="calendar-box">
						<view class="calendar-start">
							<view class="title">管理您的日历权限设置</view>
							<text class="sub-title">去设置</text>
							<image class="image" src="/static/new/右箭头@2x1.png" alt="" />
						</view>
						<view class="calendar-end">
							关闭日历权限后，我们将不再继续收集和使用您的日历个人信息，也无法为您提供相关日历授权的功能，建议您开启
						</view>
					</view>
				</view>

				<view class="card-container">
					<view class="title">如何保护您的信息安全</view>
					<view class="security-box">
						<view class="item">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/xbc9Gxz1gYl7c2vjKzr5HbK3T7VUYSJg0ceCZwwT.png"></image>
							<view class="text-box">
								<text class="text-start">技术措施</text>
								<text class="text-end">我们已采取安全防护措施保护您的信息</text>
							</view>
						</view>
						<view class="item">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/JhDblqCoZk3HIKh6JaRzn0k9kV2A8WjZqoKfHn7I.png"></image>
							<view class="text-box">
								<text class="text-start">组织管理</text>
								<text class="text-end">我们从组织、制度、人员等多方面提升系统安全性</text>
							</view>
						</view>
						<view class="item">
							<image class="image" src="https://api-test.zhaopinbei.com/storage/uploads/images/8SkSwWAgihxMJCLM4zaKo9g6eoVkqHW1kMO0gG19.png"></image>
							<view class="text-box">
								<text class="text-start">安全事件响应</text>
								<text class="text-end">我们已制定应急预案，会及时处理安全风险</text>
							</view>
						</view>
					</view>
				</view>

				<view class="bottom-text">
					<text>个人信息保护政策</text>
					<image class="image" src="/static/new/右箭头@2x2.png" alt="" />
				</view>
				<view class="bottom-text">
					<text>招聘呗日历权限信息使用规则</text>
					<image class="image" src="/static/new/右箭头@2x2.png" alt="" />
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	},
};
</script>

<style lang="scss" scoped>
.container {
	height: 100vh;
	background-color: #f5f5f7;
	background-image: url(https://api-test.zhaopinbei.com/storage/uploads/images/otitylRNqShH4ecNDEsB3z1vE58BDoHVncUL1s73.png);
	background-size: contain;
	background-repeat: no-repeat;
	display: flex;
	flex-direction: column;

	.bottom-text {
		color: #333333;
		font-size: 24rpx;
		display: flex;
		align-items: center;

		.image {
			width: 32rpx;
			height: 32rpx;
		}
	}

	.scroll-view {
		flex: 1;
		overflow-y: auto;

		.scroll-container {
			padding: 32rpx;
			display: flex;
			flex-direction: column;
			gap: 32rpx;
			padding-block-end: calc(32rpx + constant(safe-area-inset-bottom));
			padding-block-end: calc(32rpx + env(safe-area-inset-bottom));

			.title {
				color: #333333;
				font-size: 32rpx;
			}

			.sub-title {
				color: #777777;
				font-size: 24rpx;
			}

			.card-container {
				padding: 32rpx;
				border-radius: 24rpx;
				background-color: #ffffff;
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.calendar-box {
					display: flex;
					flex-direction: column;
					gap: 24rpx;

					.calendar-start {
						display: flex;
						align-items: center;

						.title {
							color: #333333;
							font-size: 28rpx;
							margin-inline-end: auto;
						}

						.sub-title {
							color: #999999;
							font-size: 24rpx;
						}

						.image {
							width: 32rpx;
							height: 32rpx;
						}
					}

					.calendar-end {
						color: #777777;
						font-size: 24rpx;
					}
				}

				.security-box {
					display: flex;
					flex-direction: column;
					gap: 30rpx;

					.item {
						display: flex;
						align-items: center;
						gap: 24rpx;

						.image {
							width: 104rpx;
							height: 104rpx;
						}

						.text-box {
							display: flex;
							flex-direction: column;
							gap: 20rpx;

							.text-start {
								color: #333333;
								font-size: 28rpx;
							}

							.text-end {
								color: #777777;
								font-size: 24rpx;
							}
						}
					}
				}

				.title {
					color: #333333;
					font-size: 28rpx;
				}
			}
		}
	}
}
</style>
