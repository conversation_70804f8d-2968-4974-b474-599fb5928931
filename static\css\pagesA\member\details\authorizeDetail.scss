page {
    background-color: #F5F5F7;
}

view {
    box-sizing: border-box;
}

#app {
    width: 100%;
    padding: 32rpx;
}

.changeTitle {
    font-weight: bold;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 16rpx;
}

.bulerBoxBig {
    width: 100%;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    background: #FFFFFF;
    margin-bottom: 24rpx;
    padding: 32rpx;
}

.butlerBox {
    display: flex;
    align-items: center;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    background: #FFFFFF;
    margin-bottom: 16rpx;

    .butlerHeadImg {
        width: 88rpx;
        height: 88rpx;
        background: #D9D9D9;
        border-radius: 88rpx 88rpx 88rpx 88rpx;
        margin-right: 16rpx;
    }

    .butlerText {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .butlerNameCla {
            font-weight: 600;
            font-size: 28rpx;
            color: #333333;
            margin-bottom: 16rpx;
        }

        .butlerCompanyCla {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
        }
    }
}

.reasonBox {
    width: 100%;
    min-height: 166rpx;
    height: auto;
    padding: 32rpx;
    background: #FFFFFF;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    margin: 24rpx 0;
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    line-height: 32rpx;
    text-align: justify;
    box-sizing: border-box;
}

.rightIcon {
    width: 32rpx;
    height: 32rpx;
}

.selectPeoCla {
    width: 100%;
    padding: 32rpx;
    background: #FFFFFF;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 32rpx;
}

.btnBox {
    width: 100%;
    padding: 28rpx 42rpx;
    background: #FFFFFF;
    font-weight: 600;
    font-size: 34rpx;
    color: #FFFFFF;
    position: fixed;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column; // 垂直排列
    align-items: center; // 内容居中
    gap: 24rpx; // 设置上下两部分之间的间距
}

.btnOne {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}
.moreText {
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    text-align: center;
    width: 100%;
	margin-bottom: 20rpx;
}

.btnCla {
    width: 100%;
    height: 100rpx;
    border-radius: 24rpx;
    text-align: center;
    line-height: 100rpx;
    margin: 0 8rpx;
}

.btnGrey {
    background: #CCCCCC;
}

.btnBlue {
    background: #4F8CF0;
}

.companyCla {
    width: 100%;
    padding: 32rpx;
    background: #FFFFFF;
    border-radius: 24rpx 24rpx 24rpx 24rpx;
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
}

.companyLogoImg {
    width: 144rpx;
    height: 144rpx;
    margin-right: 24rpx;
}

.companyCla_right {
    flex: 1;
    height: 144rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.companyCla_companyName {
    width: 450rpx;
    font-weight: bold;
    font-size: 28rpx;
    color: #333333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.companyCla_center {
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
}

.labelBox {
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 22rpx;
    color: #666666;
}

.labelBox_child {
    min-width: 130rpx;
    max-width: 150rpx;
    text-align: center;
    padding: 12rpx;
    background: #F6F6F6;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    margin-right: 16rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
// ---------------------------------

	.label {
		/*display: flex;
		align-items: center;
		justify-content: center;*/
        display: flex;
        justify-content: space-between;
		.labels {
			/*width: 330rpx;
			height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: #fff;
			margin: 10rpx 0 10rpx 0;*/
          display: flex;
          justify-content: center;
          align-items: center;
          height: 88rpx;
          display: flex;
          flex: 1;
          background: #FFFFFF;
          font-weight: 400;
          font-size: 28rpx;
          color: #999999;

          &:first-child {
            border-radius: 44rpx 0 0 44rpx;
          }

          &:last-child {
            margin-left: -40rpx;
            border-radius: 0 44rpx 44rpx 0;
          }
		}

		/*.label1 {
			margin-right: 30rpx;
			border-radius: 40rpx 0 0 40rpx;
		}

		.label2 {
			border-radius: 0 40rpx 40rpx 0;
		}*/
      .pg_1 {
        clip-path: polygon(0 0, 100% 0, 80% 100%, 0 100%);
      }

      .pg_2 {
        clip-path: polygon(20% 0, 100% 0, 100% 100%, 0 100%);
      }

      .active {
        color: #FFFFFF;
        background-color: #4F8CF0;
        font-weight: 600;
      }
	}


	.u-transition {
		margin-left: 10rpx;
	}

	.u-avatar {
		margin-right: 20rpx;
	}

	.list {
		margin-top: 20rpx;
		background-color: #fff;
		padding: 32rpx;
		border-radius: 20rpx;
		position: relative;

		.list_1 {
			display: flex;
            align-items: center;
			margin-bottom: 32rpx;
			.list_1_name {
				display: flex;
				align-items: center;
				font-size: 38rpx;
				margin-bottom: 24rpx;
			}

			.list_1_firm {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				color: #666666FF;
				justify-content: space-between;
				width: 500rpx;
			}
		}

		.list_2 {
			display: flex;
			margin-bottom: 38rpx;
		}

		.button {
			display: flex;
			justify-content: flex-end;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 15rpx;
				border-radius: 15rpx;
				// font-weight: 600;
				padding: 0 24rpx;
				height: 60rpx;
				width: 165rpx;
				font-size: 24rpx;
				color: #FFFFFF;

				&:first-child {
					margin-left: 0;
				}
			}

			.talk {
				background: linear-gradient(to right, #4F8CF0FF, #0061FFFF);
				color: #FFFFFFFF;
			}
		}

		.Recording {
			border-bottom: 1rpx solid #E6E6E6FF;
			padding-bottom: 24rpx;
			font-size: 24rpx;
			color: #999999FF;
			margin-bottom: 24rpx;
			;
		}
		.entrust{
			width: 180rpx;
			background-color: #fff;
			text-align: center;
			font-size: 32rpx;
			box-shadow: 0 0 16rpx rgba(204, 204, 204, 0.3);
			position: absolute;
			z-index: 1;
			right: 40rpx;
			top: 230rpx;
			.entrust1{
				border-bottom: 1rpx solid #E6E6E6FF;
				height: 75rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.entrust2{
				height: 75rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

	}
	.color1{
		color: #4F8CF0;
	}
	.color2{
		color: #999999FF;
	}
