<template>
	<view class="container">
		<!-- 图片列表 -->
		<!-- <view class="image-row">
			<view class="item" v-for="(item, index) in images" :key="index"
				:class="{ selected: selectedIndex === index }" @click="selectItem(index)">
				<image class="image-item" :src="item.img" mode="aspectFill"></image>
				<view style="font-size: 24rpx;">{{ item.text }}</view>
			</view>
		</view> -->

		<!-- 底部按钮 -->
		<view class="footer">
			<button class="generate-btn" :class="{ active: selectedIndex !== null }" @click="generateResume">
				生成附件简历
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// images: [{
				// 		img: '../static/images/resumeTemplate/mowhite.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite1.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite2.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite3.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite4.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite5.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite6.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite7.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite8.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite9.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite10.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite11.png',
				// 		text: '黑白'
				// 	},
				// 	{
				// 		img: '../static/images/resumeTemplate/mowhite12.png',
				// 		text: '黑白'
				// 	},
				// ],
				selectedIndex: null,
			}
		},
		methods: {
			// 选中 item
			selectItem(index) {
				this.selectedIndex = index;
			},
			// 生成附件简历
			generateResume() {
				if (this.selectedIndex === null) {
					uni.showToast({
						title: '请先选择一个模板',
						icon: 'none'
					});
					return;
				}
				uni.showToast({
					title: '生成简历成功',
					icon: 'success'
				});
				console.log('选中的模板索引:', this.selectedIndex);
				uni.navigateTo({
				    url: "/pagesA/add/addResume"
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 24rpx;
		background-color: #f5f5f7;
		padding-bottom: 120rpx;
		/* 给底部按钮留出空间 */
	}

	.image-row {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.item {
		width: 38%;
		height: 380rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;
		background-color: white;
		border-radius: 20rpx;
		padding-bottom: 60rpx;
		text-align: center;
		border: 2rpx solid transparent;
		/* 默认边框透明 */
		transition: border-color 0.3s;
		/* 添加过渡效果 */
	}

	.item.selected {
		border-color: #007AFF;
		/* 选中时边框变为蓝色 */
	}

	.image-item {
		width: 100%;
		height: 100%;
		border-radius: 10rpx;
	}

	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 20rpx;
		background-color: white;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.generate-btn {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #CCCCCC;
		/* 默认灰色 */
		color: white;
		border-radius: 40rpx;
		font-size: 28rpx;
		transition: background-color 0.3s;
		/* 添加过渡效果 */
	}

	.generate-btn.active {
		background-color: #007AFF;
		/* 激活时变为蓝色 */
	}
</style>