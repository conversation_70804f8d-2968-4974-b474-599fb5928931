import {
    REQUEST_URL
} from './url.js'
// import BASE_URL from '@/env.js' //引入接口共用地址
const pendingRequests = {}; // 用于存储正在进行的请求

export const request = (options) => {
    return new Promise((resolve, reject) => {
        // 获取用户令牌
        let token = uni.getStorageSync('token')
        let res_userInfo = uni.getStorageSync('userInfo')

        // 设置请求头
        const header = {
            'Content-Type': 'application/json',
            'login_company_type': 'mini_program',
            'login-terminal-type': 'mini_program',
            Authorization: `Bearer ${token}`,
            ...options.header // 可以传入额外的请求头参数
        }
        let IDENTITY_API = 'member-v2/'

        if (res_userInfo.role_type == 'member') {
            IDENTITY_API = 'member-v2'
        }

        if (res_userInfo != '' && res_userInfo.role_type == 'company') {
            IDENTITY_API = 'company-v2'
        }

        if (res_userInfo != '' && res_userInfo.role_type == 'headhunters') {
            IDENTITY_API = 'headhunter-v2'
        }
        let url = REQUEST_URL + options.url

        if (!options.common) {
            url = REQUEST_URL + IDENTITY_API + options.url
        }

        // ⭐在发送请求之前执行拦截操作
        // 检查当前请求是否已经进行中
        if (pendingRequests[url]) {
            // 如果存在，则阻止新的请求发出
            return // uni.showToast({
            // 			title: '请勿重复点击',
            // 			icon: 'none'
            // 		})

        }
        // 标记请求为进行中
        pendingRequests[url] = true;
        uni.showLoading({
            title: 'Loading...'
        })
        if (options.upload && options.upload == true) {
            header["Content-Type"] = "multipart/form-data";
            const fileArray = options.data.file.name.split('.')
            console.log(fileArray);

            uni.uploadFile({
                url,
                filePath: options.data.file.path,
                name: 'resume',
                header: header, // 确保 header 被正确传递
                formData: {
                    'file_name': fileArray[0],
                    'file_type': fileArray[1],
                    'id':uni.getStorageSync('userInfo').member_certification.id
                },
                success: (res) => {
                    uni.hideLoading()
                    resolve(res.data) // 使用resolve将数据传递出去
                },
                fail: (err) => {
                    uni.hideLoading()
                    reject(err)
                },
                complete: () => {
                    // 请求完成后移除标记
                    delete pendingRequests[url];
                }
            });
        } else {
            uni.request({
                url: url, //接收请求的API
                method: options.method || 'GET', //接收请求的方式,如果不传默认为GET
                data: options.data || {}, //接收请求的data,不传默认为空
                header: header, //接收请求的header
                success(res) {
                    // ⭐在这里处理接收到响应后处理响应数据
                    uni.hideLoading()
                    if (res.statusCode && res.statusCode != 200) {
                        const key = res.data?.errors ? Object.keys(res.data.errors)[0] : null
                        const errors = key ? res.data?.errors[key][0] : null
                        return uni.$u.toast(errors || res.data.message || '请求失败! ')
                    }

                    if (res.data.status_code === '401') {
                        uni.showModal({
                            title: '提示',
                            content: '请登录后操作！',
                            cancelText: '去首页',
                            confirmText: '去登录',
                            success: (res) => {
                                if (res.confirm) {
                                    uni.reLaunch({
                                        url: '/pages/login/login'
                                    })
                                } else {
                                    uni.switchTab({
                                        url: '/pages/index/index'
                                    })
                                }
                                return false
                            }
                        })
                        return
                    }

                    // ...(处理其他状态码)...

                    resolve(res.data) // 使用resolve将数据传递出去
                },
                fail: (err) => {
                    uni.hideLoading()
                    reject(err)
                },
                complete: () => {
                    // 请求完成后移除标记
                    delete pendingRequests[url];
                }
            })
        }





    })
}