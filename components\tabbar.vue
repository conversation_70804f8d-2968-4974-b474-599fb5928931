<template>
	<view class="tabbar">
		<view class="tab" v-for="(item,index) in list" :key="index" @click="go(item,index)">
			<image :src="selected==index?item.selectedIconPath:item.iconPath" mode=""></image>
			<text :class="selected==index?'selected':'unselected'">{{ item.text }}</text>
		</view>


		<u-overlay :show="isLoginOverlay">
			<view class="overlay-container">
				<template v-if="steps === '1'">
					<view class="steps-1">
						<view class="item">
							<image class="image"
								src="https://api-test.zhaopinbei.com/storage/uploads/images/cxxCIWSPKNJSx1htXSYZiWXJBN3sF3qQApTjDnE3.png">
							</image>
							<view class="text-box">
								<text class="title">我要求职</text>
								<text class="sub-title">你的未来，从这里开始！</text>
							</view>
						</view>
						<view class="item">
							<image class="image"
								src="https://api-test.zhaopinbei.com/storage/uploads/images/gqe8EpTgw853V4e9ivL44E48rabWtosN9qQE901n.png">
							</image>
							<view class="text-box">
								<text class="title">我要招聘</text>
								<text class="sub-title">让招聘变得快捷、便捷！</text>
							</view>
						</view>
						<view class="btn" @click="isLoginOverlay = false">返回</view>
					</view>
				</template>
				<template v-if="steps === '2'">
					<view class="steps-2">
						<u-form labelWidth="60" :labelStyle="{color: '#999999', fontSize: '28rpx'}">
							<u-form-item label="设置我的头像（默认）" labelPosition="top" labelWidth="200">
								<image class="avatar"
									src="https://api-test.zhaopinbei.com/storage/uploads/images/dMDkxazF8c2TfmJWBUF7kb9zlyZDNQTClmUXfkfO.png">
								</image>
							</u-form-item>
							<u-form-item label="我的姓名（昵称）" labelPosition="top" labelWidth="200">
								<view style="padding-block-start: 24rpx;">
									<u-input :customStyle="inputStyle" placeholder="请输入姓名（昵称）" border="none"></u-input>
								</view>
							</u-form-item>
							<u-form-item label="性别">
								<u-radio-group>
									<view style="display: flex;align-items: center;gap: 32rpx;">
										<u-radio shape="circle" label="男"></u-radio>
										<u-radio shape="circle" label="女"></u-radio>
									</view>
								</u-radio-group>
							</u-form-item>
							<u-form-item label="民族">
								<u-input :customStyle="inputStyle" placeholder="请输入民族" border="none"></u-input>
							</u-form-item>
							<u-form-item label="学历">
								<u-input :customStyle="inputStyle" placeholder="请输入学历" border="none"></u-input>
							</u-form-item>
							<u-form-item label="手机号">
								<u-input :customStyle="inputStyle" placeholder="请输入手机号" border="none">
									<template #suffix>
										<text class="code">发送验证码</text>
									</template>
								</u-input>
							</u-form-item>
							<u-form-item label="验证码">
								<u-input :customStyle="inputStyle" placeholder="请输入验证码" border="none"></u-input>
							</u-form-item>
							<u-form-item>
								<view class="btn">下一步</view>
							</u-form-item>
						</u-form>
					</view>
				</template>
				<template v-if="steps === '3'">
					<view class="steps-3">
						<text class="text">选择求职身份</text>
						<view class="item">
							<image class="image"
								src="https://api-test.zhaopinbei.com/storage/uploads/images/1JRfakx5POWEd1VWFY06OXNobePlzZALhFEv5Hwn.png">
							</image>
							<view class="text-box">
								<text class="title">我是学生</text>
								<text class="sub-title">梦想启航，校园招聘季来袭！</text>
							</view>
						</view>
						<view class="item">

							<image class="image"
								src="https://api-test.zhaopinbei.com/storage/uploads/images/TPBVEsZ1MIsCUo7X9Nkqo7hbJDUAvyXiiKhiOMDl.png">
							</image>
							<view class="text-box">
								<text class="title">职场精英</text>
								<text class="sub-title">为卓越而招聘，期待你的加入！</text>
							</view>
						</view>
						<view class="btn">确认</view>
					</view>
				</template>
				<template v-if="steps === '4'">
					<view class="steps-4">
						<u-form labelPosition="top" labelWidth="100"
							:labelStyle="{color: '#999999', fontSize: '28rpx'}">
							<u-form-item label="在读院校">
								<u-input :customStyle="inputStyle" placeholder="请输入在读院校" border="none"></u-input>
							</u-form-item>
							<u-form-item label="我的专业">
								<u-input :customStyle="inputStyle" placeholder="请输入专业" border="none"></u-input>
							</u-form-item>
							<u-form-item>
								<view class="btn">下一步</view>
							</u-form-item>
						</u-form>
					</view>
				</template>
				<template v-if="steps === '5'">
					<view class="steps-5">
						<view class="search-box">
							<u-input :customStyle="inputStyle" placeholder="请输入职位名称" border="none"></u-input>
						</view>
						<view class="scroll-wrap">
							<scroll-view :scroll-y="true" class="scroll-r-view">
								<view class="scroll-r-container">
									<view v-for="v in scrollLeftColumns" :key="v.id"
										:class="{item: true, is_active: scrollLeftValue === v.id}"
										@click="onScrollLeftTabsClick(v)">
										{{v.name}}
									</view>
								</view>
							</scroll-view>
							<scroll-view :scroll-y="true" class="scroll-l-view">
								<view class="scroll-l-container">
									<view v-for="v in scrollRightColumns" :key="v.id" class="item">
										{{v.name}}
									</view>
								</view>
							</scroll-view>
						</view>
						<view class="btn-container">
							<view class="title-box">
								<text class="title">我选择的职位</text>
								<text class="length">已选4/4</text>
							</view>
							<view class="content">
								<view class="tag">
									<text>我选择的职位</text>
									<image class="image"
										src="https://api-test.zhaopinbei.com/storage/uploads/images/FqP7DbBGt3b1xqHK6GWftrkEXcgIeQmzOg3ABbyr.png">
									</image>
								</view>
							</view>
							<view class="btn">确认</view>
						</view>
					</view>
				</template>
			</view>
		</u-overlay>
	</view>
</template>

<script>
	import {
		getJobClassList
	} from '../config';
	import {
		isAuth,
		isLogin
	} from '@/common/common.js';

	export default {
		name: "tabbar",
		data() {
			return {
				list_qlm: [{
						"pagePath": "/pages/index/index",
						"iconPath": "/static/images/tabbar/unhome.svg",
						"selectedIconPath": "/static/images/tabbar/sehome.png",
						"check": [],
						"text": "首页"
					},
					{
						"pagePath": "/pages/work/work",
						"iconPath": "/static/images/tabbar/unwork.png",
						"selectedIconPath": "/static/images/tabbar/sework.png",
						"check": ['login'],
						"text": "任务广场"
					},
					{
						"pagePath": "/pages/chat/message",
						"iconPath": "/static/images/tabbar/unnotice.svg",
						"selectedIconPath": "/static/images/tabbar/senotice.png",
						"check": ['login', 'info'],
						"text": "消息"
					},
					{
						"pagePath": "/pages/plat/plat",
						"iconPath": "/static/images/tabbar/unplat.png",
						"selectedIconPath": "/static/images/tabbar/seplat.png",
						"check": ['login', 'info'],
						"text": "工作台"
					},
					{
						"pagePath": "/pages/my/my",
						"iconPath": "/static/images/tabbar/unmy.png",
						"selectedIconPath": "/static/images/tabbar/semy.png",
						"check": ['login'],
						"text": "我的"
					}
				],

				list_bl: [{
						"pagePath": "/pages/index/index",
						"iconPath": "/static/images/tabbar/unhome.svg",
						"selectedIconPath": "/static/images/tabbar/sehome.png",
						"check": [],
						"text": "首页"
					},

					{
						"pagePath": "/pages/chat/message",
						"iconPath": "/static/images/tabbar/unnotice.svg",
						"selectedIconPath": "/static/images/tabbar/senotice.png",
						"check": ['login', 'info', 'auth'],
						"text": "消息"
					},
					{
						"pagePath": "/pages/plat/plat",
						"iconPath": "/static/images/tabbar/unplat.png",
						"selectedIconPath": "/static/images/tabbar/seplat.png",
						"check": ['login', 'info', 'auth', 'company_info'],
						"text": "工作台"
					},
					{
						"pagePath": "/pages/my/my",
						"iconPath": "/static/images/tabbar/unmy.png",
						"selectedIconPath": "/static/images/tabbar/semy.png",
						"check": ['login', 'info', 'auth', 'company_info'],
						"text": "我的"
					}
				],

				list_jy: [{
						"pagePath": "/pages/index/index",
						"iconPath": "/static/images/tabbar/unhome.svg",
						"selectedIconPath": "/static/images/tabbar/sehome.png",
						"check": [],
						"text": "首页"
					},

					{
						"pagePath": "/pages/chat/message",
						"iconPath": "/static/images/tabbar/unnotice.svg",
						"selectedIconPath": "/static/images/tabbar/senotice.png",
						"check": ['login', 'info', 'auth', 'company_info'],
						"text": "消息"
					},
					{
						"pagePath": "/pages/plat/plat",
						"iconPath": "/static/images/tabbar/unplat.png",
						"selectedIconPath": "/static/images/tabbar/seplat.png",
						"check": ['login', 'info', 'auth', 'company_info'],
						"text": "工作台"
					},
					{
						"pagePath": "/pages/my/my",
						"iconPath": "/static/images/tabbar/unmy.png",
						"selectedIconPath": "/static/images/tabbar/semy.png",
						"check": ['login', 'info', 'auth', 'company_info'],
						"text": "我的"
					}
				],

				isLoginOverlay: false,
				steps: '1',

				inputStyle: {
					backgroundColor: '#F5F5F7',
					borderRadius: '999rpx',
					paddingBlock: '8rpx',
					paddingInline: '24rpx',
					fontSize: '28rpx'
				},

				scrollLeftValue: '1',
				scrollLeftColumns: []
			};
		},
		computed: {
			scrollRightColumns() {
				return this.scrollLeftColumns.find(el => el.id === this.scrollLeftValue)?.children || [];
			},
			list() {
				let roleType = this.$store.state.roleType || uni.getStorageSync('roleType')
				return roleType == 'member' || roleType == '' ? this.list_qlm : this.list_jy
			},

			selected() {
				return this.$store.state.selected
			},
			tabChat() {
				return this.$store.state.tabChat
			}
		},
		watch: {
			tabChat(newValue) {
				// 监听 this.someValue 的变化
				console.log('someValue changed:', newValue);
			}
		},
		mounted() {
			console.log("组件挂载")
			this.onGetIndustryList();
		},
		methods: {
			onScrollLeftTabsClick(v) {
				this.scrollLeftValue = v.id;
			},
			async onGetIndustryList() {
				const res = await getJobClassList();
				if (res.status_code !== '200') return;
				this.scrollLeftColumns = res.data;
				this.scrollLeftValue = res.data[0].id;
			},
			go(item, index) {
				if (!isLogin()) {
					// this.isLoginOverlay = true;
					return;
				}
				if (!isAuth(item.check)) return
				this.$store.commit('setSelected', index)
				uni.switchTab({
					url: item.pagePath
				})
			},
		}
	}
</script>

<style lang="scss">
	.overlay-container {
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		.steps-5 {
			width: 90%;
			height: 70%;
			background: rgba(255, 255, 255, 0.9);
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			overflow: hidden;

			.search-box {
				padding: 32rpx;
			}

			.scroll-wrap {
				flex: 1;
				display: flex;
				gap: 32rpx;
				overflow: hidden;
				padding-inline: 32rpx;

				.scroll-r-view {
					width: 40%;
					height: 100%;
					overflow-y: auto;

					.scroll-r-container {
						font-size: 28rpx;
						display: flex;
						flex-direction: column;
						gap: 24rpx;
						padding-block-end: 24rpx;

						.item {
							display: flex;
							align-items: center;
							color: #333333;
							position: relative;
							padding-inline-start: 24rpx;

							&::after {
								content: '';
								width: 4rpx;
								height: 24rpx;
								position: absolute;
								top: calc(50% - 12rpx);
								left: 0;
								background-color: #4F8CF0;
								transform: scale(0);
								transition: all .5s linear;
							}
						}

						.is_active {
							color: #4F8CF0;

							&::after {
								transform: scale(1);
								transition: all .2s linear;
							}
						}
					}
				}

				.scroll-l-view {
					flex: 1;
					height: 100%;
					overflow-y: auto;

					.scroll-l-container {
						display: flex;
						flex-wrap: wrap;
						gap: 22rpx;
						padding-block-end: 24rpx;

						.item {
							padding-block: 12rpx;
							padding-inline: 24rpx;
							border-radius: 16rpx;
							background-color: #FFFFFF;
							color: #333333;
							display: flex;
							align-items: center;
							justify-content: center;
							font-size: 24rpx;
						}
					}
				}
			}

			.btn-container {
				padding: 32rpx;
				border-start-start-radius: 24rpx;
				border-start-end-radius: 24rpx;
				background-color: #FFFFFF;

				.content {
					padding-block: 24rpx;
					box-sizing: border-box;
					display: flex;
					flex-wrap: wrap;
					align-items: flex-start;

					.tag {
						display: flex;
						align-items: center;
						gap: 8rpx;
						background-color: rgba(79, 140, 240, 0.1);
						color: #4F8CF0;
						font-size: 24rpx;
						padding-inline: 16rpx;
						padding-block: 8rpx;
						border-radius: 8rpx;

						.image {
							width: 30rpx;
							height: 30rpx;
							object-fit: contain;
						}
					}
				}

				.title-box {
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 32rpx;

					.title {
						color: #000000;
						font-weight: 900;
					}

					.length {
						color: #999999;
					}
				}

				.btn {
					background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
					border-radius: 16rpx;
					height: 96rpx;
					color: #FFFFFF;
					font-size: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}

		.steps-4 {
			width: 90%;
			padding: 32rpx;
			background-color: #F1F1F2;
			border-radius: 24rpx;

			.btn {
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				border-radius: 16rpx;
				height: 96rpx;
				color: #FFFFFF;
				font-size: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.steps-3 {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			gap: 32rpx;

			.text {
				font-weight: 600;
				font-size: 40rpx;
				color: #FFFFFF;
				-webkit-text-stroke: 1rpx #4F8CF0;
			}

			.btn {
				width: 60%;
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				border-radius: 16rpx;
				height: 96rpx;
				color: #FFFFFF;
				font-size: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.item {
				width: 90%;
				box-sizing: border-box;
				background-color: #FFFFFF;
				border-radius: 24rpx;
				padding-block: 10rpx;
				padding-inline: 56rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.image {
					width: 188rpx;
					height: 220rpx;
					object-fit: contain;
				}

				.text-box {
					display: flex;
					flex-direction: column;
					align-items: center;
					gap: 8rpx;

					.title {
						color: #041024;
						font-size: 40rpx;
					}

					.sub-title {
						color: #999999;
						font-size: 24rpx;
					}
				}
			}
		}

		.steps-2 {
			padding: 32rpx;
			background-color: #F1F1F2;
			border-radius: 24rpx;

			.code {
				color: #4F8CF0;
				font-size: 28rpx;
			}

			.avatar {
				width: 88rpx;
				height: 88rpx;
				object-fit: contain;
				border-radius: 999rpx;
				margin-block-start: 24rpx;
			}

			.btn {
				height: 100rpx;
				color: #FFFFFF;
				font-size: 28rpx;
				background: linear-gradient(135deg, #4F8CF0 0%, #1E6DEE 100%);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.steps-1 {
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			gap: 48rpx;

			.btn {
				width: 60%;
				background-color: rgba(255, 255, 255, .9);
				border-radius: 16rpx;
				height: 96rpx;
				color: #4E4E4E;
				font-size: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.item {
				width: 80%;
				box-sizing: border-box;
				background-color: #FFFFFF;
				border-radius: 24rpx;
				padding-block: 10rpx;
				padding-inline: 56rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.image {
					width: 188rpx;
					height: 220rpx;
					object-fit: contain;
				}

				.text-box {
					display: flex;
					flex-direction: column;
					align-items: center;
					gap: 8rpx;

					.title {
						color: #041024;
						font-size: 40rpx;
					}

					.sub-title {
						color: #999999;
						font-size: 24rpx;
					}
				}
			}
		}
	}
</style>

<style lang="less" scoped>
	.tabbar {
		display: flex;
		position: fixed;
		align-items: flex-start;
		box-sizing: border-box;
		padding-block-start: 24rpx;
		z-index: 999;
		width: 100%;
		height: calc(108rpx + constant(safe-area-inset-bottom));
		height: calc(108rpx + env(safe-area-inset-bottom));
		left: 0;
		background-color: #FFFFFF;
		bottom: 0;

		.tab {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			flex: 1;

			image {
				width: 48rpx;
				height: 48rpx;
			}

			.unselected {
				font-weight: 400;
				font-size: 24rpx;
				color: #999999;
			}

			.selected {
				color: #4F8CF0;
				font-size: 24rpx;
				font-weight: 400;
			}
		}
	}
</style>